<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi NPM Package Pattern Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.idle { background-color: #f3f4f6; color: #374151; }
        .status.connecting { background-color: #fef3c7; color: #92400e; }
        .status.connected { background-color: #d1fae5; color: #065f46; }
        .status.ended { background-color: #f3f4f6; color: #374151; }
        .status.error { background-color: #fee2e2; color: #991b1b; }
        
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background-color: #2563eb; }
        button:disabled { background-color: #9ca3af; cursor: not-allowed; }
        
        .messages {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .message.user { background-color: #dbeafe; margin-left: 20px; }
        .message.assistant { background-color: #f3f4f6; margin-right: 20px; }
        
        .volume-meter {
            width: 100px;
            height: 10px;
            background-color: #e5e7eb;
            border-radius: 5px;
            overflow: hidden;
            display: inline-block;
            margin-left: 10px;
        }
        .volume-bar {
            height: 100%;
            background-color: #10b981;
            transition: width 0.1s ease;
        }
    </style>
</head>
<body>
    <h1>🎯 Vapi NPM Package Pattern Test</h1>
    <p>This page tests the Vapi Web SDK using the npm package via ES modules from CDN.</p>

    <div class="container">
        <h2>Call Controls</h2>
        <div id="status" class="status idle">Ready to start call</div>
        
        <div>
            <button id="startBtn" onclick="startCall()">Start Call</button>
            <button id="stopBtn" onclick="stopCall()" disabled>Stop Call</button>
            <button id="muteBtn" onclick="toggleMute()" disabled>Mute</button>
        </div>
        
        <div style="margin: 10px 0;">
            Volume: <span id="volumeText">0%</span>
            <div class="volume-meter">
                <div id="volumeBar" class="volume-bar" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Message Controls</h2>
        <div>
            <input type="text" id="messageInput" placeholder="Send a message to the assistant">
            <button onclick="sendMessage()" disabled id="sendBtn">Send Message</button>
        </div>
        
        <div>
            <input type="text" id="sayInput" placeholder="Make assistant say something">
            <button onclick="makeAssistantSay()" disabled id="sayBtn">Make Assistant Say</button>
        </div>
    </div>

    <div class="container">
        <h2>Conversation</h2>
        <div id="messages" class="messages">
            <div style="text-align: center; color: #6b7280; padding: 20px;">
                No messages yet. Start a call to see the conversation.
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Debug Information</h2>
        <pre id="debugInfo" style="background: #f3f4f6; padding: 10px; border-radius: 4px; font-size: 12px;">
Loading Vapi SDK...
        </pre>
    </div>

    <script type="module">
        // Configuration - Using working values
        const CONFIG = {
            apiKey: '6734febc-fc65-4669-93b0-929b31ff6564', // Correct public API key
            defaultAssistantId: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2' // Working assistant ID
        };

        // Global variables
        let vapi = null;
        let isCallActive = false;
        let isMuted = false;
        let messages = [];

        // Import Vapi using ES modules from CDN
        async function initializeVapi() {
            try {
                console.log('Loading Vapi Web SDK from CDN...');
                updateStatus('connecting', 'Loading Vapi SDK...');
                
                // Try multiple CDN sources for the Vapi Web SDK
                let Vapi;
                try {
                    // Try esm.sh first
                    const module = await import('https://esm.sh/@vapi-ai/web@latest');
                    Vapi = module.default;
                    console.log('Loaded Vapi from esm.sh');
                } catch (error) {
                    console.log('esm.sh failed, trying unpkg...');
                    try {
                        // Try unpkg as fallback
                        const module = await import('https://unpkg.com/@vapi-ai/web@latest/dist/index.esm.js');
                        Vapi = module.default;
                        console.log('Loaded Vapi from unpkg');
                    } catch (error2) {
                        console.log('unpkg failed, trying jsdelivr...');
                        // Try jsdelivr as final fallback
                        const module = await import('https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.esm.js');
                        Vapi = module.default;
                        console.log('Loaded Vapi from jsdelivr');
                    }
                }
                
                if (!Vapi) {
                    throw new Error('Failed to load Vapi from any CDN source');
                }
                
                console.log('Vapi SDK loaded, initializing...');
                
                // Create Vapi instance using the correct pattern
                vapi = new Vapi(CONFIG.apiKey);
                
                // Set up event listeners
                vapi.on('call-start', () => {
                    console.log('Call started');
                    updateStatus('connected', 'Call active');
                    isCallActive = true;
                    updateButtons();
                });

                vapi.on('call-end', () => {
                    console.log('Call ended');
                    updateStatus('ended', 'Call ended');
                    isCallActive = false;
                    updateButtons();
                });

                vapi.on('speech-start', () => {
                    console.log('Assistant started speaking');
                });

                vapi.on('speech-end', () => {
                    console.log('Assistant stopped speaking');
                });

                vapi.on('volume-level', (volume) => {
                    updateVolumeLevel(volume);
                });

                vapi.on('message', (message) => {
                    console.log('Received message:', message);
                    handleMessage(message);
                });

                vapi.on('error', (error) => {
                    console.error('Vapi error:', error);
                    updateStatus('error', `Error: ${error.message || 'Unknown error'}`);
                    isCallActive = false;
                    updateButtons();
                });

                updateStatus('idle', 'Ready to start call');
                console.log('Vapi initialized successfully');
                updateDebugInfo();
                
            } catch (error) {
                console.error('Failed to initialize Vapi:', error);
                updateStatus('error', `Initialization failed: ${error.message}`);
                updateDebugInfo();
            }
        }

        function startCall() {
            try {
                console.log('Starting call...');
                updateStatus('connecting', 'Connecting...');
                
                // Start call using the assistant ID
                vapi.start(CONFIG.defaultAssistantId);
                
                // Clear messages
                messages = [];
                updateMessagesDisplay();
                
            } catch (error) {
                console.error('Failed to start call:', error);
                updateStatus('error', `Failed to start: ${error.message}`);
            }
        }

        function stopCall() {
            try {
                console.log('Stopping call...');
                vapi.stop();
            } catch (error) {
                console.error('Failed to stop call:', error);
            }
        }

        function toggleMute() {
            try {
                isMuted = !isMuted;
                vapi.setMuted(isMuted);
                document.getElementById('muteBtn').textContent = isMuted ? 'Unmute' : 'Mute';
                console.log('Mute toggled:', isMuted);
            } catch (error) {
                console.error('Failed to toggle mute:', error);
            }
        }

        function sendMessage() {
            try {
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value.trim();
                
                if (!message) return;
                
                console.log('Sending message:', message);
                
                vapi.send({
                    type: 'add-message',
                    message: {
                        role: 'system',
                        content: message
                    }
                });
                
                messageInput.value = '';
                
            } catch (error) {
                console.error('Failed to send message:', error);
            }
        }

        function makeAssistantSay() {
            try {
                const sayInput = document.getElementById('sayInput');
                const message = sayInput.value.trim();
                
                if (!message) return;
                
                console.log('Making assistant say:', message);
                vapi.say(message);
                
                sayInput.value = '';
                
            } catch (error) {
                console.error('Failed to make assistant speak:', error);
            }
        }

        function handleMessage(message) {
            if (message.type === 'transcript' && message.transcriptType === 'final') {
                addMessage('user', message.transcript, 'transcript');
            } else if (message.type === 'model-output' && message.output && message.output.content) {
                addMessage('assistant', message.output.content, 'response');
            }
            
            updateDebugInfo();
        }

        function addMessage(role, content, type) {
            const messageObj = {
                id: Date.now(),
                role,
                content,
                type,
                timestamp: new Date().toISOString()
            };
            
            messages.push(messageObj);
            updateMessagesDisplay();
            console.log('Added message:', messageObj);
        }

        function updateMessagesDisplay() {
            const messagesDiv = document.getElementById('messages');
            
            if (messages.length === 0) {
                messagesDiv.innerHTML = '<div style="text-align: center; color: #6b7280; padding: 20px;">No messages yet. Start a call to see the conversation.</div>';
                return;
            }
            
            messagesDiv.innerHTML = messages.map(msg => `
                <div class="message ${msg.role}">
                    <div style="font-size: 12px; font-weight: bold; margin-bottom: 4px;">${msg.role === 'user' ? 'You' : 'Assistant'} ${msg.type ? `(${msg.type})` : ''}</div>
                    <div>${msg.content}</div>
                </div>
            `).join('');
            
            // Scroll to bottom
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(status, text) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = text;
        }

        function updateVolumeLevel(volume) {
            const volumeText = document.getElementById('volumeText');
            const volumeBar = document.getElementById('volumeBar');
            
            const percentage = Math.round(volume * 100);
            volumeText.textContent = `${percentage}%`;
            volumeBar.style.width = `${percentage}%`;
        }

        function updateButtons() {
            document.getElementById('startBtn').disabled = isCallActive;
            document.getElementById('stopBtn').disabled = !isCallActive;
            document.getElementById('muteBtn').disabled = !isCallActive;
            document.getElementById('sendBtn').disabled = !isCallActive;
            document.getElementById('sayBtn').disabled = !isCallActive;
        }

        function updateDebugInfo() {
            const debugInfo = {
                isCallActive,
                isMuted,
                messageCount: messages.length,
                vapiInitialized: !!vapi,
                timestamp: new Date().toISOString()
            };
            
            document.getElementById('debugInfo').textContent = JSON.stringify(debugInfo, null, 2);
        }

        // Make functions available globally for onclick handlers
        window.startCall = startCall;
        window.stopCall = stopCall;
        window.toggleMute = toggleMute;
        window.sendMessage = sendMessage;
        window.makeAssistantSay = makeAssistantSay;

        // Initialize when page loads
        window.addEventListener('load', initializeVapi);

        // Handle Enter key in input fields
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                if (e.target.id === 'messageInput') {
                    sendMessage();
                } else if (e.target.id === 'sayInput') {
                    makeAssistantSay();
                }
            }
        });

        // Update debug info periodically
        setInterval(updateDebugInfo, 1000);
    </script>
</body>
</html>
