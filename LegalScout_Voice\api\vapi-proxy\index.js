/**
 * Vapi API Proxy
 *
 * This file creates a proxy to the Vapi API to avoid CORS issues in development.
 * It forwards requests to the Vapi API and returns the responses.
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Get the API key from the request headers or environment variables
  const apiKey = req.headers.authorization?.replace('Bearer ', '') ||
                process.env.VAPI_TOKEN ||
                process.env.VITE_VAPI_SECRET_KEY ||
                process.env.VITE_VAPI_PUBLIC_KEY;

  if (!apiKey) {
    res.status(401).json({ error: 'No API key provided' });
    return;
  }

  // Get the path from the request URL
  const path = req.url.replace('/api/vapi-proxy', '');

  // Construct the Vapi API URL
  const vapiUrl = `https://api.vapi.ai${path || ''}`;

  // Log the request for debugging
  console.log(`[VapiProxy] ${req.method} ${vapiUrl}`);
  console.log(`[VapiProxy] API Key: ${apiKey ? apiKey.substring(0, 8) + '...' : 'missing'}`);
  if (req.body) {
    console.log(`[VapiProxy] Request body:`, JSON.stringify(req.body, null, 2));
  }

  try {
    // Forward the request to the Vapi API
    const response = await fetch(vapiUrl, {
      method: req.method,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined
    });

    console.log(`[VapiProxy] Response status: ${response.status} ${response.statusText}`);

    // Check if response is ok and has content
    if (!response.ok) {
      // Try to get error message from response
      let errorMessage = `Vapi API error: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.text();
        if (errorData) {
          errorMessage += ` - ${errorData}`;
        }
      } catch (textError) {
        console.warn('Could not read error response text:', textError);
      }

      console.error('Vapi API error:', errorMessage);
      return res.status(response.status).json({
        error: errorMessage,
        status: response.status,
        statusText: response.statusText
      });
    }

    // Get the response data
    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('Error parsing JSON response from Vapi API:', jsonError);
      return res.status(500).json({
        error: 'Invalid JSON response from Vapi API',
        details: jsonError.message
      });
    }

    // Return the response
    res.status(response.status).json(data);
  } catch (error) {
    console.error('Error proxying request to Vapi API:', error);
    res.status(500).json({ error: 'Error proxying request to Vapi API' });
  }
}
