/**
 * Environment Variables API
 *
 * This API endpoint returns environment variables for testing purposes.
 * It only returns non-sensitive environment variables.
 */

export default function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Return environment variables
  res.status(200).json({
    // Vapi environment variables (only return if they exist)
    VITE_VAPI_PUBLIC_KEY: process.env.VITE_VAPI_PUBLIC_KEY ? 'Set (hidden)' : 'Not set',
    VAPI_TOKEN: process.env.VAPI_TOKEN ? 'Set (hidden)' : 'Not set',
    
    // Other non-sensitive environment variables
    NODE_ENV: process.env.NODE_ENV || 'Not set',
    
    // Add timestamp for debugging
    timestamp: new Date().toISOString()
  });
}
