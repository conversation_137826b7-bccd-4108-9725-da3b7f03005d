/**
 * Fix for AttorneyProfileManager undefined attorney issue
 * 
 * This script patches the AttorneyProfileManager._initialize method to handle undefined attorney values
 * that cause "Cannot read properties of undefined (reading 'startsWith')" errors.
 */

(function() {
  console.log('[FixAttorneyUndefined] Starting fix...');

  // Wait for the StandaloneAttorneyManager to be defined
  const waitForManager = () => {
    if (window.standaloneAttorneyManager) {
      applyFix();
    } else {
      console.log('[FixAttorneyUndefined] Waiting for StandaloneAttorneyManager...');
      setTimeout(waitForManager, 100);
    }
  };

  // Apply the fix to the StandaloneAttorneyManager
  const applyFix = () => {
    try {
      console.log('[FixAttorneyUndefined] Applying fix to StandaloneAttorneyManager...');

      // Get the StandaloneAttorneyManager constructor
      const StandaloneAttorneyManager = window.standaloneAttorneyManager.constructor;

      // Save the original _initialize method
      const originalInitialize = StandaloneAttorneyManager.prototype._initialize;

      // Replace with our safer version
      StandaloneAttorneyManager.prototype._initialize = async function(userId, email = null) {
        try {
          console.log('[FixAttorneyUndefined] Safe _initialize called with userId:', userId, 'email:', email);

          // Call the original method
          const attorney = await originalInitialize.call(this, userId, email);

          // If attorney is undefined or null, create a fallback attorney
          if (!attorney) {
            console.warn('[FixAttorneyUndefined] Attorney is undefined, creating fallback attorney');
            
            const fallbackAttorney = {
              id: 'fallback-' + Date.now(),
              user_id: userId,
              email: email || `user-${userId}@example.com`,
              firm_name: 'Fallback Law Firm',
              name: email ? email.split('@')[0] : 'Fallback Attorney',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              vapi_instructions: 'You are a helpful legal assistant for a fallback environment.',
              welcome_message: `Hello! I'm Scout from Fallback Law Firm. How can I help you today?`,
              is_fallback: true
            };

            // Store the fallback attorney
            this.currentAttorney = fallbackAttorney;
            this.saveToLocalStorage(fallbackAttorney);
            this.isInitialized = true;
            this.notifyListeners();

            return fallbackAttorney;
          }

          return attorney;
        } catch (error) {
          console.error('[FixAttorneyUndefined] Error in _initialize:', error);

          // Create a fallback attorney
          const fallbackAttorney = {
            id: 'fallback-' + Date.now(),
            user_id: userId,
            email: email || `user-${userId}@example.com`,
            firm_name: 'Fallback Law Firm',
            name: email ? email.split('@')[0] : 'Fallback Attorney',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            vapi_instructions: 'You are a helpful legal assistant for a fallback environment.',
            welcome_message: `Hello! I'm Scout from Fallback Law Firm. How can I help you today?`,
            is_fallback: true
          };

          // Store the fallback attorney
          this.currentAttorney = fallbackAttorney;
          this.saveToLocalStorage(fallbackAttorney);
          this.isInitialized = true;
          this.notifyListeners();

          return fallbackAttorney;
        }
      };

      // Also patch the saveToLocalStorage method to handle undefined attorneys
      const originalSaveToLocalStorage = StandaloneAttorneyManager.prototype.saveToLocalStorage;
      StandaloneAttorneyManager.prototype.saveToLocalStorage = function(attorney) {
        if (!attorney) {
          console.warn('[FixAttorneyUndefined] Attempted to save undefined attorney to localStorage');
          return;
        }

        // Call the original method
        originalSaveToLocalStorage.call(this, attorney);
      };

      console.log('[FixAttorneyUndefined] Fix applied successfully');
    } catch (error) {
      console.error('[FixAttorneyUndefined] Error applying fix:', error);
    }
  };

  // Start the fix
  waitForManager();
})();
