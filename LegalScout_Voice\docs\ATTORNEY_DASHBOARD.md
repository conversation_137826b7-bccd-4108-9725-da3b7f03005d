# Attorney Dashboard Documentation

## Overview

The Attorney Dashboard is a central feature of LegalScout that allows attorneys to customize their AI assistant, manage their profile, view consultation history, and configure integrations. The dashboard is designed to be intuitive, responsive, and provide real-time previews of changes.

## Dashboard Structure

The dashboard is organized into several tabs:

1. **Profile** - Basic attorney and firm information
2. **Agent** - AI assistant configuration
3. **Custom Fields** - Data collection customization
4. **Automation** - Workflow automation rules
5. **Consultations** - History of client interactions
6. **Integrations** - Third-party service connections

## Component Architecture

```
DashboardNew
├── ProfileTab
├── AgentTab
├── CustomFieldsTab
├── AutomationTab
├── ConsultationsTab
├── IntegrationsTab
└── PreviewPane
    └── SimplifiedPreview
        └── VapiCall
```

## Tab Functionality

### Profile Tab

The Profile Tab allows attorneys to manage their basic information and branding:

- **Firm Information**
  - Firm name
  - Attorney name
  - Practice areas
  - State/jurisdiction
  - Contact information

- **Branding**
  - Logo/banner upload
  - Button image upload
  - Color scheme selection
  - Practice description

#### Implementation Details

- Located in `src/components/dashboard/ProfileTab.jsx`
- Uses Supabase storage for image uploads
- Updates both local state and database records
- Provides real-time preview of changes

### Agent Tab

The Agent Tab allows customization of the AI assistant:

- **Welcome Messages**
  - Initial greeting
  - Information gathering prompt

- **Voice Settings**
  - Voice selection (Waylon, Leyro, etc.)
  - Voice cloning with PlayHT
  - Voice speed adjustment

- **AI Model Configuration**
  - Model selection (GPT-4o, Claude)
  - System instructions
  - Context setting

#### Implementation Details

- Located in `src/components/dashboard/AgentTab.jsx`
- Integrates with Vapi for assistant creation/updates
- Supports voice recording for custom voices
- Updates both Supabase and Vapi configurations

### Custom Fields Tab

The Custom Fields Tab allows attorneys to define custom data collection fields:

- **Field Management**
  - Create new fields
  - Edit existing fields
  - Delete fields
  - Reorder fields

- **Field Types**
  - Text input
  - Multiple choice
  - Date picker
  - File upload

#### Implementation Details

- Located in `src/components/dashboard/CustomFieldsTab.jsx`
- Stores field definitions in Supabase
- Dynamically generates form schemas
- Integrates with AI assistant for data collection

### Automation Tab

The Automation Tab allows setting up automated workflows:

- **Trigger Configuration**
  - New consultation
  - Specific client information
  - Time-based triggers

- **Action Configuration**
  - Email notifications
  - Calendar events
  - CRM updates
  - Document generation

#### Implementation Details

- Located in `src/components/dashboard/AutomationTab.jsx`
- Uses MCP for service integrations
- Supports conditional logic
- Provides testing capabilities

### Consultations Tab

The Consultations Tab displays the history of client interactions:

- **Consultation List**
  - Date and time
  - Client information
  - Duration
  - Summary

- **Consultation Details**
  - Full transcript
  - Collected information
  - Follow-up status
  - Notes

#### Implementation Details

- Located in `src/components/dashboard/ConsultationsTab.jsx`
- Fetches data from Supabase `call_records` table
- Filters by attorney's assistant ID
- Provides sorting and filtering options

### Integrations Tab

The Integrations Tab manages connections to external services:

- **Available Integrations**
  - Calendar services
  - CRM systems
  - Document management
  - Payment processing

- **Configuration**
  - API keys
  - Webhook URLs
  - Authentication setup
  - Sync settings

#### Implementation Details

- Located in `src/components/dashboard/IntegrationsTab.jsx`
- Uses MCP for service connections
- Securely stores credentials
- Provides connection testing

## Preview Pane

The Preview Pane shows a real-time preview of the attorney's customized interface:

- **Visual Preview**
  - Logo and branding
  - Color scheme
  - Button appearance
  - Layout

- **Functional Preview**
  - Interactive AI assistant
  - Voice interaction testing
  - Custom fields testing
  - Full user experience simulation

#### Implementation Details

- Uses `SimplifiedPreview` component
- Updates in real-time as settings change
- Supports both visual and functional previews
- Can be expanded to full-screen mode

## Data Flow

1. **User Authentication**
   - Attorney logs in via Gmail OAuth
   - Supabase authenticates and retrieves attorney record
   - Dashboard loads with attorney's configuration

2. **Configuration Updates**
   - Attorney makes changes in dashboard tabs
   - Changes are saved to local state
   - Preview updates in real-time
   - Changes are saved to Supabase
   - Vapi assistant is updated if necessary

3. **Preview Interaction**
   - Attorney can test their configuration
   - VapiCall component connects to Vapi
   - AI assistant responds based on configuration
   - Results are displayed in preview pane

## Styling and Layout

The dashboard follows a responsive design approach:

- **Layout**
  - Left sidebar for navigation (40% width)
  - Right content area for configuration and preview (60% width)
  - Collapsible sections for better space utilization

- **Theme Support**
  - Light and dark mode
  - Custom color schemes
  - Consistent styling across all tabs

- **Responsive Design**
  - Adapts to different screen sizes
  - Mobile-friendly interface
  - Accessible controls

## State Management

The dashboard uses a combination of state management approaches:

- **Local Component State**
  - Tab-specific configuration
  - Form inputs and validation
  - UI state (expanded/collapsed sections)

- **Shared State**
  - Attorney profile data
  - Preview configuration
  - Authentication state

- **Persistence**
  - Supabase database for long-term storage
  - localStorage for session persistence
  - URL parameters for sharing specific views

## Error Handling

The dashboard implements comprehensive error handling:

- **Form Validation**
  - Input validation with error messages
  - Required field checking
  - Format validation

- **API Error Handling**
  - Graceful handling of Supabase errors
  - Vapi connection error recovery
  - Retry mechanisms for transient errors

- **User Feedback**
  - Success notifications
  - Error messages with recovery options
  - Loading indicators

## Performance Considerations

The dashboard is optimized for performance:

- **Lazy Loading**
  - Tabs load only when selected
  - Heavy components load on demand
  - Images and assets optimized

- **Debouncing**
  - Input changes debounced to reduce API calls
  - Preview updates throttled for smooth experience

- **Caching**
  - Frequently accessed data cached
  - Previous configurations remembered
  - Optimistic UI updates

## Security Measures

The dashboard implements several security measures:

- **Authentication**
  - Secure Gmail OAuth
  - Session management
  - Automatic timeout

- **Authorization**
  - Row-level security in Supabase
  - Access control for attorney data
  - API key protection

- **Data Protection**
  - Secure transmission with HTTPS
  - Sensitive data handling
  - Input sanitization

## Customization Options

The dashboard provides extensive customization options:

- **Visual Customization**
  - Logo and branding
  - Color scheme
  - Button appearance
  - Background settings

- **Functional Customization**
  - AI assistant behavior
  - Voice selection
  - Custom fields
  - Automation rules

- **Content Customization**
  - Welcome messages
  - Practice description
  - Information gathering prompts
  - Response templates

## Future Enhancements

Planned enhancements for the attorney dashboard:

1. **Advanced Analytics**
   - Consultation metrics
   - Conversion tracking
   - Performance analytics

2. **Enhanced Automation**
   - More trigger types
   - Advanced conditional logic
   - Multi-step workflows

3. **Team Management**
   - Multiple user accounts
   - Role-based access control
   - Collaboration features

4. **Advanced Integrations**
   - More third-party services
   - Deeper integration capabilities
   - Custom webhook support

5. **Mobile App**
   - Native mobile experience
   - Push notifications
   - Offline capabilities
