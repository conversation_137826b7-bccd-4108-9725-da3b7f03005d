/**
 * Test Current State Script
 * 
 * This script tests the current authentication and attorney state
 * to verify everything is working correctly.
 */

(function() {
  'use strict';

  console.log('🧪 [TestCurrentState] Starting current state test...');

  // Wait for everything to be ready
  function waitForReady() {
    return new Promise((resolve) => {
      const checkReady = () => {
        if (window.supabase && 
            typeof window.resolveAttorneyState === 'function' &&
            document.readyState === 'complete') {
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
    });
  }

  async function testCurrentState() {
    try {
      await waitForReady();
      console.log('✅ [TestCurrentState] All dependencies ready');

      // 1. Test authentication
      console.log('🔍 [TestCurrentState] Testing authentication...');
      const { data: { user }, error: userError } = await window.supabase.auth.getUser();
      
      if (userError || !user) {
        console.error('❌ [TestCurrentState] Authentication failed:', userError);
        return;
      }

      console.log('✅ [TestCurrentState] User authenticated:', user.email);

      // 2. Test robust state handler
      console.log('🔍 [TestCurrentState] Testing robust state handler...');
      const stateResult = await window.resolveAttorneyState(user.email);
      
      console.log('📋 [TestCurrentState] State result:', stateResult);

      if (stateResult.success) {
        console.log('✅ [TestCurrentState] Robust state handler succeeded!');
        console.log('📋 [TestCurrentState] Attorney:', stateResult.attorney?.firm_name);
        console.log('📋 [TestCurrentState] Assistant ID:', stateResult.attorney?.vapi_assistant_id);
        console.log('📋 [TestCurrentState] Assistants found:', stateResult.assistants?.length || 0);
      } else {
        console.error('❌ [TestCurrentState] Robust state handler failed:', stateResult.error);
      }

      // 3. Test direct attorney lookup
      console.log('🔍 [TestCurrentState] Testing direct attorney lookup...');
      const { data: attorneys, error: attorneyError } = await window.supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', user.id);

      if (attorneyError) {
        console.error('❌ [TestCurrentState] Attorney lookup failed:', attorneyError);
      } else {
        console.log('✅ [TestCurrentState] Direct lookup found', attorneys.length, 'attorneys');
        attorneys.forEach((attorney, index) => {
          console.log(`📋 [TestCurrentState] Attorney ${index + 1}:`, {
            id: attorney.id,
            email: attorney.email,
            firm_name: attorney.firm_name,
            subdomain: attorney.subdomain,
            vapi_assistant_id: attorney.vapi_assistant_id
          });
        });
      }

      // 4. Summary
      console.log('📊 [TestCurrentState] SUMMARY:');
      console.log('- User authenticated:', !!user);
      console.log('- User email:', user?.email);
      console.log('- User ID:', user?.id);
      console.log('- Robust state handler success:', stateResult?.success);
      console.log('- Attorney found:', !!stateResult?.attorney);
      console.log('- Assistant ID:', stateResult?.attorney?.vapi_assistant_id);
      console.log('- Direct lookup attorneys:', attorneys?.length || 0);

      // Store results globally
      window.currentStateTestResults = {
        user,
        stateResult,
        attorneys,
        timestamp: new Date().toISOString()
      };

      console.log('✅ [TestCurrentState] Test complete. Results stored in window.currentStateTestResults');

    } catch (error) {
      console.error('❌ [TestCurrentState] Test failed:', error);
    }
  }

  // Run test when everything is ready
  setTimeout(testCurrentState, 3000); // Wait 3 seconds for everything to load

  // Also expose as global function
  window.testCurrentState = testCurrentState;

})();
