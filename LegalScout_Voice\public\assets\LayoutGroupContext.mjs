// Direct replacement for LayoutGroupContext.mjs in production
console.log('[DirectReplacement] Using direct replacement for LayoutGroupContext.mjs');

// Create a simple object instead of using React.createContext
const LayoutGroupContext = {
  Provider: function(props) { return props.children || null; },
  Consumer: function(props) { return props.children ? props.children({}) : null; },
  displayName: 'LayoutGroupContext'
};

export { LayoutGroupContext };
export default LayoutGroupContext;
