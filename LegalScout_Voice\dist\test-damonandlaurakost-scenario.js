/**
 * Test <EMAIL> Scenario
 * 
 * Tests the exact scenario from the logs:
 * - Email: <EMAIL>
 * - No existing attorney record
 * - System should handle gracefully
 * - Create attorney and assistant as needed
 */

(function testDamonAndLauraKostScenario() {
  console.log('🧪 [TestDamonAndLauraKost] Testing <EMAIL> scenario...');
  
  const testEmail = '<EMAIL>';
  
  // Wait for dependencies
  let checkInterval = setInterval(() => {
    if (document.readyState === 'complete' && 
        window.supabase && 
        window.resolveAttorneyState && 
        window.loadAttorneyWithRobustHandling) {
      clearInterval(checkInterval);
      initializeTest();
    }
  }, 100);
  
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[TestDamonAndLauraKost] Timed out waiting for dependencies');
  }, 10000);
  
  function initializeTest() {
    console.log('🚀 [TestDamonAndLauraKost] Starting test initialization...');
    
    // Add test button to UI
    addTestButton();
    
    // Auto-run test if in development mode
    if (window.location.hostname === 'localhost') {
      setTimeout(() => {
        console.log('[TestDamonAndLauraKost] Auto-running test in development mode...');
        runFullTest();
      }, 2000);
    }
  }
  
  async function runFullTest() {
    console.log('🧪 [TestDamonAndLauraKost] Running full scenario test...');
    
    try {
      // Step 1: Test state resolution
      console.log('📋 Step 1: Testing state resolution...');
      const state = await window.resolveAttorneyState(testEmail);
      
      console.log('✅ State resolved:', {
        success: state.success,
        attorneyExists: !!state.attorney,
        assistantCount: state.assistants.length,
        needsCreation: state.needsCreation
      });
      
      if (!state.success) {
        throw new Error('State resolution failed: ' + state.error);
      }
      
      // Step 2: Test dashboard loading
      console.log('📋 Step 2: Testing dashboard loading...');
      const dashboardState = await window.loadAttorneyWithRobustHandling(testEmail);
      
      console.log('✅ Dashboard loaded:', {
        attorneyId: dashboardState.attorney.id,
        firmName: dashboardState.attorney.firm_name,
        assistantCount: dashboardState.assistants.length
      });
      
      // Step 3: Test assistant creation if needed
      if (dashboardState.needsCreation) {
        console.log('📋 Step 3: Testing assistant creation...');
        
        const assistantResult = await window.createControlledAssistant(dashboardState.attorney);
        
        console.log('✅ Assistant created:', {
          success: assistantResult.success,
          assistantId: assistantResult.assistant?.id,
          message: assistantResult.message
        });
        
        // Step 4: Verify final state
        console.log('📋 Step 4: Verifying final state...');
        const finalState = await window.resolveAttorneyState(testEmail);
        
        console.log('✅ Final state:', {
          attorneyExists: !!finalState.attorney,
          assistantCount: finalState.assistants.length,
          needsCreation: finalState.needsCreation,
          selectedAssistant: finalState.selectedAssistant?.id
        });
        
        if (finalState.needsCreation) {
          throw new Error('Assistant creation failed - still needs creation');
        }
      } else {
        console.log('📋 Step 3: Assistant already exists, skipping creation');
      }
      
      // Step 5: Test dropdown behavior
      console.log('📋 Step 5: Testing dropdown behavior...');
      testDropdownBehavior(dashboardState);
      
      console.log('🎉 [TestDamonAndLauraKost] All tests passed successfully!');
      showTestResult('success', 'All tests passed! <NAME_EMAIL> correctly.');
      
    } catch (error) {
      console.error('❌ [TestDamonAndLauraKost] Test failed:', error);
      showTestResult('error', `Test failed: ${error.message}`);
      throw error;
    }
  }
  
  function testDropdownBehavior(state) {
    console.log('🎨 [TestDamonAndLauraKost] Testing dropdown behavior...');
    
    // Create a mock dropdown to test behavior
    const mockDropdown = document.createElement('select');
    mockDropdown.className = 'assistant-select';
    mockDropdown.style.display = 'none';
    document.body.appendChild(mockDropdown);
    
    // Trigger the update
    const event = new CustomEvent('attorneyStateUpdated', {
      detail: state
    });
    document.dispatchEvent(event);
    
    // Check dropdown options
    const options = Array.from(mockDropdown.options).map(opt => ({
      value: opt.value,
      text: opt.textContent
    }));
    
    console.log('📋 Dropdown options:', options);
    
    // Validate dropdown state
    if (state.needsCreation) {
      if (!options.some(opt => opt.value === '')) {
        throw new Error('Missing "No Assistant" option');
      }
      if (!options.some(opt => opt.value === 'create_new')) {
        throw new Error('Missing "Create New Assistant" option');
      }
    } else {
      if (!options.some(opt => opt.value === state.selectedAssistant?.id)) {
        throw new Error('Selected assistant not in dropdown');
      }
    }
    
    // Clean up
    document.body.removeChild(mockDropdown);
    
    console.log('✅ [TestDamonAndLauraKost] Dropdown behavior test passed');
  }
  
  function addTestButton() {
    if (document.body) {
      const testButton = document.createElement('button');
      testButton.textContent = '🧪 Test <EMAIL>';
      testButton.style.cssText = `
        position: fixed;
        top: 60px;
        right: 10px;
        z-index: 10000;
        padding: 10px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 12px;
        max-width: 200px;
      `;
      
      testButton.onclick = async () => {
        try {
          testButton.textContent = '🧪 Testing...';
          testButton.disabled = true;
          
          await runFullTest();
          
        } catch (error) {
          console.error('Test button error:', error);
        } finally {
          testButton.textContent = '🧪 Test <EMAIL>';
          testButton.disabled = false;
        }
      };
      
      document.body.appendChild(testButton);
      console.log('🧪 [TestDamonAndLauraKost] Test button added to UI');
    }
  }
  
  function showTestResult(type, message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 120px;
      right: 10px;
      z-index: 10000;
      padding: 15px;
      border-radius: 5px;
      color: white;
      font-weight: 500;
      max-width: 300px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      background: ${type === 'success' ? '#28a745' : '#dc3545'};
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 8000);
  }
  
  // Expose test function globally for manual testing
  window.testDamonAndLauraKostScenario = runFullTest;
  
})();
