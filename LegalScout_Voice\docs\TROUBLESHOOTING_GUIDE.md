# 🔧 LegalScout Troubleshooting Guide

## 🚨 Common Issues & Solutions

### 1. Environment Setup Issues

#### Problem: `npm install` fails
```bash
# Solution 1: Clear cache
npm cache clean --force
rm -rf node_modules package-lock.json
npm install

# Solution 2: Use specific Node version
nvm use 18
npm install

# Solution 3: Check for permission issues (Windows)
npm config set registry https://registry.npmjs.org/
```

#### Problem: Environment variables not loading
```bash
# Check file exists
ls -la .env.local

# Verify format (no spaces around =)
VITE_VAPI_PUBLIC_KEY=your_key_here

# Restart dev server after changes
npm run dev:full
```

### 2. Vapi Integration Issues

#### Problem: Vapi MCP connection fails
```bash
# Test connection
npm run test:vapi-mcp

# Check environment variable
echo $VAPI_PRIVATE_KEY

# Verify MCP server is running
npx -y @vapi-ai/mcp-server
```

#### Problem: Assistant creation loops
```javascript
// Check for existing assistants first
const existingAssistants = await vapiService.getAssistants();
const attorneyAssistant = existingAssistants.find(
  a => a.metadata?.attorney_id === attorneyId
);

if (!attorneyAssistant) {
  // Only create if none exists
  await vapiService.createAssistant(config);
}
```

#### Problem: Voice calls not connecting
```javascript
// Debug checklist:
1. Check Vapi public key in client
2. Verify assistant ID exists
3. Test with simple assistant first
4. Check browser permissions for microphone
5. Verify HTTPS in production
```

### 3. Supabase Issues

#### Problem: Database connection fails
```bash
# Test connection
node test-supabase-connection.js

# Check environment variables
echo $VITE_SUPABASE_URL
echo $VITE_SUPABASE_ANON_KEY

# Verify Supabase project status
# Check Supabase dashboard for outages
```

#### Problem: RLS (Row Level Security) blocking queries
```sql
-- Check RLS policies
SELECT * FROM pg_policies WHERE tablename = 'attorneys';

-- Temporarily disable for testing (DEV ONLY)
ALTER TABLE attorneys DISABLE ROW LEVEL SECURITY;

-- Re-enable with proper policy
ALTER TABLE attorneys ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view own data" ON attorneys
  FOR SELECT USING (auth.uid() = user_id);
```

#### Problem: Authentication not working
```javascript
// Debug auth state
import { supabase } from './lib/supabase';

const checkAuth = async () => {
  const { data: { session }, error } = await supabase.auth.getSession();
  console.log('Session:', session);
  console.log('Error:', error);
};
```

### 4. Build & Deployment Issues

#### Problem: Build fails with Framer Motion errors
```javascript
// Use fallback component
import { FramerMotionFallback } from './components/FramerMotionFallback';

// Conditional import
const MotionDiv = React.lazy(() => 
  import('framer-motion').then(module => ({ 
    default: module.motion.div 
  })).catch(() => ({ 
    default: FramerMotionFallback 
  }))
);
```

#### Problem: Vercel deployment fails
```bash
# Check build locally first
npm run build

# Verify environment variables in Vercel dashboard
# Check function timeout limits
# Review build logs for specific errors
```

#### Problem: API routes not working
```javascript
// Verify file structure
api/
  index.js          // Main handler
  vapi-mcp-server/  // MCP integration
  webhook/          // Webhook handlers

// Check vercel.json configuration
{
  "functions": {
    "api/index.js": {
      "maxDuration": 30
    }
  }
}
```

### 5. React & Frontend Issues

#### Problem: Context initialization errors
```javascript
// Wrap with error boundary
import { ErrorBoundary } from './components/ErrorBoundary';

<ErrorBoundary>
  <AuthProvider>
    <App />
  </AuthProvider>
</ErrorBoundary>

// Check context provider order
<AuthProvider>
  <SyncProvider>
    <ThemeProvider>
      <App />
    </ThemeProvider>
  </SyncProvider>
</AuthProvider>
```

#### Problem: Infinite re-renders
```javascript
// Use useCallback for functions
const handleClick = useCallback(() => {
  // handler logic
}, [dependencies]);

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// Check dependency arrays
useEffect(() => {
  // effect logic
}, []); // Empty array for mount only
```

#### Problem: State not updating
```javascript
// Check for state mutation
// Wrong:
state.items.push(newItem);
setState(state);

// Correct:
setState(prevState => ({
  ...prevState,
  items: [...prevState.items, newItem]
}));
```

### 6. Voice & Audio Issues

#### Problem: Microphone not working
```javascript
// Check browser permissions
navigator.mediaDevices.getUserMedia({ audio: true })
  .then(stream => {
    console.log('Microphone access granted');
    stream.getTracks().forEach(track => track.stop());
  })
  .catch(error => {
    console.error('Microphone access denied:', error);
  });
```

#### Problem: Audio playback issues
```javascript
// Check audio context state
const audioContext = new AudioContext();
if (audioContext.state === 'suspended') {
  await audioContext.resume();
}

// Verify HTTPS for audio features
if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
  console.warn('Audio features require HTTPS');
}
```

### 7. Performance Issues

#### Problem: Slow page loads
```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist

# Check for large dependencies
npm ls --depth=0 | grep MB

# Use code splitting
const LazyComponent = React.lazy(() => import('./LazyComponent'));
```

#### Problem: Memory leaks
```javascript
// Clean up subscriptions
useEffect(() => {
  const subscription = service.subscribe(callback);
  
  return () => {
    subscription.unsubscribe();
  };
}, []);

// Clean up timers
useEffect(() => {
  const timer = setInterval(callback, 1000);
  
  return () => {
    clearInterval(timer);
  };
}, []);
```

## 🛠️ Debugging Tools

### Browser DevTools
```javascript
// Console debugging
console.log('Debug info:', { variable, state });
console.table(arrayData);
console.time('operation');
// ... operation
console.timeEnd('operation');

// Network tab
// - Check API calls and responses
// - Verify request headers
// - Monitor WebSocket connections

// Application tab
// - Inspect localStorage/sessionStorage
// - Check service worker status
// - Review cookies and auth tokens
```

### VS Code Debugging
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug React App",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/.bin/vite",
      "args": ["--port", "5174"],
      "console": "integratedTerminal"
    }
  ]
}
```

### API Testing
```bash
# Test API endpoints
curl -X GET http://localhost:3001/api/health

# Test with authentication
curl -X GET http://localhost:3001/api/attorneys \
  -H "Authorization: Bearer $TOKEN"

# Test Vapi webhook
curl -X POST http://localhost:3001/api/webhook/vapi \
  -H "Content-Type: application/json" \
  -d '{"event": "test"}'
```

## 📋 Diagnostic Checklist

### Before Reporting Issues
- [ ] Check this troubleshooting guide
- [ ] Search existing GitHub issues
- [ ] Test in incognito/private browser window
- [ ] Clear browser cache and localStorage
- [ ] Restart development servers
- [ ] Check browser console for errors
- [ ] Verify environment variables
- [ ] Test with minimal reproduction case

### When Reporting Issues
Include:
- [ ] Exact error message
- [ ] Steps to reproduce
- [ ] Browser and version
- [ ] Operating system
- [ ] Node.js version
- [ ] Relevant code snippets
- [ ] Console logs
- [ ] Network requests (if applicable)

## 🆘 Getting Help

### Internal Resources
1. **This troubleshooting guide**
2. **Team chat/Slack**
3. **GitHub issues**
4. **Code review requests**

### External Resources
1. **Vapi Documentation:** https://docs.vapi.ai/
2. **Supabase Docs:** https://supabase.com/docs
3. **React DevTools:** Browser extension
4. **Vercel Support:** For deployment issues

### Emergency Contacts
- **Critical Production Issues:** Use bug reporter in app
- **Security Issues:** Direct message team lead
- **Infrastructure Issues:** Check status pages first

---

**Remember:** Most issues have been encountered before. Check the docs, search the issues, and don't hesitate to ask for help! 🤝
