/**
 * OAuth Callback API Route
 * 
 * This is the server-side API route that handles OAuth callbacks.
 * It runs on the server (Node.js) so it can safely use process.env.
 */

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { code, state } = req.body;

  if (!code || !state) {
    return res.status(400).json({ error: 'Missing required parameters' });
  }

  try {
    // Complete the OAuth flow with MCP
    const response = await fetch('https://api.makecomputer.io/v1/oauth/callback', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.MCP_API_KEY || process.env.VITE_MCP_API_KEY || ''}`
      },
      body: JSON.stringify({
        code,
        state,
        redirect_uri: process.env.GMAIL_REDIRECT_URI || process.env.VITE_GMAIL_REDIRECT_URI || `${req.headers.origin || 'http://localhost:5173'}/auth/callback`
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || 'Failed to complete authentication');
    }

    return res.status(200).json({ success: true });
  } catch (error) {
    console.error('OAuth callback error:', error);
    return res.status(500).json({ error: 'Failed to complete authentication' });
  }
}
