# SimplifiedPreview Component Issues

## Current Status

We have implemented a simplified preview component (`SimplifiedPreview.jsx`) as an alternative to the complex `PreviewInterface.tsx` component to address stability and rendering issues. The simplified version provides basic functionality but has several issues that need to be fixed:

1. **Logo Display Issues**: 
   - The uploaded logo doesn't replace the default "PRIMARY CLEAR.png" image in the start consultation button
   - Logo priority is incorrect (should prefer user-uploaded logoUrl over default mascot)

2. **Color Application Issues**:
   - Not all customization colors are properly applied to the preview elements
   - Button opacity and text background color need better implementation

3. **Call Functionality**:
   - When clicking the "Start Consultation" button, the component only opens a simulated text/chat interface
   - It should initiate a proper call with active call components as seen on the home page
   - The component is missing the integration with the VAPI call system

4. **Missing Features**:
   - Proper connection to the parent component for live call features
   - Integration with the actual assistant system
   - Dark/light mode toggle functionality
   - Knowledge base integration

## Required Fixes

### Immediate Priorities

1. **Fix Logo Display**:
   - Ensure the user-uploaded logo appears in the start consultation button
   - Fix priority so that logoUrl takes precedence over default mascot
   - Add proper debugging to trace logo paths

2. **Fix Color Application**:
   - Ensure all customization colors (primary, secondary, button opacity, text background) are properly applied
   - Add proper hex to RGB conversion for opacity handling
   - Validate color application across all UI elements

3. **Fix Call Integration**:
   - Update the handleStartConsultation function to properly integrate with VAPI call system
   - Ensure parent communication is working correctly for iframe scenarios
   - Switch from simulated chat to actual call interface

### Long-term Improvements

1. **Component Refactoring**:
   - Consider merging improvements from SimplifiedPreview back into PreviewInterface
   - Standardize the approach to preview rendering
   - Create proper documentation for both components

2. **Better Error Handling**:
   - Add more robust error handling for messaging and call initialization
   - Implement recovery mechanisms for failed calls
   - Add debugging tools for troubleshooting

3. **Testing Suite**:
   - Create comprehensive tests for the preview components
   - Test in multiple browsers and environments
   - Create a test plan for the preview functionality

## Integration Points

The preview component needs to properly integrate with:

1. **App.jsx**: Receiving customization props correctly
2. **VAPI System**: For initiating actual calls
3. **Parent Window**: For proper iframe communication
4. **Theme System**: For consistent dark/light mode handling

## Documentation Updates Needed

After fixing the issues, update:
1. `memory.md` with the latest status and improvements
2. Create detailed documentation on the preview component architecture
3. Update the todo.md to reflect completed tasks 