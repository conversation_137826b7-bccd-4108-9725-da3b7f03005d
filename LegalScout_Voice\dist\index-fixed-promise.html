<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout</title>
    <link rel="icon" type="image/png" href="/favicon.png">
    
    <!-- Fix scripts - load these before the application -->
    <script src="/fix-promise-catch.js"></script>
    <script src="/fix-profile-editing.js"></script>
    <script src="/fix-api-errors.js"></script>
    <script src="/fix-attorney-id-v2.js"></script>
    <script src="/fix-profile-tab.js"></script>
    <script src="/fix-supabase-client.js"></script>
    <script src="/fix-react-context-timeout.js"></script>
    
    <!-- Preload key resources -->
    <link rel="preload" href="/PRIMARY CLEAR.png" as="image">
    
    <!-- Supabase client -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Three.js and Globe visualization -->
    <script src="https://unpkg.com/three-globe@2.42.1/dist/three-globe.min.js"></script>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- Additional error handling -->
    <script>
        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error caught:', event.error);
            
            // Check if it's the specific error we're trying to fix
            if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
                console.log('Caught the Promise.catch error, preventing page crash');
                event.preventDefault();
            }
        });
        
        // Initialize Supabase if needed
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Supabase is already initialized
            if (!window.supabase && typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
                try {
                    // Supabase URL and key
                    const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
                    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
                    
                    console.log('Creating Supabase client manually');
                    window.supabase = supabase.createClient(supabaseUrl, supabaseKey);
                    console.log('Supabase client created successfully');
                } catch (error) {
                    console.error('Error initializing Supabase client:', error);
                }
            }
        });
    </script>
    
    <!-- Application script will be injected here by the build process -->
</body>
</html>
