/**
 * Import.meta Polyfill for Non-Module Scripts
 * 
 * This polyfill prevents "Cannot use import statement outside a module" errors
 * by providing safe access to import.meta functionality in regular script contexts.
 */

(function() {
  'use strict';
  
  console.log('[ImportMetaPolyfill] 🔧 Initializing import.meta polyfill...');
  
  // Create a safe import.meta.env accessor
  function createSafeImportMetaEnv() {
    // Try to get environment variables from various sources
    const env = {};
    
    // Source 1: Window globals (set by Vite or other build tools)
    const windowVars = [
      'VITE_VAPI_PUBLIC_KEY',
      'VITE_VAPI_SECRET_KEY', 
      'VITE_VAPI_DEFAULT_ASSISTANT_ID',
      'VITE_SUPABASE_URL',
      'VITE_SUPABASE_KEY',
      'VITE_SUPABASE_ANON_KEY',
      'VITE_DEV_MODE',
      'NODE_ENV'
    ];
    
    windowVars.forEach(varName => {
      if (window[varName]) {
        env[varName] = window[varName];
      }
    });
    
    // Source 2: Try to access real import.meta.env if available
    try {
      const realImportMeta = eval('import.meta');
      if (realImportMeta && realImportMeta.env) {
        Object.assign(env, realImportMeta.env);
      }
    } catch (e) {
      // import.meta not available, use fallbacks
    }
    
    // Source 3: Hardcoded fallbacks for development
    const fallbacks = {
      VITE_VAPI_PUBLIC_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
      VITE_VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
      VITE_VAPI_DEFAULT_ASSISTANT_ID: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
      VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co',
      VITE_SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
      NODE_ENV: 'development',
      MODE: 'development',
      DEV: true,
      PROD: false
    };
    
    // Apply fallbacks for missing values
    Object.keys(fallbacks).forEach(key => {
      if (!env[key]) {
        env[key] = fallbacks[key];
      }
    });
    
    return env;
  }
  
  // Create the polyfill
  if (typeof window !== 'undefined') {
    // Store the safe environment
    window.__VITE_ENV__ = createSafeImportMetaEnv();
    
    // Create a global safe accessor function
    window.getSafeImportMetaEnv = function() {
      return window.__VITE_ENV__;
    };
    
    // Create import.meta polyfill if it doesn't exist
    if (typeof window.import === 'undefined') {
      window.import = {};
    }
    
    if (typeof window.import.meta === 'undefined') {
      window.import.meta = {
        env: window.__VITE_ENV__,
        url: window.location.href
      };
    }
    
    // Ensure import.meta.env exists
    if (typeof window.import.meta.env === 'undefined') {
      window.import.meta.env = window.__VITE_ENV__;
    }
    
    console.log('[ImportMetaPolyfill] ✅ Polyfill installed successfully');
    console.log('[ImportMetaPolyfill] 📊 Environment variables available:', Object.keys(window.__VITE_ENV__).length);
    
    // Debug: Show available environment variables (without values for security)
    const envKeys = Object.keys(window.__VITE_ENV__);
    if (envKeys.length > 0) {
      console.log('[ImportMetaPolyfill] 🔑 Available env vars:', envKeys.join(', '));
    }
  }
  
  // Export for module environments
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
      createSafeImportMetaEnv,
      getSafeImportMetaEnv: () => window.__VITE_ENV__
    };
  }
  
})();
