<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Label/Input Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            box-sizing: border-box;
        }
        .error {
            color: red;
            font-size: 12px;
        }
        .success {
            color: green;
            font-size: 12px;
        }
        #results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>Label/Input Fix Test Page</h1>
    <p>This page tests the label/input ID mismatch fix script.</p>

    <div class="test-section">
        <h3>Test 1: Correct Label/Input Association</h3>
        <div class="form-group">
            <label for="correctInput">This label should work correctly:</label>
            <input type="text" id="correctInput" placeholder="This should work fine">
        </div>
    </div>

    <div class="test-section">
        <h3>Test 2: Mismatched Label (should be fixed automatically)</h3>
        <div class="form-group">
            <label for="wrongId">This label points to wrong ID:</label>
            <input type="text" id="actualInput" placeholder="This input has different ID">
        </div>
    </div>

    <div class="test-section">
        <h3>Test 3: Input without Label (should get associated)</h3>
        <div class="form-group">
            <label>Nearby label without 'for' attribute</label>
            <input type="email" id="orphanInput" placeholder="This input needs a label">
        </div>
    </div>

    <div class="test-section">
        <h3>Test 4: Multiple Form Elements</h3>
        <div class="form-group">
            <label for="selectTest">Select Element:</label>
            <select id="selectTest">
                <option>Option 1</option>
                <option>Option 2</option>
            </select>
        </div>
        <div class="form-group">
            <label for="textareaTest">Textarea Element:</label>
            <textarea id="textareaTest" placeholder="Enter text here"></textarea>
        </div>
    </div>

    <div id="results">
        <h3>Test Results</h3>
        <p>Loading fix script and running tests...</p>
    </div>

    <!-- Load the fix script -->
    <script src="/fix-label-input-mismatch.js"></script>

    <script>
        // Test the fix after it loads
        setTimeout(function() {
            const results = document.getElementById('results');
            let html = '<h3>Test Results</h3>';
            
            // Test 1: Check if correct association still works
            const correctLabel = document.querySelector('label[for="correctInput"]');
            const correctInput = document.getElementById('correctInput');
            if (correctLabel && correctInput) {
                html += '<p class="success">✓ Test 1 PASSED: Correct label/input association preserved</p>';
            } else {
                html += '<p class="error">✗ Test 1 FAILED: Correct association broken</p>';
            }

            // Test 2: Check if mismatch was fixed
            const fixedLabel = document.querySelector('label[for="actualInput"]');
            if (fixedLabel) {
                html += '<p class="success">✓ Test 2 PASSED: Mismatched label was fixed to point to correct input</p>';
            } else {
                html += '<p class="error">✗ Test 2 FAILED: Mismatched label was not fixed</p>';
            }

            // Test 3: Check if orphan input got a label
            const orphanLabel = document.querySelector('label[for="orphanInput"]');
            if (orphanLabel) {
                html += '<p class="success">✓ Test 3 PASSED: Orphan input got associated with nearby label</p>';
            } else {
                html += '<p class="error">✗ Test 3 FAILED: Orphan input still has no label association</p>';
            }

            // Test 4: Check other form elements
            const selectLabel = document.querySelector('label[for="selectTest"]');
            const textareaLabel = document.querySelector('label[for="textareaTest"]');
            if (selectLabel && textareaLabel) {
                html += '<p class="success">✓ Test 4 PASSED: Select and textarea elements have correct labels</p>';
            } else {
                html += '<p class="error">✗ Test 4 FAILED: Some form elements missing labels</p>';
            }

            // Summary
            const allLabels = document.querySelectorAll('label[for]');
            const allInputs = document.querySelectorAll('input[id], textarea[id], select[id]');
            html += `<p><strong>Summary:</strong> Found ${allLabels.length} labels and ${allInputs.length} form elements with IDs</p>`;

            // Check for any remaining mismatches
            let mismatches = 0;
            allLabels.forEach(label => {
                const forValue = label.getAttribute('for');
                const target = document.getElementById(forValue);
                if (!target) {
                    mismatches++;
                }
            });

            if (mismatches === 0) {
                html += '<p class="success">✓ No remaining label/input mismatches found!</p>';
            } else {
                html += `<p class="error">✗ ${mismatches} label/input mismatches still exist</p>`;
            }

            results.innerHTML = html;
        }, 1000); // Wait 1 second for the fix to run
    </script>
</body>
</html>
