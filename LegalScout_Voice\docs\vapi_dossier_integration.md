# Vapi live_dossier Integration Guide

This document outlines how to integrate the Vapi live_dossier tool (ID: 4a0d63cf-0b84-4eec-bddf-9c5869439d7e) with the LegalScout application.

## Overview

The live_dossier tool provides real-time updates about case information during a call. This data drives the visual display in our application, particularly the interactive map and attorney search functionality.

## Tool Configuration

The live_dossier tool is configured in Vapi with the following structure:

```json
{
  "📁 Client Background": "string",
  "🔄 STATUS": "string",
  "📍 Jurisdiction": {
    "address": "string",
    "lat": "number",
    "lng": "number"
  },
  "📝 Statement of Facts": "string",
  "⚖️ Legal Issues": "string",
  "🎯 Objectives": "string",
  "Urgent": "string",  // Optional property information
  "Property Name": "string"  // Optional property information
}
```

## Integration Implementation

### 1. Update VapiCall Component

Modify the `VapiCall.jsx` component to handle the live_dossier tool:

```javascript
// Inside VapiCall.jsx

// Set up tool handler for the live_dossier tool
vapiInstance.onToolCall((toolCall) => {
  console.log("Tool call received:", toolCall);
  
  // Check if this is a dossier update from the live_dossier tool
  if (toolCall.name === 'live_dossier' && toolCall.parameters) {
    const dossierUpdate = toolCall.parameters;
    
    // Process and transform the data
    const processedData = {
      clientBackground: dossierUpdate["📁 Client Background"],
      status: dossierUpdate["🔄 STATUS"],
      location: dossierUpdate["📍 Jurisdiction"],
      statementOfFacts: dossierUpdate["📝 Statement of Facts"],
      legalIssues: dossierUpdate["⚖️ Legal Issues"],
      objectives: dossierUpdate["🎯 Objectives"],
      propertyDetails: {
        urgent: dossierUpdate["Urgent"],
        name: dossierUpdate["Property Name"]
      }
    };
    
    // Update local state
    updateDossierData(processedData);
    
    // If using Convex, save the update to the database
    if (convex) {
      convex.mutation('dossierUpdates:saveDossierUpdate', {
        sessionId: sessionId,
        data: processedData
      });
    }
  }
});

// Helper function to update dossier data
const updateDossierData = (data) => {
  setDossierData(prev => {
    const newData = { ...prev };
    
    // Update each field if it exists in the incoming data
    if (data.clientBackground) newData.clientBackground = data.clientBackground;
    if (data.status) newData.status = data.status;
    
    // Handle location updates - critical for map functionality
    if (data.location) {
      // Check if we have coordinates
      if (data.location.lat && data.location.lng) {
        newData.location = {
          lat: data.location.lat,
          lng: data.location.lng,
          address: data.location.address || newData.location?.address || 'Unknown location'
        };
      } 
      // If we only have an address, we should geocode it
      // (implementation depends on geocoding service)
      else if (data.location.address && !data.location.lat && !data.location.lng) {
        // In a complete implementation, we would geocode the address here
        // For now, just store the address
        newData.location = {
          ...(newData.location || {}),
          address: data.location.address
        };
      }
    }
    
    // Update other fields
    if (data.statementOfFacts) newData.statementOfFacts = data.statementOfFacts;
    if (data.legalIssues) newData.legalIssues = data.legalIssues;
    if (data.objectives) newData.objectives = data.objectives;
    
    return newData;
  });
};
```

### 2. Connect MapDossierView

There are two approaches to connect the MapDossierView component with live_dossier updates:

#### Direct Approach (without Convex)

Pass the dossier data directly from VapiCall to CallController to MapDossierView:

```javascript
// In VapiCall.jsx
return (
  <CallController
    subdomain={subdomain}
    onEndCall={handleCallEnd}
    dossierData={dossierData}
  />
);

// In CallController.jsx (already implemented)
useEffect(() => {
  if (Object.keys(dossierData).length > 0) {
    console.log("Updating case data with:", dossierData);
    setCaseData(prevData => ({
      ...prevData,
      ...dossierData
    }));
  }
}, [dossierData]);

// Then pass to MapDossierView (already implemented)
<MapDossierView
  isVisible={showMapView}
  location={caseData.location}
  attorneys={showAttorneyResults ? attorneyMatches : []}
/>
```

#### Convex Approach (for persistence and real-time updates)

Use Convex to store dossier updates and create a subscription in MapDossierView:

```javascript
// In MapDossierView.jsx, add a useQuery hook to get updates from Convex
import { useQuery } from "../convex/_generated/react";

const MapDossierView = ({ isVisible, sessionId, attorneys = [] }) => {
  // Get the most recent dossier update from Convex
  const dossierData = useQuery("dossierUpdates:getRecentDossierUpdate", { sessionId }) || {};
  
  // Use location from dossier data
  const location = dossierData.jurisdiction || null;
  
  // Rest of the component remains the same, but uses location from dossierData
  // ...
};
```

## Handling Map Updates

When the `📍 Jurisdiction` field is updated in the live_dossier tool, the map should respond as follows:

1. Update the client marker position on the map
2. If attorneys have been searched, recenter the map to fit all markers
3. If this is a new location (first initialization), zoom to an appropriate level
4. If there's a change in region/state/county, potentially trigger a new attorney search

The existing code in MapDossierView already handles most of this logic through its useEffect hooks.

## Recommended Fields to Add

Based on the current implementation and your requirements, consider adding these fields to the Vapi live_dossier tool configuration:

1. **Practice Area**: A dedicated field for the legal practice area that can drive attorney search
2. **Client Type**: Individual or Business, to help filter appropriate attorney matches
3. **Urgency Level**: High/Medium/Low to visually indicate priority
4. **Case Timeline**: Information about relevant dates/deadlines
5. **Attorney Preferences**: Any specific attorney requirements the client has

## Direct Vapi Listening vs. Convex Integration

### Option 1: Direct Vapi Listening

**Pros:**
- Simpler implementation with fewer dependencies
- Lower latency for updates (no database round-trip)
- Easier to test and debug

**Cons:**
- No persistence between sessions
- No shared state between multiple clients
- Limited scalability as the application grows

### Option 2: Convex Integration

**Pros:**
- Persistent data between sessions
- Shared real-time state across multiple clients
- Better foundation for future features
- Easier integration with attorney profiles and custom configurations
- Better error recovery and offline handling

**Cons:**
- More complex implementation
- Additional service dependency
- Slightly higher latency for updates

## Recommendation

For the immediate MVP, **Direct Vapi Listening** is sufficient and easier to implement. However, as you expand to include attorney profiles with subdomain lookups and custom configurations, moving to the **Convex Integration** approach will provide better scalability and feature expansion.

You can also implement a hybrid approach:
1. Start with direct Vapi listening for immediate map updates
2. Implement Convex for attorney profiles and persistent data
3. Gradually migrate dossier updates to use Convex as the application matures 