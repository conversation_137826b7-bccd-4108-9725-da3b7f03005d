// Direct replacement for framer-motion's LayoutGroupContext.mjs
// This version doesn't use React.createContext at all

// Create a standalone context object that mimics React.createContext
const createContext = (defaultValue) => {
  return {
    Provider: function(props) { 
      return typeof props.children !== 'undefined' ? props.children : null; 
    },
    Consumer: function(props) { 
      return props.children && typeof props.children === 'function' 
        ? props.children(defaultValue) 
        : null; 
    },
    displayName: 'LayoutGroupContext',
    _currentValue: defaultValue,
    _currentValue2: defaultValue,
    _threadCount: 0,
    _defaultValue: defaultValue
  };
};

// Create the LayoutGroupContext with a default value
const LayoutGroupContext = createContext({});

// Make it available globally
if (typeof window !== 'undefined') {
  window.LayoutGroupContext = LayoutGroupContext;
}

// Export both as named export and default
export { LayoutGroupContext };
export default LayoutGroupContext;
