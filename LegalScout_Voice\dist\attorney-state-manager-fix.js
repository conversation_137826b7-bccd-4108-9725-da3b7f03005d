/**
 * Attorney State Manager Fix
 * 
 * This script patches the old attorney state manager to prevent it from throwing errors.
 */

(function() {
  console.log('[AttorneyStateManagerFix] Starting fix...');
  
  // Wait for the attorney state manager to be initialized
  const checkInterval = setInterval(() => {
    // Check if the attorney state manager module has been loaded
    const modules = Object.keys(window).filter(key => key.startsWith('__VITE_PRELOAD__'));
    
    for (const moduleKey of modules) {
      try {
        const module = window[moduleKey];
        
        // Look for the attorney state manager in the module exports
        if (module && typeof module === 'object') {
          const keys = Object.keys(module);
          
          for (const key of keys) {
            const exportedValue = module[key];
            
            // Check if this is the attorney state manager
            if (exportedValue && 
                typeof exportedValue === 'object' && 
                exportedValue.syncWithVapi && 
                exportedValue.loadAttorney) {
              
              console.log('[AttorneyStateManagerFix] Found attorney state manager, patching methods...');
              
              // Patch the syncWithVapi method
              const originalSyncWithVapi = exportedValue.syncWithVapi;
              exportedValue.syncWithVapi = function(...args) {
                try {
                  // If we have a standalone attorney manager, use that instead
                  if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    console.log('[AttorneyStateManagerFix] Using standalone attorney manager for syncWithVapi');
                    return Promise.resolve({ action: 'none' });
                  }
                  
                  // Otherwise, call the original method
                  return originalSyncWithVapi.apply(this, args);
                } catch (error) {
                  console.warn('[AttorneyStateManagerFix] Error in syncWithVapi:', error);
                  return Promise.resolve({ action: 'none' });
                }
              };
              
              // Patch the loadAttorney method
              const originalLoadAttorney = exportedValue.loadAttorney;
              exportedValue.loadAttorney = function(...args) {
                try {
                  // If we have a standalone attorney manager, use that instead
                  if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    console.log('[AttorneyStateManagerFix] Using standalone attorney manager for loadAttorney');
                    return Promise.resolve(window.standaloneAttorneyManager.attorney);
                  }
                  
                  // Otherwise, call the original method
                  return originalLoadAttorney.apply(this, args);
                } catch (error) {
                  console.warn('[AttorneyStateManagerFix] Error in loadAttorney:', error);
                  return Promise.resolve(null);
                }
              };
              
              // Patch the initialize method
              const originalInitialize = exportedValue.initialize;
              exportedValue.initialize = function(...args) {
                try {
                  // If we have a standalone attorney manager, use that instead
                  if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
                    console.log('[AttorneyStateManagerFix] Using standalone attorney manager for initialize');
                    this.attorney = window.standaloneAttorneyManager.attorney;
                    return Promise.resolve(this.attorney);
                  }
                  
                  // Otherwise, call the original method
                  return originalInitialize.apply(this, args);
                } catch (error) {
                  console.warn('[AttorneyStateManagerFix] Error in initialize:', error);
                  return Promise.resolve(null);
                }
              };
              
              // Patch the saveToLocalStorage method
              const originalSaveToLocalStorage = exportedValue.saveToLocalStorage;
              exportedValue.saveToLocalStorage = function(...args) {
                try {
                  // If we have a standalone attorney manager, use that instead
                  if (window.standaloneAttorneyManager) {
                    console.log('[AttorneyStateManagerFix] Using standalone attorney manager for saveToLocalStorage');
                    return window.standaloneAttorneyManager.saveToLocalStorage.apply(window.standaloneAttorneyManager, args);
                  }
                  
                  // Otherwise, call the original method
                  return originalSaveToLocalStorage.apply(this, args);
                } catch (error) {
                  console.warn('[AttorneyStateManagerFix] Error in saveToLocalStorage:', error);
                }
              };
              
              console.log('[AttorneyStateManagerFix] Attorney state manager patched successfully');
              clearInterval(checkInterval);
              break;
            }
          }
        }
      } catch (error) {
        console.warn('[AttorneyStateManagerFix] Error checking module:', error);
      }
    }
  }, 100);
  
  // Clear interval after 10 seconds if we can't find the attorney state manager
  setTimeout(() => {
    clearInterval(checkInterval);
    console.log('[AttorneyStateManagerFix] Timed out waiting for attorney state manager');
  }, 10000);
})();
