/**
 * Web Search API Endpoint
 *
 * This serverless function handles web search requests from the Vapi MCP tool.
 * It uses the webSearchService to perform the actual search.
 */

import { searchWeb, searchLegalInfo, formatSearchResultsForDisplay } from '../src/services/webSearchService.js';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the request body
    const { query, numResults = 5, type = 'web', format = 'cards' } = req.body;

    // Validate the query
    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }

    // Perform the search based on type
    let searchResults;

    if (type === 'legal') {
      searchResults = await searchLegalInfo(query);
    } else {
      searchResults = await searchWeb(query, numResults);
    }

    // Format the results for display
    const formattedResults = formatSearchResultsForDisplay(searchResults, format);

    // Return the results
    return res.status(200).json({
      success: true,
      query,
      type,
      format,
      results: formattedResults
    });
  } catch (error) {
    console.error('Web search API error:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'An error occurred during the search'
    });
  }
}
