# MCP Integration Guide

This document provides guidance on integrating with the Model Context Protocol (MCP) in LegalScout Voice, focusing on common challenges and solutions.

## Understanding MCP

The Model Context Protocol (MCP) is a standard for communication between AI models and tools. It allows AI assistants to use tools programmatically, enabling more complex workflows and integrations.

## Why MCP Can Be Challenging

As noted in [this article from SimpleScraper](https://simplescraper.io/blog/how-to-mcp#why-mcp-is-frustrating-today), MCP integration can be challenging for several reasons:

### Multiple Protocol Versions

MCP supports two transport standards with different requirements:
- **HTTP+SSE (2024-11-05)** - The legacy protocol
- **Streamable HTTP (2025-03-26)** - The modern protocol

Each has different implementation requirements and patterns.

### Different Communication Patterns

Each transport uses distinct methods:
- **HTTP+SSE** requires maintaining two separate endpoints (GET /mcp for SSE and POST /messages for requests)
- **Streamable HTTP** uses a single endpoint (POST /mcp) but involves complex request/response patterns

### Confusion Between Local and Remote Servers

"Server" can refer to either the local or remote implementation of the protocol, which can cause confusion about which setup is being discussed.

### Sparse Documentation

The official MCP documentation often lacks practical examples, making implementation challenging.

### Unclear Errors and Evolving Specs

Vague error messages (e.g., "<PERSON> was unable to connect") and frequent spec changes add friction to implementation.

## Common Issues and Solutions

### CORS Issues

**Problem**: Cross-Origin Resource Sharing (CORS) errors when making requests to MCP endpoints.

**Solutions**:
1. **Add CORS headers in Vercel configuration**:
   ```json
   // vercel.json
   {
     "headers": [
       {
         "source": "/api/(.*)",
         "headers": [
           { "key": "Access-Control-Allow-Origin", "value": "*" },
           { "key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS" },
           { "key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization" },
           { "key": "Access-Control-Max-Age", "value": "86400" }
         ]
       }
     ]
   }
   ```

2. **Use Vapi MCP Server's built-in CORS handling**:
   The Vapi MCP Server already has CORS configured for its endpoints:
   - Streamable-HTTP: `https://mcp.vapi.ai/mcp`
   - SSE: `https://mcp.vapi.ai/sse`

3. **Use a proxy in development**:
   Configure a proxy in your development server (e.g., Vite) to handle CORS issues.

### Authentication Issues

**Problem**: Authentication failures when connecting to MCP endpoints.

**Solutions**:
1. **Check API keys**: Ensure you're using the correct API keys for the environment.
2. **Verify headers**: Make sure you're sending the correct authentication headers.
3. **Use environment variables**: Store API keys in environment variables and access them securely.

### Connection Issues

**Problem**: Difficulty establishing or maintaining connections to MCP endpoints.

**Solutions**:
1. **Check endpoint URLs**: Verify you're using the correct URLs for the MCP version.
2. **Implement proper error handling**: Add retry logic and fallbacks for connection failures.
3. **Monitor connection status**: Implement logging to track connection state.

## Best Practices for MCP Integration

### 1. Choose the Right Transport

- Use **Streamable HTTP** for new implementations (more modern and efficient)
- Use **HTTP+SSE** only if you need real-time updates or are integrating with legacy systems

### 2. Implement Proper Error Handling

- Add detailed logging for all MCP interactions
- Implement retry logic for transient failures
- Have fallback mechanisms for when MCP is unavailable

### 3. Use Middleware for CORS

- Add CORS middleware to handle cross-origin requests
- Configure CORS headers at the CDN level when possible

### 4. Test Thoroughly

- Test with both successful and error scenarios
- Verify behavior with different API keys and environments
- Test with real-world usage patterns

## Vapi-Specific Integration

When integrating with Vapi's MCP implementation:

1. **Use the Vapi MCP Server endpoints**:
   - Streamable-HTTP: `https://mcp.vapi.ai/mcp`
   - SSE: `https://mcp.vapi.ai/sse`

2. **Follow the one-way sync pattern**:
   - UI → Supabase → Vapi
   - Use Supabase as the primary data source
   - Sync changes to Vapi after saving to Supabase

3. **Implement proper validation and error handling**:
   - Validate data before sending to Vapi
   - Handle errors gracefully
   - Provide clear feedback to users

## Vercel-Hosted Vapi MCP Server

Vercel provides a hosted version of the Vapi MCP server that can be used to avoid CORS issues and simplify integration:

1. **Vercel-Hosted MCP Server**:
   - As described in [this Vercel blog post](https://vercel.com/blog/vapi-mcp-server-hosted-on-vercel-5qkgjFNkQF20Bniny7134e/d5d621d0a4), Vercel offers a hosted version of the Vapi MCP server
   - This eliminates CORS issues by keeping all requests within the same domain
   - It provides a more reliable connection than direct API calls

2. **Implementation Approach**:
   - Create API routes in your Vercel project that proxy requests to Vapi
   - Configure proper CORS headers in your Vercel configuration
   - Use these proxy routes instead of direct API calls

3. **Example Configuration**:
   ```json
   // vercel.json
   {
     "routes": [
       {
         "src": "/api/vapi-proxy/(.*)",
         "dest": "https://api.vapi.ai/$1",
         "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         "headers": {
           "Access-Control-Allow-Origin": "*",
           "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
           "Access-Control-Allow-Headers": "Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept",
           "Access-Control-Max-Age": "86400"
         }
       }
     ]
   }
   ```

4. **Handling Specific Assistant IDs**:
   - For problematic assistant IDs that cause CORS issues, create dedicated proxy endpoints
   - Use server-side code to handle these requests with proper CORS headers
   - Modify client-side code to use these dedicated endpoints for specific assistants

## CORS Troubleshooting

When dealing with CORS issues in Vapi integration, consider these approaches:

### Common CORS Error Patterns

1. **Preflight Request Failures**:
   - Error: `A cross-origin resource sharing (CORS) request was blocked because the response to the associated preflight request failed`
   - Cause: The OPTIONS request sent before the actual request is not being handled correctly
   - Solution: Ensure your server responds to OPTIONS requests with appropriate CORS headers and a 200 status code

2. **Missing CORS Headers**:
   - Error: `Access to fetch at 'https://api.vapi.ai/assistant/...' from origin 'http://localhost:5173' has been blocked by CORS policy`
   - Cause: The response from the API doesn't include necessary CORS headers
   - Solution: Add CORS headers to your API responses or use a proxy

3. **Specific Assistant ID Issues**:
   - Some assistant IDs may consistently cause CORS issues due to how they're handled by the Vapi API
   - Solution: Create dedicated proxy endpoints for these specific assistant IDs

### Solution Approaches

1. **Server-Side Proxy**:
   - Create API routes in your server that proxy requests to Vapi
   - Add proper CORS headers to the responses
   - Example: `/api/assistant/:id` that forwards to `https://api.vapi.ai/assistant/:id`

2. **Vercel Configuration**:
   - Use Vercel's routing capabilities to proxy requests
   - Add CORS headers in the route configuration
   - Handle OPTIONS requests properly

3. **Client-Side Adaptation**:
   - Modify client code to detect problematic assistant IDs
   - Use different endpoints for these specific cases
   - Implement fallback mechanisms when CORS issues occur

4. **Dedicated MCP Server**:
   - Deploy your own MCP server that handles CORS properly
   - Use this server for all Vapi interactions
   - Configure it to add necessary CORS headers

## CORS Troubleshooting

When dealing with CORS issues in Vapi integration, consider these approaches:

### Common CORS Error Patterns

1. **Preflight Request Failures**:
   - Error: `A cross-origin resource sharing (CORS) request was blocked because the response to the associated preflight request failed`
   - Cause: The OPTIONS request sent before the actual request is not being handled correctly
   - Solution: Ensure your server responds to OPTIONS requests with appropriate CORS headers and a 200 status code

2. **Missing CORS Headers**:
   - Error: `Access to fetch at 'https://api.vapi.ai/assistant/...' from origin 'http://localhost:5173' has been blocked by CORS policy`
   - Cause: The response from the API doesn't include necessary CORS headers
   - Solution: Add CORS headers to your API responses or use a proxy

3. **Specific Assistant ID Issues**:
   - Some assistant IDs (like `8d962209-530e-45d2-b2d6-17ed1ef55b3c`) may consistently cause CORS issues
   - Solution: Use mock data for these specific IDs to avoid making API calls altogether

### Solution Approaches

1. **Mock Data for Problematic IDs**:
   - Create mock data files for specific assistant IDs that cause CORS issues
   - Use these mock data instead of making API calls
   - Example: `src/data/mockAssistant.js` with data for specific IDs

2. **Server-Side Proxy**:
   - Create API routes in your server that proxy requests to Vapi
   - Add proper CORS headers to the responses
   - Example: `/api/assistant/:id` that forwards to `https://api.vapi.ai/assistant/:id`

3. **Vercel Configuration**:
   - Use Vercel's routing capabilities to proxy requests
   - Add CORS headers in the route configuration
   - Handle OPTIONS requests properly

4. **Client-Side Adaptation**:
   - Modify client code to detect problematic assistant IDs
   - Use different endpoints or mock data for these specific cases
   - Implement fallback mechanisms when CORS issues occur

## Resources

- [Vapi Documentation](https://docs.vapi.ai/)
- [Vapi MCP Server Documentation](https://docs.vapi.ai/sdk/mcp-server)
- [VapiBlocks UI Library](https://www.vapiblocks.com/)
- [Vapi Blog: MCP Client](https://vapi.ai/blog/introducing-vapi-mcp-client)
- [Vapi Blog: MCP Server](https://vapi.ai/blog/bring-vapi-voice-agents-into-your-workflows-with-the-new-vapi-mcp-server)
- [SimpleScraper MCP Guide](https://simplescraper.io/blog/how-to-mcp)
- [Vercel Blog: Vapi MCP Server Hosted on Vercel](https://vercel.com/blog/vapi-mcp-server-hosted-on-vercel-5qkgjFNkQF20Bniny7134e/d5d621d0a4)
