<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Official Pattern Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.idle { background-color: #f3f4f6; color: #374151; }
        .status.connecting { background-color: #fef3c7; color: #92400e; }
        .status.connected { background-color: #d1fae5; color: #065f46; }
        .status.ended { background-color: #f3f4f6; color: #374151; }
        .status.error { background-color: #fee2e2; color: #991b1b; }
        
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background-color: #2563eb; }
        button:disabled { background-color: #9ca3af; cursor: not-allowed; }
        button.danger { background-color: #dc2626; }
        button.danger:hover { background-color: #b91c1c; }
        
        .config-section {
            background-color: #f9fafb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .config-section h3 { margin-top: 0; }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 4px;
        }
        .old-way { background-color: #fef2f2; border-left: 4px solid #ef4444; }
        .new-way { background-color: #f0fdf4; border-left: 4px solid #22c55e; }
    </style>
</head>
<body>
    <h1>🎯 Vapi Official Pattern Test</h1>
    <p>This page tests the official Vapi HTML script tag pattern exactly as documented.</p>

    <div class="comparison">
        <div class="comparison-item old-way">
            <h3>❌ Custom Implementation</h3>
            <ul>
                <li>ES module imports</li>
                <li>Custom event handling</li>
                <li>Manual SDK loading</li>
                <li>Complex initialization</li>
            </ul>
        </div>
        <div class="comparison-item new-way">
            <h3>✅ Official HTML Pattern</h3>
            <ul>
                <li>Direct script tag inclusion</li>
                <li>Built-in widget functionality</li>
                <li>Automatic initialization</li>
                <li>Proven reliability</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>Configuration</h2>
        <div class="config-section">
            <h3>Assistant Settings</h3>
            <label>Assistant ID:</label>
            <input type="text" id="assistantId" value="cd0b44b7-397e-410d-8835-ce9c3ba584b2" placeholder="Enter assistant ID">

            <label>API Key:</label>
            <input type="text" id="apiKey" value="6734febc-fc65-4669-93b0-929b31ff6564" placeholder="Enter public API key">
        </div>
    </div>

    <div class="container">
        <h2>Call Controls</h2>
        <div id="status" class="status idle">Ready to initialize</div>
        
        <div>
            <button onclick="initializeVapi()">Initialize Vapi</button>
            <button onclick="startCall()" id="startBtn" disabled>Start Call</button>
            <button onclick="stopCall()" id="stopBtn" disabled>Stop Call</button>
        </div>
    </div>

    <div class="container">
        <h2>Debug Information</h2>
        <pre id="debugInfo" style="background: #f3f4f6; padding: 10px; border-radius: 4px; font-size: 12px;">
Waiting for initialization...
        </pre>
    </div>

    <script>
        var vapiInstance = null;
        var isInitialized = false;

        function updateStatus(status, text) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = text;
        }

        function updateDebugInfo(info) {
            document.getElementById('debugInfo').textContent = JSON.stringify(info, null, 2);
        }

        function updateButtons() {
            document.getElementById('startBtn').disabled = !isInitialized;
            document.getElementById('stopBtn').disabled = !isInitialized;
        }

        function initializeVapi() {
            try {
                const assistant = document.getElementById('assistantId').value;
                const apiKey = document.getElementById('apiKey').value;
                
                if (!assistant || !apiKey) {
                    updateStatus('error', 'Please enter both Assistant ID and API Key');
                    return;
                }

                updateStatus('connecting', 'Loading Vapi SDK...');
                
                // Use the exact official pattern from Vapi documentation
                const buttonConfig = {
                    position: "bottom-right",
                    offset: "40px",
                    width: "50px",
                    height: "50px",
                    idle: {
                        color: `linear-gradient(7deg, #2563eb, #3b82f6)`,
                        type: "pill",
                        title: "Have a quick question?",
                        subtitle: "Talk with our AI assistant",
                        icon: `https://unpkg.com/lucide@latest/dist/esm/icons/phone.js`,
                    },
                    loading: {
                        color: `linear-gradient(7deg, #2563eb, #3b82f6)`,
                        type: "pill",
                        title: "Connecting...",
                        subtitle: "Please wait",
                        icon: `https://unpkg.com/lucide@latest/dist/esm/icons/loader-2.js`,
                    },
                    active: {
                        color: `linear-gradient(7deg, #dc2626, #ef4444)`,
                        type: "pill",
                        title: "Call is in progress...",
                        subtitle: "End the call.",
                        icon: `https://unpkg.com/lucide@latest/dist/esm/icons/phone-off.js`,
                    },
                };

                (function (d, t) {
                    var g = document.createElement(t),
                        s = d.getElementsByTagName(t)[0];
                    g.src = "https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js";
                    g.defer = true;
                    g.async = true;
                    s.parentNode.insertBefore(g, s);

                    g.onload = function () {
                        console.log('Vapi SDK loaded successfully');
                        
                        vapiInstance = window.vapiSDK.run({
                            apiKey: apiKey,
                            assistant: assistant,
                            config: buttonConfig,
                        });

                        console.log('Vapi instance created:', vapiInstance);
                        
                        isInitialized = true;
                        updateStatus('idle', 'Vapi initialized successfully');
                        updateButtons();
                        
                        updateDebugInfo({
                            initialized: true,
                            assistant: assistant,
                            apiKey: apiKey.substring(0, 8) + '...',
                            timestamp: new Date().toISOString()
                        });
                    };

                    g.onerror = function() {
                        console.error('Failed to load Vapi SDK');
                        updateStatus('error', 'Failed to load Vapi SDK');
                        updateDebugInfo({
                            error: 'Failed to load Vapi SDK',
                            timestamp: new Date().toISOString()
                        });
                    };
                })(document, "script");

            } catch (error) {
                console.error('Initialization error:', error);
                updateStatus('error', `Initialization failed: ${error.message}`);
                updateDebugInfo({
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }

        function startCall() {
            if (vapiInstance && vapiInstance.start) {
                console.log('Starting call...');
                updateStatus('connecting', 'Starting call...');
                vapiInstance.start();
            } else {
                console.error('Vapi instance not available or start method not found');
                updateStatus('error', 'Vapi instance not available');
            }
        }

        function stopCall() {
            if (vapiInstance && vapiInstance.stop) {
                console.log('Stopping call...');
                updateStatus('ended', 'Stopping call...');
                vapiInstance.stop();
            } else {
                console.error('Vapi instance not available or stop method not found');
                updateStatus('error', 'Vapi instance not available');
            }
        }

        // Update debug info periodically
        setInterval(() => {
            if (isInitialized) {
                updateDebugInfo({
                    initialized: isInitialized,
                    vapiInstance: !!vapiInstance,
                    timestamp: new Date().toISOString()
                });
            }
        }, 2000);
    </script>
</body>
</html>
