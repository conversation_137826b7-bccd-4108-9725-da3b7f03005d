/**
 * Production CORS Fix
 * 
 * Comprehensive fix for CORS and API endpoint issues in production
 * Based on analysis of production logs and error patterns
 */

(function() {
  'use strict';

  console.log('[ProductionCorsFix] 🔧 Starting comprehensive CORS and API fixes...');

  // 1. Fix API Endpoint URLs
  function fixApiEndpoints() {
    console.log('[ProductionCorsFix] 🔗 Fixing API endpoint URLs...');
    
    const originalFetch = window.fetch;
    
    window.fetch = function(url, options = {}) {
      let fixedUrl = url;
      
      // Fix wrong dashboard.vapi.ai URLs to use api.vapi.ai
      if (typeof url === 'string' && url.includes('dashboard.vapi.ai/api/')) {
        fixedUrl = url.replace('dashboard.vapi.ai/api/', 'api.vapi.ai/');
        console.log('[ProductionCorsFix] 🔄 Fixed endpoint:', url, '→', fixedUrl);
      }
      
      // Handle MCP requests that will fail due to CORS
      if (typeof url === 'string' && url.includes('mcp.vapi.ai')) {
        console.log('[ProductionCorsFix] ⚠️ MCP request detected - checking if live transcript request');

        // Check if this is a live transcript request (getCall for monitoring)
        const isLiveTranscriptRequest = options && (
          options.method === 'POST' &&
          options.body &&
          (options.body.includes('get_call') || options.body.includes('getCall'))
        );

        if (isLiveTranscriptRequest) {
          console.log('[ProductionCorsFix] 🔴 Live transcript request - proxying through our API');

          // Extract call ID from the request body
          let callId;
          try {
            const body = JSON.parse(options.body);
            callId = body.arguments?.callId || body.params?.callId;
          } catch (e) {
            console.warn('[ProductionCorsFix] Could not parse MCP request body');
          }

          if (callId) {
            // Proxy through our API endpoint
            const proxyUrl = `/api/mcp-proxy`;
            const proxyOptions = {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                server: 'vapi',
                tool: 'get_call_vapi-mcp-server',
                transport: 'http',
                callId: callId
              })
            };

            return originalFetch.call(this, proxyUrl, proxyOptions)
              .then(response => response.json())
              .then(data => {
                if (data.success) {
                  // Return in the format expected by MCP client
                  return new Response(JSON.stringify({
                    content: data.data
                  }), {
                    status: 200,
                    statusText: 'OK',
                    headers: {
                      'Content-Type': 'application/json'
                    }
                  });
                } else {
                  throw new Error(data.error || 'Proxy request failed');
                }
              });
          }
        }

        // For non-live transcript MCP requests, use fallback strategy
        console.log('[ProductionCorsFix] 📋 Non-live MCP request - using fallback to direct API');
        return Promise.reject(new Error('CORS_BLOCKED_MCP_FALLBACK_TO_DIRECT_API'));
      }
      
      // Ensure proper headers for Vapi API calls
      if (typeof fixedUrl === 'string' && fixedUrl.includes('api.vapi.ai')) {
        const enhancedOptions = {
          ...options,
          mode: 'cors',
          headers: {
            ...options.headers,
            'Content-Type': 'application/json'
          }
        };
        
        console.log('[ProductionCorsFix] 📡 Enhanced Vapi API request:', fixedUrl);
        return originalFetch.call(this, fixedUrl, enhancedOptions);
      }
      
      return originalFetch.call(this, fixedUrl, options);
    };
    
    console.log('[ProductionCorsFix] ✅ API endpoint fixes applied');
  }

  // 2. Fix Environment Variables for Production
  function fixEnvironmentVariables() {
    console.log('[ProductionCorsFix] 🌍 Ensuring production environment variables...');
    
    // Ensure import.meta.env exists and has correct values (safely check for import.meta)
    try {
      if (typeof window !== 'undefined' && window.import && window.import.meta && window.import.meta.env) {
        // Production environment variables - FIXED: Use correct key names
        window.import.meta.env.VITE_VAPI_SECRET_KEY = window.import.meta.env.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
        window.import.meta.env.VITE_VAPI_PUBLIC_KEY = window.import.meta.env.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
        window.import.meta.env.VITE_SUPABASE_URL = window.import.meta.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
        window.import.meta.env.VITE_SUPABASE_KEY = window.import.meta.env.VITE_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
        window.import.meta.env.MODE = 'production';
        window.import.meta.env.PROD = true;
        window.import.meta.env.DEV = false;
      }
    } catch (e) {
      // Ignore import.meta errors in non-module contexts
      console.log('[ProductionCorsFix] Skipping import.meta.env (not in module context)');
    }

    // Ensure window globals exist - FIXED: Use correct key names
    window.VITE_VAPI_SECRET_KEY = window.VITE_VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';
    window.VITE_VAPI_PUBLIC_KEY = window.VITE_VAPI_PUBLIC_KEY || '310f0d43-27c2-47a5-a76d-e55171d024f7';
    window.VITE_SUPABASE_URL = window.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
    window.VITE_SUPABASE_KEY = window.VITE_SUPABASE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
    
    console.log('[ProductionCorsFix] ✅ Environment variables configured');
  }

  // 3. Fix CSP Issues
  function fixCSPIssues() {
    console.log('[ProductionCorsFix] 🛡️ Fixing CSP issues...');
    
    // Remove duplicate CSP meta tags
    const cspMetaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
    if (cspMetaTags.length > 1) {
      console.log('[ProductionCorsFix] 🔄 Removing duplicate CSP meta tags');
      for (let i = 1; i < cspMetaTags.length; i++) {
        cspMetaTags[i].remove();
      }
    }
    
    // Ensure CSP allows necessary domains
    const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (cspMeta) {
      const currentCSP = cspMeta.getAttribute('content');
      if (!currentCSP.includes('api.vapi.ai')) {
        console.log('[ProductionCorsFix] ⚠️ CSP may need updating to include api.vapi.ai');
      }
    }
    
    console.log('[ProductionCorsFix] ✅ CSP issues addressed');
  }

  // 4. Enhanced Error Handling for API Calls
  function enhanceErrorHandling() {
    console.log('[ProductionCorsFix] 🚨 Enhancing error handling...');
    
    // Global error handler for unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
      if (event.reason && event.reason.message && event.reason.message.includes('CORS')) {
        console.warn('[ProductionCorsFix] 🚫 CORS error caught, fallback should handle this:', event.reason.message);
        // Don't prevent default - let the application handle the fallback
      }
    });
    
    // Global error handler for fetch errors
    window.addEventListener('error', function(event) {
      if (event.error && event.error.message && event.error.message.includes('Failed to fetch')) {
        console.warn('[ProductionCorsFix] 🚫 Fetch error caught, fallback should handle this:', event.error.message);
      }
    });
    
    console.log('[ProductionCorsFix] ✅ Enhanced error handling installed');
  }

  // 5. Force Direct API Mode for Production
  function forceDirectApiMode() {
    console.log('[ProductionCorsFix] 🎯 Forcing direct API mode for production...');
    
    // Set a flag that services can check
    window.FORCE_DIRECT_API_MODE = true;
    window.PRODUCTION_CORS_FIX_ACTIVE = true;
    
    // Override any MCP connection attempts
    if (window.enhancedVapiMcpService) {
      console.log('[ProductionCorsFix] 🔄 Configuring existing Vapi service for direct API mode');
      window.enhancedVapiMcpService.useDirect = true;
      window.enhancedVapiMcpService.useStreamableHTTP = false;
    }
    
    console.log('[ProductionCorsFix] ✅ Direct API mode configured');
  }

  // 6. Test API Connectivity
  async function testApiConnectivity() {
    console.log('[ProductionCorsFix] 🧪 Testing API connectivity...');
    
    try {
      // Test direct Vapi API
      const testResponse = await fetch('https://api.vapi.ai/assistant/f9b97d13-f9c4-40af-a660-62ba5925ff2a', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564',
          'Content-Type': 'application/json'
        }
      });
      
      if (testResponse.ok) {
        console.log('[ProductionCorsFix] ✅ Direct Vapi API connectivity confirmed');
        window.VAPI_API_WORKING = true;
      } else {
        console.warn('[ProductionCorsFix] ⚠️ Direct Vapi API returned:', testResponse.status, testResponse.statusText);
        window.VAPI_API_WORKING = false;
      }
    } catch (error) {
      console.warn('[ProductionCorsFix] ⚠️ Direct Vapi API test failed:', error.message);
      window.VAPI_API_WORKING = false;
    }
  }

  // 7. Initialize All Fixes
  function initializeAllFixes() {
    console.log('[ProductionCorsFix] 🚀 Initializing all production fixes...');
    
    try {
      fixEnvironmentVariables();
      fixApiEndpoints();
      fixCSPIssues();
      enhanceErrorHandling();
      forceDirectApiMode();
      
      // Test connectivity after a short delay
      setTimeout(testApiConnectivity, 1000);
      
      console.log('[ProductionCorsFix] 🎉 All production fixes initialized successfully');
      
      // Set global flag
      window.PRODUCTION_CORS_FIX_COMPLETE = true;
      
      // Dispatch event for other scripts
      window.dispatchEvent(new CustomEvent('productionCorsFixComplete', {
        detail: {
          timestamp: new Date().toISOString(),
          fixes: [
            'apiEndpoints',
            'environmentVariables', 
            'cspIssues',
            'errorHandling',
            'directApiMode'
          ]
        }
      }));
      
    } catch (error) {
      console.error('[ProductionCorsFix] 💥 Error during initialization:', error);
    }
  }

  // Run immediately if DOM is ready, otherwise wait
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAllFixes);
  } else {
    initializeAllFixes();
  }

})();
