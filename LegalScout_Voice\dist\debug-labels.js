/**
 * Debug Label/Input Associations
 * 
 * This script provides detailed debugging information about label/input associations
 * and can be run manually in the browser console to identify specific issues.
 */

window.debugLabels = function() {
  console.group('🔍 Label/Input Debug Analysis');
  
  // Find all labels with 'for' attributes
  const labels = document.querySelectorAll('label[for]');
  console.log(`Found ${labels.length} labels with 'for' attributes:`);
  
  const issues = [];
  
  labels.forEach((label, index) => {
    const forValue = label.getAttribute('for');
    const targetElement = document.getElementById(forValue);
    const labelText = label.textContent.trim();
    
    console.group(`Label ${index + 1}: "${labelText}"`);
    console.log('For attribute:', forValue);
    console.log('Target found:', !!targetElement);
    
    if (targetElement) {
      console.log('Target element:', targetElement);
      console.log('Target tag:', targetElement.tagName);
      console.log('Target type:', targetElement.type || 'N/A');
      console.log('Target ID:', targetElement.id);
      
      if (!['INPUT', 'TEXTAREA', 'SELECT', 'BUTTON'].includes(targetElement.tagName)) {
        console.warn('⚠️ Target is not a form element!');
        issues.push({
          type: 'invalid-target',
          label: labelText,
          forValue: forValue,
          targetTag: targetElement.tagName
        });
      } else {
        console.log('✅ Valid association');
      }
    } else {
      console.error('❌ No target element found!');
      issues.push({
        type: 'missing-target',
        label: labelText,
        forValue: forValue
      });
    }
    console.groupEnd();
  });
  
  // Find all form elements
  const formElements = document.querySelectorAll('input, textarea, select, button');
  console.log(`\nFound ${formElements.length} form elements:`);
  
  formElements.forEach((element, index) => {
    const elementId = element.id;
    const elementType = element.type || element.tagName;
    const elementName = element.name || 'unnamed';
    
    console.group(`Form Element ${index + 1}: ${element.tagName}[${elementType}]`);
    console.log('ID:', elementId || 'NO ID');
    console.log('Name:', elementName);
    
    if (elementId) {
      const associatedLabel = document.querySelector(`label[for="${elementId}"]`);
      const parentLabel = element.closest('label');
      
      console.log('Has explicit label:', !!associatedLabel);
      console.log('Has implicit label (parent):', !!parentLabel);
      
      if (!associatedLabel && !parentLabel) {
        console.warn('⚠️ No label association found!');
        issues.push({
          type: 'missing-label',
          elementId: elementId,
          elementType: elementType,
          elementName: elementName
        });
      } else {
        console.log('✅ Has label association');
      }
    } else {
      console.log('ℹ️ No ID (label association not possible)');
    }
    console.groupEnd();
  });
  
  // Summary
  console.group('📊 Summary');
  console.log(`Total labels: ${labels.length}`);
  console.log(`Total form elements: ${formElements.length}`);
  console.log(`Issues found: ${issues.length}`);
  
  if (issues.length > 0) {
    console.group('🚨 Issues Details');
    issues.forEach((issue, index) => {
      console.group(`Issue ${index + 1}: ${issue.type}`);
      Object.keys(issue).forEach(key => {
        if (key !== 'type') {
          console.log(`${key}:`, issue[key]);
        }
      });
      console.groupEnd();
    });
    console.groupEnd();
  } else {
    console.log('✅ No issues found!');
  }
  
  console.groupEnd();
  console.groupEnd();
  
  return {
    labels: labels.length,
    formElements: formElements.length,
    issues: issues
  };
};

// Auto-run on load
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
      console.log('🔍 Auto-running label debug analysis...');
      window.debugLabels();
    }, 1000);
  });
} else {
  setTimeout(() => {
    console.log('🔍 Auto-running label debug analysis...');
    window.debugLabels();
  }, 1000);
}

console.log('🔍 Label Debug Tool loaded. Run window.debugLabels() to analyze labels.');
