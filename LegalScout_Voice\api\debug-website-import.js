/**
 * Debug Website Import API Handler
 * 
 * This endpoint provides debugging information for website import functionality.
 * It helps diagnose issues with website scraping, content extraction, and import processes.
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const { method, query, body } = req;
    
    console.log('[DebugWebsiteImport] Request received:', {
      method,
      query,
      bodyKeys: body ? Object.keys(body) : []
    });

    switch (method) {
      case 'GET':
        return handleGetDebugInfo(req, res);
      case 'POST':
        return handleDebugImport(req, res);
      default:
        return res.status(405).json({ 
          error: 'Method not allowed',
          allowedMethods: ['GET', 'POST']
        });
    }
  } catch (error) {
    console.error('[DebugWebsiteImport] Error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      debug: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}

/**
 * Handle GET requests - return debug information
 */
async function handleGetDebugInfo(req, res) {
  const debugInfo = {
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'unknown',
    features: {
      websiteImport: true,
      contentExtraction: true,
      urlValidation: true,
      errorHandling: true
    },
    endpoints: {
      debug: '/api/debug-website-import',
      import: '/api/website-import',
      health: '/api/health'
    },
    status: 'operational'
  };

  console.log('[DebugWebsiteImport] Returning debug info');
  return res.status(200).json(debugInfo);
}

/**
 * Handle POST requests - debug a specific import
 */
async function handleDebugImport(req, res) {
  const { url, options = {} } = req.body;

  if (!url) {
    return res.status(400).json({
      error: 'Missing required parameter: url'
    });
  }

  console.log('[DebugWebsiteImport] Debugging import for URL:', url);

  const debugResult = {
    url,
    timestamp: new Date().toISOString(),
    steps: [],
    warnings: [],
    errors: []
  };

  try {
    // Step 1: URL Validation
    debugResult.steps.push({
      step: 'url_validation',
      status: 'started',
      timestamp: new Date().toISOString()
    });

    const urlObj = new URL(url);
    debugResult.steps.push({
      step: 'url_validation',
      status: 'completed',
      result: {
        protocol: urlObj.protocol,
        hostname: urlObj.hostname,
        pathname: urlObj.pathname,
        valid: true
      }
    });

    // Step 2: Accessibility Check
    debugResult.steps.push({
      step: 'accessibility_check',
      status: 'started',
      timestamp: new Date().toISOString()
    });

    // Simple fetch test
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        headers: {
          'User-Agent': 'LegalScout-Debug/1.0'
        },
        timeout: 5000
      });

      debugResult.steps.push({
        step: 'accessibility_check',
        status: 'completed',
        result: {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          accessible: response.ok
        }
      });

      if (!response.ok) {
        debugResult.warnings.push(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (fetchError) {
      debugResult.steps.push({
        step: 'accessibility_check',
        status: 'failed',
        error: fetchError.message
      });
      debugResult.errors.push(`Accessibility check failed: ${fetchError.message}`);
    }

    // Step 3: Content Type Detection
    debugResult.steps.push({
      step: 'content_type_detection',
      status: 'completed',
      result: {
        expectedType: 'text/html',
        note: 'Full content type detection requires actual content fetch'
      }
    });

    // Step 4: Import Simulation
    debugResult.steps.push({
      step: 'import_simulation',
      status: 'completed',
      result: {
        wouldAttempt: true,
        estimatedComplexity: 'medium',
        note: 'This is a simulation - actual import would require full content processing'
      }
    });

    debugResult.summary = {
      totalSteps: debugResult.steps.length,
      completedSteps: debugResult.steps.filter(s => s.status === 'completed').length,
      failedSteps: debugResult.steps.filter(s => s.status === 'failed').length,
      warningCount: debugResult.warnings.length,
      errorCount: debugResult.errors.length,
      overallStatus: debugResult.errors.length > 0 ? 'failed' : 
                    debugResult.warnings.length > 0 ? 'warning' : 'success'
    };

    console.log('[DebugWebsiteImport] Debug completed:', debugResult.summary);
    return res.status(200).json(debugResult);

  } catch (error) {
    debugResult.errors.push(`Debug process failed: ${error.message}`);
    debugResult.summary = {
      overallStatus: 'error',
      errorCount: debugResult.errors.length
    };

    console.error('[DebugWebsiteImport] Debug failed:', error);
    return res.status(500).json(debugResult);
  }
}
