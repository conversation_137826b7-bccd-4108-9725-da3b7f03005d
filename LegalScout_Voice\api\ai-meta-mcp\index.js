/**
 * AI Meta MCP Server Serverless Function
 *
 * This serverless function provides an API endpoint for the AI Meta MCP Server.
 * It adapts the AI Meta MCP Server to run as a Vercel serverless function.
 * It includes web evaluation tools inspired by the web-eval-agent project.
 */

// No need for MCP SDK import since we're using direct API calls
import { z } from "zod";
import { VM } from "vm2";
import fs from "fs/promises";
import path from "path";
import { execSync } from "child_process";
import { registerWebEvalTools } from "./web-eval-tools.js";

// Configuration from environment variables
const ALLOW_JS_EXECUTION = process.env.ALLOW_JS_EXECUTION !== "false";
const ALLOW_PYTHON_EXECUTION = process.env.ALLOW_PYTHON_EXECUTION === "true";
const ALLOW_SHELL_EXECUTION = process.env.ALLOW_SHELL_EXECUTION === "true";
const PERSIST_TOOLS = process.env.PERSIST_TOOLS !== "false";
const TOOLS_DB_PATH = process.env.TOOLS_DB_PATH || "/tmp/tools.json";

// Global registry of custom tools
let customTools = {};

// Create an MCP server
const server = new McpServer({
  name: "ai-meta-mcp-server",
  version: "1.0.0",
});

// Initialize tools database
async function initializeToolsDatabase() {
  if (!PERSIST_TOOLS) return;

  try {
    const data = await fs.readFile(TOOLS_DB_PATH, "utf-8");
    customTools = JSON.parse(data);
    console.log(`Loaded ${Object.keys(customTools).length} custom tools from ${TOOLS_DB_PATH}`);

    // Register all loaded tools with the server
    for (const [name, toolDef] of Object.entries(customTools)) {
      registerToolWithServer(toolDef);
    }
  } catch (err) {
    if (err.code !== "ENOENT") {
      console.error("Error loading tools database:", err);
    }
  }
}

// Save tools database
async function saveToolsDatabase() {
  if (!PERSIST_TOOLS) return;

  try {
    await fs.writeFile(TOOLS_DB_PATH, JSON.stringify(customTools, null, 2), "utf-8");
    console.log(`Saved ${Object.keys(customTools).length} custom tools to ${TOOLS_DB_PATH}`);
  } catch (err) {
    console.error("Error saving tools database:", err);
  }
}

// Register a tool with the server
function registerToolWithServer(toolDef) {
  try {
    const { name, description, inputSchema, implementation, executionEnvironment } = toolDef;

    // Parse the input schema
    let parsedSchema;
    if (typeof inputSchema === "string") {
      parsedSchema = JSON.parse(inputSchema);
    } else {
      parsedSchema = inputSchema;
    }

    // Register the tool with the server
    server.tool(
      name,
      parsedSchema,
      async (args) => {
        try {
          // Execute the tool implementation
          const result = await executeImplementation(implementation, args, executionEnvironment);
          return {
            content: [{ type: "text", text: String(result) }]
          };
        } catch (err) {
          console.error(`Error executing tool ${name}:`, err);
          return {
            content: [{ type: "text", text: `Error: ${err.message}` }]
          };
        }
      },
      { description }
    );

    console.log(`Registered tool: ${name}`);
  } catch (err) {
    console.error(`Error registering tool ${toolDef.name}:`, err);
  }
}

// Execute tool implementation
async function executeImplementation(implementation, args, environment) {
  switch (environment) {
    case "javascript":
      if (!ALLOW_JS_EXECUTION) {
        throw new Error("JavaScript execution is disabled");
      }
      return executeJavaScript(implementation, args);

    case "python":
      if (!ALLOW_PYTHON_EXECUTION) {
        throw new Error("Python execution is disabled");
      }
      return executePython(implementation, args);

    case "shell":
      if (!ALLOW_SHELL_EXECUTION) {
        throw new Error("Shell execution is disabled");
      }
      return executeShell(implementation, args);

    default:
      throw new Error(`Unsupported execution environment: ${environment}`);
  }
}

// Execute JavaScript implementation
function executeJavaScript(implementation, args) {
  const vm = new VM({
    timeout: 5000,
    sandbox: { args }
  });

  return vm.run(`(${implementation})(args)`);
}

// Execute Python implementation
function executePython(implementation, args) {
  throw new Error("Python execution is not supported in serverless functions");
}

// Execute Shell implementation
function executeShell(implementation, args) {
  throw new Error("Shell execution is not supported in serverless functions");
}

// Define function tool
server.tool(
  "define_function",
  {
    name: z.string().min(1).max(100),
    description: z.string().min(1).max(1000),
    parameters_schema: z.any(),
    implementation_code: z.string().min(1).max(10000),
    execution_environment: z.enum(["javascript", "python", "shell"]).default("javascript")
  },
  async ({ name, description, parameters_schema, implementation_code, execution_environment }) => {
    // Check if the tool already exists
    if (customTools[name]) {
      return {
        content: [{ type: "text", text: `Tool ${name} already exists. Use update_function to modify it.` }]
      };
    }

    // Create the tool definition
    const toolDef = {
      name,
      description,
      inputSchema: parameters_schema,
      implementation: implementation_code,
      executionEnvironment: execution_environment,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Register the tool
    registerToolWithServer(toolDef);

    // Save the tool definition
    customTools[name] = toolDef;
    await saveToolsDatabase();

    return {
      content: [{ type: "text", text: `Function ${name} defined successfully.` }]
    };
  },
  { description: "Define a new function that can be called by the AI" }
);

// Update function tool
server.tool(
  "update_function",
  {
    name: z.string().min(1).max(100),
    description: z.string().min(1).max(1000).optional(),
    parameters_schema: z.any().optional(),
    implementation_code: z.string().min(1).max(10000).optional(),
    execution_environment: z.enum(["javascript", "python", "shell"]).optional()
  },
  async ({ name, description, parameters_schema, implementation_code, execution_environment }) => {
    // Check if the tool exists
    if (!customTools[name]) {
      return {
        content: [{ type: "text", text: `Tool ${name} does not exist. Use define_function to create it.` }]
      };
    }

    // Update the tool definition
    const toolDef = customTools[name];

    if (description !== undefined) toolDef.description = description;
    if (parameters_schema !== undefined) toolDef.inputSchema = parameters_schema;
    if (implementation_code !== undefined) toolDef.implementation = implementation_code;
    if (execution_environment !== undefined) toolDef.executionEnvironment = execution_environment;

    toolDef.updatedAt = new Date();

    // Re-register the tool
    registerToolWithServer(toolDef);

    // Save the tool definition
    customTools[name] = toolDef;
    await saveToolsDatabase();

    return {
      content: [{ type: "text", text: `Function ${name} updated successfully.` }]
    };
  },
  { description: "Update an existing function" }
);

// Delete function tool
server.tool(
  "delete_function",
  {
    name: z.string().min(1).max(100)
  },
  async ({ name }) => {
    // Check if the tool exists
    if (!customTools[name]) {
      return {
        content: [{ type: "text", text: `Tool ${name} does not exist.` }]
      };
    }

    // Delete the tool
    delete customTools[name];
    await saveToolsDatabase();

    return {
      content: [{ type: "text", text: `Function ${name} deleted successfully.` }]
    };
  },
  { description: "Delete an existing function" }
);

// List functions tool
server.tool(
  "list_functions",
  {},
  async () => {
    const functionList = Object.entries(customTools).map(([name, toolDef]) => ({
      name,
      description: toolDef.description,
      executionEnvironment: toolDef.executionEnvironment,
      createdAt: toolDef.createdAt,
      updatedAt: toolDef.updatedAt
    }));

    return {
      content: [{ type: "text", text: JSON.stringify(functionList, null, 2) }]
    };
  },
  { description: "List all available functions" }
);

// Get function details tool
server.tool(
  "get_function_details",
  {
    name: z.string().min(1).max(100)
  },
  async ({ name }) => {
    // Check if the tool exists
    if (!customTools[name]) {
      return {
        content: [{ type: "text", text: `Tool ${name} does not exist.` }]
      };
    }

    // Get the tool definition
    const toolDef = customTools[name];

    return {
      content: [{ type: "text", text: JSON.stringify(toolDef, null, 2) }]
    };
  },
  { description: "Get details of a specific function" }
);

// Register web evaluation tools
registerWebEvalTools(server);

// Initialize the server
initializeToolsDatabase().catch(err => {
  console.error("Error initializing tools database:", err);
});

// Export the serverless function handler
export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the request body
    const { name, arguments: toolArgs } = req.body;

    // Check if the tool exists
    if (!server.tools[name]) {
      return res.status(404).json({ error: `Tool ${name} not found` });
    }

    // Call the tool
    const result = await server.tools[name].handler(toolArgs);

    // Return the result
    return res.status(200).json(result);
  } catch (err) {
    console.error('Error handling request:', err);
    return res.status(500).json({ error: err.message });
  }
}
