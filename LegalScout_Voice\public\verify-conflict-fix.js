/**
 * Verification script to confirm the assistant conflict has been resolved
 * Run this in the browser console to verify the fix
 */

window.verifyConflictFix = async function() {
  console.log('🔍 Verifying assistant conflict resolution...\n');

  try {
    // Import required modules
    const { supabase } = await import('/src/lib/supabase.js');

    // Check all attorney assignments
    const { data: attorneys, error } = await supabase
      .from('attorneys')
      .select('email, firm_name, subdomain, vapi_assistant_id, voice_provider, voice_id')
      .in('email', ['<EMAIL>', '<EMAIL>', '<EMAIL>'])
      .order('email');

    if (error) {
      console.error('❌ Error fetching attorneys:', error);
      return;
    }

    console.log('👥 Current attorney assignments:');
    attorneys.forEach(attorney => {
      console.log(`  📧 ${attorney.email}`);
      console.log(`     🏢 Firm: ${attorney.firm_name}`);
      console.log(`     🌐 Subdomain: ${attorney.subdomain}`);
      console.log(`     🤖 Assistant ID: ${attorney.vapi_assistant_id}`);
      console.log(`     🎤 Voice: ${attorney.voice_provider}/${attorney.voice_id}`);
      console.log('');
    });

    // Check for duplicates
    const assistantIds = attorneys.map(a => a.vapi_assistant_id).filter(Boolean);
    const uniqueIds = [...new Set(assistantIds)];
    
    if (assistantIds.length === uniqueIds.length) {
      console.log('✅ No duplicate assistant IDs found!');
    } else {
      console.log('⚠️ Duplicate assistant IDs still exist:');
      const duplicates = assistantIds.filter((id, index) => assistantIds.indexOf(id) !== index);
      duplicates.forEach(id => console.log(`  🔄 ${id}`));
    }

    // Test the current user's assignment
    const currentUser = localStorage.getItem('attorney');
    if (currentUser) {
      const parsedUser = JSON.parse(currentUser);
      console.log('\n🎯 Current user in localStorage:');
      console.log(`  📧 Email: ${parsedUser.email}`);
      console.log(`  🤖 Assistant ID: ${parsedUser.vapi_assistant_id}`);
      
      const matchingAttorney = attorneys.find(a => a.email === parsedUser.email);
      if (matchingAttorney) {
        if (matchingAttorney.vapi_assistant_id === parsedUser.vapi_assistant_id) {
          console.log('✅ localStorage matches database');
        } else {
          console.log('⚠️ localStorage assistant ID differs from database');
          console.log(`  📱 localStorage: ${parsedUser.vapi_assistant_id}`);
          console.log(`  🗄️ Database: ${matchingAttorney.vapi_assistant_id}`);
          
          // Update localStorage
          parsedUser.vapi_assistant_id = matchingAttorney.vapi_assistant_id;
          localStorage.setItem('attorney', JSON.stringify(parsedUser));
          console.log('🔄 Updated localStorage with database value');
        }
      }
    }

    return {
      success: true,
      attorneys,
      hasDuplicates: assistantIds.length !== uniqueIds.length,
      duplicateCount: assistantIds.length - uniqueIds.length
    };

  } catch (error) {
    console.error('❌ Error during verification:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

console.log('🧪 Conflict verification loaded! Run: verifyConflictFix()');
