/**
 * Test script to verify that our Framer Motion fix is working
 */

(function() {
  console.log('[TestFramerFix] Starting test...');

  // Check if React is defined
  if (typeof window.React === 'undefined') {
    console.error('[TestFramerFix] Error: window.React is undefined');
  } else {
    console.log('[TestFramerFix] Success: window.React is defined');
  }

  // Check if React.createContext is defined
  if (typeof window.React.createContext === 'undefined') {
    console.error('[TestFramerFix] Error: window.React.createContext is undefined');
  } else {
    console.log('[TestFramerFix] Success: window.React.createContext is defined');
  }

  // Check if LayoutGroupContext is defined
  if (typeof window.LayoutGroupContext === 'undefined') {
    console.error('[TestFramerFix] Error: window.LayoutGroupContext is undefined');
  } else {
    console.log('[TestFramerFix] Success: window.LayoutGroupContext is defined');
  }

  // Try to create a context using React.createContext
  try {
    const TestContext = window.React.createContext(null);
    console.log('[TestFramerFix] Success: Created a context using React.createContext');
  } catch (error) {
    console.error('[TestFramerFix] Error creating context:', error);
  }

  console.log('[TestFramerFix] Test complete');
})();
