/**
 * Override for AttorneyFixV2
 * 
 * This script overrides the AttorneyFixV2 script to prevent it from waiting for Supa<PERSON>
 * and instead use a default attorney.
 */

(function() {
  console.log('[AttorneyFixV2Override] Starting override...');
  
  // Wait for the original script to load
  const checkInterval = setInterval(() => {
    // Check if the original script has started
    if (document.querySelector('script[src*="fix-attorney-id-v2.js"]')) {
      clearInterval(checkInterval);
      applyOverride();
    }
  }, 100);
  
  // Clear interval after 10 seconds
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[AttorneyFixV2Override] Timed out waiting for original script');
  }, 10000);
  
  // Apply the override
  function applyOverride() {
    console.log('[AttorneyFixV2Override] Applying override...');
    
    // Create a default attorney
    const defaultAttorney = {
      id: generateUUID(),
      subdomain: 'default',
      firm_name: 'Your Law Firm',
      name: 'Your Name',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      voice_provider: '11labs',
      voice_id: 'sarah',
      welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
      information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
      vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
      vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
    };
    
    // Save to localStorage
    localStorage.setItem('attorney', JSON.stringify(defaultAttorney));
    localStorage.setItem('attorney_id', defaultAttorney.id);
    localStorage.setItem('attorney_version', Date.now().toString());
    
    console.log('[AttorneyFixV2Override] Saved default attorney to localStorage:', defaultAttorney.id);
    
    // Override the waitForSupabase function
    window.waitForSupabaseOverridden = true;
    
    console.log('[AttorneyFixV2Override] Override applied');
  }
  
  // Generate a UUID v4
  function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
})();
