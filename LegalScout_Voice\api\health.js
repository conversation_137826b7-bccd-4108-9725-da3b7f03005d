/**
 * Health check endpoint for Vapi Web SDK
 * This endpoint is required by Vapi to validate server health before establishing calls
 */

export default function handler(req, res) {
  // Set CORS headers to allow <PERSON>api to access this endpoint
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow GET requests for health check
  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    // Return health status
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'LegalScout Voice API',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      checks: {
        server: 'ok',
        memory: {
          used: process.memoryUsage().heapUsed,
          total: process.memoryUsage().heapTotal,
          status: 'ok'
        }
      }
    };

    console.log('[Health Check] Vapi health check requested:', {
      timestamp: healthStatus.timestamp,
      userAgent: req.headers['user-agent'],
      origin: req.headers.origin
    });

    res.status(200).json(healthStatus);
  } catch (error) {
    console.error('[Health Check] Error during health check:', error);
    
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
}
