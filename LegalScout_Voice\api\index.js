/**
 * Consolidated API Router - Handles all API endpoints to stay within Vercel's 12 function limit
 * Only essential endpoints are kept as separate files (webhook, etc.)
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  // CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Vapi-Signature, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Parse the URL path
  const { url } = req;
  const path = url.split('?')[0]; // Remove query parameters

  console.log(`[API Router] ${req.method} ${path}`);

  try {
    // Route to appropriate handler
    if (path === '/api/health') {
      return handleHealth(req, res);
    } else if (path === '/api/env') {
      return handleEnv(req, res);
    } else if (path === '/api/call-logs') {
      return handleCallLogs(req, res);
    } else if (path.startsWith('/api/sync-tools/')) {
      return handleSyncTools(req, res, path);
    } else if (path.startsWith('/api/vapi/')) {
      return handleVapi(req, res, path);
    } else if (path.startsWith('/api/vapi-mcp-server')) {
      return handleVapiMcp(req, res, path);
    } else if (path === '/api/ai-meta-mcp') {
      return handleAiMetaMcp(req, res);
    } else if (path === '/api/bug-report') {
      return handleBugReport(req, res);
    } else if (path === '/api/test') {
      return handleTest(req, res);
    } else {
      return res.status(404).json({
        error: 'API endpoint not found',
        path: path,
        availableEndpoints: [
          '/api/health',
          '/api/env',
          '/api/call-logs',
          '/api/sync-tools/*',
          '/api/vapi/*',
          '/api/vapi-mcp-server',
          '/api/ai-meta-mcp',
          '/api/bug-report',
          '/api/test'
        ]
      });
    }
  } catch (error) {
    console.error('[API Router] Error:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message
    });
  }
}

// Health check endpoint
async function handleHealth(req, res) {
  return res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
}

// Environment check endpoint
async function handleEnv(req, res) {
  return res.status(200).json({
    hasSupabaseUrl: !!process.env.SUPABASE_URL,
    hasSupabaseKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    hasVapiKey: !!process.env.VAPI_TOKEN,
    nodeEnv: process.env.NODE_ENV || 'development'
  });
}

// Call logs endpoint
async function handleCallLogs(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: 'Authorization header required' });
    }

    // For now, return empty array - implement actual call log fetching as needed
    return res.status(200).json([]);
  } catch (error) {
    console.error('[Call Logs] Error:', error);
    return res.status(500).json({ error: 'Failed to fetch call logs' });
  }
}

// Sync tools endpoints
async function handleSyncTools(req, res, path) {
  const endpoint = path.replace('/api/sync-tools/', '');

  if (endpoint === 'sync-attorney-profile') {
    return handleSyncAttorneyProfile(req, res);
  } else if (endpoint === 'manage-auth-state') {
    return handleManageAuthState(req, res);
  } else if (endpoint === 'validate-configuration') {
    return handleValidateConfiguration(req, res);
  } else if (endpoint === 'check-preview-consistency') {
    return handleCheckPreviewConsistency(req, res);
  } else {
    return res.status(404).json({ error: 'Sync tool endpoint not found' });
  }
}

async function handleSyncAttorneyProfile(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { attorneyId, forceUpdate } = req.body;

    // Basic validation
    if (!attorneyId) {
      return res.status(400).json({ error: 'Attorney ID required' });
    }

    // For now, return success - implement actual sync logic as needed
    return res.status(200).json({
      success: true,
      message: 'Attorney profile sync completed',
      attorneyId,
      forceUpdate
    });
  } catch (error) {
    console.error('[Sync Attorney Profile] Error:', error);
    return res.status(500).json({ error: 'Failed to sync attorney profile' });
  }
}

async function handleManageAuthState(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { authData, action } = req.body;

    // For now, return success - implement actual auth state management as needed
    return res.status(200).json({
      success: true,
      message: 'Auth state managed successfully',
      action
    });
  } catch (error) {
    console.error('[Manage Auth State] Error:', error);
    return res.status(500).json({ error: 'Failed to manage auth state' });
  }
}

async function handleValidateConfiguration(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // For now, return success - implement actual validation as needed
    return res.status(200).json({
      success: true,
      message: 'Configuration validation completed',
      valid: true
    });
  } catch (error) {
    console.error('[Validate Configuration] Error:', error);
    return res.status(500).json({ error: 'Failed to validate configuration' });
  }
}

async function handleCheckPreviewConsistency(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // For now, return success - implement actual consistency check as needed
    return res.status(200).json({
      success: true,
      message: 'Preview consistency check completed',
      consistent: true
    });
  } catch (error) {
    console.error('[Check Preview Consistency] Error:', error);
    return res.status(500).json({ error: 'Failed to check preview consistency' });
  }
}

// Vapi endpoints
async function handleVapi(req, res, path) {
  const endpoint = path.replace('/api/vapi/', '');

  if (endpoint === 'config') {
    return handleVapiConfig(req, res);
  } else {
    return res.status(404).json({ error: 'Vapi endpoint not found' });
  }
}

async function handleVapiConfig(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Return basic Vapi configuration
    return res.status(200).json({
      voices: [
        { id: 'echo', name: 'Echo', provider: 'playht' },
        { id: 'cho', name: 'Cho', provider: 'playht' },
        { id: 'alloy', name: 'Alloy', provider: 'openai' }
      ],
      models: [
        { id: 'gpt-4', name: 'GPT-4', provider: 'openai' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'openai' }
      ]
    });
  } catch (error) {
    console.error('[Vapi Config] Error:', error);
    return res.status(500).json({ error: 'Failed to fetch Vapi configuration' });
  }
}

// Vapi MCP Server endpoint
async function handleVapiMcp(req, res, path) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // For now, return a basic response - implement actual MCP server logic as needed
    return res.status(200).json({
      jsonrpc: '2.0',
      id: req.body?.id || 1,
      result: {
        success: true,
        message: 'MCP server endpoint - implement as needed'
      }
    });
  } catch (error) {
    console.error('[Vapi MCP] Error:', error);
    return res.status(500).json({
      jsonrpc: '2.0',
      id: req.body?.id || 1,
      error: {
        code: -32603,
        message: 'Internal error',
        data: error.message
      }
    });
  }
}

// AI Meta MCP endpoint
async function handleAiMetaMcp(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, arguments: args } = req.body;

    // For now, return a basic response - implement actual AI Meta MCP logic as needed
    return res.status(200).json({
      success: true,
      message: 'AI Meta MCP endpoint - implement as needed',
      tool: name,
      arguments: args
    });
  } catch (error) {
    console.error('[AI Meta MCP] Error:', error);
    return res.status(500).json({ error: 'Failed to process AI Meta MCP request' });
  }
}

// Bug report endpoint
async function handleBugReport(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { message, userAgent, url, timestamp } = req.body;

    // For now, just log the bug report - implement actual bug reporting as needed
    console.log('[Bug Report]', { message, userAgent, url, timestamp });

    return res.status(200).json({
      success: true,
      message: 'Bug report received'
    });
  } catch (error) {
    console.error('[Bug Report] Error:', error);
    return res.status(500).json({ error: 'Failed to submit bug report' });
  }
}

// Test endpoint
async function handleTest(req, res) {
  return res.status(200).json({
    success: true,
    message: 'API router is working',
    timestamp: new Date().toISOString(),
    method: req.method,
    path: req.url
  });
}
