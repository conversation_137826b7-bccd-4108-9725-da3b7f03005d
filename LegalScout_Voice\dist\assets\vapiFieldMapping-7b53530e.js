import{G as m}from"./pages-5c5506e6.js";import"./vendor-068d85d4.js";import"./vendor-react-3e31a257.js";const i=m("vapiFieldMapping"),d={welcome_message:"firstMessage",vapi_instructions:"instructions",voice_id:"voice.voiceId",voice_provider:"voice.provider",ai_model:"llm.model",summary_prompt:"analysis.summary.prompt",structured_data_prompt:"analysis.structuredData.prompt",structured_data_schema:"analysis.structuredData.schema"},s={firstMessage:"Hello, I'm Scout from your law firm. How can I help you today?",firstMessageMode:"assistant-speaks-first",instructions:"You are a legal assistant. Help potential clients understand their legal needs and collect relevant information for consultation.",voice:{provider:"11labs",voiceId:"sarah",model:"eleven_turbo_v2_5"},llm:{provider:"openai",model:"gpt-4o"},transcriber:{provider:"deepgram",model:"nova-3"}},c=e=>{if(!e)return i.error("No attorney data provided"),{...s};const r={...s};return r.name=`${e.firm_name||"Law Firm"} Legal Assistant`,e.welcome_message&&(r.firstMessage=e.welcome_message),e.vapi_instructions&&(r.instructions=e.vapi_instructions),(e.voice_id||e.voice_provider)&&(r.voice={...r.voice,voiceId:e.voice_id||r.voice.voiceId,provider:e.voice_provider||r.voice.provider}),e.ai_model&&(r.llm={...r.llm,model:e.ai_model}),(e.summary_prompt||e.structured_data_prompt||e.structured_data_schema)&&(r.analysis={summary:{enabled:!!e.summary_prompt,prompt:e.summary_prompt||""},structuredData:{enabled:!!(e.structured_data_prompt&&e.structured_data_schema),prompt:e.structured_data_prompt||"",schema:e.structured_data_schema||{}}}),i.info("Converted attorney data to Vapi format"),r},t=e=>{if(!e)return i.error("No Vapi data provided"),{};const r={};return e.firstMessage&&(r.welcome_message=e.firstMessage),e.instructions&&(r.vapi_instructions=e.instructions),e.voice&&(e.voice.voiceId&&(r.voice_id=e.voice.voiceId),e.voice.provider&&(r.voice_provider=e.voice.provider)),e.llm&&e.llm.model&&(r.ai_model=e.llm.model),e.analysis&&(e.analysis.summary&&e.analysis.summary.prompt&&(r.summary_prompt=e.analysis.summary.prompt),e.analysis.structuredData&&(e.analysis.structuredData.prompt&&(r.structured_data_prompt=e.analysis.structuredData.prompt),e.analysis.structuredData.schema&&(r.structured_data_schema=e.analysis.structuredData.schema))),i.info("Extracted attorney fields from Vapi data"),r},u=(e,r={})=>{if(!e||Object.keys(e).length===0)return i.warn("No changed fields provided"),{};const o={};return e.welcome_message!==void 0&&(o.firstMessage=e.welcome_message),e.vapi_instructions!==void 0&&(o.instructions=e.vapi_instructions),(e.voice_id!==void 0||e.voice_provider!==void 0)&&(o.voice={...r.voice||{},voiceId:e.voice_id!==void 0?e.voice_id:r.voice?.voiceId||"sarah",provider:e.voice_provider!==void 0?e.voice_provider:r.voice?.provider||"11labs"}),e.ai_model!==void 0&&(o.llm={...r.llm||{},model:e.ai_model}),i.info("Created Vapi update data from changed fields"),o},_={FIELD_MAPPING:d,DEFAULT_VALUES:s,attorneyToVapi:c,vapiToAttorney:t,createVapiUpdateData:u};export{s as DEFAULT_VALUES,d as FIELD_MAPPING,c as attorneyToVapi,u as createVapiUpdateData,_ as default,t as vapiToAttorney};
//# sourceMappingURL=vapiFieldMapping-7b53530e.js.map
