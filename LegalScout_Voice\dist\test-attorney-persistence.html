<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Attorney Persistence Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background-color: #4B74AA;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #3A5A8C;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
        }
        .log-container {
            margin-top: 20px;
        }
    </style>
    <!-- Include the fix scripts -->
    <script src="/fix-attorney-persistence.js"></script>
    <script src="/sync-tools-fix.js"></script>
    <script src="/fix-auth-state.js"></script>
</head>
<body>
    <h1>Attorney Persistence Test</h1>
    
    <div class="section">
        <h2>Current Attorney Data</h2>
        <pre id="currentAttorney">Loading...</pre>
        <button id="refreshAttorney">Refresh</button>
        <button id="clearAttorney">Clear Attorney Data</button>
    </div>
    
    <div class="section">
        <h2>Current Auth Data</h2>
        <pre id="currentAuth">Loading...</pre>
        <button id="refreshAuth">Refresh</button>
        <button id="clearAuth">Clear Auth Data</button>
    </div>
    
    <div class="section">
        <h2>Create Test Attorney</h2>
        <div>
            <label for="attorneyId">Attorney ID:</label>
            <input type="text" id="attorneyId" placeholder="UUID or leave blank for auto-generated">
        </div>
        <div>
            <label for="attorneyName">Name:</label>
            <input type="text" id="attorneyName" value="Test Attorney">
        </div>
        <div>
            <label for="attorneyEmail">Email:</label>
            <input type="email" id="attorneyEmail" value="<EMAIL>">
        </div>
        <div>
            <label for="firmName">Firm Name:</label>
            <input type="text" id="firmName" value="Test Law Firm">
        </div>
        <button id="createAttorney">Create Test Attorney</button>
    </div>
    
    <div class="section">
        <h2>Fix Attorney ID</h2>
        <button id="fixAttorneyId">Fix Attorney ID</button>
        <button id="simulateSync">Simulate Sync</button>
        <button id="simulateConsistencyCheck">Simulate Consistency Check</button>
    </div>
    
    <div class="section">
        <h2>Log</h2>
        <div class="log-container">
            <pre id="log"></pre>
        </div>
        <button id="clearLog">Clear Log</button>
    </div>
    
    <script>
        // Helper function to log messages
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
            logElement.textContent = `[${timestamp}] ${message}\n` + logElement.textContent;
        }
        
        // Helper function to get attorney data
        function getAttorneyData() {
            try {
                const storedAttorney = localStorage.getItem('attorney');
                if (storedAttorney) {
                    return JSON.parse(storedAttorney);
                }
            } catch (error) {
                log(`Error getting attorney data: ${error.message}`);
            }
            return null;
        }
        
        // Helper function to get auth data
        function getAuthData() {
            try {
                const authData = localStorage.getItem('supabase.auth.token');
                if (authData) {
                    return JSON.parse(authData);
                }
            } catch (error) {
                log(`Error getting auth data: ${error.message}`);
            }
            return null;
        }
        
        // Helper function to generate a UUID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                const r = Math.random() * 16 | 0;
                const v = c === 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }
        
        // Update the attorney display
        function updateAttorneyDisplay() {
            const attorneyElement = document.getElementById('currentAttorney');
            const attorney = getAttorneyData();
            
            if (attorney) {
                attorneyElement.textContent = JSON.stringify(attorney, null, 2);
            } else {
                attorneyElement.textContent = 'No attorney data found';
            }
        }
        
        // Update the auth display
        function updateAuthDisplay() {
            const authElement = document.getElementById('currentAuth');
            const auth = getAuthData();
            
            if (auth) {
                // Only show relevant parts of the auth data
                const simplifiedAuth = {
                    currentSession: auth.currentSession ? {
                        user: auth.currentSession.user ? {
                            id: auth.currentSession.user.id,
                            email: auth.currentSession.user.email,
                            user_metadata: auth.currentSession.user.user_metadata
                        } : null
                    } : null
                };
                
                authElement.textContent = JSON.stringify(simplifiedAuth, null, 2);
            } else {
                authElement.textContent = 'No auth data found';
            }
        }
        
        // Initialize displays
        updateAttorneyDisplay();
        updateAuthDisplay();
        
        // Set up event listeners
        document.getElementById('refreshAttorney').addEventListener('click', () => {
            updateAttorneyDisplay();
            log('Attorney data refreshed');
        });
        
        document.getElementById('clearAttorney').addEventListener('click', () => {
            localStorage.removeItem('attorney');
            updateAttorneyDisplay();
            log('Attorney data cleared');
        });
        
        document.getElementById('refreshAuth').addEventListener('click', () => {
            updateAuthDisplay();
            log('Auth data refreshed');
        });
        
        document.getElementById('clearAuth').addEventListener('click', () => {
            localStorage.removeItem('supabase.auth.token');
            updateAuthDisplay();
            log('Auth data cleared');
        });
        
        document.getElementById('createAttorney').addEventListener('click', () => {
            const attorneyId = document.getElementById('attorneyId').value.trim() || generateUUID();
            const name = document.getElementById('attorneyName').value.trim();
            const email = document.getElementById('attorneyEmail').value.trim();
            const firmName = document.getElementById('firmName').value.trim();
            
            const attorney = {
                id: attorneyId,
                name,
                email,
                firm_name: firmName,
                welcome_message: 'Welcome to my law firm. How can I help you today?',
                vapi_instructions: 'You are a legal assistant for a law firm.',
                voice_provider: 'playht',
                voice_id: 'ranger',
                user_id: attorneyId
            };
            
            localStorage.setItem('attorney', JSON.stringify(attorney));
            updateAttorneyDisplay();
            log(`Created test attorney with ID: ${attorneyId}`);
            
            // Also create a mock auth token if none exists
            if (!getAuthData()) {
                const mockAuth = {
                    currentSession: {
                        user: {
                            id: attorneyId,
                            email,
                            user_metadata: {
                                name
                            }
                        }
                    }
                };
                
                localStorage.setItem('supabase.auth.token', JSON.stringify(mockAuth));
                updateAuthDisplay();
                log('Created mock auth token');
            }
        });
        
        document.getElementById('fixAttorneyId').addEventListener('click', () => {
            if (typeof window.fixAttorneyId === 'function') {
                const result = window.fixAttorneyId();
                log(`Fix attorney ID result: ${result}`);
                updateAttorneyDisplay();
            } else {
                log('fixAttorneyId function not found');
            }
        });
        
        document.getElementById('simulateSync').addEventListener('click', async () => {
            try {
                const attorney = getAttorneyData();
                if (!attorney) {
                    log('No attorney data found, cannot simulate sync');
                    return;
                }
                
                log('Simulating sync...');
                
                const response = await fetch('/api/sync-tools/sync-attorney-profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        attorneyId: attorney.id,
                        forceUpdate: true
                    })
                });
                
                const result = await response.json();
                log(`Sync result: ${JSON.stringify(result, null, 2)}`);
                updateAttorneyDisplay();
            } catch (error) {
                log(`Error simulating sync: ${error.message}`);
            }
        });
        
        document.getElementById('simulateConsistencyCheck').addEventListener('click', async () => {
            try {
                const attorney = getAttorneyData();
                if (!attorney) {
                    log('No attorney data found, cannot simulate consistency check');
                    return;
                }
                
                log('Simulating consistency check...');
                
                const response = await fetch('/api/sync-tools/check-preview-consistency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        attorneyId: attorney.id
                    })
                });
                
                const result = await response.json();
                log(`Consistency check result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error simulating consistency check: ${error.message}`);
            }
        });
        
        document.getElementById('clearLog').addEventListener('click', () => {
            document.getElementById('log').textContent = '';
            log('Log cleared');
        });
        
        // Initial log
        log('Attorney Persistence Test Page Loaded');
    </script>
</body>
</html>
