# SimplifiedPreview Component Tasks

## Priority Tasks

### 1. Logo Display Fixes

- [ ] Add debug logging for logoUrl and mascot values
- [ ] Fix logo priority in the button to use logoUrl over mascot
- [ ] Ensure proper image loading in both header and button
- [ ] Test with different logo URLs to verify functionality
- [ ] Add error handling for when images fail to load

### 2. VAPI Integration

- [ ] Update SimplifiedPreview.jsx to import vapiService
- [ ] Modify handleStartConsultation to properly initiate VAPI calls
- [ ] Add proper parent window communication for iframe scenarios
- [ ] Implement loading states during call initialization
- [ ] Add fallback UI for when calls fail to initialize

### 3. UI and Styling Fixes

- [ ] Ensure consistent application of all customization colors
- [ ] Test buttonOpacity with different values
- [ ] Verify practiceAreaBackgroundOpacity is applied correctly
- [ ] Test with various theme settings (dark/light)
- [ ] Ensure responsive design works on different screen sizes

## Testing Tasks

- [ ] Test standalone preview (direct URL access)
- [ ] Test embedded preview (inside iframe)
- [ ] Verify all customization options
- [ ] Test with mock VAPI service
- [ ] Test with real VAPI service

## Documentation Tasks

- [ ] Update implementation documentation with fixes
- [ ] Create user guide for customizing the preview
- [ ] Update technical documentation for VAPI integration
- [ ] Document component props and their usage

## Future Enhancements

- [ ] Add knowledge base integration
- [ ] Implement voice customization options
- [ ] Add feedback mechanism after calls
- [ ] Consider merging improvements back into PreviewInterface.tsx
- [ ] Add more customization options for the preview 