# Voice Integration System

## Overview
LegalScout uses Vapi.ai for voice-based interactions, providing a natural language interface for users to describe their legal needs and connect with attorneys. The system handles real-time voice processing, conversation management, and data collection.

## Core Components

### VapiCall Component
```jsx
const VapiCall = ({ onEndCall, subdomain = 'default' }) => {
  // Component implementation
}
```

#### Props
- `onEndCall`: Callback function triggered when call ends
- `subdomain`: Attorney subdomain for customization (default: 'default')

#### Features
- Real-time voice processing
- Message history management
- Audio level monitoring
- Assistant speaking state tracking
- Error handling
- Subdomain-specific configuration

### useVapiCall Hook
```typescript
interface VapiCallHook {
  status: CallStatus;
  dossierData: DossierData;
  volumeLevel: number;
  assistantIsSpeaking: boolean;
  errorMessage: string | null;
  subdomainConfig: SubdomainConfig;
  startCall: () => void;
  stopCall: () => void;
  vapi: VapiInstance;
  messageHistory: Message[];
}
```

#### Features
- Call state management
- Audio processing
- Message handling
- Dossier data collection
- Error handling
- Subdomain configuration

## Data Structures

### Message Format
```typescript
interface Message {
  type: 'user' | 'assistant';
  text: string;
  timestamp?: Date;
}
```

### Dossier Data
```typescript
interface DossierData {
  clientName?: string;
  legalIssue?: string;
  practiceArea?: string;
  location?: string;
  urgency?: string;
  noteworthy?: string;
}
```

### Call Status
```typescript
enum CallStatus {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  ACTIVE = 'active',
  ERROR = 'error',
  COMPLETED = 'completed'
}
```

## Conversation Flow

1. **Initialization**
   ```jsx
   const vapi = useVapiCall({ subdomain, onEndCall });
   ```

2. **Starting a Call**
   ```jsx
   vapi.startCall();
   ```

3. **Message Processing**
   - User speaks
   - Speech-to-text conversion
   - Intent processing
   - Response generation
   - Text-to-speech conversion

4. **Data Collection**
   - Gather client information
   - Identify legal needs
   - Determine practice area
   - Collect location data
   - Assess urgency

5. **Call Completion**
   ```jsx
   vapi.stopCall();
   onEndCall(dossierData);
   ```

## Subdomain Configuration

### Configuration Structure
```typescript
interface SubdomainConfig {
  instructions: string;
  context: string;
  practiceAreas: string[];
  depositUrl?: string;
}
```

### Example Configuration
```json
{
  "instructions": "Gather information about personal injury cases...",
  "context": "Personal injury law firm specializing in...",
  "practiceAreas": ["Personal Injury", "Car Accidents", "Medical Malpractice"],
  "depositUrl": "https://example.com/deposit"
}
```

## Error Handling

### Error Types
```typescript
enum VapiErrorType {
  CONNECTION_ERROR = 'connection_error',
  AUDIO_ERROR = 'audio_error',
  PROCESSING_ERROR = 'processing_error',
  CONFIGURATION_ERROR = 'configuration_error'
}
```

### Error Handling Example
```jsx
try {
  await vapi.startCall();
} catch (error) {
  handleVapiError(error);
}
```

## Best Practices

1. **Audio Quality**
   - Monitor volume levels
   - Provide feedback for poor audio
   - Handle background noise

2. **Conversation Management**
   - Clear turn-taking indicators
   - Visual feedback during processing
   - Graceful error recovery

3. **Data Collection**
   - Progressive information gathering
   - Validation of critical fields
   - Confirmation of understanding

4. **User Experience**
   - Clear status indicators
   - Loading states
   - Error messages
   - Recovery options

## Integration Example

```jsx
function LegalConsultation() {
  const handleCallEnd = (dossierData) => {
    // Process collected data
    saveBrief(dossierData);
  };

  return (
    <div className="consultation">
      <VapiCall
        subdomain="personal-injury"
        onEndCall={handleCallEnd}
      />
      <CallControls />
      <MessageHistory />
    </div>
  );
}
``` 