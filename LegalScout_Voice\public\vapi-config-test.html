<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Configuration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .key-display {
            font-family: monospace;
            background: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Vapi Configuration Test</h1>
        <p>This page tests the Vapi API configuration and key access in the current environment.</p>
        
        <div class="test-section info">
            <h3>Environment Information</h3>
            <div id="environment-info">Loading...</div>
        </div>

        <div class="test-section info">
            <h3>Available Environment Variables</h3>
            <div id="env-vars">Loading...</div>
        </div>

        <div class="test-section info">
            <h3>Vapi Configuration</h3>
            <div id="vapi-config">Loading...</div>
        </div>

        <div class="test-section">
            <h3>API Key Tests</h3>
            <button onclick="testApiKeys()">Test API Keys</button>
            <div id="api-key-results"></div>
        </div>

        <div class="test-section">
            <h3>Phone Numbers Test</h3>
            <button onclick="testPhoneNumbers()">Test Phone Numbers API</button>
            <div id="phone-numbers-results"></div>
        </div>

        <div class="test-section">
            <h3>Assistants Test</h3>
            <button onclick="testAssistants()">Test Assistants API</button>
            <div id="assistants-results"></div>
        </div>
    </div>

    <script>
        // Environment detection
        function detectEnvironment() {
            const hostname = window.location.hostname;
            const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
            const isVercel = hostname.includes('vercel.app');
            const isProduction = hostname.includes('legalscout.net');
            
            return {
                hostname,
                isLocalhost,
                isVercel,
                isProduction,
                isDevelopment: isLocalhost,
                environment: isLocalhost ? 'development' : (isVercel ? 'vercel' : (isProduction ? 'production' : 'unknown'))
            };
        }

        // Get environment variables
        function getEnvironmentVariables() {
            const envVars = {};
            
            // Check import.meta.env (Vite)
            if (typeof import !== 'undefined' && import.meta && import.meta.env) {
                Object.keys(import.meta.env).forEach(key => {
                    if (key.includes('VAPI')) {
                        envVars[`import.meta.env.${key}`] = import.meta.env[key];
                    }
                });
            }
            
            // Check window variables
            ['VITE_VAPI_PUBLIC_KEY', 'VITE_VAPI_SECRET_KEY', 'VITE_VAPI_PRIVATE_KEY', 'VAPI_TOKEN', 'VAPI_PRIVATE_KEY'].forEach(key => {
                if (window[key]) {
                    envVars[`window.${key}`] = window[key];
                }
            });
            
            return envVars;
        }

        // Test API keys
        async function testApiKeys() {
            const resultsDiv = document.getElementById('api-key-results');
            resultsDiv.innerHTML = '<p>Testing API keys...</p>';
            
            try {
                // Try to import the Vapi config
                const vapiConfigModule = await import('/src/config/vapiConfig.js');
                const { getVapiApiKey, validateVapiConfig } = vapiConfigModule;
                
                const validation = validateVapiConfig();
                const clientKey = getVapiApiKey('client');
                const serverKey = getVapiApiKey('server');
                
                let html = '<div class="success"><h4>✅ Vapi Config Module Loaded Successfully</h4></div>';
                
                html += '<h4>Validation Results:</h4>';
                html += `<pre>${JSON.stringify(validation, null, 2)}</pre>`;
                
                html += '<h4>API Keys:</h4>';
                html += `<p><strong>Client Key:</strong> <span class="key-display">${clientKey ? clientKey.substring(0, 8) + '...' : 'Not available'}</span></p>`;
                html += `<p><strong>Server Key:</strong> <span class="key-display">${serverKey ? serverKey.substring(0, 8) + '...' : 'Not available'}</span></p>`;
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error"><h4>❌ Error Loading Vapi Config</h4><pre>${error.message}</pre></div>`;
            }
        }

        // Test phone numbers API
        async function testPhoneNumbers() {
            const resultsDiv = document.getElementById('phone-numbers-results');
            resultsDiv.innerHTML = '<p>Testing phone numbers API...</p>';
            
            try {
                // Try to import the Vapi config
                const vapiConfigModule = await import('/src/config/vapiConfig.js');
                const { getVapiApiKey } = vapiConfigModule;
                
                const serverKey = getVapiApiKey('server');
                
                if (!serverKey) {
                    throw new Error('No server API key available');
                }
                
                const response = await fetch('https://api.vapi.ai/phone-number', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${serverKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API request failed: ${response.status} ${response.statusText}\n${errorText}`);
                }
                
                const data = await response.json();
                
                let html = '<div class="success"><h4>✅ Phone Numbers API Success</h4></div>';
                html += `<p><strong>Status:</strong> ${response.status}</p>`;
                html += `<p><strong>Count:</strong> ${Array.isArray(data) ? data.length : 'N/A'}</p>`;
                html += `<h4>Response:</h4><pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error"><h4>❌ Phone Numbers API Error</h4><pre>${error.message}</pre></div>`;
            }
        }

        // Test assistants API
        async function testAssistants() {
            const resultsDiv = document.getElementById('assistants-results');
            resultsDiv.innerHTML = '<p>Testing assistants API...</p>';
            
            try {
                // Try to import the Vapi config
                const vapiConfigModule = await import('/src/config/vapiConfig.js');
                const { getVapiApiKey } = vapiConfigModule;
                
                const serverKey = getVapiApiKey('server');
                
                if (!serverKey) {
                    throw new Error('No server API key available');
                }
                
                const response = await fetch('https://api.vapi.ai/assistant', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${serverKey}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API request failed: ${response.status} ${response.statusText}\n${errorText}`);
                }
                
                const data = await response.json();
                
                let html = '<div class="success"><h4>✅ Assistants API Success</h4></div>';
                html += `<p><strong>Status:</strong> ${response.status}</p>`;
                html += `<p><strong>Count:</strong> ${Array.isArray(data) ? data.length : 'N/A'}</p>`;
                html += `<h4>Response:</h4><pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                resultsDiv.innerHTML = html;
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error"><h4>❌ Assistants API Error</h4><pre>${error.message}</pre></div>`;
            }
        }

        // Initialize page
        function init() {
            // Display environment info
            const envInfo = detectEnvironment();
            document.getElementById('environment-info').innerHTML = `<pre>${JSON.stringify(envInfo, null, 2)}</pre>`;
            
            // Display environment variables
            const envVars = getEnvironmentVariables();
            document.getElementById('env-vars').innerHTML = `<pre>${JSON.stringify(envVars, null, 2)}</pre>`;
            
            // Try to load Vapi config
            import('/src/config/vapiConfig.js').then(vapiConfigModule => {
                const config = vapiConfigModule.getVapiConfig();
                document.getElementById('vapi-config').innerHTML = `<pre>${JSON.stringify({
                    hasPublicKey: !!config.publicKey,
                    hasSecretKey: !!config.secretKey,
                    publicKeyPrefix: config.publicKey ? config.publicKey.substring(0, 8) + '...' : 'none',
                    secretKeyPrefix: config.secretKey ? config.secretKey.substring(0, 8) + '...' : 'none',
                    apiUrl: config.apiUrl,
                    mcpUrl: config.mcpUrl,
                    isDevelopment: config.isDevelopment
                }, null, 2)}</pre>`;
            }).catch(error => {
                document.getElementById('vapi-config').innerHTML = `<div class="error">Error loading Vapi config: ${error.message}</div>`;
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>
