/**
 * Dead Simple Website Import API
 * Uses Jina AI Reader + OpenAI for 1-click attorney profile extraction
 */

export default async function handler(req, res) {
  // Enable CORS with comprehensive headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept, X-Vapi-Signature, X-Requested-With');
  res.setHeader('Access-Control-Max-Age', '86400');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'URL is required' });
    }

    console.log('Importing website:', url);

    // Step 1: Use Jina AI Reader to get clean content
    const jinaResponse = await fetch(`https://r.jina.ai/${url}`, {
      headers: {
        'Accept': 'application/json',
        'X-With-Generated-Alt': 'true'
      }
    });

    if (!jinaResponse.ok) {
      throw new Error(`Failed to fetch website content: ${jinaResponse.status}`);
    }

    const content = await jinaResponse.text();

    // Step 2: Use OpenAI to extract structured attorney profile data
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        response_format: { type: "json_object" },
        messages: [
          {
            role: 'system',
            content: `Extract comprehensive attorney/law firm data from website content. Return JSON with ALL these fields:

{
  // PROFILE TAB - Basic Information
  "firmName": "string",
  "attorneyName": "string (full name of primary attorney)",
  "barId": "string (bar number if found)",
  "state": "string (2-letter state code)",
  "phone": "string (formatted phone number)",
  "email": "string (contact email)",
  "officeAddress": "string (full address)",
  "schedulingLink": "string (calendly/booking link if found)",

  // PROFILE TAB - Practice Information
  "practiceAreas": ["array of specific practice areas"],
  "practiceDescription": "string (detailed description of practice)",

  // AGENT TAB - Instructions & Context
  "systemPrompt": "string (detailed AI assistant instructions based on practice)",
  "knowledgeBase": "string (firm policies, procedures, specialties)",
  "practiceAreaTemplate": "string (best matching template: personal_injury, family_law, etc)",

  // AGENT TAB - Messages & Text
  "titleText": "string (main heading for agent page)",
  "welcomeMessage": "string (personalized greeting)",
  "informationGatheringPrompt": "string (what info to collect from clients)",
  "buttonText": "string (call-to-action button)",
  "disclaimerText": "string (legal disclaimers found)",

  // AGENT TAB - Appearance & Branding
  "colors": {
    "primary": "hex color (main brand color)",
    "secondary": "hex color (accent color)",
    "accent": "hex color (highlight color)",
    "background": "hex color (page background)",
    "text": "hex color (main text color)"
  },
  "logoUrl": "string (logo image URL if found)",
  "bannerImageUrl": "string (hero/banner image URL)",
  "fontFamily": "string (primary font used)",

  // AGENT TAB - Voice & Personality
  "voicePersonality": "string (professional, friendly, authoritative, etc)",
  "communicationStyle": "string (formal, conversational, empathetic)",
  "targetClientType": "string (individuals, businesses, specific demographics)",

  // DEDUCED INFORMATION
  "firmSize": "string (solo, small, medium, large firm)",
  "yearsInPractice": "number (if mentioned)",
  "specializations": ["array of specific specialties within practice areas"],
  "clientFocus": "string (who they primarily serve)",
  "marketingTone": "string (how they present themselves)",
  "competitiveAdvantages": ["array of unique selling points"],
  "serviceOfferings": ["array of specific services"],

  // TECHNICAL DEDUCTIONS
  "suggestedSubdomain": "string (clean subdomain suggestion)",
  "seoKeywords": ["array of relevant keywords found"],
  "socialMediaPresence": {
    "linkedin": "string (URL if found)",
    "facebook": "string (URL if found)",
    "twitter": "string (URL if found)",
    "instagram": "string (URL if found)"
  },

  // CUSTOM FIELDS SUGGESTIONS (based on practice area)
  "customFields": [
    {
      "name": "string (field name)",
      "type": "string (text, select, date, etc)",
      "options": ["array if select type"],
      "required": "boolean"
    }
  ],

  // METADATA
  "websiteUrl": "string (original URL)",
  "extractionConfidence": "number (0-1, how confident in extraction)",
  "missingInformation": ["array of fields that couldn't be determined"]
}

EXTRACTION GUIDELINES:
1. Extract ACTUAL data from content - don't make up information
2. For colors: analyze any color mentions, brand guidelines, or suggest professional legal colors
3. For voice/personality: analyze the tone and language used on the website
4. For custom fields: suggest relevant intake fields based on practice areas
5. For system prompt: create detailed instructions specific to their practice and client needs
6. Be thorough but accurate - mark confidence level and missing info`
          },
          {
            role: 'user',
            content: content
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      })
    });

    if (!openaiResponse.ok) {
      throw new Error(`OpenAI API error: ${openaiResponse.status}`);
    }

    const openaiResult = await openaiResponse.json();
    const extractedData = JSON.parse(openaiResult.choices[0].message.content);

    // Step 3: Enhance with defaults and validation
    const profileData = {
      // PROFILE TAB - Basic Information
      firmName: extractedData.firmName || 'Law Firm',
      attorneyName: extractedData.attorneyName || '',
      barId: extractedData.barId || '',
      state: extractedData.state || '',
      phone: extractedData.phone || '',
      email: extractedData.email || '',
      officeAddress: extractedData.officeAddress || '',
      schedulingLink: extractedData.schedulingLink || '',

      // PROFILE TAB - Practice Information
      practiceAreas: extractedData.practiceAreas || [],
      practiceDescription: extractedData.practiceDescription || '',

      // AGENT TAB - Instructions & Context
      vapiInstructions: extractedData.systemPrompt || generateDefaultSystemPrompt(extractedData),
      vapiContext: extractedData.knowledgeBase || '',
      practiceAreaTemplate: extractedData.practiceAreaTemplate || 'general',

      // AGENT TAB - Messages & Text
      titleText: extractedData.titleText || extractedData.firmName || '',
      welcomeMessage: extractedData.welcomeMessage || generateWelcomeMessage(extractedData),
      informationGatheringPrompt: extractedData.informationGatheringPrompt || generateInfoPrompt(extractedData),
      buttonText: extractedData.buttonText || generateButtonText(extractedData),
      disclaimerText: extractedData.disclaimerText || '',

      // AGENT TAB - Appearance & Branding
      primaryColor: extractedData.colors?.primary || '#1e40af',
      secondaryColor: extractedData.colors?.secondary || '#3b82f6',
      accentColor: extractedData.colors?.accent || '#60a5fa',
      backgroundColor: extractedData.colors?.background || '#ffffff',
      textColor: extractedData.colors?.text || '#1f2937',
      logoUrl: extractedData.logoUrl || '',
      bannerImageUrl: extractedData.bannerImageUrl || '',
      fontFamily: extractedData.fontFamily || '',

      // AGENT TAB - Voice & Personality
      voicePersonality: extractedData.voicePersonality || 'professional',
      communicationStyle: extractedData.communicationStyle || 'professional',
      targetClientType: extractedData.targetClientType || '',

      // DEDUCED INFORMATION
      firmSize: extractedData.firmSize || '',
      yearsInPractice: extractedData.yearsInPractice || null,
      specializations: extractedData.specializations || [],
      clientFocus: extractedData.clientFocus || '',
      marketingTone: extractedData.marketingTone || '',
      competitiveAdvantages: extractedData.competitiveAdvantages || [],
      serviceOfferings: extractedData.serviceOfferings || [],

      // TECHNICAL DEDUCTIONS
      suggestedSubdomain: extractedData.suggestedSubdomain || generateSubdomain(extractedData.firmName),
      seoKeywords: extractedData.seoKeywords || [],
      socialMediaPresence: extractedData.socialMediaPresence || {},

      // CUSTOM FIELDS SUGGESTIONS
      customFields: extractedData.customFields || generateCustomFields(extractedData.practiceAreas),

      // METADATA
      websiteUrl: url,
      extractionConfidence: extractedData.extractionConfidence || 0.8,
      missingInformation: extractedData.missingInformation || [],
      importedFrom: url,
      importedAt: new Date().toISOString()
    };

    console.log('Successfully extracted profile data:', profileData);

    return res.status(200).json({
      success: true,
      data: profileData,
      message: 'Profile data extracted successfully'
    });

  } catch (error) {
    console.error('Website import error:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to import website data'
    });
  }
}

/**
 * Generate a clean subdomain from firm name
 */
function generateSubdomain(firmName) {
  if (!firmName) return '';

  return firmName
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '')
    .replace(/law(firm|office|group)?|llp|llc|pc|pa|associates|legal/g, '')
    .substring(0, 20);
}

/**
 * Generate default system prompt based on extracted data
 */
function generateDefaultSystemPrompt(data) {
  const firmName = data.firmName || 'this law firm';
  const practiceAreas = data.practiceAreas || [];

  let prompt = `You are an AI assistant for ${firmName}. `;

  if (practiceAreas.length > 0) {
    prompt += `You specialize in ${practiceAreas.join(', ')}. `;
  }

  prompt += `Your role is to:
1. Gather information from potential clients about their legal needs
2. Provide helpful information about the firm's services
3. Determine if their case is a good fit for the firm
4. Collect contact information for follow-up
5. Be professional, empathetic, and helpful at all times

Always ask relevant questions based on their legal issue and practice area.`;

  return prompt;
}

/**
 * Generate welcome message based on extracted data
 */
function generateWelcomeMessage(data) {
  const firmName = data.firmName || 'our law firm';
  const practiceAreas = data.practiceAreas || [];

  let message = `Welcome to ${firmName}! `;

  if (practiceAreas.length > 0) {
    if (practiceAreas.length === 1) {
      message += `We specialize in ${practiceAreas[0]}. `;
    } else {
      message += `We specialize in ${practiceAreas.slice(0, -1).join(', ')} and ${practiceAreas[practiceAreas.length - 1]}. `;
    }
  }

  message += 'How can we help you today?';
  return message;
}

/**
 * Generate information gathering prompt based on practice areas
 */
function generateInfoPrompt(data) {
  const practiceAreas = data.practiceAreas || [];

  let prompt = 'To better assist you, I need to gather some information about your situation. ';

  if (practiceAreas.some(area => /personal injury|accident|injury/i.test(area))) {
    prompt += 'Could you tell me about the incident, when it occurred, and what injuries you sustained?';
  } else if (practiceAreas.some(area => /family|divorce/i.test(area))) {
    prompt += 'Could you tell me about your family law matter and what outcome you\'re hoping to achieve?';
  } else if (practiceAreas.some(area => /criminal|defense/i.test(area))) {
    prompt += 'Could you tell me about the charges you\'re facing and when your court date is?';
  } else if (practiceAreas.some(area => /estate|will|trust/i.test(area))) {
    prompt += 'Could you tell me about your estate planning needs and family situation?';
  } else if (practiceAreas.some(area => /business|corporate/i.test(area))) {
    prompt += 'Could you tell me about your business and what legal assistance you need?';
  } else {
    prompt += 'Could you tell me about your legal situation and how we might be able to help?';
  }

  return prompt;
}

/**
 * Generate button text based on practice areas
 */
function generateButtonText(data) {
  const practiceAreas = data.practiceAreas || [];

  if (practiceAreas.some(area => /personal injury|accident/i.test(area))) {
    return 'Discuss Your Case';
  } else if (practiceAreas.some(area => /family|divorce/i.test(area))) {
    return 'Get Family Law Help';
  } else if (practiceAreas.some(area => /criminal|defense/i.test(area))) {
    return 'Get Legal Defense';
  } else if (practiceAreas.some(area => /estate|will|trust/i.test(area))) {
    return 'Plan Your Estate';
  } else if (practiceAreas.some(area => /business|corporate/i.test(area))) {
    return 'Business Consultation';
  }

  return 'Start Consultation';
}

/**
 * Generate custom fields based on practice areas
 */
function generateCustomFields(practiceAreas = []) {
  const fields = [];

  // Common fields for all practice areas
  fields.push(
    { name: 'Full Name', type: 'text', required: true },
    { name: 'Phone Number', type: 'tel', required: true },
    { name: 'Email Address', type: 'email', required: true },
    { name: 'Preferred Contact Method', type: 'select', options: ['Phone', 'Email', 'Text'], required: false }
  );

  // Practice area specific fields
  if (practiceAreas.some(area => /personal injury|accident/i.test(area))) {
    fields.push(
      { name: 'Date of Incident', type: 'date', required: true },
      { name: 'Type of Accident', type: 'select', options: ['Car Accident', 'Slip and Fall', 'Medical Malpractice', 'Work Injury', 'Other'], required: true },
      { name: 'Injuries Sustained', type: 'textarea', required: true },
      { name: 'Insurance Company', type: 'text', required: false },
      { name: 'Medical Treatment Received', type: 'textarea', required: false }
    );
  }

  if (practiceAreas.some(area => /family|divorce/i.test(area))) {
    fields.push(
      { name: 'Type of Family Matter', type: 'select', options: ['Divorce', 'Child Custody', 'Child Support', 'Adoption', 'Prenuptial Agreement', 'Other'], required: true },
      { name: 'Marriage Date', type: 'date', required: false },
      { name: 'Children Involved', type: 'select', options: ['None', '1', '2', '3', '4+'], required: false },
      { name: 'Current Living Situation', type: 'textarea', required: false }
    );
  }

  if (practiceAreas.some(area => /criminal|defense/i.test(area))) {
    fields.push(
      { name: 'Type of Charges', type: 'textarea', required: true },
      { name: 'Court Date', type: 'date', required: false },
      { name: 'Arresting Agency', type: 'text', required: false },
      { name: 'Prior Criminal History', type: 'select', options: ['None', 'Misdemeanor', 'Felony', 'Multiple'], required: false }
    );
  }

  if (practiceAreas.some(area => /estate|will|trust/i.test(area))) {
    fields.push(
      { name: 'Estate Planning Need', type: 'select', options: ['Will', 'Trust', 'Power of Attorney', 'Healthcare Directive', 'Probate', 'Other'], required: true },
      { name: 'Estimated Estate Value', type: 'select', options: ['Under $100k', '$100k-$500k', '$500k-$1M', 'Over $1M'], required: false },
      { name: 'Number of Beneficiaries', type: 'select', options: ['1', '2', '3', '4+'], required: false }
    );
  }

  if (practiceAreas.some(area => /business|corporate/i.test(area))) {
    fields.push(
      { name: 'Business Type', type: 'select', options: ['Sole Proprietorship', 'LLC', 'Corporation', 'Partnership', 'Startup', 'Other'], required: true },
      { name: 'Legal Issue Type', type: 'select', options: ['Formation', 'Contracts', 'Employment', 'Intellectual Property', 'Litigation', 'Other'], required: true },
      { name: 'Business Size', type: 'select', options: ['Just Me', '2-10 employees', '11-50 employees', '50+ employees'], required: false }
    );
  }

  // Always add a general description field
  fields.push(
    { name: 'Brief Description of Your Situation', type: 'textarea', required: true }
  );

  return fields;
}
