<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset LegalScout</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 100px auto;
            padding: 20px;
            text-align: center;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .reset-btn {
            background-color: #dc3545;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
        }
        .reset-btn:hover {
            background-color: #c82333;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Reset LegalScout</h1>
        <p>This will clear all subdomain settings and return you to the normal home page.</p>
        
        <div id="status"></div>
        
        <button class="reset-btn" onclick="resetApp()">
            🏠 Reset to Home Page
        </button>
        
        <p><small>This will clear localStorage and reload the app.</small></p>
    </div>

    <script>
        function updateStatus(message, type = 'loading') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function resetApp() {
            updateStatus('🔄 Resetting application...', 'loading');
            
            // Clear ALL localStorage keys that might affect subdomain detection
            const keysToRemove = [
                'legalscout_test_subdomain',
                'testSubdomain', 
                'attorney_id',
                'currentAttorneyId',
                'attorney',
                'subdomain'
            ];

            let clearedKeys = [];

            // Remove specific keys
            keysToRemove.forEach(key => {
                if (localStorage.getItem(key)) {
                    localStorage.removeItem(key);
                    clearedKeys.push(key);
                }
            });

            // Also remove any keys that contain subdomain-related terms
            Object.keys(localStorage).forEach(key => {
                if (key.includes('legal') || 
                    key.includes('subdomain') || 
                    key.includes('attorney') ||
                    key.includes('test')) {
                    localStorage.removeItem(key);
                    if (!clearedKeys.includes(key)) {
                        clearedKeys.push(key);
                    }
                }
            });

            console.log(`✅ Cleared ${clearedKeys.length} localStorage keys:`, clearedKeys);
            
            updateStatus(`✅ Cleared ${clearedKeys.length} settings. Redirecting to home page...`, 'success');
            
            // Force reload to apply changes
            setTimeout(() => {
                window.location.href = 'http://localhost:5173/';
            }, 2000);
        }

        // Auto-run reset if URL contains ?auto=true
        if (window.location.search.includes('auto=true')) {
            setTimeout(resetApp, 1000);
        }
    </script>
</body>
</html>
