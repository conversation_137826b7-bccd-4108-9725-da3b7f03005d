/**
 * Deployment Safe Fix
 * 
 * This script provides targeted fixes for deployment-specific issues
 * while maintaining Vapi MCP integration.
 */

(function() {
  console.log('[DeploymentSafeFix] Initializing...');
  
  /**
   * Safely check if a value starts with a prefix
   * @param {any} value - The value to check
   * @param {string} prefix - The prefix to check for
   * @returns {boolean} Whether the value starts with the prefix
   */
  function safeStartsWith(value, prefix) {
    return typeof value === 'string' && 
           typeof value.startsWith === 'function' && 
           value.startsWith(prefix);
  }
  
  /**
   * Patch the AttorneyProfileManager._initialize method to handle undefined values
   */
  function patchAttorneyProfileManagerInitialize() {
    // Wait for the AttorneyProfileManager to be defined
    const checkInterval = setInterval(() => {
      try {
        // Find the AttorneyProfileManager class
        const AttorneyProfileManager = window.attorneyProfileManager && 
                                      window.attorneyProfileManager.constructor;
        
        if (AttorneyProfileManager && AttorneyProfileManager.prototype) {
          clearInterval(checkInterval);
          
          console.log('[DeploymentSafeFix] Found AttorneyProfileManager, patching _initialize method');
          
          // Save the original _initialize method
          const originalInitialize = AttorneyProfileManager.prototype._initialize;
          
          // Replace with our safer version
          AttorneyProfileManager.prototype._initialize = async function(userId, email = null) {
            try {
              console.log('[DeploymentSafeFix] Safe _initialize called with userId:', userId, 'email:', email);
              
              if (this.isInitialized && this.currentAttorney) {
                console.log('[DeploymentSafeFix] Already initialized with attorney:', this.currentAttorney.id);
                return this.currentAttorney;
              }
              
              // Original implementation with added safety checks
              try {
                // Try multiple methods to find the attorney profile
                let attorney = null;
                
                // Method 1: Try to load by user ID
                if (userId) {
                  try {
                    attorney = await this.loadAttorneyByUserId(userId);
                    if (attorney) {
                      console.log('[DeploymentSafeFix] Found attorney by userId:', attorney.id);
                    }
                  } catch (error) {
                    console.warn('[DeploymentSafeFix] Error loading by userId:', error);
                  }
                }
                
                // Method 2: Try to load by email if available
                if (!attorney && email) {
                  try {
                    attorney = await this.loadAttorneyByEmail(email);
                    if (attorney) {
                      console.log('[DeploymentSafeFix] Found attorney by email:', attorney.id);
                      
                      // If found by email but not linked to user ID, update the link
                      if (userId && (!attorney.user_id || attorney.user_id !== userId)) {
                        console.log('[DeploymentSafeFix] Linking attorney to userId:', userId);
                        attorney = await this.updateAttorneyInSupabase({
                          id: attorney.id,
                          user_id: userId
                        });
                      }
                    }
                  } catch (error) {
                    console.warn('[DeploymentSafeFix] Error loading by email:', error);
                  }
                }
                
                // Method 3: Try to load from localStorage
                if (!attorney) {
                  try {
                    attorney = this.loadFromLocalStorage();
                    if (attorney) {
                      console.log('[DeploymentSafeFix] Found attorney in localStorage:', attorney.id);
                    }
                  } catch (error) {
                    console.warn('[DeploymentSafeFix] Error loading from localStorage:', error);
                  }
                }
                
                // Method 4: Create a new attorney if none found
                if (!attorney) {
                  // In development mode, create a development attorney
                  const isDevelopment = typeof window !== 'undefined' && 
                                       (window.location.hostname === 'localhost' || 
                                        window.location.hostname === '127.0.0.1');
                  
                  if (isDevelopment) {
                    attorney = this.createDefaultAttorney({
                      id: 'dev-attorney-' + Date.now(),
                      name: 'Development Attorney',
                      email: email || '<EMAIL>',
                      user_id: userId
                    });
                    
                    console.log('[DeploymentSafeFix] Created development attorney:', attorney.id);
                  } else {
                    // In production, create a regular attorney
                    attorney = this.createDefaultAttorney({
                      user_id: userId,
                      email: email
                    });
                    
                    // Try to save to Supabase
                    try {
                      attorney = await this.createAttorneyInSupabase(attorney);
                      console.log('[DeploymentSafeFix] Created attorney in Supabase:', attorney.id);
                    } catch (error) {
                      console.warn('[DeploymentSafeFix] Error creating attorney in Supabase:', error);
                    }
                  }
                }
                
                if (attorney) {
                  // Store the attorney data
                  this.currentAttorney = attorney;
                  
                  // Save to localStorage for offline access
                  this.saveToLocalStorage(attorney);
                  
                  // Set up Supabase Realtime subscription for non-development attorneys
                  // Use our safe startsWith check
                  if (!safeStartsWith(attorney.id, 'dev-')) {
                    this.setupRealtimeSubscription(attorney.id);
                  } else {
                    console.log('[DeploymentSafeFix] Development attorney, skipping Realtime subscription');
                  }
                  
                  // Check Vapi synchronization - with error handling
                  try {
                    await this.checkVapiSynchronization(attorney);
                  } catch (error) {
                    console.warn('[DeploymentSafeFix] Error checking Vapi synchronization:', error);
                    
                    // Set a default sync status
                    this.syncStatus = {
                      consistent: true,
                      message: 'Vapi service not available, using local data',
                      lastChecked: new Date(),
                      warning: error.message
                    };
                    
                    this.lastSyncTime = new Date();
                  }
                  
                  this.isInitialized = true;
                  this.notifyListeners();
                  
                  return attorney;
                }
                
                throw new Error('Failed to initialize attorney profile');
              } catch (innerError) {
                console.error('[DeploymentSafeFix] Inner initialization error:', innerError);
                throw innerError;
              }
            } catch (error) {
              console.error('[DeploymentSafeFix] Outer initialization error:', error);
              
              // Create a fallback attorney
              const fallbackAttorney = this.createDefaultAttorney({
                id: 'fallback-' + Date.now(),
                name: 'Attorney',
                email: email || '',
                user_id: userId
              });
              
              // Store the fallback attorney
              this.currentAttorney = fallbackAttorney;
              this.saveToLocalStorage(fallbackAttorney);
              this.isInitialized = true;
              this.notifyListeners();
              
              return fallbackAttorney;
            }
          };
          
          console.log('[DeploymentSafeFix] Successfully patched AttorneyProfileManager._initialize');
        }
      } catch (error) {
        console.error('[DeploymentSafeFix] Error patching AttorneyProfileManager:', error);
      }
    }, 500);
    
    // Clear interval after 30 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      console.warn('[DeploymentSafeFix] Timed out waiting for AttorneyProfileManager');
    }, 30000);
  }
  
  /**
   * Add fallback methods to VapiMcpService to handle connection errors gracefully
   */
  function enhanceVapiMcpService() {
    // Wait for the VapiMcpService to be defined
    const checkInterval = setInterval(() => {
      try {
        // Find the VapiMcpService instance
        const vapiMcpService = window.vapiMcpService || 
                              (window.vapiServiceManager && window.vapiServiceManager.getMcpService());
        
        if (vapiMcpService) {
          clearInterval(checkInterval);
          
          console.log('[DeploymentSafeFix] Found VapiMcpService, enhancing methods');
          
          // Add a fallback method for creating assistants
          if (!vapiMcpService.createAssistantFallback) {
            vapiMcpService.createAssistantFallback = function(assistantConfig) {
              console.log('[DeploymentSafeFix] Using fallback to create assistant:', assistantConfig.name);
              
              return {
                id: 'fallback-assistant-' + Date.now(),
                name: assistantConfig.name,
                instructions: assistantConfig.instructions,
                firstMessage: assistantConfig.firstMessage,
                voice: assistantConfig.voice,
                fallback: true
              };
            };
          }
          
          // Add a fallback method for updating assistants
          if (!vapiMcpService.updateAssistantFallback) {
            vapiMcpService.updateAssistantFallback = function(assistantId, assistantConfig) {
              console.log('[DeploymentSafeFix] Using fallback to update assistant:', assistantId);
              
              return {
                id: assistantId,
                name: assistantConfig.name,
                instructions: assistantConfig.instructions,
                firstMessage: assistantConfig.firstMessage,
                voice: assistantConfig.voice,
                fallback: true,
                updated: true
              };
            };
          }
          
          // Enhance the createAssistant method with fallback
          const originalCreateAssistant = vapiMcpService.createAssistant;
          
          if (originalCreateAssistant) {
            vapiMcpService.createAssistant = async function(assistantConfig) {
              try {
                // Try the original method
                return await originalCreateAssistant.call(this, assistantConfig);
              } catch (error) {
                console.warn('[DeploymentSafeFix] Error creating Vapi assistant, using fallback:', error);
                
                // Use the fallback method
                return this.createAssistantFallback(assistantConfig);
              }
            };
          }
          
          // Enhance the updateAssistant method with fallback
          const originalUpdateAssistant = vapiMcpService.updateAssistant;
          
          if (originalUpdateAssistant) {
            vapiMcpService.updateAssistant = async function(assistantId, assistantConfig) {
              try {
                // Try the original method
                return await originalUpdateAssistant.call(this, assistantId, assistantConfig);
              } catch (error) {
                console.warn('[DeploymentSafeFix] Error updating Vapi assistant, using fallback:', error);
                
                // Use the fallback method
                return this.updateAssistantFallback(assistantId, assistantConfig);
              }
            };
          }
          
          console.log('[DeploymentSafeFix] Successfully enhanced VapiMcpService');
        }
      } catch (error) {
        console.error('[DeploymentSafeFix] Error enhancing VapiMcpService:', error);
      }
    }, 500);
    
    // Clear interval after 30 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      console.warn('[DeploymentSafeFix] Timed out waiting for VapiMcpService');
    }, 30000);
  }
  
  /**
   * Add a sync button to manually trigger synchronization
   */
  function addSyncButton() {
    // Wait for the document to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', createSyncButton);
    } else {
      // Wait a bit to ensure other scripts have loaded
      setTimeout(createSyncButton, 1000);
    }
  }
  
  /**
   * Create the sync button
   */
  function createSyncButton() {
    // Check if we're on a dashboard page
    const isDashboard = window.location.pathname.includes('/dashboard') || 
                        window.location.pathname.includes('/profile') ||
                        document.querySelector('.dashboard-container') !== null;
    
    if (!isDashboard) {
      console.log('[DeploymentSafeFix] Not on dashboard, skipping sync button');
      return;
    }
    
    // Check if the button already exists
    if (document.getElementById('deployment-sync-button')) {
      console.log('[DeploymentSafeFix] Sync button already exists');
      return;
    }
    
    console.log('[DeploymentSafeFix] Creating sync button');
    
    // Create the button container
    const buttonContainer = document.createElement('div');
    buttonContainer.style.position = 'fixed';
    buttonContainer.style.bottom = '20px';
    buttonContainer.style.right = '20px';
    buttonContainer.style.zIndex = '9999';
    
    // Create the button
    const button = document.createElement('button');
    button.id = 'deployment-sync-button';
    button.innerHTML = '<i class="fas fa-sync"></i> Sync Profile';
    button.style.padding = '10px 15px';
    button.style.backgroundColor = '#4a90e2';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '5px';
    button.style.cursor = 'pointer';
    button.style.fontWeight = 'bold';
    button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    button.style.display = 'flex';
    button.style.alignItems = 'center';
    button.style.gap = '8px';
    
    // Add hover effect
    button.onmouseover = function() {
      this.style.backgroundColor = '#3a80d2';
    };
    button.onmouseout = function() {
      this.style.backgroundColor = '#4a90e2';
    };
    
    // Add click event
    button.onclick = async function() {
      try {
        // Disable the button and show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
        
        // Get the attorney profile manager
        const attorneyProfileManager = window.attorneyProfileManager;
        if (!attorneyProfileManager) {
          throw new Error('Attorney profile manager not available');
        }
        
        // Get the current attorney
        const attorney = attorneyProfileManager.getCurrentAttorney ? 
                        attorneyProfileManager.getCurrentAttorney() : 
                        attorneyProfileManager.getAttorney();
        
        if (!attorney) {
          throw new Error('No attorney profile loaded');
        }
        
        // Refresh from Supabase
        let refreshedAttorney = attorney;
        try {
          if (attorney.id) {
            refreshedAttorney = await attorneyProfileManager.loadAttorneyById(attorney.id);
            if (refreshedAttorney) {
              // Update local state
              attorneyProfileManager.currentAttorney = refreshedAttorney;
              attorneyProfileManager.saveToLocalStorage(refreshedAttorney);
              attorneyProfileManager.notifyListeners();
            }
          }
        } catch (refreshError) {
          console.warn('[DeploymentSafeFix] Error refreshing from Supabase:', refreshError);
        }
        
        // Try to sync with Vapi if available
        let vapiResult = { success: false, message: 'Vapi service not available' };
        try {
          if (typeof attorneyProfileManager.checkVapiSynchronization === 'function') {
            await attorneyProfileManager.checkVapiSynchronization(refreshedAttorney);
            vapiResult = { success: true, message: 'Vapi synchronization checked' };
          }
        } catch (vapiError) {
          console.warn('[DeploymentSafeFix] Error syncing with Vapi:', vapiError);
          vapiResult = { success: false, message: 'Vapi sync error: ' + vapiError.message };
        }
        
        // Show success state
        button.innerHTML = '<i class="fas fa-check"></i> Synced!';
        button.style.backgroundColor = '#4CAF50';
        
        // Show a notification
        showNotification('Profile synced with Supabase' + 
                         (vapiResult.success ? ' and Vapi' : '') + 
                         '!', true);
        
        // Reset button after 3 seconds
        setTimeout(() => {
          button.innerHTML = '<i class="fas fa-sync"></i> Sync Profile';
          button.style.backgroundColor = '#4a90e2';
          button.disabled = false;
        }, 3000);
      } catch (error) {
        console.error('[DeploymentSafeFix] Error syncing profile:', error);
        
        // Show error state
        button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
        button.style.backgroundColor = '#f44336';
        
        // Show a notification
        showNotification('Error syncing profile: ' + error.message, false);
        
        // Reset button after 3 seconds
        setTimeout(() => {
          button.innerHTML = '<i class="fas fa-sync"></i> Sync Profile';
          button.style.backgroundColor = '#4a90e2';
          button.disabled = false;
        }, 3000);
      }
    };
    
    // Add the button to the container
    buttonContainer.appendChild(button);
    
    // Add the container to the body
    document.body.appendChild(buttonContainer);
    
    console.log('[DeploymentSafeFix] Sync button created');
    
    // Also check when the URL changes
    const pushState = history.pushState;
    history.pushState = function() {
      pushState.apply(history, arguments);
      
      // Check if we're on a dashboard page
      const isDashboard = window.location.pathname.includes('/dashboard') || 
                          window.location.pathname.includes('/profile') ||
                          document.querySelector('.dashboard-container') !== null;
      
      // Show or hide the button based on the page
      if (isDashboard) {
        buttonContainer.style.display = 'block';
      } else {
        buttonContainer.style.display = 'none';
      }
    };
    
    // And check when the popstate event is fired
    window.addEventListener('popstate', function() {
      // Check if we're on a dashboard page
      const isDashboard = window.location.pathname.includes('/dashboard') || 
                          window.location.pathname.includes('/profile') ||
                          document.querySelector('.dashboard-container') !== null;
      
      // Show or hide the button based on the page
      if (isDashboard) {
        buttonContainer.style.display = 'block';
      } else {
        buttonContainer.style.display = 'none';
      }
    });
  }
  
  /**
   * Show a notification
   * @param {string} message - The message to show
   * @param {boolean} isSuccess - Whether it's a success or error notification
   */
  function showNotification(message, isSuccess) {
    // Create the notification container
    const notificationContainer = document.createElement('div');
    notificationContainer.style.position = 'fixed';
    notificationContainer.style.top = '20px';
    notificationContainer.style.right = '20px';
    notificationContainer.style.zIndex = '10000';
    notificationContainer.style.padding = '15px 20px';
    notificationContainer.style.backgroundColor = isSuccess ? '#4CAF50' : '#f44336';
    notificationContainer.style.color = 'white';
    notificationContainer.style.borderRadius = '5px';
    notificationContainer.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    notificationContainer.style.opacity = '0';
    notificationContainer.style.transition = 'opacity 0.3s ease-in-out';
    
    // Add the message
    notificationContainer.textContent = message;
    
    // Add the notification to the body
    document.body.appendChild(notificationContainer);
    
    // Show the notification
    setTimeout(() => {
      notificationContainer.style.opacity = '1';
    }, 100);
    
    // Remove the notification after 5 seconds
    setTimeout(() => {
      notificationContainer.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notificationContainer);
      }, 300);
    }, 5000);
  }
  
  // Apply the fixes
  patchAttorneyProfileManagerInitialize();
  enhanceVapiMcpService();
  addSyncButton();
  
  console.log('[DeploymentSafeFix] Initialization complete');
})();
