// Direct replacement for framer-motion's MotionConfigContext.mjs
// This version doesn't use React.createContext at all

// Create a standalone context object that mimics React.createContext
const createContext = (defaultValue) => {
  return {
    Provider: function(props) { 
      return typeof props.children !== 'undefined' ? props.children : null; 
    },
    Consumer: function(props) { 
      return props.children && typeof props.children === 'function' 
        ? props.children(defaultValue) 
        : null; 
    },
    displayName: 'MotionContext',
    _currentValue: defaultValue,
    _currentValue2: defaultValue,
    _threadCount: 0,
    _defaultValue: defaultValue
  };
};

// Create the MotionConfigContext with a default value
const MotionConfigContext = createContext({
  transformPagePoint: undefined,
  isStatic: false,
  reducedMotion: "never"
});

// Make it available globally
if (typeof window !== 'undefined') {
  window.MotionConfigContext = MotionConfigContext;
}

// Export both as named export and default
export { MotionConfigContext };
export default MotionConfigContext;
