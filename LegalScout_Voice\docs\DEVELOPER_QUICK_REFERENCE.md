# 🚀 LegalScout Developer Quick Reference

## 🏃‍♂️ Quick Start Commands

```bash
# Setup
npm install
cp .env.example .env.local
npm run dev:full

# Development
npm run dev          # Frontend only (:5174)
npm run dev:api      # API only (:3001)
npm run dev:full     # Both servers

# Testing
npm test             # All tests
npm run test:vapi-mcp # Vapi MCP connection
npm run test:vapi-key # Vapi API key

# Build & Deploy
npm run build        # Production build
npm run preview      # Preview build
npm run lint         # Code linting
```

## 🌐 Important URLs

- **Local Frontend:** http://localhost:5174
- **Local API:** http://localhost:3001
- **Dashboard:** http://localhost:5174/dashboard
- **Production:** https://legalscout.net
- **Staging:** https://staging.legalscout.net

## 🔑 Environment Variables

```env
# Required for development
VITE_VAPI_PUBLIC_KEY=310f0d43-27c2-47a5-a76d-e55171d024f7
VAPI_PRIVATE_KEY=6734febc-fc65-4669-93b0-929b31ff6564
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:3001
```

## 📁 Key File Locations

### Configuration
- `src/config/sessionTemplates.js` - Multi-agent workflow definitions
- `src/config/vapiConfig.js` - Vapi integration settings
- `src/config/env.js` - Environment configuration
- `vercel.json` - Deployment configuration

### Core Components
- `src/App.jsx` - Main application component
- `src/pages/Dashboard.jsx` - Attorney dashboard
- `src/components/dashboard/` - Dashboard components
- `src/components/call/` - Voice call components

### Services & APIs
- `api/index.js` - Main API handler
- `api/vapi-mcp-server/` - Vapi MCP integration
- `src/services/` - Business logic services
- `src/hooks/` - Custom React hooks

### Documentation
- `docs/LEGALSCOUT_MASTER_PLAN.md` - Complete vision
- `docs/IMPLEMENTATION_ROADMAP.md` - 4-week sprint plan
- `docs/VAPI_IMPLEMENTATION_GUIDELINES.md` - Vapi best practices

## 🎯 Current Sprint Tasks (Phase 1)

### Week 1: Database & UI Foundation
- [ ] Create session template database schema
- [ ] Build SessionTemplateManager UI component
- [ ] Implement session creation flow
- [ ] Add payment integration (Stripe)

### Week 2: Multi-Agent Orchestration
- [ ] Build SessionOrchestrator class
- [ ] Create AI agent framework
- [ ] Implement communication hub
- [ ] Add handoff management

### Week 3: Workflow Engine
- [ ] Complete workflow execution logic
- [ ] Add phase dependency management
- [ ] Implement deliverable tracking
- [ ] Build monitoring dashboard

### Week 4: Production Launch
- [ ] Security & compliance implementation
- [ ] Monitoring & analytics setup
- [ ] Pilot attorney onboarding (10 attorneys)
- [ ] First 50 sessions completed

## 🔧 Development Patterns

### Component Pattern
```jsx
import React from 'react';
import { useVapi } from '../hooks/useVapi';
import './ComponentName.css';

export const ComponentName = ({ prop1, prop2 }) => {
  const { vapiState, vapiActions } = useVapi();
  
  return (
    <div className="component-name">
      {/* Component content */}
    </div>
  );
};
```

### Service Pattern
```javascript
export class ServiceName {
  constructor(dependencies) {
    this.dependencies = dependencies;
  }
  
  async performAction(params) {
    try {
      // Business logic
      return result;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }
}
```

### Hook Pattern
```javascript
export const useCustomHook = (params) => {
  const [state, setState] = useState(initialState);
  
  const actions = useMemo(() => ({
    action1: () => { /* implementation */ },
    action2: () => { /* implementation */ }
  }), [dependencies]);
  
  return { state, actions };
};
```

## 🚨 Common Issues & Quick Fixes

### Vapi Connection Issues
```bash
# Check MCP connection
npm run test:vapi-mcp

# Verify API key
npm run test:vapi-key

# List all assistants
node scripts/get-all-assistants.js
```

### Build Issues
```bash
# Clear cache
npm run clean && npm install

# Check for conflicts
npm ls --depth=0
```

### Authentication Issues
```bash
# Test Supabase connection
node test-supabase-connection.js

# Verify environment
node scripts/verify-vapi-setup.js
```

## 🎨 UI/UX Guidelines

### Design System
- **Colors:** Light blue accents, beige-blue gradients
- **Typography:** Clean, professional fonts
- **Spacing:** Consistent padding and margins
- **Components:** Reusable, composable components

### Responsive Design
- **Mobile First:** Design for mobile, enhance for desktop
- **Breakpoints:** sm (640px), md (768px), lg (1024px), xl (1280px)
- **Touch Targets:** Minimum 44px for interactive elements

### Voice UI Principles
- **Clear Feedback:** Visual indicators for voice states
- **Error Handling:** Graceful degradation for voice failures
- **Accessibility:** Screen reader compatible

## 🧪 Testing Guidelines

### Test Structure
```javascript
// Component test
import { render, screen } from '@testing-library/react';
import { ComponentName } from './ComponentName';

describe('ComponentName', () => {
  it('renders correctly', () => {
    render(<ComponentName prop1="value" />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Service Test
```javascript
// Service test
import { ServiceName } from './ServiceName';

describe('ServiceName', () => {
  it('performs action correctly', async () => {
    const service = new ServiceName(mockDependencies);
    const result = await service.performAction(testParams);
    expect(result).toEqual(expectedResult);
  });
});
```

## 📊 Key Metrics to Track

### Development Metrics
- **Build Time:** <2 minutes
- **Test Coverage:** >80%
- **Bundle Size:** <500KB gzipped
- **Lighthouse Score:** >90

### Business Metrics
- **Session Completion Rate:** >95%
- **Attorney Satisfaction:** >4.5/5
- **Client Satisfaction:** >4.5/5
- **Revenue per Session:** $199-$2,999

## 🔗 Useful Links

### Documentation
- [Vapi Docs](https://docs.vapi.ai/)
- [Supabase Docs](https://supabase.com/docs)
- [React Docs](https://react.dev/)
- [Tailwind CSS](https://tailwindcss.com/)

### Tools
- [Vercel Dashboard](https://vercel.com/dashboard)
- [Supabase Dashboard](https://supabase.com/dashboard)
- [GitHub Repository](https://github.com/damonkost/LegalScout_Voice)

### AI Resources
- [MCP Protocol](https://modelcontextprotocol.io/)
- [OpenAI API](https://platform.openai.com/)
- [Anthropic Claude](https://console.anthropic.com/)

## 🆘 Getting Help

### Immediate Help
1. **Check this quick reference**
2. **Search existing GitHub issues**
3. **Review relevant documentation**
4. **Ask in team chat**

### Escalation Path
1. **Technical Issues:** GitHub issues
2. **Architecture Questions:** Team meetings
3. **Urgent Bugs:** Bug reporter in app
4. **General Questions:** Team chat

---

**Keep this reference handy!** Bookmark this page for quick access to essential information while developing LegalScout. 🔖
