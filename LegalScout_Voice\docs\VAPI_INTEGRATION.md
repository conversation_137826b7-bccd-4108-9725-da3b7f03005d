# Vapi Integration Documentation

## Overview

LegalScout integrates with Vapi.ai to provide voice-guided legal consultations. This integration enables natural language conversations between users and an AI assistant, with customization options for attorneys to personalize their voice assistant experience.

## Integration Architecture

The Vapi integration consists of several components:

1. **VapiCall Component** - React component for voice interaction
2. **Vapi Service** - Service layer for Vapi API communication
3. **Vapi MCP Service** - Model Context Protocol integration for programmatic control
4. **Vapi Assistant Service** - Service for creating and managing assistants

## Key Components

### VapiCall Component

The `VapiCall` component is the main interface for voice interaction:

```jsx
<VapiCall
  onEndCall={handleEndCall}
  subdomain="attorney-subdomain"
  customInstructions={instructionsObject}
  assistantId="vapi-assistant-id"
  isDemo={false}
  attorneyData={attorneyObject}
  forceDefaultAssistant={false}
/>
```

#### Props
- `onEndCall` - Callback function when call ends
- `subdomain` - Attorney subdomain for configuration
- `customInstructions` - Custom instructions for the assistant
- `assistantId` - Vapi assistant ID
- `isDemo` - Whether this is a demo call
- `attorneyData` - Attorney profile data
- `forceDefaultAssistant` - Whether to use the default assistant

#### Implementation Details
- Located in `src/components/VapiCall.jsx`
- Uses the `useVapiCall` hook for call management
- Handles different call states (connecting, active, ended)
- Provides UI for call controls and transcription

### Vapi Service

The Vapi Service handles communication with the Vapi API:

```javascript
// Initialize Vapi
const vapiInstance = await vapiService.initialize(apiKey, subdomain);

// Start a call
await vapiService.startCall(vapiInstance, assistantId, assistantOverrides);

// Set up event listeners
vapiService.setupEventListeners(vapiInstance, callbacks);
```

#### Key Functions
- `initialize(apiKey, subdomain)` - Initialize Vapi with API key
- `createVapiInstance(apiKey, options)` - Create a Vapi instance
- `startCall(vapiInstance, assistantId, assistantOverrides)` - Start a voice call
- `setupEventListeners(vapiInstance, callbacks)` - Set up event listeners
- `makeDirectApiCall(apiKey, payload)` - Make a direct API call to Vapi

#### Implementation Details
- Located in `src/services/vapiService.jsx`
- Handles Vapi SDK initialization
- Manages call lifecycle
- Provides error handling and fallbacks

### Vapi MCP Service

The Vapi MCP Service uses the Model Context Protocol to control Vapi programmatically:

```javascript
// Connect to Vapi MCP server
await vapiMcpService.connect(apiKey);

// List assistants
const assistants = await vapiMcpService.listAssistants();

// Create a new assistant
const assistant = await vapiMcpService.createAssistant(assistantConfig);

// Create a call
const call = await vapiMcpService.createCall(assistantId, phoneNumber, options);
```

#### Key Functions
- `connect(apiKey)` - Connect to the Vapi MCP server
- `listAssistants()` - List all assistants
- `getAssistant(assistantId)` - Get a specific assistant
- `createAssistant(config)` - Create a new assistant
- `createCall(assistantId, phoneNumber, options)` - Create a new call

#### Implementation Details
- Located in `src/services/vapiMcpService.js`
- Uses MCP client for communication
- Provides error handling and reconnection logic
- Supports development mode with fallbacks

### Vapi Assistant Service

The Vapi Assistant Service manages assistant creation and configuration:

```javascript
// Create a new assistant for an attorney
const assistant = await vapiAssistantService.createAssistantForAttorney(attorneyData);

// Update an existing assistant
await vapiAssistantService.updateAssistantForAttorney(assistantId, attorneyData);

// Get an assistant by ID
const assistant = await vapiAssistantService.getAssistant(assistantId);
```

#### Key Functions
- `createAssistantForAttorney(attorneyData)` - Create a new assistant
- `updateAssistantForAttorney(assistantId, attorneyData)` - Update an assistant
- `getAssistant(assistantId)` - Get an assistant by ID
- `updateAttorneyAssistantId(attorneyId, assistantId)` - Update attorney record with assistant ID

#### Implementation Details
- Located in `src/services/vapiAssistantService.js`
- Integrates with Vapi MCP Service
- Manages assistant configuration
- Updates Supabase records

## Assistant Configuration

### Basic Configuration

```javascript
const assistantConfig = {
  name: `${attorneyData.firm_name} Assistant`,
  instructions: attorneyData.vapi_instructions || DEFAULT_INSTRUCTIONS,
  firstMessage: attorneyData.welcome_message || DEFAULT_WELCOME_MESSAGE,
  firstMessageMode: "assistant-speaks-first",
  llm: {
    provider: "openai",
    model: attorneyData.ai_model || "gpt-4o"
  },
  voice: {
    provider: attorneyData.voice_provider || "playht",
    voiceId: attorneyData.voice_id || "sarah"
  },
  transcriber: {
    provider: "deepgram",
    model: "nova-3"
  }
};
```

### Advanced Configuration

```javascript
// Add analysis configuration
assistantConfig.analysis = {
  // Summary configuration
  summary: {
    prompt: attorneyData.summary_prompt || DEFAULT_SUMMARY_PROMPT
  },
  // Structured data configuration
  structuredData: {
    prompt: attorneyData.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT,
    schema: structuredDataSchema
  }
};
```

## Voice Customization

### Available Voice Providers
- **11labs** - High-quality voices with emotion
- **PlayHT** - Voice cloning capabilities
- **OpenAI** - TTS voices from OpenAI

### Voice Selection
Attorneys can select from predefined voices or create custom voices:

```javascript
// Predefined voice
const voice = {
  provider: "11labs",
  voiceId: "sarah"
};

// Custom voice (cloned)
const voice = {
  provider: "playht",
  voiceId: "s3://voice-cloning-zero-shot/2cbffa49-5dfe-4378-a54f-b824f7bbb032/theodoresaad/manifest.json"
};
```

### Voice Recording
The dashboard provides voice recording capabilities for custom voices:

1. Attorney records a sample using the microphone
2. Recording is uploaded to Supabase storage
3. PlayHT API is used to create a voice clone
4. Voice ID is stored in the attorney record

## Call Handling

### Call Lifecycle

1. **Initialization**
   - Create Vapi instance
   - Set up event listeners
   - Configure assistant parameters

2. **Call Start**
   - Connect to Vapi
   - Start the call with assistant ID
   - Handle initial greeting

3. **During Call**
   - Process user speech
   - Handle assistant responses
   - Update UI with transcription

4. **Call End**
   - Stop the call
   - Process call summary
   - Clean up resources

### Event Handling

The Vapi integration handles various events:

```javascript
const callbacks = {
  onCallStart: (callId) => {
    console.log('Call started with ID:', callId);
    setCallId(callId);
    setStatus('active');
  },
  onCallEnd: () => {
    console.log('Call ended');
    setStatus('ended');
    onEndCall && onEndCall();
  },
  onMessage: (event) => {
    console.log('Message received:', event);
    handleMessage(event);
  },
  onError: (error) => {
    console.error('Vapi error:', error);
    setErrorMessage(error.message || 'An error occurred');
    setStatus('error');
  }
};
```

## Data Collection

### Live Dossier Tool

The Vapi assistant uses a structured format for data collection:

```json
{
  "🚨Urgency": "High/Medium/Low",
  "Case Dossier": {
    "📁 Client Background": "Client information",
    "🔄 STATUS": "Case status",
    "📍 Jurisdiction": "Location information",
    "📝 Statement of Facts": "Case facts",
    "⚖️ Legal Issues": "Legal issues",
    "🎯 Objectives": "Client goals"
  }
}
```

### Custom Fields Integration

Custom fields defined by attorneys are integrated into the assistant's data collection:

1. Attorney defines custom fields in the dashboard
2. Fields are stored in the Supabase database
3. Fields are included in the assistant configuration
4. Assistant collects data based on field definitions
5. Collected data is stored in structured format

## Webhook Integration

### Call Recording

Call recordings can be stored in Supabase:

```javascript
// Configure Vapi to store recordings in Supabase
vapiInstance.configure({
  recordingEnabled: true,
  recordingProvider: 'supabase',
  recordingMetadata: {
    attorneyId: attorneyId,
    source: 'legalscout-web'
  }
});
```

### Call Analysis

Vapi provides call analysis capabilities:

```javascript
// Configure analysis
assistantConfig.analysis = {
  summary: {
    prompt: "Summarize the key points of this legal consultation"
  },
  structuredData: {
    prompt: "Extract the following information from the conversation",
    schema: {
      type: "object",
      properties: {
        legalIssue: { type: "string" },
        urgency: { type: "string", enum: ["Low", "Medium", "High"] },
        jurisdiction: { type: "string" },
        clientName: { type: "string" }
      }
    }
  }
};
```

## Error Handling

The Vapi integration includes comprehensive error handling:

### Connection Errors
- Retry logic for connection failures
- Fallback to default assistant
- User-friendly error messages

### API Errors
- Validation of API responses
- Error classification and handling
- Logging for debugging

### Runtime Errors
- Exception handling for unexpected errors
- Graceful degradation
- Error reporting

## Development and Testing

### Local Development
- Use of environment variables for API keys
- Development mode with mock data
- Console logging for debugging

### Testing
- Test assistants for development
- Simulated calls for testing
- Error simulation

## Security Considerations

### API Key Management
- API keys stored in environment variables
- Keys never exposed to client-side code
- Secure transmission of keys

### Data Protection
- Secure storage of call recordings
- Privacy considerations for user data
- Compliance with legal requirements

## Performance Optimization

### Connection Optimization
- Efficient initialization
- Resource cleanup
- Memory management

### UI Responsiveness
- Non-blocking operations
- Loading states
- Smooth transitions

## Future Enhancements

Planned enhancements for the Vapi integration:

1. **Multi-party Calls**
   - Include attorney in calls
   - Conference call capabilities
   - Role-based permissions

2. **Enhanced Voice Customization**
   - More voice options
   - Emotion control
   - Accent customization

3. **Advanced Analysis**
   - Sentiment analysis
   - Intent recognition
   - Legal issue classification

4. **Integration Expansion**
   - Additional LLM providers
   - More voice providers
   - Enhanced transcription options
