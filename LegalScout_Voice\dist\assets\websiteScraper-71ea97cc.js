const d=async(a,t={})=>{const{useFirecrawl:r=!0,useAI:n=!0,autoConfig:s=!0}=t;try{const e=await f(a,{useFirecrawl:r,useAI:n}),i=n?await aiEnhanceData(e):e,o=await generateAttorneyConfiguration(i);return s&&(o.vapiAssistant=await autoConfigureVapiAssistant(o)),o}catch(e){throw console.error("1-click attorney configuration failed:",e),e}},l=async a=>{try{const t=m(a),n=await fetch("/api/ai-meta-mcp",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:"web_evaluate",arguments:{url:t,evaluationScript:`
      // Extract attorney profile information
      function extractAttorneyProfile() {
        const doc = document;
        const result = {
          firmName: '',
          attorneyName: '',
          practiceAreas: [],
          description: '',
          contactInfo: {
            phone: '',
            email: '',
            address: ''
          },
          socialMedia: {},
          visualElements: {
            logo: '',
            colors: {
              primary: '',
              secondary: '',
              accent: '',
              background: '',
              text: ''
            },
            fonts: {
              heading: '',
              body: ''
            },
            images: []
          },
          welcomeMessage: '',
          informationGathering: '',
          buttonText: '',
          state: '',
          subdomain: ''
        };

        try {
          // Extract firm name
          const possibleFirmNameElements = [
            doc.querySelector('header h1, header .logo, #header h1, #header .logo'),
            doc.querySelector('.firm-name, .logo-text, .site-title, .brand'),
            doc.querySelector('h1'),
            doc.querySelector('title')
          ].filter(Boolean);

          if (possibleFirmNameElements.length > 0) {
            result.firmName = possibleFirmNameElements[0].textContent.trim();
          }

          // Extract attorney name
          const possibleAttorneyElements = [
            doc.querySelector('.attorney-name, .lawyer-name, .profile-name'),
            doc.querySelector('h1:not(.firm-name), h2:not(.firm-name)'),
            doc.querySelector('header h2, #header h2')
          ].filter(Boolean);

          if (possibleAttorneyElements.length > 0) {
            result.attorneyName = possibleAttorneyElements[0].textContent.trim();
          }

          // Extract practice areas
          const practiceAreaElements = Array.from(
            doc.querySelectorAll('.practice-areas li, .areas-of-practice li, .services li, .practice-areas a, .areas-of-practice a')
          );

          if (practiceAreaElements.length > 0) {
            result.practiceAreas = practiceAreaElements.map(el => el.textContent.trim());
          } else {
            // Try to find practice areas in headings
            const headings = Array.from(doc.querySelectorAll('h2, h3'));
            const practiceHeadings = headings.filter(h =>
              /practice|areas|services/i.test(h.textContent)
            );

            if (practiceHeadings.length > 0) {
              // Get the next element after the heading
              const practiceList = practiceHeadings[0].nextElementSibling;
              if (practiceList && practiceList.tagName === 'UL') {
                result.practiceAreas = Array.from(practiceList.querySelectorAll('li'))
                  .map(li => li.textContent.trim());
              }
            }
          }

          // Extract description
          const possibleDescriptionElements = [
            doc.querySelector('.about-us p, .about p, .bio p, .profile p, .description p'),
            doc.querySelector('main p, #main p, .main p'),
            doc.querySelector('p')
          ].filter(Boolean);

          if (possibleDescriptionElements.length > 0) {
            result.description = possibleDescriptionElements[0].textContent.trim();
          }

          // Extract contact information
          // Phone
          const phoneRegex = /\\(\\d{3}\\)\\s?\\d{3}-\\d{4}|\\d{3}-\\d{3}-\\d{4}/;
          const phoneElements = Array.from(doc.querySelectorAll('a[href^="tel:"], .phone, .contact .phone, footer .phone'));

          if (phoneElements.length > 0) {
            result.contactInfo.phone = phoneElements[0].textContent.trim();
          } else {
            // Try to find phone in text
            const allText = doc.body.textContent;
            const phoneMatch = allText.match(phoneRegex);
            if (phoneMatch) {
              result.contactInfo.phone = phoneMatch[0];
            }
          }

          // Email
          const emailElements = Array.from(doc.querySelectorAll('a[href^="mailto:"], .email, .contact .email, footer .email'));

          if (emailElements.length > 0) {
            result.contactInfo.email = emailElements[0].textContent.trim();
          }

          // Address
          const addressElements = Array.from(doc.querySelectorAll('.address, .contact .address, footer .address, address'));

          if (addressElements.length > 0) {
            result.contactInfo.address = addressElements[0].textContent.trim();
          }

          // Social media
          const socialLinks = Array.from(doc.querySelectorAll('a[href*="facebook.com"], a[href*="twitter.com"], a[href*="linkedin.com"], a[href*="instagram.com"]'));

          socialLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href.includes('facebook.com')) {
              result.socialMedia.facebook = href;
            } else if (href.includes('twitter.com')) {
              result.socialMedia.twitter = href;
            } else if (href.includes('linkedin.com')) {
              result.socialMedia.linkedin = href;
            } else if (href.includes('instagram.com')) {
              result.socialMedia.instagram = href;
            }
          });

          // Extract logo URL
          const logoElement = doc.querySelector('header img, #header img, .logo img, .site-logo img');
          if (logoElement) {
            const logoSrc = logoElement.getAttribute('src');
            if (logoSrc) {
              // Convert relative URL to absolute
              const logoUrl = new URL(logoSrc, window.location.origin).href;
              result.visualElements.logo = logoUrl;
            }
          }

          // Extract colors
          const computedStyles = {
            headerBg: window.getComputedStyle(doc.querySelector('header, #header') || doc.body).backgroundColor,
            headerColor: window.getComputedStyle(doc.querySelector('header, #header') || doc.body).color,
            buttonBg: window.getComputedStyle(doc.querySelector('button, .button, a.btn, .cta, .btn-primary') || doc.body).backgroundColor,
            buttonColor: window.getComputedStyle(doc.querySelector('button, .button, a.btn, .cta, .btn-primary') || doc.body).color,
            linkColor: window.getComputedStyle(doc.querySelector('a') || doc.body).color,
            accentColor: window.getComputedStyle(doc.querySelector('.accent, .highlight, .featured, .cta, .btn-secondary') || doc.body).backgroundColor,
            bodyBg: window.getComputedStyle(doc.body).backgroundColor,
            bodyColor: window.getComputedStyle(doc.body).color,
            navBg: window.getComputedStyle(doc.querySelector('nav, .nav, .navbar, .navigation') || doc.body).backgroundColor,
            footerBg: window.getComputedStyle(doc.querySelector('footer, .footer') || doc.body).backgroundColor
          };

          // Use the most prominent colors
          const colors = Object.entries(computedStyles)
            .filter(([_, value]) => value !== 'transparent' && value !== 'rgba(0, 0, 0, 0)')
            .map(([key, value]) => ({ key, value }));

          if (colors.length > 0) {
            // Primary color (from header or buttons)
            const primaryColorCandidates = colors.filter(c =>
              c.key === 'headerBg' || c.key === 'buttonBg' || c.key === 'navBg'
            );
            if (primaryColorCandidates.length > 0) {
              result.visualElements.colors.primary = primaryColorCandidates[0].value;
            }

            // Secondary color (from links)
            const secondaryColorCandidates = colors.filter(c =>
              c.key === 'linkColor' || c.key === 'buttonColor'
            );
            if (secondaryColorCandidates.length > 0) {
              result.visualElements.colors.secondary = secondaryColorCandidates[0].value;
            }

            // Accent color
            const accentColorCandidates = colors.filter(c =>
              c.key === 'accentColor' || c.key === 'footerBg'
            );
            if (accentColorCandidates.length > 0) {
              result.visualElements.colors.accent = accentColorCandidates[0].value;
            } else if (secondaryColorCandidates.length > 0) {
              // Use secondary as accent if no accent color found
              result.visualElements.colors.accent = secondaryColorCandidates[0].value;
            }

            // Background color
            const bgColorCandidates = colors.filter(c =>
              c.key === 'bodyBg'
            );
            if (bgColorCandidates.length > 0) {
              result.visualElements.colors.background = bgColorCandidates[0].value;
            }

            // Text color
            const textColorCandidates = colors.filter(c =>
              c.key === 'bodyColor' || c.key === 'headerColor'
            );
            if (textColorCandidates.length > 0) {
              result.visualElements.colors.text = textColorCandidates[0].value;
            }
          }

          // Extract fonts
          const fontElements = {
            heading: doc.querySelector('h1, h2, .heading, .title'),
            body: doc.querySelector('p, .body, article, main')
          };

          if (fontElements.heading) {
            const headingStyle = window.getComputedStyle(fontElements.heading);
            result.visualElements.fonts.heading = headingStyle.fontFamily;
          }

          if (fontElements.body) {
            const bodyStyle = window.getComputedStyle(fontElements.body);
            result.visualElements.fonts.body = bodyStyle.fontFamily;
          }

          // Extract background images
          const backgroundElements = [
            doc.querySelector('header, #header, .hero, .banner, .jumbotron'),
            doc.querySelector('body'),
            doc.querySelector('main, .main-content')
          ].filter(Boolean);

          backgroundElements.forEach(element => {
            const style = window.getComputedStyle(element);
            const backgroundImage = style.backgroundImage;

            if (backgroundImage && backgroundImage !== 'none') {
              // Extract URL from the background-image property
              const urlMatch = backgroundImage.match(/url\\(['"]?([^'"\\)]+)['"]?\\)/);
              if (urlMatch && urlMatch[1]) {
                const imageUrl = new URL(urlMatch[1], window.location.origin).href;
                result.visualElements.images.push({
                  url: imageUrl,
                  type: 'background',
                  element: element.tagName.toLowerCase()
                });
              }
            }
          });

          // Extract other images
          const contentImages = Array.from(doc.querySelectorAll('img')).filter(img => {
            const rect = img.getBoundingClientRect();
            // Only include reasonably sized images
            return rect.width > 200 && rect.height > 100;
          });

          contentImages.forEach(img => {
            const src = img.getAttribute('src');
            if (src) {
              const imageUrl = new URL(src, window.location.origin).href;
              result.visualElements.images.push({
                url: imageUrl,
                type: 'content',
                alt: img.getAttribute('alt') || ''
              });
            }
          });

          // Generate welcome message
          result.welcomeMessage = generateWelcomeMessage(result);

          // Generate information gathering prompt
          result.informationGathering = generateInformationGatheringPrompt(result);

          // Generate button text
          result.buttonText = generateButtonText(result);

          // Generate suggested subdomain
          result.subdomain = generateSubdomain(result.firmName);

          // Extract state from address if available
          if (result.contactInfo && result.contactInfo.address) {
            const stateMatch = result.contactInfo.address.match(/[A-Z]{2}/);
            if (stateMatch) {
              result.state = stateMatch[0];
            }
          }

          // Generate a suggested prompt based on the extracted data
          result.suggestedPrompt = generatePromptFromData(result);
        } catch (error) {
          console.error('Error extracting attorney profile:', error);
        }

        return result;
      }

      // Helper function to generate a welcome message
      function generateWelcomeMessage(data) {
        let message = '';

        if (data.firmName) {
          message = 'Welcome to ' + data.firmName + '! ';
        } else {
          message = 'Welcome to our legal assistant! ';
        }

        if (data.practiceAreas && data.practiceAreas.length > 0) {
          if (data.practiceAreas.length === 1) {
            message += 'We specialize in ' + data.practiceAreas[0] + '. ';
          } else if (data.practiceAreas.length === 2) {
            message += 'We specialize in ' + data.practiceAreas[0] + ' and ' + data.practiceAreas[1] + '. ';
          } else {
            const lastArea = data.practiceAreas[data.practiceAreas.length - 1];
            const otherAreas = data.practiceAreas.slice(0, -1).join(', ');
            message += 'We specialize in ' + otherAreas + ', and ' + lastArea + '. ';
          }
        }

        message += 'How can we assist you today?';

        return message;
      }

      // Helper function to generate an information gathering prompt
      function generateInformationGatheringPrompt(data) {
        let prompt = 'To better assist you, I need to gather some information. ';

        if (data.practiceAreas && data.practiceAreas.length > 0) {
          prompt += 'Could you please tell me about your situation';

          // Add specific questions based on practice areas
          if (data.practiceAreas.some(area => /personal injury|accident|injury/i.test(area))) {
            prompt += ', including when and how the injury occurred';
          } else if (data.practiceAreas.some(area => /family|divorce/i.test(area))) {
            prompt += ' and what family law matter you need assistance with';
          } else if (data.practiceAreas.some(area => /criminal|defense/i.test(area))) {
            prompt += ' and what legal charges or issues you're facing';
          } else if (data.practiceAreas.some(area => /estate|will|trust/i.test(area))) {
            prompt += ' and what estate planning needs you have';
          } else if (data.practiceAreas.some(area => /business|corporate/i.test(area))) {
            prompt += ' and what business legal matters you need help with';
          } else if (data.practiceAreas.some(area => /immigration/i.test(area))) {
            prompt += ' and what immigration matter you need assistance with';
          }

          prompt += '?';
        } else {
          prompt += 'Could you please tell me about your legal situation and how we might be able to help you?';
        }

        return prompt;
      }

      // Helper function to generate button text
      function generateButtonText(data) {
        // Default button text
        let buttonText = 'Start Consultation';

        // Customize based on practice areas if available
        if (data.practiceAreas && data.practiceAreas.length > 0) {
          if (data.practiceAreas.some(area => /personal injury|accident|injury/i.test(area))) {
            buttonText = 'Discuss Your Case';
          } else if (data.practiceAreas.some(area => /family|divorce/i.test(area))) {
            buttonText = 'Get Family Law Help';
          } else if (data.practiceAreas.some(area => /criminal|defense/i.test(area))) {
            buttonText = 'Get Legal Defense';
          } else if (data.practiceAreas.some(area => /estate|will|trust/i.test(area))) {
            buttonText = 'Plan Your Estate';
          } else if (data.practiceAreas.some(area => /business|corporate/i.test(area))) {
            buttonText = 'Business Consultation';
          }
        }

        return buttonText;
      }

      // Helper function to generate a subdomain from firm name
      function generateSubdomain(firmName) {
        if (!firmName) return '';

        // Remove special characters, spaces, and common legal terms
        return firmName
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '')
          .replace(/law(firm|office|group)?|llp|llc|pc|pa|associates|legal/g, '')
          .substring(0, 20); // Limit to 20 characters
      }

      // Helper function to generate a prompt from the extracted data
      function generatePromptFromData(data) {
        let prompt = '';

        if (data.firmName) {
          prompt += 'You are an AI assistant for ' + data.firmName + '. ';
        } else {
          prompt += 'You are an AI legal assistant. ';
        }

        if (data.practiceAreas && data.practiceAreas.length > 0) {
          prompt += 'You specialize in ' + data.practiceAreas.join(', ') + '. ';
        }

        prompt += 'Your role is to gather information from potential clients about their legal needs and provide helpful information about the firm\\'s services. ';

        if (data.description) {
          prompt += 'About the firm: ' + data.description + ' ';
        }

        if (data.contactInfo && (data.contactInfo.phone || data.contactInfo.email)) {
          prompt += 'You can offer to connect the client with an attorney';
          if (data.contactInfo.phone) {
            prompt += ' at ' + data.contactInfo.phone;
          }
          if (data.contactInfo.email) {
            prompt += ' or via email at ' + data.contactInfo.email;
          }
          prompt += '. ';
        }

        prompt += 'Be professional, courteous, and helpful at all times.';

        return prompt;
      }

      return extractAttorneyProfile();
    `,options:{timeout:15e3}}})});if(!n.ok)throw new Error("API error: "+n.status);const s=await n.json();if(s&&s.content&&s.content[0]&&s.content[0].text)try{const e=JSON.parse(s.content[0].text);if(e.success&&e.result){const i={firmName:e.result.firmName||"Unknown Firm",attorneyName:e.result.attorneyName||"",logo:{url:e.result.visualElements?.logo||"",width:200,height:80,backgroundColor:e.result.visualElements?.colors?.background||"#ffffff",textColor:e.result.visualElements?.colors?.text||"#000000"},colors:{primary:e.result.visualElements?.colors?.primary||"#123456",secondary:e.result.visualElements?.colors?.secondary||"#789abc",accent:e.result.visualElements?.colors?.accent||"#def012",background:e.result.visualElements?.colors?.background||"#ffffff",text:e.result.visualElements?.colors?.text||"#333333"},backgroundImages:e.result.visualElements?.images?.filter(o=>o.type==="background")?.map(o=>o.url)||[],practiceAreas:e.result.practiceAreas||[],attorneys:[{name:e.result.attorneyName||"Unknown Attorney",title:"Attorney",profileImage:"",specialties:e.result.practiceAreas||[],education:""}],address:{street:"",city:"",state:e.result.state||"",zip:""},contactInfo:e.result.contactInfo||{},contentAnalysis:{keyPhrases:[],services:e.result.practiceAreas||[],clientFocus:[]},welcomeMessage:e.result.welcomeMessage||"Welcome! How can I assist you today?",informationGathering:e.result.informationGathering||"Could you tell me about your legal situation?",buttonText:e.result.buttonText||"Start Consultation",subdomain:e.result.subdomain||"",fonts:{heading:e.result.visualElements?.fonts?.heading||"",body:e.result.visualElements?.fonts?.body||""},suggestedPrompt:e.result.suggestedPrompt||u(e.result)};if(e.result.contactInfo?.address){const o=e.result.contactInfo.address.split(",");if(o.length>=2&&(i.address.street=o[0].trim(),i.address.city=o[1].trim(),o.length>=3)){const c=o[2].trim().split(" ");c.length>=2&&(i.address.state=c[0].trim(),i.address.zip=c[1].trim())}}return i}else throw new Error(e.error||"Failed to extract profile information")}catch(e){throw console.error("Error parsing result:",e),new Error("Invalid response format")}else throw new Error("Invalid response format")}catch(t){throw console.error("Error scraping website:",t),new Error("Failed to scrape website: "+t.message)}},m=a=>{if(!a)throw new Error("URL is required");return!a.startsWith("http://")&&!a.startsWith("https://")?"https://"+a:a},u=async a=>"Generated prompt based on website content",p=async(a,t)=>(console.log("Demo mode: Would save data to database",{data:a,userId:t}),{success:!0,message:"Data saved (demo mode)"}),f=async(a,t={})=>{const{useFirecrawl:r=!0}=t;try{const n=await l(a);let s={};if(r)try{s=await g(a)}catch(e){console.warn("Firecrawl extraction failed, using basic data:",e.message)}return y(n,s)}catch(n){throw console.error("Enhanced web scraping failed:",n),n}},g=async a=>{try{const t=await fetch("/api/firecrawl-search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:`site:${new URL(a).hostname} attorney law firm profile`,numResults:1,format:"detailed",legalFocus:!0})});if(!t.ok)throw new Error(`Firecrawl API error: ${t.status}`);const r=await t.json();return h(r)}catch(t){throw console.error("Firecrawl scraping error:",t),t}},h=a=>{const t=a.results||[];if(t.length===0)return{};const r=t[0];return{enhancedDescription:r.summary||r.content?.substring(0,500),structuredContent:r.structured_data||{},metadata:r.metadata||{},additionalImages:r.images||[],socialProfiles:r.social_links||{}}},y=(a,t)=>({...a,description:t.enhancedDescription||a.description,metadata:t.metadata||{},socialMedia:{...a.socialMedia,...t.socialProfiles},visualElements:{...a.visualElements,images:[...a.visualElements?.images||[],...t.additionalImages||[]]},rawFirecrawlData:t}),b={scrapeWebsite:l,oneClickAttorneyConfig:d,saveExtractedData:p};export{b as default,d as oneClickAttorneyConfig,p as saveExtractedData,l as scrapeWebsite};
//# sourceMappingURL=websiteScraper-71ea97cc.js.map
