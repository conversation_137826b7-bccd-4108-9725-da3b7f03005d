# Vapi MCP Server Integration

This document provides an overview of the Vapi Model Context Protocol (MCP) Server integration in LegalScout Voice.

## What is Vapi MCP Server?

The Vapi MCP Server is an implementation of the Model Context Protocol that exposes Vapi's APIs as callable tools. This allows programmatic control of Vapi voice agents, calls, and other resources through a standardized protocol.

## Features

- **Assistant Management**: Create, retrieve, and list Vapi assistants
- **Call Management**: Create outbound calls, schedule future calls, and retrieve call details
- **Phone Number Management**: List and retrieve phone numbers
- **Tool Discovery**: Dynamically discover available tools and capabilities

## Installation

The integration requires the following packages:

```bash
npm install @modelcontextprotocol/sdk @vapi-ai/mcp-server
```

## Usage

### Basic Connection

```javascript
import { vapiMcpService } from '../services/vapiMcpService';

// Connect to the Vapi MCP Server
const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
const connected = await vapiMcpService.connect(apiKey);

if (connected) {
  console.log('Connected to Vapi MCP Server');
}
```

### Listing Assistants

```javascript
// List all assistants
const assistants = await vapiMcpService.listAssistants();
console.log('Assistants:', assistants);
```

### Creating a Call

```javascript
// Create an outbound call
const assistantId = 'your-assistant-id';
const phoneNumber = '+1234567890';
const call = await vapiMcpService.createCall(assistantId, phoneNumber);
console.log('Call created:', call);
```

### Scheduling a Future Call

```javascript
// Schedule a call for the future
const assistantId = 'your-assistant-id';
const phoneNumber = '+1234567890';
const scheduledTime = new Date();
scheduledTime.setHours(scheduledTime.getHours() + 1); // Schedule 1 hour from now

const call = await vapiMcpService.createCall(assistantId, phoneNumber, {
  scheduledAt: scheduledTime.toISOString()
});
console.log('Call scheduled:', call);
```

## Command Line Usage

The `scripts/vapi-mcp-example.js` script demonstrates how to use the Vapi MCP Server from a command line environment:

```bash
# Create a .env file with your Vapi API key
echo "VAPI_TOKEN=your_vapi_api_key" > .env

# Run the example script
node scripts/vapi-mcp-example.js
```

## Integration with Claude Desktop

You can also use the Vapi MCP Server with Claude Desktop:

1. Open Claude Desktop and press `CMD + ,` (Mac) to go to `Settings`
2. Click on the `Developer` tab
3. Click on the `Edit Config` button
4. Add the following configuration to the file:

```json
{
  "mcpServers": {
    "vapi-mcp-server": {
      "command": "npx",
      "args": [
        "-y",
        "@vapi-ai/mcp-server"
      ],
      "env": {
        "VAPI_TOKEN": "your_vapi_api_key"
      }
    }
  }
}
```

5. Replace `your_vapi_api_key` with your actual Vapi API key
6. Save the file and restart Claude Desktop

## Use Cases

- **Automated Client Intake**: Schedule follow-up calls with potential clients
- **Attorney Availability Management**: Allow attorneys to update their availability and schedule calls
- **Multi-Agent Legal Consultations**: Start with a general intake agent and transfer to specialized agents
- **Client Follow-up System**: Schedule automated check-ins with clients

## Error Handling

The `vapiMcpService` includes built-in error handling and connection management:

```javascript
try {
  const assistants = await vapiMcpService.listAssistants();
  // Process assistants
} catch (error) {
  console.error('Error listing assistants:', error);
  // Handle error
}
```

## References

- [Vapi MCP Server Documentation](https://docs.vapi.ai/sdk/mcp-server)
- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [Vapi Dashboard](https://dashboard.vapi.ai/)
