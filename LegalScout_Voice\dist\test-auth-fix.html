<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Authentication Fix Test</h1>
        <p>This page tests the definitive authentication fix for Supabase API calls.</p>

        <div class="test-section info">
            <h3>Test Status</h3>
            <div id="status">Initializing...</div>
        </div>

        <div class="test-section">
            <h3>Test Controls</h3>
            <button onclick="testSupabaseConnection()">Test Supabase Connection</button>
            <button onclick="testAuthenticatedRequest()">Test Authenticated Request</button>
            <button onclick="testUnauthenticatedRequest()">Test Unauthenticated Request</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>Test Log</h3>
            <div id="log"></div>
        </div>
    </div>

    <!-- Load Supabase from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Load our definitive auth fix -->
    <script src="/definitive-auth-fix.js"></script>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(message);
        }

        function clearLog() {
            logElement.textContent = '';
        }

        function updateStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = type;
        }

        // Initialize Supabase client
        let supabaseClient = null;

        function initializeSupabase() {
            try {
                const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
                
                if (typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
                    supabaseClient = supabase.createClient(supabaseUrl, supabaseKey);
                    window.supabase = supabaseClient;
                    log('✅ Supabase client initialized successfully');
                    updateStatus('Supabase client ready', 'success');
                    return true;
                } else {
                    log('❌ Supabase library not available');
                    updateStatus('Supabase library not available', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ Error initializing Supabase: ${error.message}`);
                updateStatus('Supabase initialization failed', 'error');
                return false;
            }
        }

        async function testSupabaseConnection() {
            log('🔍 Testing basic Supabase connection...');
            
            if (!supabaseClient) {
                log('❌ Supabase client not initialized');
                return;
            }

            try {
                // Test a simple query that should work with anon key
                const { data, error } = await supabaseClient
                    .from('attorneys')
                    .select('id')
                    .limit(1);

                if (error) {
                    log(`❌ Supabase connection test failed: ${error.message}`);
                    updateStatus('Connection test failed', 'error');
                } else {
                    log('✅ Supabase connection test successful');
                    log(`📊 Query returned ${data ? data.length : 0} results`);
                    updateStatus('Connection test passed', 'success');
                }
            } catch (error) {
                log(`❌ Supabase connection test error: ${error.message}`);
                updateStatus('Connection test error', 'error');
            }
        }

        async function testAuthenticatedRequest() {
            log('🔐 Testing authenticated request...');
            
            if (!supabaseClient) {
                log('❌ Supabase client not initialized');
                return;
            }

            try {
                // Try to get current session
                const { data: sessionData, error: sessionError } = await supabaseClient.auth.getSession();
                
                if (sessionError) {
                    log(`❌ Error getting session: ${sessionError.message}`);
                } else if (sessionData.session) {
                    log('✅ User session found');
                    log(`👤 User ID: ${sessionData.session.user.id}`);
                    log(`📧 User email: ${sessionData.session.user.email}`);
                    
                    // Test an authenticated query
                    const { data, error } = await supabaseClient
                        .from('attorneys')
                        .select('*')
                        .eq('user_id', sessionData.session.user.id);

                    if (error) {
                        log(`❌ Authenticated query failed: ${error.message}`);
                    } else {
                        log('✅ Authenticated query successful');
                        log(`📊 Found ${data ? data.length : 0} attorney records`);
                    }
                } else {
                    log('ℹ️ No user session found - user not authenticated');
                }
            } catch (error) {
                log(`❌ Authenticated request error: ${error.message}`);
            }
        }

        async function testUnauthenticatedRequest() {
            log('🌐 Testing unauthenticated request...');
            
            try {
                // Make a direct fetch request to test our interceptor
                const response = await fetch('https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/attorneys?select=id&limit=1', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log('✅ Unauthenticated request successful');
                    log(`📊 Response: ${JSON.stringify(data)}`);
                    updateStatus('All tests passing', 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ Unauthenticated request failed: ${response.status} ${response.statusText}`);
                    log(`📄 Error response: ${errorText}`);
                    updateStatus('Unauthenticated request failed', 'error');
                }
            } catch (error) {
                log(`❌ Unauthenticated request error: ${error.message}`);
                updateStatus('Request error', 'error');
            }
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            log('🚀 Authentication fix test page loaded');
            
            // Wait a moment for scripts to load
            setTimeout(() => {
                if (initializeSupabase()) {
                    log('🔧 Running initial connection test...');
                    testSupabaseConnection();
                }
            }, 1000);
        });

        // Monitor fetch calls
        const originalFetch = window.fetch;
        let fetchCallCount = 0;
        
        window.fetch = function(...args) {
            fetchCallCount++;
            const url = args[0];
            if (typeof url === 'string' && url.includes('supabase.co')) {
                log(`📡 Fetch call #${fetchCallCount} to Supabase: ${url.split('?')[0]}`);
            }
            return originalFetch.apply(this, args);
        };
    </script>
</body>
</html>
