import React, { useState, useEffect, useReducer, useRef, useCallback, useMemo } from 'react';
import { DEFAULT_ASSISTANT_ID } from '../../constants/vapiConstants';
import ReactMarkdown from 'react-markdown';
import StickThrowButton from '../StickThrowButton';
import VapiCall from '../VapiCall';
import { motion } from 'framer-motion';
import './SimplifiedPreview.css';

// Utility to convert hex to RGB for opacity handling
const hexToRgb = (hex) => {
  if (!hex) return '0, 0, 0';
  
  // Remove the # if present
  hex = hex.replace('#', '');
  
  // Parse the hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  
  // Return the RGB values as a string
  return `${r}, ${g}, ${b}`;
};

const SimplifiedPreview = ({ 
  firmName = 'Your Law Firm',
  primaryColor = '#4B74AA',
  secondaryColor = '#2C3E50',
  practiceDescription = "Your AI legal assistant is ready to help",
  welcomeMessage = "Hello! I'm Scout, your legal assistant. How can I help you today?",
  informationGathering = "Tell me about your situation, and I'll help find the right solution for you.",
  theme = 'dark',
  logoUrl = '/PRIMARY CLEAR.png',
  buttonText = 'Start Consultation',
  mascot = '/PRIMARY CLEAR.png',
  vapiInstructions = '',
  buttonOpacity = 1,
  practiceAreaBackgroundOpacity = 0.1,
  textBackgroundColor = '#634C38'
}) => {
  // State management
  const [messages, setMessages] = useState([
    { sender: 'bot', text: welcomeMessage }
  ]);
  const [message, setMessage] = useState('');
  const [showStartButton, setShowStartButton] = useState(true);
  const [chatActive, setChatActive] = useState(false);
  const isDark = theme !== 'light';
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [notificationShown, setNotificationShown] = useState(false);
  const [originalButtonText, setOriginalButtonText] = useState(buttonText);
  const [originalButtonStyle, setOriginalButtonStyle] = useState(null);
  const [showTooltip, setShowTooltip] = useState(false);
  
  // Add local state with setters for all configurable properties
  const [localFirmName, setFirmName] = useState(firmName);
  const [localPrimaryColor, setPrimaryColor] = useState(primaryColor);
  const [localSecondaryColor, setSecondaryColor] = useState(secondaryColor);
  const [localButtonColor, setButtonColor] = useState(secondaryColor);
  const [localPracticeDescription, setPracticeDescription] = useState(practiceDescription);
  const [localWelcomeMessage, setWelcomeMessage] = useState(welcomeMessage);
  const [localInformationGathering, setInformationGathering] = useState(informationGathering);
  const [localTheme, setTheme] = useState(theme);
  const [localLogoUrl, setLogoUrl] = useState(logoUrl);
  const [localButtonText, setButtonText] = useState(buttonText);
  const [localButtonOpacity, setButtonOpacity] = useState(buttonOpacity);
  const [localBackgroundColor, setBackgroundColor] = useState('#1a1a1a');
  const [localBackgroundOpacity, setBackgroundOpacity] = useState(0.9);
  const [localPracticeAreaBackgroundOpacity, setPracticeAreaBackgroundOpacity] = useState(practiceAreaBackgroundOpacity);
  const [localTextBackgroundColor, setTextBackgroundColor] = useState(textBackgroundColor);
  
  // Refs
  const buttonRef = useRef(null);
  const renderCount = useRef(0);
  
  // Force update hook for logo changes
  const [, forceUpdate] = useReducer(x => x + 1, 0);

  // Get current port for navigation
  const getCurrentPort = useCallback(() => {
    return window.location.port || '5173';
  }, []);

  const forceActivateCall = useCallback(() => {
    console.log('🔥 [SimplifiedPreview] FORCE ACTIVATE CALL - Maximum aggression');
    const currentPort = getCurrentPort();
    const callUrl = `http://localhost:${currentPort}/preview?direct_call=true&t=${Date.now()}`;
    console.log('🔄 [SimplifiedPreview] Attempting to navigate parent to direct call URL:', callUrl);
    
    try {
      if (window.parent && window !== window.parent) {
        window.parent.location.href = callUrl;
      } else {
        window.location.href = callUrl;
      }
    } catch (err) {
      console.error('❌ [SimplifiedPreview] Failed to force activate call:', err);
    }
  }, [getCurrentPort]);

  // Debug effect to track re-renders
  useEffect(() => {
    renderCount.current += 1;
    console.log(`🔄 [SimplifiedPreview] Re-render #${renderCount.current}`);
  });

  // Consolidate all message filtering logic into a single function
  const shouldIgnoreMessage = (event) => {
    // Early return if no data
    if (!event.data) {
      return true;
    }

    // Debug message structure
    if (renderCount.current <= 2) { // Only log for first couple renders
      console.log('🔍 [SimplifiedPreview] Message structure:', {
        source: event.data.source,
        type: event.data.type,
        hasPayload: !!event.data.payload,
        origin: event.origin
      });
    }

    // Ignore all Vite HMR related messages
    if (
      event.data.type?.includes('vite') ||
      event.data.type?.includes('hmr') ||
      event.data.type?.includes('webpack') ||
      event.data.source?.includes('webpack') ||
      (event.data.payload && 
       typeof event.data.payload === 'object' && 
       'type' in event.data.payload && 
       (event.data.payload.type.includes('hmr') || 
        event.data.payload.type.includes('vite')))
    ) {
      return true;
    }

    // Ignore React DevTools messages
    if (
      event.data.source?.includes('react-devtools') ||
      event.data.payload?.type?.includes('react-devtools') ||
      event.data.type?.includes('react-devtools')
    ) {
      return true;
    }

    // Validate message structure
    if (typeof event.data.type !== 'string') {
      return true;
    }

    // Validate origin
    if (
      event.origin !== window.location.origin &&
      event.origin !== window.parent.location.origin &&
      !event.origin.includes('localhost')
    ) {
      return true;
    }

    return false;
  };

  useEffect(() => {
    console.log('🔍 [SimplifiedPreview] Component mounted');
    
    const messageHandler = (event) => {
      // Use consolidated message filtering
      if (shouldIgnoreMessage(event)) {
        return;
      }

      // Only process specific message types we care about
      const validMessageTypes = [
        'CONSULTATION_STARTED',
        'CONSULTATION_ENDED',
        'UPDATE_ATTORNEY_PROFILE',
        'PREVIEW_READY',
        'updateCustomizations'  // Add the updateCustomizations message type
      ];

      if (!validMessageTypes.includes(event.data.type)) {
        return;
      }

      // Log only valid messages that we'll actually process
      console.log('📩 [SimplifiedPreview] Processing message:', {
        type: event.data.type,
        origin: event.origin
      });

      // Handle specific message types
      switch (event.data.type) {
        case 'CONSULTATION_STARTED':
          console.log('✅ [SimplifiedPreview] Consultation started confirmation received');
          break;
        case 'CONSULTATION_ENDED':
          console.log('⏹️ [SimplifiedPreview] Consultation ended message received');
          resetConsultationState();
          break;
        case 'UPDATE_ATTORNEY_PROFILE':
          if (event.data.profile) {
            console.log('📝 [SimplifiedPreview] Updating attorney profile:', event.data.profile);
            window.attorneyProfile = event.data.profile;
            // Force re-render to update logo
            forceUpdate();
          }
          break;
        case 'updateCustomizations':
          if (event.data.customizations) {
            console.log('🔄 [SimplifiedPreview] Received customization updates:', event.data.customizations);
            const customizations = event.data.customizations;
            
            // Update all the component props from customizations
            if (customizations.firmName) setFirmName?.(customizations.firmName);
            if (customizations.primaryColor) setPrimaryColor?.(customizations.primaryColor);
            if (customizations.secondaryColor) setSecondaryColor?.(customizations.secondaryColor);
            // Use buttonColor if available, otherwise fall back to secondaryColor
            if (customizations.buttonColor) {
              console.log('Setting button color to:', customizations.buttonColor);
              setButtonColor?.(customizations.buttonColor);
            } else if (customizations.secondaryColor) {
              setButtonColor?.(customizations.secondaryColor);
            }
            if (customizations.practiceDescription) setPracticeDescription?.(customizations.practiceDescription);
            if (customizations.welcomeMessage) setWelcomeMessage?.(customizations.welcomeMessage);
            if (customizations.informationGathering) setInformationGathering?.(customizations.informationGathering);
            if (customizations.theme) setTheme?.(customizations.theme);
            if (customizations.logoUrl) setLogoUrl?.(customizations.logoUrl);
            if (customizations.buttonText) setButtonText?.(customizations.buttonText);
            if (customizations.buttonOpacity !== undefined) setButtonOpacity?.(customizations.buttonOpacity);
            if (customizations.backgroundColor) setBackgroundColor?.(customizations.backgroundColor);
            if (customizations.backgroundOpacity !== undefined) setBackgroundOpacity?.(customizations.backgroundOpacity);
            if (customizations.practiceAreaBackgroundOpacity !== undefined) setPracticeAreaBackgroundOpacity?.(customizations.practiceAreaBackgroundOpacity);
            if (customizations.textBackgroundColor) {
              console.log('Setting text background color to:', customizations.textBackgroundColor);
              setTextBackgroundColor?.(customizations.textBackgroundColor);
            }
            
            // Force re-render to update all properties
            forceUpdate();
            
            // Update messages if welcome message has changed
            if (customizations.welcomeMessage && messages[0]?.text !== customizations.welcomeMessage) {
              setMessages([{ sender: 'bot', text: customizations.welcomeMessage }]);
            }
            
            // Store original button text for reference
            if (customizations.buttonText) {
              setOriginalButtonText(customizations.buttonText);
            }
          }
          break;
      }
    };

    window.addEventListener('message', messageHandler);
    
    // Send ready message to parent only once on mount
    if (window.parent && window !== window.parent) {
      try {
        window.parent.postMessage({ type: 'PREVIEW_READY' }, '*');
        console.log('🔄 [SimplifiedPreview] Sent ready message to parent');
      } catch (err) {
        console.warn('❌ [SimplifiedPreview] Failed to send ready message:', err);
      }
    }
    
    return () => {
      console.log('🔍 [SimplifiedPreview] Component unmounting');
      window.removeEventListener('message', messageHandler);
    };
  }, []); // Empty dependency array to ensure effect runs only once

  // Memoize getLogoToUse to prevent unnecessary recalculations
  const getLogoToUse = useCallback(() => {
    // First check if we have a custom logo URL
    if (logoUrl && logoUrl !== '/PRIMARY CLEAR.png') {
      console.log('✅ [SimplifiedPreview] Using custom logoUrl:', logoUrl);
      return logoUrl;
    }
    
    // Then check mascot
    if (mascot && mascot !== '/PRIMARY CLEAR.png') {
      console.log('✅ [SimplifiedPreview] Using mascot:', mascot);
      return mascot;
    }
    
    // Default fallback
    console.log('✅ [SimplifiedPreview] Using default logo');
    return '/PRIMARY CLEAR.png';
  }, [logoUrl, mascot]);

  // Memoize logo value and force update when attorney profile changes
  const logoToUse = useMemo(() => {
    const logo = getLogoToUse();
    console.log('🔄 [SimplifiedPreview] Logo updated to:', logo);
    return logo;
  }, [getLogoToUse, window.attorneyProfile?.logo]); // Add dependency on attorney profile logo

  // Update local state when props change
  useEffect(() => {
    setFirmName(firmName);
    setPrimaryColor(primaryColor);
    setSecondaryColor(secondaryColor);
    setPracticeDescription(practiceDescription);
    setWelcomeMessage(welcomeMessage);
    setInformationGathering(informationGathering);
    setTheme(theme);
    setLogoUrl(logoUrl);
    setButtonText(buttonText);
    setButtonOpacity(buttonOpacity);
    setTextBackgroundColor(textBackgroundColor);
    setPracticeAreaBackgroundOpacity(practiceAreaBackgroundOpacity);
    
    // Update messages when welcome message changes
    setMessages([{ sender: 'bot', text: welcomeMessage }]);
    
    console.log('Props updated, local state refreshed with new values');
    console.log('Button opacity:', buttonOpacity);
    console.log('Text background color:', textBackgroundColor);
  }, [firmName, primaryColor, secondaryColor, practiceDescription, welcomeMessage, 
      informationGathering, theme, logoUrl, buttonText, buttonOpacity, 
      textBackgroundColor, practiceAreaBackgroundOpacity]);

  // Debug logging for logo paths
  useEffect(() => {
    console.log('SimplifiedPreview mounted with props:');
    console.log('logoUrl:', localLogoUrl);
    console.log('mascot:', mascot);
    console.log('buttonText:', localButtonText);
    console.log('primaryColor:', localPrimaryColor);
    console.log('secondaryColor:', localSecondaryColor);
    console.log('theme:', localTheme);
  }, [localLogoUrl, mascot, localButtonText, localPrimaryColor, localSecondaryColor, localTheme]);

  // Auto-start consultation when component mounts
  useEffect(() => {
    // Small delay to ensure everything is loaded
    const timer = setTimeout(() => {
      handleStartConsultation();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  // Handle start consultation button click
  const handleStartConsultation = () => {
    console.log("Starting consultation...");
    
    // Check if we're in an iframe
    const isInIframe = window !== window.parent;
    
    if (isInIframe) {
      // If in iframe, send message to parent to handle the call
      window.parent.postMessage({ type: 'START_VAPI_CALL' }, '*');
      console.log("Sent START_VAPI_CALL message to parent");
    } else {
      // If standalone, show the VapiCall component
      setChatActive(true);
      console.log("Showing VapiCall component (standalone mode)");
    }
  };

  // Handle ending the call
  const handleEndCall = () => {
    setChatActive(false);
  };

  const handleSendMessage = () => {
    if (!message.trim()) return;
    
    // Add user message
    setMessages(prev => [...prev, { sender: 'user', text: message }]);
    
    // Clear input
    const userMessage = message;
    setMessage('');
    
    // Simulate bot response
    setTimeout(() => {
      setMessages(prev => [
        ...prev, 
        { 
          sender: 'bot', 
          text: `Thanks for your message: "${userMessage}". This is a demo of how the chat interface works.` 
        }
      ]);
    }, 1000);
  };

  // Convert markdown to HTML for practice description
  const renderPracticeDescription = () => {
    if (!localPracticeDescription) return '';
    
    // Simple markdown parser
    return localPracticeDescription
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>') // Bold
      .replace(/\*(.*?)\*/g, '<em>$1</em>') // Italic
      .replace(/### (.*?)$/gm, '<h3>$1</h3>') // H3
      .replace(/## (.*?)$/gm, '<h2>$1</h2>') // H2
      .replace(/# (.*?)$/gm, '<h1>$1</h1>') // H1
      .replace(/- (.*?)$/gm, '<li>$1</li>') // List items
      .replace(/<li>(.*?)<\/li>/g, '<ul><li>$1</li></ul>') // Wrap list items
      .replace(/<\/ul><ul>/g, '') // Fix multiple lists
      .replace(/\n/g, '<br>'); // Line breaks
  };

  // Function to reset the consultation state
  const resetConsultationState = () => {
    setShowStartButton(true);
    setChatActive(false);
    setMessages([{ sender: 'bot', text: localWelcomeMessage || welcomeMessage }]);
    setMessage('');
  };

  return (
    <div className="simplified-preview-container">
      {!chatActive ? (
        <motion.div 
          className="preview-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h1>Legal consultation made easy</h1>
          <p>Connect with a legal assistant who can help understand your situation</p>
          
          <button 
            className="start-consultation-button"
            onClick={handleStartConsultation}
          >
            Start Consultation
          </button>
        </motion.div>
      ) : (
        <div className="chat-ui-container">
          <div className="chat-header">
            <div className="chat-header-logo">
              <img src={localLogoUrl} alt={localFirmName} className="firm-logo" />
              <span className="firm-name">{localFirmName}</span>
            </div>
            <button 
              className="end-call-button"
              onClick={handleEndCall}
            >
              End Call
            </button>
          </div>
          
          <div className="chat-content">
            <VapiCall 
              onEndCall={handleEndCall}
              subdomain="default"
              customInstructions={{
                initialMessage: localWelcomeMessage || welcomeMessage,
                firmName: localFirmName || firmName,
                assistantId: 'legal-consultation',
                ...(vapiInstructions ? vapiInstructions : {})
              }}
              isDarkTheme={localTheme !== 'light'}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default SimplifiedPreview; 