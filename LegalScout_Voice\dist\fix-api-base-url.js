/**
 * Fix API Base URL for Development
 * 
 * This script ensures that all API calls use the correct base URL in development
 * by intercepting fetch requests and rewriting URLs that go to production.
 */

(function() {
  console.log('[APIBaseURLFix] Starting API base URL fix...');
  
  // Detect if we're in development
  const isDevelopment = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1' ||
                       window.location.port === '5174' ||
                       window.location.port === '5173';
  
  if (!isDevelopment) {
    console.log('[APIBaseURLFix] Not in development mode, skipping fix');
    return;
  }
  
  console.log('[APIBaseURLFix] Development mode detected, applying fix');
  
  // Store the original fetch function
  const originalFetch = window.fetch;
  
  // Override the fetch function to fix URLs
  window.fetch = function(url, options = {}) {
    let fixedUrl = url;
    
    // If the URL is a string and contains the production domain, fix it
    if (typeof url === 'string') {
      // Fix production domain URLs
      if (url.includes('https://dashboard.legalscout.net')) {
        fixedUrl = url.replace('https://dashboard.legalscout.net', window.location.origin);
        console.log('[APIBaseURLFix] Fixed production URL:', url, '→', fixedUrl);
      }
      
      // Fix malformed URLs with [object Object]
      if (url.includes('[object Object]')) {
        console.warn('[APIBaseURLFix] Detected malformed URL with [object Object]:', url);
        // Try to extract the path part
        const pathMatch = url.match(/\/api\/[^\/]+/);
        if (pathMatch) {
          fixedUrl = window.location.origin + pathMatch[0];
          console.log('[APIBaseURLFix] Fixed malformed URL:', url, '→', fixedUrl);
        }
      }
      
      // Ensure relative API URLs use the correct origin
      if (url.startsWith('/api/')) {
        fixedUrl = window.location.origin + url;
        console.log('[APIBaseURLFix] Fixed relative API URL:', url, '→', fixedUrl);
      }
    }
    
    // Log the request for debugging
    if (fixedUrl !== url) {
      console.log('[APIBaseURLFix] URL rewritten:', url, '→', fixedUrl);
    }
    
    // Call the original fetch with the fixed URL
    return originalFetch(fixedUrl, options);
  };
  
  console.log('[APIBaseURLFix] Fetch function patched successfully');
})();
