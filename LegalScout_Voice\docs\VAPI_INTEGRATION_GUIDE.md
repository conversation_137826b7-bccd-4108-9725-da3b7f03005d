# Vapi Integration Guide

## Overview

This document provides a comprehensive guide to integrating Vapi's voice assistant <PERSON><PERSON> into LegalScout Voice, including assistant management, call handling, SMS notifications, and call control.

## Architecture

### Core Components

1. **Vapi MCP Server**: A Model Context Protocol server that exposes Vapi's APIs as callable tools
2. **Enhanced Vapi Service**: A service that handles all Vapi interactions
3. **Call Control System**: A secure system for attorney call monitoring and control
4. **SMS Notification System**: A system for notifying attorneys about ongoing calls

### Transport Methods

The integration uses two transport methods:

1. **SSE (Server-Sent Events)**: Used for real-time operations like call monitoring and control
   - Better for continuous updates and streaming data
   - Recommended for in-call tools and real-time monitoring
   - Connected via `https://mcp.vapi.ai/sse`

2. **Streamable HTTP**: Used for administrative operations like assistant management
   - Better for request-response patterns
   - More reliable in serverless environments
   - Connected via `https://mcp.vapi.ai/mcp`

## Implementation Details

### Assistant Management

Attorneys can configure their assistants through the dashboard. The configuration is:
1. Saved to Supabase
2. Synced to <PERSON>ap<PERSON> using the MCP server

Key fields mapped between Supabase and Vapi:
- `welcome_message` (Supabase) → `firstMessage` (Vapi)
- `vapi_instructions` (Supabase) → `instructions` (Vapi)
- `voice_id` (Supabase) → `voice.voiceId` (Vapi)
- `voice_provider` (Supabase) → `voice.provider` (Vapi)
- `ai_model` (Supabase) → `llm.model` (Vapi)

### Call Creation

Calls are created using the attorney's Vapi assistant ID. The process:
1. Retrieve the attorney's assistant ID from Supabase
2. Create a call using the Vapi MCP server
3. Monitor call state and events
4. Send SMS notification to the attorney when appropriate

### SMS Notifications

When a call is in progress and determined to be relevant to an attorney, an SMS is sent with:
1. Information about the caller and their legal issue
2. A secure link to the call control interface

### Call Control

The call control system allows attorneys to:
1. Listen to ongoing calls
2. View real-time transcripts
3. Take over calls from the AI assistant
4. Provide guidance to the AI assistant
5. End calls

#### Security

Call control links are secured using:
1. Unique tokens that include call ID, attorney ID, and expiration time
2. Authentication verification before granting access
3. Limited permissions based on the attorney's role

## Integration with VapiBlocks

[VapiBlocks](https://www.vapiblocks.com/) provides UI components for Vapi integration, including:
1. Call controls
2. Audio visualization
3. Transcript display
4. Call status indicators

These components are used in the call control interface to provide a seamless experience.

## Iframe Message Communication

### Iframe Message Communication

The most critical insight is that Vapi's SDK embeds an iframe for handling calls and communicates with the parent window through `window.postMessage()`. These messages have a specific format that differs from the direct SDK events:

```javascript
{
  what: 'iframe-call-message',
  action: 'remote-participants-audio-level',
  participantsAudioLevel: {...},
  callClientId: '17477145600300.6862929156165595',
  from: 'embedded'
}
```

Understanding this message format is essential for properly implementing features like volume level indicators and transcript rendering.

### Audio Level Events

Audio level events are sent as iframe messages with the action `remote-participants-audio-level`:

```javascript
{
  action: 'remote-participants-audio-level',
  participantsAudioLevel: {
    // Object with participant IDs as keys and audio levels as values
  },
  what: 'iframe-call-message',
  callClientId: '17477145600300.6862929156165595',
  from: 'embedded'
}
```

The `participantsAudioLevel` object contains audio level data that needs to be processed to update the volume level indicator.

### Transcript Events

Transcript events can come in various formats through iframe messages:

```javascript
{
  action: 'transcript',
  transcript: '...',
  is_final: true/false,
  what: 'iframe-call-message',
  callClientId: '17477145600300.6862929156165595',
  from: 'embedded'
}
```

Or embedded in message objects:

```javascript
{
  message: {
    content: '...',
    is_final: true/false
  },
  what: 'iframe-call-message',
  callClientId: '17477145600300.6862929156165595',
  from: 'embedded'
}
```

## Implementation Guide

### 1. Window Message Listener

The most reliable way to capture Vapi events is to add a window message listener:

```javascript
useEffect(() => {
  // Create a window message handler
  const handleWindowMessage = (event) => {
    if (!event.data) return;

    console.log('Window message received:', event.data);

    // Check if this is a Vapi iframe message
    if (event.data.what === 'iframe-call-message') {
      // Process audio level events
      if (event.data.action === 'remote-participants-audio-level' &&
          event.data.participantsAudioLevel) {
        processAudioLevels(event.data.participantsAudioLevel);
      }

      // Process transcript events
      if (event.data.action === 'transcript' ||
          event.data.transcript ||
          (event.data.message && event.data.message.content)) {
        processTranscript(event.data);
      }
    }
  };

  // Add window event listener
  window.addEventListener('message', handleWindowMessage);

  // Cleanup function
  return () => {
    window.removeEventListener('message', handleWindowMessage);
  };
}, []);
```

### 2. Processing Audio Levels

To process audio levels from the `participantsAudioLevel` object:

```javascript
const processAudioLevels = (participantsAudioLevel) => {
  // Extract the audio level values
  const audioLevels = Object.values(participantsAudioLevel);

  if (audioLevels.length > 0) {
    // Convert to numbers and filter out NaN values
    const numericLevels = audioLevels
      .map(level => typeof level === 'number' ? level : parseFloat(level))
      .filter(level => !isNaN(level));

    if (numericLevels.length > 0) {
      // Find the maximum level
      const level = Math.max(...numericLevels);

      // Scale the level to be more visible (raw levels are often very small)
      const scaledLevel = Math.min(level * 5, 1);

      // Update the volume level indicator
      updateVolumeIndicator(scaledLevel);
    }
  }
};
```

### 3. Processing Transcripts

To process transcript data from various message formats:

```javascript
const processTranscript = (data) => {
  // Extract transcript text and final status
  let transcriptText = '';
  let isFinal = false;

  if (data.transcript) {
    transcriptText = data.transcript;
    isFinal = data.is_final || false;
  } else if (data.message && data.message.content) {
    transcriptText = data.message.content;
    isFinal = data.message.is_final || false;
  } else if (data.content) {
    transcriptText = data.content;
    isFinal = data.is_final || false;
  }

  if (transcriptText && transcriptText.trim()) {
    // Update the current transcript display
    updateTranscriptDisplay(transcriptText);

    // If this is a final transcript, add it to the conversation
    if (isFinal) {
      addTranscriptToConversation(transcriptText);
    }
  }
};
```

### 4. Direct DOM Manipulation

For more reliable updates, especially in complex React applications, consider using direct DOM manipulation as a fallback:

```javascript
const updateVolumeIndicator = (level) => {
  // Update React state
  setVolumeLevel(level);

  // Direct DOM manipulation as a fallback
  try {
    const volumeBars = document.querySelectorAll('.volume-bar');
    if (volumeBars && volumeBars.length > 0) {
      const activeBarCount = Math.floor(level * 10);

      volumeBars.forEach((bar, index) => {
        if (index < activeBarCount) {
          bar.classList.add('active');
        } else {
          bar.classList.remove('active');
        }
      });
    }
  } catch (error) {
    console.warn('Error directly manipulating volume bars:', error);
  }
};
```

### 5. Dual Approach to Event Handling

For maximum reliability, use both direct SDK event listeners and window message listeners:

```javascript
// Direct SDK event listeners
vapi.on('volume-level', handleVolumeLevel);
vapi.on('transcript', handleTranscript);

// Window message listener
window.addEventListener('message', handleWindowMessage);
```

This ensures that events are captured regardless of how they're emitted by the SDK.

## Troubleshooting

### Common Issues

1. **Volume level indicator not responding**:
   - Check if you're receiving `remote-participants-audio-level` events in the console
   - Verify that you're correctly extracting values from the `participantsAudioLevel` object
   - Try scaling the audio levels (they're often very small)

2. **Transcripts not appearing**:
   - Check if you're receiving transcript events in the console
   - Verify that you're checking all possible fields where transcript text might be stored
   - Try direct DOM manipulation to update the UI

3. **React state updates not working**:
   - Use direct DOM manipulation as a fallback
   - Check for scope issues in event handlers
   - Ensure that state setters are properly defined and accessible

### Debugging Tips

1. Add comprehensive logging for all window messages:
   ```javascript
   window.addEventListener('message', (event) => {
     console.log('Window message received:', event.data);
   });
   ```

2. Inspect the browser console for messages with `what: 'iframe-call-message'`

3. Use browser developer tools to monitor DOM updates

## Best Practices

1. **Handle Multiple Event Formats**: Always check for multiple event formats to ensure compatibility with different SDK versions

2. **Scale Audio Levels**: Raw audio levels are often very small (0.01-0.1), so scale them up for better visualization

3. **Direct DOM Manipulation**: Use direct DOM manipulation as a fallback for critical UI updates

4. **Comprehensive Logging**: Log all events and their data for debugging

5. **Proper Cleanup**: Always remove event listeners when components unmount

6. **Error Handling**: Wrap all event processing in try-catch blocks to prevent crashes

## Environment Variables

The following environment variables are required:

- `VAPI_TOKEN` or `VAPI_SECRET_KEY`: Vapi private API key for server-side operations
- `VITE_VAPI_PUBLIC_KEY`: Vapi public API key for client-side operations
- `APP_URL`: Base URL of the application (for generating call control links)

## Best Practices

1. **One-Way Sync Pattern**: Changes flow from UI → Supabase → Vapi (never in reverse)
2. **Save-Then-Sync**: Always save to Supabase first, then sync to Vapi
3. **Error Handling**: Implement robust error handling for all Vapi operations
4. **Fallback Mechanisms**: Have fallback mechanisms for when Vapi operations fail
5. **Security**: Secure all tokens and links to prevent unauthorized access
6. **Handle Multiple Event Formats**: Always check for multiple event formats to ensure compatibility with different SDK versions
7. **Scale Audio Levels**: Raw audio levels are often very small (0.01-0.1), so scale them up for better visualization
8. **Direct DOM Manipulation**: Use direct DOM manipulation as a fallback for critical UI updates
9. **Comprehensive Logging**: Log all events and their data for debugging
10. **Proper Cleanup**: Always remove event listeners when components unmount

## Troubleshooting

Common issues and solutions:

1. **401 Unauthorized**: Check that your Vapi API keys are correctly set in environment variables
2. **Connection Failures**: Ensure your MCP server is properly configured
3. **Missing Assistant**: Verify that the attorney has a valid Vapi assistant ID
4. **SMS Not Sending**: Check Vapi SMS configuration and permissions
5. **Volume level indicator not responding**: Check if you're receiving `remote-participants-audio-level` events
6. **Transcripts not appearing**: Verify that you're checking all possible fields where transcript text might be stored

## Resources

- [Vapi Documentation](https://docs.vapi.ai/)
- [Vapi MCP Server Documentation](https://docs.vapi.ai/sdk/mcp-server)
- [VapiBlocks UI Library](https://www.vapiblocks.com/)
- [Vapi Blog: MCP Client](https://vapi.ai/blog/introducing-vapi-mcp-client)
- [Vapi Blog: MCP Server](https://vapi.ai/blog/bring-vapi-voice-agents-into-your-workflows-with-the-new-vapi-mcp-server)

## Conclusion

Properly integrating Vapi's voice assistant SDK requires understanding both the iframe message communication model and the MCP server architecture. By following this guide, you can implement a robust Vapi integration that includes assistant management, call handling, SMS notifications, and call control.
