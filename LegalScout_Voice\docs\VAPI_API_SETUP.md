# Vapi API Setup Guide

This guide provides instructions on how to set up the Vapi API integration for the LegalScout application.

## Overview

LegalScout uses Vapi for voice assistant functionality. To use Vapi, you need to:

1. Create a Vapi account
2. Generate an API key
3. Configure the application with your API key

## Step 1: Create a Vapi Account

1. Go to [https://app.vapi.ai/signup](https://app.vapi.ai/signup)
2. Create an account using your email address
3. Verify your email address

## Step 2: Generate an API Key

1. Log in to your Vapi account at [https://app.vapi.ai](https://app.vapi.ai)
2. Navigate to Settings > API Keys
3. Click "Create API Key"
4. Give your API key a name (e.g., "LegalScout Development")
5. Copy the generated API key (you won't be able to see it again)

## Step 3: Configure the Application

1. Open the `.env.development` file in the root of the project
2. Replace the placeholder value for `VITE_VAPI_PUBLIC_KEY` with your actual API key:

```
VITE_VAPI_PUBLIC_KEY=your_actual_api_key_here
```

3. Save the file

## Step 4: Test the API Key

1. Run the test script to verify that your API key works:

```
npm run test:vapi-key
```

2. If the test is successful, you should see output indicating that the API key is valid and can be used to access the Vapi API.

## Troubleshooting

### API Key Not Working

If your API key is not working, check the following:

1. Make sure you've copied the entire API key without any extra spaces
2. Verify that your Vapi account is active and in good standing
3. Check if there are any usage limits or restrictions on your account

### Connection Issues

If you're having connection issues, try the following:

1. Run the diagnostics to check the connection:

```javascript
VapiDebug.diagnose()
```

2. Check the network logs to see if there are any API errors:

```javascript
VapiMcpDebugger.getNetworkLogs()
```

3. Check the connection logs to see if there are any connection issues:

```javascript
VapiMcpDebugger.getLogs()
```

## Assistant ID

The application uses a default assistant ID (`VITE_VAPI_DEFAULT_ASSISTANT_ID`) as a fallback when creating new assistants. This ID is used when:

1. A new attorney is created without a Vapi assistant ID
2. An attorney's Vapi assistant ID is invalid or the assistant doesn't exist

You can create your own assistant in the Vapi dashboard and use its ID instead of the default one:

1. Log in to your Vapi account at [https://app.vapi.ai](https://app.vapi.ai)
2. Navigate to Assistants
3. Create a new assistant or select an existing one
4. Copy the assistant ID from the URL or the assistant details
5. Update the `.env.development` file with your assistant ID:

```
VITE_VAPI_DEFAULT_ASSISTANT_ID=your_assistant_id
```

## Additional Resources

- [Vapi Documentation](https://docs.vapi.ai)
- [Vapi API Reference](https://docs.vapi.ai/api-reference)
- [Vapi Dashboard](https://app.vapi.ai)
- [LegalScout Vapi Debugging Guide](./VAPI_DEBUGGING.md)
