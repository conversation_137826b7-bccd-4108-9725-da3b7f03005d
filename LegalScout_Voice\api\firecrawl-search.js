/**
 * Firecrawl Search API Endpoint
 * 
 * This serverless function handles web search requests using Firecrawl.
 */

// Firecrawl API configuration
const FIRECRAWL_API_KEY = 'fc-dcf7841e59c643be991ff4a74807c90a';
const FIRECRAWL_API_URL = 'https://api.firecrawl.dev/search';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }
  
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }
  
  try {
    // Parse the request body
    const { query, numResults = 3, format = 'simple', legalFocus = false } = req.body;
    
    // Validate the query
    if (!query) {
      return res.status(400).json({ error: 'Search query is required' });
    }
    
    // Prepare request body for Firecrawl
    const requestBody = {
      query,
      max_results: Math.min(numResults, 10), // Limit to 10 results max
      extract_content: true,
      summarize: true
    };
    
    // Add legal focus if requested
    if (legalFocus) {
      // Enhance query with legal focus
      requestBody.query = `${query} legal information law`;
      
      // Prioritize legal domains
      requestBody.domain_filter = [
        'law.cornell.edu',
        'justia.com',
        'findlaw.com',
        'nolo.com',
        'uscourts.gov',
        'courtlistener.com',
        'casetext.com',
        'lexisnexis.com',
        'westlaw.com'
      ];
    }
    
    // Make the API request to Firecrawl
    console.log('Sending Firecrawl request:', requestBody);
    const response = await fetch(FIRECRAWL_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${FIRECRAWL_API_KEY}`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Firecrawl API error: ${errorData.error || response.statusText}`);
    }
    
    const data = await response.json();
    
    // Format the results based on the requested format
    let formattedResults;
    
    switch (format) {
      case 'detailed':
        formattedResults = {
          type: 'detailed',
          items: data.results.map(result => ({
            title: result.title,
            url: result.url,
            content: result.content_html || result.content_text || '',
            summary: result.summary || '',
            source: new URL(result.url).hostname
          }))
        };
        break;
        
      case 'legal':
        formattedResults = {
          type: 'legal',
          items: data.results.map(result => {
            // Extract citation if available
            const citationMatch = result.title.match(/(\d+\s+[A-Za-z\.]+\s+\d+|\d+\s+[A-Za-z\.]+\s+\d+d\s+\d+)/);
            const citation = citationMatch ? citationMatch[0] : '';
            
            return {
              title: result.title,
              url: result.url,
              content: result.content_text || '',
              summary: result.summary || '',
              citation,
              source: new URL(result.url).hostname
            };
          })
        };
        break;
        
      case 'simple':
      default:
        formattedResults = {
          type: 'simple',
          items: data.results.map(result => ({
            title: result.title,
            url: result.url,
            summary: result.summary || '',
            source: new URL(result.url).hostname
          }))
        };
    }
    
    // Return the formatted results
    return res.status(200).json({
      success: true,
      query,
      format,
      results: formattedResults
    });
  } catch (error) {
    console.error('Firecrawl search API error:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'An error occurred during the search'
    });
  }
}
