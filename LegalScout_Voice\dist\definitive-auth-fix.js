/**
 * DEFINITIVE AUTHENTICATION FIX
 * 
 * This is the single, authoritative solution for all authentication issues.
 * It replaces all other fetch interceptors and handles both Supabase and Vapi authentication.
 */

(function() {
  console.log('🔧 [DefinitiveAuthFix] Starting comprehensive authentication fix...');

  // Prevent multiple installations
  if (window.__DEFINITIVE_AUTH_FIX_INSTALLED) {
    console.log('🔧 [DefinitiveAuthFix] Already installed, skipping...');
    return;
  }

  // Store the original fetch function (before any other interceptors)
  const originalFetch = window.originalFetchBeforeInterceptors || window.fetch;

  // Mark as installed
  window.__DEFINITIVE_AUTH_FIX_INSTALLED = true;
  
  // Create our definitive fetch interceptor
  window.fetch = async function(url, options = {}) {
    try {
      // Fix relative URLs first
      let fixedUrl = url;
      if (typeof url === 'string' && url.startsWith('/') && !url.startsWith('//')) {
        fixedUrl = window.location.origin + url;
        console.log('🔧 [DefinitiveAuthFix] Fixed relative URL:', url, '→', fixedUrl);
      }

      // Handle Supabase authentication
      if (typeof fixedUrl === 'string' && fixedUrl.includes('supabase.co')) {
        console.log('🔧 [DefinitiveAuthFix] Handling Supabase request:', fixedUrl.split('?')[0]);
        
        // Ensure options object exists
        options = options || {};
        options.headers = options.headers || {};

        // Convert Headers object to plain object if needed
        if (options.headers instanceof Headers) {
          const headersObj = {};
          for (const [key, value] of options.headers.entries()) {
            headersObj[key] = value;
          }
          options.headers = headersObj;
        }

        // Supabase anon key (always required)
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';

        // Always set the apikey header (required for all Supabase requests)
        options.headers['apikey'] = supabaseAnonKey;

        // Try to get user's JWT token for authenticated requests
        let userToken = null;
        
        try {
          // Method 1: Try to get from Supabase client
          if (window.supabase && window.supabase.auth) {
            const { data } = await window.supabase.auth.getSession();
            userToken = data?.session?.access_token;
          }
        } catch (e) {
          console.log('🔧 [DefinitiveAuthFix] Could not get session from Supabase client');
        }

        if (!userToken) {
          try {
            // Method 2: Try to get from localStorage
            const authKey = 'sb-utopqxsvudgrtiwenlzl-auth-token';
            const authData = localStorage.getItem(authKey);
            if (authData) {
              const parsed = JSON.parse(authData);
              userToken = parsed.access_token;
            }
          } catch (e) {
            console.log('🔧 [DefinitiveAuthFix] Could not get token from localStorage');
          }
        }

        // Set Authorization header
        if (userToken) {
          options.headers['Authorization'] = `Bearer ${userToken}`;
          console.log('🔧 [DefinitiveAuthFix] Using user JWT token for authenticated request');
        } else {
          options.headers['Authorization'] = `Bearer ${supabaseAnonKey}`;
          console.log('🔧 [DefinitiveAuthFix] Using anon key for unauthenticated request');
        }

        // Log final headers for debugging
        console.log('🔧 [DefinitiveAuthFix] Final headers:', {
          apikey: options.headers['apikey'] ? 'SET' : 'MISSING',
          Authorization: options.headers['Authorization'] ? 'SET' : 'MISSING',
          'Content-Type': options.headers['Content-Type'] || 'NOT SET'
        });

        // Ensure proper content type for POST/PATCH/PUT requests
        if (!options.headers['Content-Type'] && 
            (options.method === 'POST' || options.method === 'PATCH' || options.method === 'PUT')) {
          options.headers['Content-Type'] = 'application/json';
        }

        // Ensure Accept header
        if (!options.headers['Accept']) {
          options.headers['Accept'] = 'application/json';
        }

        console.log('✅ [DefinitiveAuthFix] Supabase headers configured');
      }

      // Handle Vapi authentication (don't interfere with Vapi requests)
      if (typeof fixedUrl === 'string' && fixedUrl.includes('api.vapi.ai')) {
        console.log('🔧 [DefinitiveAuthFix] Vapi request detected - preserving original headers');
        // Don't modify Vapi requests - they have their own authentication
      }

      // Make the request
      return originalFetch.call(this, fixedUrl, options);
      
    } catch (error) {
      console.error('🚨 [DefinitiveAuthFix] Error in fetch interceptor:', error);
      // Fallback to original fetch
      return originalFetch.call(this, url, options);
    }
  };

  // Clear any existing authentication state that might be corrupted
  function clearCorruptedAuthState() {
    console.log('🔧 [DefinitiveAuthFix] Checking for corrupted auth state...');
    
    try {
      // Check localStorage for corrupted tokens
      const authKey = 'sb-utopqxsvudgrtiwenlzl-auth-token';
      const authData = localStorage.getItem(authKey);
      
      if (authData) {
        const parsed = JSON.parse(authData);
        
        // Check if token is expired or malformed
        if (parsed.access_token) {
          try {
            // Decode JWT to check expiration
            const payload = JSON.parse(atob(parsed.access_token.split('.')[1]));
            const now = Math.floor(Date.now() / 1000);
            
            if (payload.exp && payload.exp < now) {
              console.log('🔧 [DefinitiveAuthFix] Found expired token, clearing...');
              localStorage.removeItem(authKey);
            }
          } catch (e) {
            console.log('🔧 [DefinitiveAuthFix] Found malformed token, clearing...');
            localStorage.removeItem(authKey);
          }
        }
      }
    } catch (e) {
      console.log('🔧 [DefinitiveAuthFix] Error checking auth state:', e);
    }
  }

  // Initialize the fix
  function initialize() {
    console.log('🔧 [DefinitiveAuthFix] Initializing...');
    
    // Clear corrupted auth state
    clearCorruptedAuthState();
    
    // Set up auth state change listener
    if (window.supabase && window.supabase.auth) {
      window.supabase.auth.onAuthStateChange((event, session) => {
        console.log('🔧 [DefinitiveAuthFix] Auth state changed:', event);
        
        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
          // Clear any cached tokens
          console.log('🔧 [DefinitiveAuthFix] Clearing cached tokens after auth change');
        }
      });
    }
    
    console.log('✅ [DefinitiveAuthFix] Initialization complete');
  }

  // Wait for Supabase to be available, then initialize
  if (window.supabase) {
    initialize();
  } else {
    const checkInterval = setInterval(() => {
      if (window.supabase) {
        clearInterval(checkInterval);
        initialize();
      }
    }, 100);
    
    // Clear interval after 10 seconds
    setTimeout(() => {
      clearInterval(checkInterval);
      console.log('🔧 [DefinitiveAuthFix] Initialized without Supabase client');
    }, 10000);
  }

  console.log('✅ [DefinitiveAuthFix] Definitive authentication fix installed');
})();
