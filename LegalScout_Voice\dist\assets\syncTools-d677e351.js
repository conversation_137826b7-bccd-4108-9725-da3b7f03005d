import{H as l,I as p,J as y,K as v,M as h,N as g,O as w,P as _}from"./pages-5c5506e6.js";import"./vendor-068d85d4.js";import"./vendor-react-3e31a257.js";const E=async n=>{const{attorneyId:o,forceUpdate:s=!1}=n;console.log(`Syncing attorney profile for ${o}, forceUpdate: ${s}`);try{const e=await l({attorneyId:o,forceUpdate:s});return console.log(`Profile persistence result for ${o}:`,{action:e.action,success:e.success,sources:e.sources}),{action:e.action,assistantId:e.vapiResult?.assistantId,message:e.message,success:e.success,sources:e.sources,discrepancies:e.vapiResult?.discrepancies||null}}catch(e){return console.error(`Error syncing attorney profile for ${o}:`,e),{action:"error",error:e.message,message:`Error syncing attorney profile: ${e.message}`}}},V=async n=>{const{authData:o,action:s}=n;console.log(`Managing auth state for action: ${s}`);try{switch(s){case"login":const{user:e,session:a}=o;if(!e||!e.email)return{action:"login",success:!1,message:"Invalid user data provided for login"};let t=await y(e.email);t?t.user_id||(console.log(`Updating existing attorney record with user_id for ${e.email}`),t=await h(t.id,{user_id:e.id})):(console.log(`Creating new attorney record for ${e.email}`),t=await v({email:e.email,name:e.user_metadata?.name||e.email.split("@")[0],user_id:e.id})),console.log(`Ensuring attorney ${t.id} profile persistence`);const c=await l({attorneyId:t.id,localData:t,forceUpdate:!0});return{action:"login",success:!0,attorney:t,syncResult:c,session:a,message:"Authentication state synchronized"};case"logout":return{action:"logout",success:!0,message:"User logged out successfully"};case"refresh":if(!o.user)return{action:"refresh",success:!1,message:"No user data provided for refresh"};const r=await p(o.user.id);if(!r)return{action:"refresh",success:!1,message:"Attorney record not found for this auth ID"};console.log(`Ensuring profile persistence for attorney ${r.id} on refresh`);const i=await l({attorneyId:r.id,localData:r});return{action:"refresh",success:!0,attorney:r,syncResult:i,message:"Authentication state refreshed and synchronized"};default:return{action:"unknown",success:!1,message:`Unknown action: ${s}`}}}catch(e){return console.error(`Error managing auth state for action ${s}:`,e),{action:s,success:!1,error:e.message,message:`Error managing auth state: ${e.message}`}}},C=async n=>{const{attorneyId:o,configData:s}=n;console.log(`Validating configuration for attorney ${o}`),console.log("Config data:",s);try{const e=await g(o);if(!e)return{valid:!1,errors:["Attorney not found"],message:"Cannot validate configuration for non-existent attorney"};const a={profile:["name","email"],appearance:["firm_name"],agent:["welcome_message","vapi_instructions"],voice:["voice_provider","voice_id"]},t={...e,...s},c={};let r=!1;Object.entries(a).forEach(([d,f])=>{const u=f.filter(m=>!t[m]);u.length>0&&(c[d]=u,r=!0)});const i=[];return t.welcome_message&&t.welcome_message.length>500&&(i.push("Welcome message exceeds maximum length of 500 characters"),r=!0),t.voice_provider&&t.voice_id&&((await w(t.voice_provider)).includes(t.voice_id)||(i.push(`Voice ID '${t.voice_id}' is not valid for provider '${t.voice_provider}'`),r=!0)),{valid:!r,missingFields:Object.keys(c).length>0?c:null,validationErrors:i.length>0?i:null,message:r?"Configuration validation failed":"Configuration is valid"}}catch(e){return console.error(`Error validating configuration for attorney ${o}:`,e),{valid:!1,error:e.message,message:`Error validating configuration: ${e.message}`}}},k=async n=>{const{attorneyId:o}=n;console.log(`Checking preview consistency for attorney ${o}`);try{const s=await g(o);if(!s)return{consistent:!1,errors:["Attorney not found"],message:"Cannot check consistency for non-existent attorney"};const e=s.vapi_assistant_id;if(!e)return{consistent:!1,errors:["No Vapi assistant ID found"],message:"Attorney record is missing Vapi assistant ID"};let a;try{a=await _(e)}catch(r){return{consistent:!1,errors:[`Error fetching Vapi assistant: ${r.message}`],message:"Failed to fetch Vapi assistant"}}const t={};if(Object.entries({firm_name:"name",welcome_message:"firstMessage",vapi_instructions:"instructions"}).forEach(([r,i])=>{s[r]!==a[i]&&(t[r]={attorney:s[r],vapi:a[i]})}),(s.voice_provider!==a.voice?.provider||s.voice_id!==a.voice?.voiceId)&&(t.voice={attorney:{provider:s.voice_provider,voiceId:s.voice_id},vapi:a.voice}),Object.keys(t).length>0){console.log(`Found Vapi discrepancies for attorney ${o}:`,t);const r=await l({attorneyId:s.id,forceUpdate:!0});return{consistent:!1,discrepancies:{vapi:t},action:"fixed",syncResult:r,message:"Vapi assistant discrepancies found and fixed"}}return{consistent:!0,message:"Preview is consistent with deployment"}}catch(s){return console.error(`Error checking preview consistency for attorney ${o}:`,s),{consistent:!1,error:s.message,message:`Error checking preview consistency: ${s.message}`}}};export{k as checkPreviewConsistency,V as manageAuthState,E as syncAttorneyProfile,C as validateConfiguration};
//# sourceMappingURL=syncTools-d677e351.js.map
