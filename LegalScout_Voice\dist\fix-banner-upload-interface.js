/**
 * Fix Banner Upload Interface
 * 
 * This script ensures that the banner upload interface appears properly
 * after a banner is removed, allowing users to upload a new banner.
 */

console.log('[FixBannerUploadInterface] Starting fix...');

// Function to ensure upload interface is visible after banner removal
function ensureUploadInterfaceVisible() {
  try {
    // Look for the banner upload container
    const uploadContainers = document.querySelectorAll('.logo-upload-container, .banner-upload-container');
    
    uploadContainers.forEach(container => {
      // Check if this container has both upload and preview elements
      const uploadDiv = container.querySelector('.logo-upload, .banner-upload');
      const previewDiv = container.querySelector('.logo-preview, .banner-preview');
      
      if (uploadDiv && previewDiv) {
        // Check if banner was recently removed
        const bannerRemovalState = localStorage.getItem('banner_removal_state');
        let wasRecentlyRemoved = false;
        
        if (bannerRemovalState) {
          try {
            const state = JSON.parse(bannerRemovalState);
            wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 60000; // 1 minute
          } catch (e) {
            // Ignore parsing errors
          }
        }
        
        if (wasRecentlyRemoved) {
          console.log('[FixBannerUploadInterface] Banner was removed, ensuring upload interface is visible');
          
          // Hide the preview div and show the upload div
          previewDiv.style.display = 'none';
          uploadDiv.style.display = 'block';
          
          // Also ensure the upload input is enabled
          const fileInput = uploadDiv.querySelector('input[type="file"]');
          if (fileInput) {
            fileInput.disabled = false;
            fileInput.value = ''; // Clear any previous value
            console.log('[FixBannerUploadInterface] Upload input enabled and cleared');
          }
        }
      }
    });
  } catch (error) {
    console.error('[FixBannerUploadInterface] Error ensuring upload interface visibility:', error);
  }
}

// Function to monitor for banner removal events
function monitorBannerRemovalEvents() {
  // Listen for the custom banner removal event
  window.addEventListener('bannerRemoved', function(event) {
    console.log('[FixBannerUploadInterface] Banner removal event detected, updating interface');
    
    setTimeout(() => {
      ensureUploadInterfaceVisible();
    }, 100);
  });
  
  // Also monitor for clicks on remove buttons
  document.addEventListener('click', function(event) {
    const target = event.target;
    
    if (target && target.tagName === 'BUTTON') {
      const buttonText = (target.textContent || target.innerText || '').toLowerCase();
      
      if (buttonText.includes('remove') && (buttonText.includes('banner') || buttonText.includes('logo'))) {
        console.log('[FixBannerUploadInterface] Remove button clicked, will update interface');
        
        setTimeout(() => {
          ensureUploadInterfaceVisible();
        }, 200);
      }
    }
  }, true);
  
  console.log('[FixBannerUploadInterface] Banner removal event monitoring set up');
}

// Function to patch React state updates to show upload interface
function patchReactStateUpdates() {
  // Monitor for state changes that might affect the upload interface
  setTimeout(() => {
    try {
      // Look for React components that manage logo state
      const allElements = document.querySelectorAll('*');
      
      for (let element of allElements) {
        const fiber = element._reactInternalFiber || element._reactInternals;
        if (fiber) {
          let currentFiber = fiber;
          
          while (currentFiber) {
            if (currentFiber.stateNode && currentFiber.stateNode.state) {
              const component = currentFiber.stateNode;
              const state = component.state;
              
              // Check if this component has logo-related state
              if (state.logoUrl !== undefined || state.formData?.logoUrl !== undefined) {
                console.log('[FixBannerUploadInterface] Found component with logo state, patching...');
                
                // Override setState to ensure upload interface appears when logo is removed
                const originalSetState = component.setState;
                component.setState = function(updater, callback) {
                  const result = originalSetState.call(this, updater, callback);
                  
                  // Check if logo was just removed
                  setTimeout(() => {
                    const currentState = this.state;
                    const logoUrl = currentState.logoUrl || currentState.formData?.logoUrl;
                    
                    if (!logoUrl || logoUrl === '') {
                      console.log('[FixBannerUploadInterface] Logo removed in state, ensuring upload interface is visible');
                      ensureUploadInterfaceVisible();
                    }
                  }, 50);
                  
                  return result;
                };
                
                break;
              }
            }
            
            currentFiber = currentFiber.child || currentFiber.sibling || currentFiber.return;
          }
        }
      }
    } catch (error) {
      console.error('[FixBannerUploadInterface] Error patching React state updates:', error);
    }
  }, 2000);
}

// Function to force show upload interface when needed
function forceShowUploadInterface() {
  try {
    // Look for upload containers that might be hidden
    const uploadContainers = document.querySelectorAll('.logo-upload-container, .banner-upload-container');
    
    uploadContainers.forEach(container => {
      const uploadDiv = container.querySelector('.logo-upload, .banner-upload');
      const previewDiv = container.querySelector('.logo-preview, .banner-preview');
      
      if (uploadDiv && previewDiv) {
        // Check if preview is showing but there's no actual image
        const img = previewDiv.querySelector('img');
        
        if (img && (!img.src || img.src === '' || img.src.includes('undefined'))) {
          console.log('[FixBannerUploadInterface] Found empty preview, showing upload interface');
          
          previewDiv.style.display = 'none';
          uploadDiv.style.display = 'block';
          
          // Enable the file input
          const fileInput = uploadDiv.querySelector('input[type="file"]');
          if (fileInput) {
            fileInput.disabled = false;
            fileInput.value = '';
          }
        }
      }
    });
  } catch (error) {
    console.error('[FixBannerUploadInterface] Error forcing upload interface visibility:', error);
  }
}

// Function to handle file input changes to clear removal state
function handleFileInputChanges() {
  document.addEventListener('change', function(event) {
    const target = event.target;
    
    if (target && target.type === 'file' && (target.id.includes('logo') || target.id.includes('banner'))) {
      if (target.files && target.files.length > 0) {
        console.log('[FixBannerUploadInterface] New file selected, clearing removal state');
        
        // Clear the banner removal state
        localStorage.removeItem('banner_removal_state');
        
        if (window.safeBannerManager) {
          window.safeBannerManager.clear();
        }
      }
    }
  });
  
  console.log('[FixBannerUploadInterface] File input change handling set up');
}

// Function to periodically check and fix upload interface visibility
function periodicUploadInterfaceCheck() {
  setInterval(() => {
    // Check if banner was recently removed
    const bannerRemovalState = localStorage.getItem('banner_removal_state');
    let wasRecentlyRemoved = false;
    
    if (bannerRemovalState) {
      try {
        const state = JSON.parse(bannerRemovalState);
        wasRecentlyRemoved = state.isRemoved && (Date.now() - state.timestamp) < 60000; // 1 minute
      } catch (e) {
        // Ignore parsing errors
      }
    }
    
    if (wasRecentlyRemoved) {
      ensureUploadInterfaceVisible();
      forceShowUploadInterface();
    }
  }, 2000); // Check every 2 seconds
  
  console.log('[FixBannerUploadInterface] Periodic upload interface check started');
}

// Function to create upload interface if it doesn't exist
function createUploadInterfaceIfMissing() {
  try {
    // Look for containers that might be missing upload interface
    const containers = document.querySelectorAll('.logo-upload-container, .banner-upload-container');
    
    containers.forEach(container => {
      const uploadDiv = container.querySelector('.logo-upload, .banner-upload');
      const previewDiv = container.querySelector('.logo-preview, .banner-preview');
      
      // If we have a preview but no upload div, create one
      if (previewDiv && !uploadDiv) {
        console.log('[FixBannerUploadInterface] Creating missing upload interface');
        
        const uploadDiv = document.createElement('div');
        uploadDiv.className = 'logo-upload';
        uploadDiv.style.display = 'block';
        
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.id = 'logo-upload';
        fileInput.className = 'file-input';
        
        const label = document.createElement('label');
        label.htmlFor = 'logo-upload';
        label.className = 'file-input-label';
        label.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7"></path>
            <line x1="16" y1="5" x2="22" y2="5"></line>
            <line x1="19" y1="2" x2="19" y2="8"></line>
            <circle cx="9" cy="9" r="2"></circle>
            <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
          </svg>
          Upload Banner
        `;
        
        uploadDiv.appendChild(fileInput);
        uploadDiv.appendChild(label);
        container.appendChild(uploadDiv);
        
        console.log('[FixBannerUploadInterface] Upload interface created');
      }
    });
  } catch (error) {
    console.error('[FixBannerUploadInterface] Error creating upload interface:', error);
  }
}

// Function to apply all fixes
function applyFixes() {
  try {
    ensureUploadInterfaceVisible();
    monitorBannerRemovalEvents();
    patchReactStateUpdates();
    handleFileInputChanges();
    periodicUploadInterfaceCheck();
    createUploadInterfaceIfMissing();
    
    console.log('[FixBannerUploadInterface] All banner upload interface fixes applied');
  } catch (error) {
    console.error('[FixBannerUploadInterface] Error applying banner upload interface fixes:', error);
  }
}

// Apply fixes immediately
applyFixes();

// Apply fixes when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFixes);
} else {
  setTimeout(applyFixes, 100);
}

// Reapply fixes periodically
setTimeout(applyFixes, 2000);
setTimeout(applyFixes, 5000);

console.log('[FixBannerUploadInterface] Banner upload interface fix script loaded');
