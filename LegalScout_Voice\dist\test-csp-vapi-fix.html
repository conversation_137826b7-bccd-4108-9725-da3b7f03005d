<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Vapi Fix Test</title>
    <!-- Same CSP as main app -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: https://cdn.jsdelivr.net https://unpkg.com https://cdnjs.cloudflare.com https://cdn.vapi.ai https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com https://c.daily.co https://*.daily.co; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: blob: https:; media-src 'self' blob: data: https:; connect-src 'self' https://api.vapi.ai https://mcp.vapi.ai https://dashboard.vapi.ai https://utopqxsvudgrtiwenlzl.supabase.co https://c.daily.co https://*.daily.co https://o77906.ingest.sentry.io wss: ws:; frame-src 'self' https://c.daily.co https://*.daily.co; worker-src 'self' blob:; object-src 'none'; base-uri 'self';">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔒 CSP Vapi Fix Test</h1>
    
    <div class="test-container">
        <h2>Content Security Policy Test</h2>
        <p>This page tests if the CSP allows loading Vapi SDK from cdn.vapi.ai</p>
        <button onclick="testVapiCDNLoading()">Test Vapi CDN Loading</button>
        <button onclick="testOfficialPattern()">Test Official Pattern</button>
        <button onclick="checkCSPViolations()">Check CSP Violations</button>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>Console Logs</h2>
        <div id="console-logs"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('test-results');
        const logsDiv = document.getElementById('console-logs');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        function addLog(message) {
            const div = document.createElement('div');
            div.innerHTML = `<pre>${new Date().toISOString()}: ${message}</pre>`;
            logsDiv.appendChild(div);
            console.log(message);
        }

        // Monitor CSP violations
        document.addEventListener('securitypolicyviolation', (e) => {
            addLog(`CSP Violation: ${e.violatedDirective} - ${e.blockedURI}`);
            addResult(`❌ CSP Violation: ${e.violatedDirective} blocked ${e.blockedURI}`, 'error');
        });

        async function testVapiCDNLoading() {
            addResult('🧪 Testing Vapi CDN loading with CSP...', 'info');
            
            try {
                const script = document.createElement('script');
                script.src = `https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js?t=${Date.now()}`;
                
                const loadPromise = new Promise((resolve, reject) => {
                    script.onload = () => {
                        addLog('Vapi SDK loaded successfully from CDN');
                        if (window.Vapi) {
                            addResult('✅ Vapi SDK loaded and available from cdn.vapi.ai', 'success');
                            addLog(`Vapi constructor type: ${typeof window.Vapi}`);
                            resolve(true);
                        } else {
                            addResult('⚠️ Script loaded but Vapi not available', 'error');
                            reject(new Error('Vapi not available after script load'));
                        }
                    };
                    
                    script.onerror = () => {
                        addResult('❌ Failed to load Vapi SDK from CDN', 'error');
                        reject(new Error('Script failed to load'));
                    };
                });
                
                document.head.appendChild(script);
                return await loadPromise;
                
            } catch (error) {
                addLog(`Error: ${error.message}`);
                addResult(`❌ Vapi CDN loading failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testOfficialPattern() {
            addResult('🧪 Testing official Vapi pattern...', 'info');
            
            try {
                // Ensure SDK is loaded first
                const sdkLoaded = await testVapiCDNLoading();
                if (!sdkLoaded) {
                    throw new Error('Vapi SDK not loaded');
                }

                // Wait a moment for SDK to initialize
                await new Promise(resolve => setTimeout(resolve, 1000));

                const apiKey = '310f0d43-27c2-47a5-a76d-e55171d024f7'; // Public key
                addLog(`Testing official pattern: new Vapi("${apiKey.substring(0, 8)}...")`);
                
                // Test official pattern: new Vapi(apiKey)
                const vapi = new window.Vapi(apiKey);
                
                if (vapi) {
                    addResult('✅ Official Vapi pattern works: new Vapi(apiKey)', 'success');
                    addLog(`Vapi instance methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(vapi)).join(', ')}`);
                    
                    // Test that required methods exist
                    const requiredMethods = ['start', 'stop', 'on'];
                    const missingMethods = requiredMethods.filter(method => typeof vapi[method] !== 'function');
                    
                    if (missingMethods.length === 0) {
                        addResult('✅ All required methods available on Vapi instance', 'success');
                        
                        // Test event listener setup
                        try {
                            vapi.on('error', (error) => {
                                addLog(`Vapi error event: ${error.message}`);
                            });
                            addResult('✅ Event listeners can be set up', 'success');
                        } catch (eventError) {
                            addResult(`⚠️ Event listener setup failed: ${eventError.message}`, 'error');
                        }
                        
                        return vapi;
                    } else {
                        addResult(`❌ Missing required methods: ${missingMethods.join(', ')}`, 'error');
                        return null;
                    }
                } else {
                    addResult('❌ Failed to create Vapi instance', 'error');
                    return null;
                }
                
            } catch (error) {
                addLog(`Error: ${error.message}`);
                addResult(`❌ Official pattern test failed: ${error.message}`, 'error');
                return null;
            }
        }

        function checkCSPViolations() {
            addResult('🧪 Checking for CSP violations...', 'info');
            
            // Check if there are any CSP violation logs
            const violationLogs = Array.from(logsDiv.children).filter(child => 
                child.textContent.includes('CSP Violation')
            );
            
            if (violationLogs.length === 0) {
                addResult('✅ No CSP violations detected', 'success');
            } else {
                addResult(`❌ Found ${violationLogs.length} CSP violations`, 'error');
                violationLogs.forEach(log => {
                    addResult(`CSP Violation: ${log.textContent}`, 'error');
                });
            }
            
            // Test if we can access Vapi domains
            const vapiDomains = [
                'https://api.vapi.ai',
                'https://cdn.vapi.ai',
                'https://mcp.vapi.ai',
                'https://dashboard.vapi.ai'
            ];
            
            addLog('Testing access to Vapi domains...');
            vapiDomains.forEach(domain => {
                try {
                    // Test if domain is allowed in CSP
                    const testUrl = new URL('/test', domain);
                    addLog(`✅ Can construct URL for: ${domain}`);
                } catch (error) {
                    addLog(`❌ Cannot access: ${domain} - ${error.message}`);
                }
            });
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            addResult('🔒 CSP Vapi Fix Test Page Loaded', 'info');
            addLog('Test page initialized with CSP');
            
            // Show current CSP
            const cspMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
            if (cspMeta) {
                addLog(`Current CSP: ${cspMeta.content.substring(0, 200)}...`);
            }
        });
    </script>
</body>
</html>
