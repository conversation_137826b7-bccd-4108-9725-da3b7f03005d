# Dashboard Integration Example

This document provides an example of how to integrate the enhanced Vapi components into the dashboard while keeping the original components for reference.

## Overview

The enhanced Vapi components provide a more modern and consistent UI for voice interactions with Vapi assistants. They use the `use-vapi` hook for consistent functionality and are designed to be drop-in replacements for the existing components.

## Integration Steps

### 1. Import the Enhanced Components

```jsx
// In your dashboard component
import EnhancedPreviewTab from '../components/dashboard/EnhancedPreviewTab';
import { useState } from 'react';
```

### 2. Add State for Component Selection

```jsx
// Add state to toggle between original and enhanced components
const [useEnhancedComponents, setUseEnhancedComponents] = useState(false);
```

### 3. Add a Toggle Button

```jsx
<div className="component-toggle">
  <button 
    className={`toggle-button ${useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(true)}
  >
    Use Enhanced Components
  </button>
  <button 
    className={`toggle-button ${!useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(false)}
  >
    Use Original Components
  </button>
</div>
```

### 4. Conditionally Render Components

```jsx
// In your renderTabContent function
const renderTabContent = () => {
  switch (activeTab) {
    case 'preview':
      return useEnhancedComponents ? (
        <EnhancedPreviewTab 
          attorney={attorney}
          isDarkTheme={isDarkTheme}
          onToggleTheme={toggleTheme}
        />
      ) : (
        <OriginalPreviewTab 
          attorney={attorney}
          isDarkTheme={isDarkTheme}
          onToggleTheme={toggleTheme}
        />
      );
    // Other tabs...
  }
};
```

### 5. Complete Example

Here's a complete example of how to integrate the enhanced components into the dashboard:

```jsx
import React, { useState } from 'react';
import EnhancedPreviewTab from '../components/dashboard/EnhancedPreviewTab';
import OriginalPreviewTab from '../components/dashboard/PreviewTab';

const Dashboard = ({ attorney }) => {
  const [activeTab, setActiveTab] = useState('preview');
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  const [useEnhancedComponents, setUseEnhancedComponents] = useState(false);

  const toggleTheme = () => {
    setIsDarkTheme(!isDarkTheme);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'preview':
        return useEnhancedComponents ? (
          <EnhancedPreviewTab 
            attorney={attorney}
            isDarkTheme={isDarkTheme}
            onToggleTheme={toggleTheme}
          />
        ) : (
          <OriginalPreviewTab 
            attorney={attorney}
            isDarkTheme={isDarkTheme}
            onToggleTheme={toggleTheme}
          />
        );
      // Other tabs...
      default:
        return null;
    }
  };

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>Attorney Dashboard</h1>
        <div className="component-toggle">
          <button 
            className={`toggle-button ${useEnhancedComponents ? 'active' : ''}`}
            onClick={() => setUseEnhancedComponents(true)}
          >
            Use Enhanced Components
          </button>
          <button 
            className={`toggle-button ${!useEnhancedComponents ? 'active' : ''}`}
            onClick={() => setUseEnhancedComponents(false)}
          >
            Use Original Components
          </button>
        </div>
      </div>
      <div className="dashboard-tabs">
        <button 
          className={`tab-button ${activeTab === 'preview' ? 'active' : ''}`}
          onClick={() => setActiveTab('preview')}
        >
          Preview
        </button>
        {/* Other tab buttons */}
      </div>
      <div className="dashboard-content">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default Dashboard;
```

## Agent Page Integration

Similarly, you can integrate the enhanced components into the agent page:

```jsx
import React, { useState } from 'react';
import EnhancedCallController from '../components/call/EnhancedCallController';
import OriginalCallController from '../components/CallController';

const AgentPage = ({ attorney }) => {
  const [useEnhancedComponents, setUseEnhancedComponents] = useState(false);

  return (
    <div className="agent-page">
      <div className="agent-header">
        <h1>{attorney.firm_name}</h1>
        <div className="component-toggle">
          <button 
            className={`toggle-button ${useEnhancedComponents ? 'active' : ''}`}
            onClick={() => setUseEnhancedComponents(true)}
          >
            Use Enhanced Components
          </button>
          <button 
            className={`toggle-button ${!useEnhancedComponents ? 'active' : ''}`}
            onClick={() => setUseEnhancedComponents(false)}
          >
            Use Original Components
          </button>
        </div>
      </div>
      <div className="agent-content">
        {useEnhancedComponents ? (
          <EnhancedCallController 
            assistantId={attorney.vapi_assistant_id}
            showTranscript={true}
            showVisualization={true}
          />
        ) : (
          <OriginalCallController 
            assistantId={attorney.vapi_assistant_id}
          />
        )}
      </div>
    </div>
  );
};

export default AgentPage;
```

## Styling

You can add the following CSS to style the component toggle:

```css
.component-toggle {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.toggle-button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background-color: var(--secondary-color, #6b7280);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button.active {
  background-color: var(--primary-color, #3b82f6);
}

.toggle-button:hover {
  opacity: 0.9;
}
```

## Next Steps

1. **Test the Integration**: Test the integration to ensure that both the original and enhanced components work as expected.

2. **Gather Feedback**: Gather feedback from users on the enhanced components to determine if they should be used by default.

3. **Phase Out Original Components**: Once the enhanced components have been thoroughly tested and approved, you can phase out the original components.
