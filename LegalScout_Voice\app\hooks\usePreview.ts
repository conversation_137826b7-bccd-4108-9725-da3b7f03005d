import { useState, useCallback } from 'react';
import { useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';

interface PreviewState {
  isOpen: boolean;
  isLoading: boolean;
  previewId: Id<"preview_instances"> | null;
  error: string | null;
}

interface UsePreviewOptions {
  onSuccess?: (previewId: Id<"preview_instances">) => void;
  onError?: (error: Error) => void;
}

export const usePreview = (options: UsePreviewOptions = {}) => {
  const [state, setState] = useState<PreviewState>({
    isOpen: false,
    isLoading: false,
    previewId: null,
    error: null,
  });

  const createPreview = useMutation(api.preview.createPreviewInstance);
  const updatePreview = useMutation(api.preview.updatePreviewCustomizations);
  const updateScrapedData = useMutation(api.preview.updateScrapedData);

  const openPreview = useCallback(async (initialData: {
    firmName: string;
    attorneyName: string;
    practiceAreas: string[];
    state: string;
    logoUrl?: string;
    backgroundColor: string;
    templateColors: {
      primary: string;
      secondary: string;
    };
    customInstructions?: string;
  }) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      const previewId = await createPreview(initialData);
      setState(prev => ({
        ...prev,
        isOpen: true,
        isLoading: false,
        previewId,
      }));
      options.onSuccess?.(previewId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create preview';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      if (error instanceof Error) {
        options.onError?.(error);
      }
    }
  }, [createPreview, options]);

  const closePreview = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false,
    }));
  }, []);

  const updateCustomizations = useCallback(async (updates: Parameters<typeof updatePreview>[0]['updates']) => {
    if (!state.previewId) return;
    
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      await updatePreview({
        id: state.previewId,
        updates,
      });
      setState(prev => ({ ...prev, isLoading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update preview';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      if (error instanceof Error) {
        options.onError?.(error);
      }
    }
  }, [state.previewId, updatePreview, options]);

  const updateScraping = useCallback(async (url: string, content: any[]) => {
    if (!state.previewId) return;
    
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      await updateScrapedData({
        id: state.previewId,
        url,
        content,
      });
      setState(prev => ({ ...prev, isLoading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update scraped data';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      if (error instanceof Error) {
        options.onError?.(error);
      }
    }
  }, [state.previewId, updateScrapedData, options]);

  return {
    ...state,
    openPreview,
    closePreview,
    updateCustomizations,
    updateScraping,
  };
}; 