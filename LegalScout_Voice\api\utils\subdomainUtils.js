/**
 * Server-side subdomain utilities for API routes
 * 
 * This file contains utilities for extracting subdomains from request headers
 * in serverless functions and API routes.
 */

/**
 * Extract subdomain from a hostname string
 * @param {string} hostname - The hostname to extract subdomain from
 * @returns {string} The subdomain or 'default' if none found
 */
function extractSubdomainFromHostname(hostname) {
  if (!hostname) return 'default';
  
  const parts = hostname.split('.');
  
  // Handle main domain cases (example.com or www.example.com)
  if (parts.length <= 2) {
    return 'default';
  }
  
  // Handle www subdomain as default
  if (parts[0] === 'www') {
    return 'default';
  }
  
  // Handle special subdomains that should be treated as default
  const defaultSubdomains = ['app', 'dashboard', 'api', 'mail', 'admin'];
  if (defaultSubdomains.includes(parts[0].toLowerCase())) {
    return 'default';
  }
  
  // Return the subdomain
  return parts[0].toLowerCase();
}

/**
 * Extract subdomain from request host header (server-side)
 * @param {string} host - The host header value from request
 * @returns {string} The subdomain or 'default' if none found
 */
export function extractSubdomainFromHost(host) {
  return extractSubdomainFromHostname(host);
}

/**
 * Check if a subdomain should be treated as an attorney subdomain
 * @param {string} subdomain - The subdomain to check
 * @returns {boolean} Whether this is an attorney subdomain
 */
export function isAttorneySubdomain(subdomain) {
  return subdomain !== 'default' &&
         subdomain !== 'www' &&
         subdomain !== '' &&
         subdomain !== null &&
         subdomain !== undefined;
}
