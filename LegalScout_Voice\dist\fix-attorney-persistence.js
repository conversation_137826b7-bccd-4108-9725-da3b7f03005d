/**
 * Attorney Persistence Fix
 *
 * This script ensures that attorney ID is properly maintained throughout the application.
 * It adds additional fallback mechanisms and ensures attorney data is properly stored
 * in localStorage for persistence across page refreshes.
 */

(function() {
  console.log('[AttorneyPersistenceFix] Initializing attorney persistence fix');

  // Function to get attorney data from localStorage
  const getStoredAttorney = () => {
    try {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        const parsedAttorney = JSON.parse(storedAttorney);

        // Handle the case where the attorney is an array or has numeric keys (multiple attorneys)
        if (Array.isArray(parsedAttorney) || (typeof parsedAttorney === 'object' && parsedAttorney !== null && Object.keys(parsedAttorney).some(key => !isNaN(parseInt(key))))) {
          console.log('[AttorneyPersistenceFix] Found multiple attorneys in localStorage, normalizing');

          // If it's an array, use the first element
          if (Array.isArray(parsedAttorney)) {
            return parsedAttorney[0];
          }

          // If it has numeric keys, use the first one
          const numericKeys = Object.keys(parsedAttorney).filter(key => !isNaN(parseInt(key)));
          if (numericKeys.length > 0) {
            // Create a new object with only the non-numeric properties and the first attorney's properties
            const firstAttorney = parsedAttorney[numericKeys[0]];
            const normalizedAttorney = { ...firstAttorney };

            // Copy over any non-numeric properties
            Object.keys(parsedAttorney).forEach(key => {
              if (isNaN(parseInt(key))) {
                normalizedAttorney[key] = parsedAttorney[key];
              }
            });

            return normalizedAttorney;
          }
        }

        return parsedAttorney;
      }
    } catch (error) {
      console.error('[AttorneyPersistenceFix] Error parsing stored attorney:', error);
    }
    return null;
  };

  // Function to store attorney data in localStorage
  const storeAttorney = (attorney) => {
    if (attorney) {
      try {
        // Handle the case where the attorney is an array or has numeric keys (multiple attorneys)
        let normalizedAttorney = attorney;

        if (Array.isArray(attorney)) {
          console.log('[AttorneyPersistenceFix] Normalizing attorney array before storing');
          normalizedAttorney = attorney[0];
        } else if (typeof attorney === 'object' && attorney !== null && Object.keys(attorney).some(key => !isNaN(parseInt(key)))) {
          console.log('[AttorneyPersistenceFix] Normalizing attorney object with numeric keys before storing');

          // Get the first numeric key
          const numericKeys = Object.keys(attorney).filter(key => !isNaN(parseInt(key)));
          if (numericKeys.length > 0) {
            // Create a new object with only the non-numeric properties and the first attorney's properties
            const firstAttorney = attorney[numericKeys[0]];
            normalizedAttorney = { ...firstAttorney };

            // Copy over any non-numeric properties
            Object.keys(attorney).forEach(key => {
              if (isNaN(parseInt(key))) {
                normalizedAttorney[key] = attorney[key];
              }
            });
          }
        }

        // Ensure the attorney has an ID
        if (!normalizedAttorney.id) {
          const userId = getAttorneyIdFromAuth();
          if (userId) {
            normalizedAttorney.id = userId;
            console.log('[AttorneyPersistenceFix] Added ID to attorney:', userId);

            // Also store the ID separately for redundancy
            localStorage.setItem('attorney_id', userId);
          }
        } else {
          // Store the ID separately for redundancy
          localStorage.setItem('attorney_id', normalizedAttorney.id);
        }

        localStorage.setItem('attorney', JSON.stringify(normalizedAttorney));
        console.log('[AttorneyPersistenceFix] Stored attorney in localStorage:', normalizedAttorney.id);
      } catch (error) {
        console.error('[AttorneyPersistenceFix] Error storing attorney:', error);
      }
    }
  };

  // Function to get attorney ID from Supabase auth data
  const getAttorneyIdFromAuth = () => {
    try {
      const authData = localStorage.getItem('supabase.auth.token');
      if (authData) {
        const parsedData = JSON.parse(authData);
        const userId = parsedData?.currentSession?.user?.id;

        if (userId) {
          // Store the user ID as the attorney ID for future use
          localStorage.setItem('attorney_id', userId);
          console.log('[AttorneyPersistenceFix] Stored attorney ID in localStorage:', userId);
          return userId;
        }
      }
    } catch (error) {
      console.error('[AttorneyPersistenceFix] Error getting user ID from localStorage:', error);
    }

    // Try to get the attorney ID from the dedicated storage
    try {
      const storedId = localStorage.getItem('attorney_id');
      if (storedId) {
        console.log('[AttorneyPersistenceFix] Retrieved attorney ID from dedicated storage:', storedId);
        return storedId;
      }
    } catch (error) {
      console.error('[AttorneyPersistenceFix] Error getting attorney ID from dedicated storage:', error);
    }

    return null;
  };

  // Function to create a fallback attorney object
  const createFallbackAttorney = (userId) => {
    if (!userId) return null;

    return {
      id: userId, // Use the user ID as the attorney ID
      name: 'Default Attorney',
      email: '<EMAIL>',
      firm_name: 'Default Law Firm',
      user_id: userId,
      subdomain: 'default',
      fallback: true,
      is_fallback: true
    };
  };

  // Monitor for attorney state changes
  const originalSetItem = localStorage.setItem;
  localStorage.setItem = function(key, value) {
    // Call the original function first
    originalSetItem.call(this, key, value);

    // If this is an attorney update, ensure it has an ID
    if (key === 'attorney') {
      try {
        const attorney = JSON.parse(value);
        if (!attorney.id) {
          console.log('[AttorneyPersistenceFix] Attorney missing ID, attempting to fix');

          // Try to get ID from auth
          const userId = getAttorneyIdFromAuth();
          if (userId) {
            attorney.id = userId;
            console.log('[AttorneyPersistenceFix] Added ID to attorney:', userId);

            // Store the fixed attorney
            originalSetItem.call(localStorage, 'attorney', JSON.stringify(attorney));
          }
        }
      } catch (error) {
        console.error('[AttorneyPersistenceFix] Error processing attorney update:', error);
      }
    }

    // If this is an auth token update, check if we need to update attorney
    if (key === 'supabase.auth.token') {
      try {
        const authData = JSON.parse(value);
        const userId = authData?.currentSession?.user?.id;

        if (userId) {
          // Check if we have an attorney with this ID
          const storedAttorney = getStoredAttorney();

          if (!storedAttorney || !storedAttorney.id) {
            console.log('[AttorneyPersistenceFix] Creating fallback attorney for user:', userId);
            const fallbackAttorney = createFallbackAttorney(userId);
            storeAttorney(fallbackAttorney);
          } else if (storedAttorney.id !== userId) {
            console.log('[AttorneyPersistenceFix] Updating attorney ID to match user ID');
            storedAttorney.id = userId;
            storedAttorney.user_id = userId;
            storeAttorney(storedAttorney);
          }
        }
      } catch (error) {
        console.error('[AttorneyPersistenceFix] Error processing auth update:', error);
      }
    }
  };

  // Patch window.fetch to intercept attorney-related requests
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // For attorney profile updates, ensure ID is included
    if (typeof url === 'string' && url.includes('/api/attorneys/') && options.method === 'PUT') {
      try {
        const body = JSON.parse(options.body || '{}');

        if (!body.id) {
          console.log('[AttorneyPersistenceFix] Attorney update missing ID, attempting to fix');

          // Try to get ID from stored attorney
          const storedAttorney = getStoredAttorney();
          if (storedAttorney && storedAttorney.id) {
            body.id = storedAttorney.id;
            console.log('[AttorneyPersistenceFix] Added ID to attorney update:', storedAttorney.id);

            // Update the request body
            options.body = JSON.stringify(body);
          }
        }
      } catch (error) {
        console.error('[AttorneyPersistenceFix] Error processing attorney update request:', error);
      }
    }

    return originalFetch(url, options);
  };

  // Initialize by checking if we need to create a fallback attorney
  const initializeFallbackAttorney = () => {
    const storedAttorney = getStoredAttorney();

    if (!storedAttorney || !storedAttorney.id) {
      console.log('[AttorneyPersistenceFix] No valid attorney found, checking for user ID');

      const userId = getAttorneyIdFromAuth();
      if (userId) {
        console.log('[AttorneyPersistenceFix] Creating fallback attorney for user:', userId);
        const fallbackAttorney = createFallbackAttorney(userId);
        storeAttorney(fallbackAttorney);
      }
    }
  };

  // Run initialization
  initializeFallbackAttorney();

  // Add a global function to fix attorney ID issues
  window.fixAttorneyId = function() {
    console.log('[AttorneyPersistenceFix] Manual attorney ID fix triggered');

    const userId = getAttorneyIdFromAuth();
    if (userId) {
      const storedAttorney = getStoredAttorney();

      if (storedAttorney) {
        storedAttorney.id = userId;
        storedAttorney.user_id = userId;
        storeAttorney(storedAttorney);
        console.log('[AttorneyPersistenceFix] Updated attorney ID to:', userId);
      } else {
        const fallbackAttorney = createFallbackAttorney(userId);
        storeAttorney(fallbackAttorney);
        console.log('[AttorneyPersistenceFix] Created fallback attorney with ID:', userId);
      }

      return true;
    }

    console.log('[AttorneyPersistenceFix] Could not fix attorney ID - no user ID found');
    return false;
  };

  console.log('[AttorneyPersistenceFix] Attorney persistence fix initialized');
})();
