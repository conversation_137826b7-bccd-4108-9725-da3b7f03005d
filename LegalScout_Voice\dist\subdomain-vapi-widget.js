/**
 * Vapi Widget for Attorney Subdomains
 * 
 * This script implements the official Vapi HTML script tag pattern
 * for attorney subdomain pages. It should be included in public-facing
 * attorney websites to provide voice call functionality.
 * 
 * Usage:
 * <script src="/subdomain-vapi-widget.js"></script>
 * <script>
 *   initializeVapiWidget({
 *     subdomain: 'attorney-subdomain',
 *     assistantId: 'assistant-id-override', // optional
 *     customConfig: { ... } // optional
 *   });
 * </script>
 */

(function(window, document) {
  'use strict';

  // Default configuration
  const DEFAULT_CONFIG = {
    apiKey: "6734febc-fc65-4669-93b0-929b31ff6564", // Public API key
    assistantId: "f9b97d13-f9c4-40af-a660-62ba5925ff2a", // Default assistant
    buttonConfig: {
      position: "bottom-right",
      offset: "40px",
      width: "50px",
      height: "50px",
      idle: {
        color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
        type: "pill",
        title: "Have a legal question?",
        subtitle: "Talk with our AI assistant",
        icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone.svg`,
      },
      loading: {
        color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
        type: "pill",
        title: "Connecting...",
        subtitle: "Please wait",
        icon: `https://unpkg.com/lucide-static@0.321.0/icons/loader-2.svg`,
      },
      active: {
        color: `linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%)`,
        type: "pill",
        title: "Call is in progress...",
        subtitle: "End the call.",
        icon: `https://unpkg.com/lucide-static@0.321.0/icons/phone-off.svg`,
      },
    }
  };

  // Global variables
  let vapiInstance = null;
  let isInitialized = false;

  /**
   * Load attorney configuration from Supabase
   */
  async function loadAttorneyConfig(subdomain) {
    try {
      console.log(`[VapiWidget] Loading config for subdomain: ${subdomain}`);
      
      // This would typically fetch from your API
      // For now, return default config
      return {
        assistantId: DEFAULT_CONFIG.assistantId,
        customInstructions: {
          firmName: subdomain.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
          welcomeMessage: `Hello! I'm Scout, the AI assistant for ${subdomain}. How can I help you today?`
        }
      };
    } catch (error) {
      console.error('[VapiWidget] Error loading attorney config:', error);
      return null;
    }
  }

  /**
   * Initialize the Vapi widget
   */
  function initializeVapiWidget(options = {}) {
    if (isInitialized) {
      console.warn('[VapiWidget] Widget already initialized');
      return;
    }

    const config = {
      ...DEFAULT_CONFIG,
      ...options
    };

    console.log('[VapiWidget] Initializing widget with config:', config);

    // Load the official Vapi HTML script tag SDK
    (function (d, t) {
      var g = document.createElement(t),
        s = d.getElementsByTagName(t)[0];
      g.src = "https://cdn.jsdelivr.net/gh/VapiAI/html-script-tag@latest/dist/assets/index.js";
      g.defer = true;
      g.async = true;
      s.parentNode.insertBefore(g, s);

      g.onload = async function () {
        console.log('[VapiWidget] Official Vapi SDK loaded successfully');
        
        try {
          // Load attorney-specific configuration if subdomain provided
          let assistantId = config.assistantId;
          let buttonConfig = config.buttonConfig;

          if (config.subdomain && config.subdomain !== 'default') {
            const attorneyConfig = await loadAttorneyConfig(config.subdomain);
            if (attorneyConfig) {
              assistantId = attorneyConfig.assistantId || assistantId;
              
              // Customize button config with attorney info
              if (attorneyConfig.customInstructions) {
                buttonConfig.idle.title = `Have a question for ${attorneyConfig.customInstructions.firmName}?`;
                buttonConfig.idle.subtitle = attorneyConfig.customInstructions.welcomeMessage;
              }
            }
          }

          // Merge custom config if provided
          if (config.customConfig) {
            buttonConfig = { ...buttonConfig, ...config.customConfig };
          }

          console.log('[VapiWidget] Creating Vapi instance with assistant:', assistantId);

          // Create the Vapi instance using the official pattern
          vapiInstance = window.vapiSDK.run({
            apiKey: config.apiKey,
            assistant: assistantId,
            config: buttonConfig,
          });

          if (vapiInstance) {
            console.log('[VapiWidget] Vapi widget initialized successfully');
            isInitialized = true;

            // Set up event listeners if available
            if (vapiInstance.on && typeof vapiInstance.on === 'function') {
              vapiInstance.on('call-start', () => {
                console.log('[VapiWidget] Call started');
                // Notify parent window if in iframe
                if (window.parent !== window) {
                  window.parent.postMessage({ type: 'vapi-call-start' }, '*');
                }
              });

              vapiInstance.on('call-end', () => {
                console.log('[VapiWidget] Call ended');
                // Notify parent window if in iframe
                if (window.parent !== window) {
                  window.parent.postMessage({ type: 'vapi-call-end' }, '*');
                }
              });

              vapiInstance.on('error', (error) => {
                console.error('[VapiWidget] Call error:', error);
                // Notify parent window if in iframe
                if (window.parent !== window) {
                  window.parent.postMessage({ type: 'vapi-call-error', error: error.message }, '*');
                }
              });
            }

            // Expose instance globally for debugging
            window.vapiWidgetInstance = vapiInstance;

          } else {
            console.error('[VapiWidget] Failed to create Vapi instance');
          }
        } catch (error) {
          console.error('[VapiWidget] Error initializing widget:', error);
        }
      };

      g.onerror = function() {
        console.error('[VapiWidget] Failed to load official Vapi SDK');
      };
    })(document, "script");
  }

  /**
   * Get the current Vapi instance
   */
  function getVapiInstance() {
    return vapiInstance;
  }

  /**
   * Check if widget is initialized
   */
  function isWidgetInitialized() {
    return isInitialized;
  }

  // Expose functions globally
  window.initializeVapiWidget = initializeVapiWidget;
  window.getVapiInstance = getVapiInstance;
  window.isVapiWidgetInitialized = isWidgetInitialized;

  // Auto-initialize if data attributes are present
  document.addEventListener('DOMContentLoaded', function() {
    const scriptTag = document.querySelector('script[data-vapi-subdomain]');
    if (scriptTag) {
      const subdomain = scriptTag.getAttribute('data-vapi-subdomain');
      const assistantId = scriptTag.getAttribute('data-vapi-assistant-id');
      
      initializeVapiWidget({
        subdomain: subdomain,
        assistantId: assistantId
      });
    }
  });

})(window, document);
