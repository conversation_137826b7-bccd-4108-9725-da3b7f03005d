# LegalScout Voice Implementation Guide

## 🎯 Current Implementation Status (June 3, 2025)

**MVP Status**: ✅ **PRODUCTION READY**

### Critical Issues Resolved
- ✅ **Duplicate Assistant Prevention**: Implemented creation guards and cooldown periods
- ✅ **Profile Sync Stability**: Fixed attorney profile loading and consolidation
- ✅ **Authentication Flow**: Corrected routing to dashboard after login
- ✅ **System Architecture**: Implemented one-way sync pattern (UI → Supabase → Vapi)

### Current Production Configuration
- **Primary Attorney**: <EMAIL> (ID: 571390ac-5a83-46b2-ad3a-18b9cf39d701)
- **Vapi Assistant**: f9b97d13-f9c4-40af-a660-62ba5925ff2a (LegalScout Assistant)
- **Voice Configuration**: 11labs/sarah
- **Sync Pattern**: Manual triggers only, no auto-sync during initialization

## Implementation Guidelines

### 1. Message Display Improvements

#### Message Chunking and Animation
```javascript
// Update handleMessage in CallController.jsx
const handleMessage = (event) => {
  if (event.type === 'model-output') {
    // Accumulate chunks until punctuation or sufficient length
    setTypingMessage(prev => {
      const content = prev ? prev.content + event.output : event.output;
      // Only update if we have a complete thought or sufficient length
      if (content.match(/[.!?]$/) || content.length > 50) {
        return {
          type: 'assistant',
          content,
          id: Date.now()
        };
      }
      return prev;
    });
  }
  // ... other message handling
};
```

#### Styling Updates
```css
.conversation-messages {
  height: 70vh;
  overflow-y: auto;
  padding: 20px;
  background: transparent;
  scroll-behavior: smooth;
}

.message {
  margin: 8px 0;
  opacity: 0.9;
  transition: opacity 0.3s;
}

.message:hover {
  opacity: 1;
}

.message-content {
  background: none;
  border: none;
  color: #fff;
  font-size: 1.1em;
  line-height: 1.5;
}

.typewriter-text {
  white-space: pre-wrap;
}
```

### 2. End Call Button Fix
```css
.vapi-end-call-button {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  /* Other existing styles */
}
```

### 3. MapDossierView Ref Fix
```javascript
// Update MapDossierView.jsx
const MapDossierView = React.forwardRef((props, ref) => {
  // Component implementation
});

export default MapDossierView;

// Update CallController.jsx
const mapRef = useRef(null);
// ...
<MapDossierView
  ref={mapRef}
  caseLocation={caseData.location}
  attorneys={attorneyMatches}
  isSearching={isSearchingAttorneys}
/>
```

### 4. Vercel Deployment Setup

#### Basic Authentication Implementation
```javascript
// pages/api/auth.js
export default function handler(req, res) {
  const { password } = req.body;
  const validPasswords = ['preview2024', 'legalscout2024']; // Store securely in env

  if (validPasswords.includes(password)) {
    res.status(200).json({ token: 'temporary-auth-token' });
  } else {
    res.status(401).json({ error: 'Invalid password' });
  }
}

// Add to _app.js or similar
const ProtectedRoute = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // Authentication logic
  
  if (!isAuthenticated) {
    return <LoginPage />;
  }
  
  return children;
};
```

### 5. Message Handling Optimization

#### Consolidate Model Output
```javascript
const consolidateModelOutput = (chunks) => {
  return chunks.reduce((acc, chunk) => {
    if (chunk.type === 'model-output') {
      return acc + chunk.output;
    }
    return acc;
  }, '');
};

// Update message handling
useEffect(() => {
  let outputBuffer = '';
  let bufferTimeout;

  const handleMessage = (event) => {
    if (event.type === 'model-output') {
      outputBuffer += event.output;
      
      clearTimeout(bufferTimeout);
      bufferTimeout = setTimeout(() => {
        if (outputBuffer) {
          setMessages(prev => [...prev, {
            type: 'assistant',
            content: outputBuffer,
            id: Date.now()
          }]);
          outputBuffer = '';
        }
      }, 100); // Adjust timing as needed
    }
    // ... handle other message types
  };

  vapiClient.on('message', handleMessage);
  return () => {
    vapiClient.off('message', handleMessage);
    clearTimeout(bufferTimeout);
  };
}, [vapiClient]);
```

## Next Steps

1. Implement the message display improvements first, as they affect the core user experience
2. Fix the end call button and ref warning as they are quick wins
3. Set up Vercel deployment with basic authentication
4. Optimize message handling and chunking
5. Polish UI and clean up code

Remember to test each change thoroughly, especially the message handling improvements, as they affect the core functionality of the application. 