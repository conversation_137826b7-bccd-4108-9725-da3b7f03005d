/**
 * Patch for LayoutGroupContext.mjs
 * 
 * This script specifically targets the LayoutGroupContext.mjs file that's causing the error
 * by intercepting script loading and patching the content.
 */

(function() {
  console.log('[Patcher] Setting up LayoutGroupContext patcher');

  // Override the fetch function to intercept requests for LayoutGroupContext.mjs
  const originalFetch = window.fetch;
  window.fetch = function(url, options) {
    if (url && typeof url === 'string' && url.includes('LayoutGroupContext.mjs')) {
      console.log('[Patcher] Intercepted fetch for LayoutGroupContext.mjs');
      
      // Return a mock response
      return Promise.resolve({
        ok: true,
        status: 200,
        text: function() {
          return Promise.resolve(`
            // Mock implementation of LayoutGroupContext.mjs
            import React from 'react';
            
            // Use a simple object instead of React.createContext
            const LayoutGroupContext = {
              Provider: function(props) { return props.children || null; },
              Consumer: function(props) { return props.children ? props.children({}) : null; }
            };
            
            export { LayoutGroupContext };
            export default LayoutGroupContext;
          `);
        }
      });
    }
    
    // Otherwise, use the original fetch
    return originalFetch.apply(this, arguments);
  };

  // Override the XMLHttpRequest.prototype.open method to intercept requests
  const originalOpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
    if (url && typeof url === 'string' && url.includes('LayoutGroupContext.mjs')) {
      console.log('[Patcher] Intercepted XMLHttpRequest for LayoutGroupContext.mjs');
      
      // Modify the URL to point to a non-existent resource
      url = '/empty.js';
    }
    
    // Call the original open method
    return originalOpen.call(this, method, url, async, user, password);
  };

  // Create an empty.js file in memory
  const blob = new Blob([`
    // Empty file
    console.log('[Patcher] Using empty.js instead of LayoutGroupContext.mjs');
  `], { type: 'application/javascript' });
  
  // Create a URL for the blob
  const emptyJsUrl = URL.createObjectURL(blob);
  
  // Override the createElement method to intercept script tags
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.call(document, tagName);
    
    if (tagName.toLowerCase() === 'script') {
      const originalSetAttribute = element.setAttribute;
      element.setAttribute = function(name, value) {
        if (name === 'src' && value && value.includes('LayoutGroupContext.mjs')) {
          console.log('[Patcher] Intercepted script src for LayoutGroupContext.mjs');
          
          // Replace the src with our empty.js URL
          return originalSetAttribute.call(this, name, emptyJsUrl);
        }
        
        // Otherwise, use the original setAttribute
        return originalSetAttribute.apply(this, arguments);
      };
    }
    
    return element;
  };

  console.log('[Patcher] LayoutGroupContext patcher setup complete');
})();
