{"version": 3, "file": "syncTools-d677e351.js", "sources": ["../../src/services/syncTools.js"], "sourcesContent": ["/**\r\n * Synchronization Tools\r\n *\r\n * This file contains the core synchronization tools for the application.\r\n */\r\n\r\nimport {\r\n  fetchFromSupabase,\r\n  updateSupabaseAttorney,\r\n  getAttorneyByEmail,\r\n  getAttorneyByAuthId,\r\n  getAttorneyById,\r\n  createAttorney,\r\n  update<PERSON><PERSON>rney,\r\n  fetchFromVapi,\r\n  createVapiAssistant,\r\n  updateVapiAssistant,\r\n  getVapiAssistant,\r\n  findProfileDiscrepancies,\r\n  getValidVoicesForProvider,\r\n  ensureProfilePersistence\r\n} from './syncHelpers.js';\r\n\r\n/**\r\n * Synchronize attorney profile data between Supabase and Vapi\r\n *\r\n * This function ensures that the attorney's profile data is consistent\r\n * between Supabase and Vapi. It checks for discrepancies and updates\r\n * Vapi to match Supabase (which is considered the source of truth).\r\n *\r\n * @param {Object} params - The parameters for the function\r\n * @param {string} params.attorneyId - The ID of the attorney to synchronize\r\n * @param {boolean} [params.forceUpdate=false] - Whether to force an update even if no discrepancies are found\r\n * @returns {Object} The result of the synchronization\r\n */\r\nexport const syncAttorneyProfile = async (params) => {\r\n  const { attorneyId, forceUpdate = false } = params;\r\n\r\n  console.log(`Syncing attorney profile for ${attorneyId}, forceUpdate: ${forceUpdate}`);\r\n\r\n  try {\r\n    // Use the enhanced profile persistence function\r\n    const result = await ensureProfilePersistence({\r\n      attorneyId,\r\n      forceUpdate\r\n    });\r\n\r\n    // Log the result\r\n    console.log(`Profile persistence result for ${attorneyId}:`, {\r\n      action: result.action,\r\n      success: result.success,\r\n      sources: result.sources\r\n    });\r\n\r\n    // Return a compatible result format\r\n    return {\r\n      action: result.action,\r\n      assistantId: result.vapiResult?.assistantId,\r\n      message: result.message,\r\n      success: result.success,\r\n      sources: result.sources,\r\n      discrepancies: result.vapiResult?.discrepancies || null\r\n    };\r\n  } catch (error) {\r\n    console.error(`Error syncing attorney profile for ${attorneyId}:`, error);\r\n    return {\r\n      action: \"error\",\r\n      error: error.message,\r\n      message: `Error syncing attorney profile: ${error.message}`\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Manage authentication state across systems\r\n *\r\n * This function handles authentication state management, ensuring that\r\n * when a user logs in, logs out, or refreshes their session, all systems\r\n * (Supabase, application state, Vapi) are properly synchronized.\r\n *\r\n * @param {Object} params - The parameters for the function\r\n * @param {Object} params.authData - Authentication data\r\n * @param {string} params.action - Authentication action (login, logout, refresh)\r\n * @returns {Object} The result of the authentication state management\r\n */\r\nexport const manageAuthState = async (params) => {\r\n  const { authData, action } = params;\r\n\r\n  console.log(`Managing auth state for action: ${action}`);\r\n\r\n  try {\r\n    switch (action) {\r\n      case \"login\":\r\n        // Handle login (OAuth or email/password)\r\n        const { user, session } = authData;\r\n\r\n        if (!user || !user.email) {\r\n          return {\r\n            action: \"login\",\r\n            success: false,\r\n            message: \"Invalid user data provided for login\"\r\n          };\r\n        }\r\n\r\n        // Check if attorney record exists\r\n        let attorney = await getAttorneyByEmail(user.email);\r\n\r\n        if (!attorney) {\r\n          // Create new attorney record\r\n          console.log(`Creating new attorney record for ${user.email}`);\r\n          attorney = await createAttorney({\r\n            email: user.email,\r\n            name: user.user_metadata?.name || user.email.split('@')[0],\r\n            user_id: user.id\r\n          });\r\n        } else if (!attorney.user_id) {\r\n          // Update existing attorney with user ID\r\n          console.log(`Updating existing attorney record with user_id for ${user.email}`);\r\n          attorney = await updateAttorney(attorney.id, {\r\n            user_id: user.id\r\n          });\r\n        }\r\n\r\n        // Ensure attorney profile persistence\r\n        console.log(`Ensuring attorney ${attorney.id} profile persistence`);\r\n        const syncResult = await ensureProfilePersistence({\r\n          attorneyId: attorney.id,\r\n          localData: attorney,\r\n          forceUpdate: true\r\n        });\r\n\r\n        return {\r\n          action: \"login\",\r\n          success: true,\r\n          attorney,\r\n          syncResult,\r\n          session,\r\n          message: \"Authentication state synchronized\"\r\n        };\r\n\r\n      case \"logout\":\r\n        // Handle logout\r\n        // No specific sync needed for logout\r\n        return {\r\n          action: \"logout\",\r\n          success: true,\r\n          message: \"User logged out successfully\"\r\n        };\r\n\r\n      case \"refresh\":\r\n        // Handle session refresh\r\n        if (!authData.user) {\r\n          return {\r\n            action: \"refresh\",\r\n            success: false,\r\n            message: \"No user data provided for refresh\"\r\n          };\r\n        }\r\n\r\n        // Get attorney data\r\n        const refreshedAttorney = await getAttorneyByAuthId(authData.user.id);\r\n\r\n        if (!refreshedAttorney) {\r\n          return {\r\n            action: \"refresh\",\r\n            success: false,\r\n            message: \"Attorney record not found for this auth ID\"\r\n          };\r\n        }\r\n\r\n        // Ensure profile persistence on refresh\r\n        console.log(`Ensuring profile persistence for attorney ${refreshedAttorney.id} on refresh`);\r\n        const refreshSyncResult = await ensureProfilePersistence({\r\n          attorneyId: refreshedAttorney.id,\r\n          localData: refreshedAttorney\r\n        });\r\n\r\n        return {\r\n          action: \"refresh\",\r\n          success: true,\r\n          attorney: refreshedAttorney,\r\n          syncResult: refreshSyncResult,\r\n          message: \"Authentication state refreshed and synchronized\"\r\n        };\r\n\r\n      default:\r\n        return {\r\n          action: \"unknown\",\r\n          success: false,\r\n          message: `Unknown action: ${action}`\r\n        };\r\n    }\r\n  } catch (error) {\r\n    console.error(`Error managing auth state for action ${action}:`, error);\r\n    return {\r\n      action,\r\n      success: false,\r\n      error: error.message,\r\n      message: `Error managing auth state: ${error.message}`\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Validate configuration before updates\r\n *\r\n * This function validates the configuration data before it is saved to Supabase.\r\n * It checks for required fields and ensures that the data is valid.\r\n *\r\n * @param {Object} params - The parameters for the function\r\n * @param {string} params.attorneyId - The ID of the attorney\r\n * @param {Object} params.configData - The configuration data to validate\r\n * @returns {Object} The validation result\r\n */\r\nexport const validateConfiguration = async (params) => {\r\n  const { attorneyId, configData } = params;\r\n\r\n  console.log(`Validating configuration for attorney ${attorneyId}`);\r\n  console.log('Config data:', configData);\r\n\r\n  try {\r\n    // Get current attorney data\r\n    const attorney = await getAttorneyById(attorneyId);\r\n\r\n    if (!attorney) {\r\n      return {\r\n        valid: false,\r\n        errors: [\"Attorney not found\"],\r\n        message: \"Cannot validate configuration for non-existent attorney\"\r\n      };\r\n    }\r\n\r\n    // Define required fields for different sections\r\n    const requiredFields = {\r\n      profile: [\"name\", \"email\"],\r\n      appearance: [\"firm_name\"],\r\n      agent: [\"welcome_message\", \"vapi_instructions\"],\r\n      voice: [\"voice_provider\", \"voice_id\"]\r\n    };\r\n\r\n    // Merge current data with new config data\r\n    const mergedConfig = { ...attorney, ...configData };\r\n\r\n    // Validate all required fields\r\n    const missingFields = {};\r\n    let hasErrors = false;\r\n\r\n    Object.entries(requiredFields).forEach(([section, fields]) => {\r\n      const missing = fields.filter(field => !mergedConfig[field]);\r\n      if (missing.length > 0) {\r\n        missingFields[section] = missing;\r\n        hasErrors = true;\r\n      }\r\n    });\r\n\r\n    // Check for specific validation rules\r\n    const validationErrors = [];\r\n\r\n    // Example: Validate welcome message length\r\n    if (mergedConfig.welcome_message && mergedConfig.welcome_message.length > 500) {\r\n      validationErrors.push(\"Welcome message exceeds maximum length of 500 characters\");\r\n      hasErrors = true;\r\n    }\r\n\r\n    // Example: Validate voice provider and ID combination\r\n    if (mergedConfig.voice_provider && mergedConfig.voice_id) {\r\n      const validVoices = await getValidVoicesForProvider(mergedConfig.voice_provider);\r\n      if (!validVoices.includes(mergedConfig.voice_id)) {\r\n        validationErrors.push(`Voice ID '${mergedConfig.voice_id}' is not valid for provider '${mergedConfig.voice_provider}'`);\r\n        hasErrors = true;\r\n      }\r\n    }\r\n\r\n    return {\r\n      valid: !hasErrors,\r\n      missingFields: Object.keys(missingFields).length > 0 ? missingFields : null,\r\n      validationErrors: validationErrors.length > 0 ? validationErrors : null,\r\n      message: hasErrors\r\n        ? \"Configuration validation failed\"\r\n        : \"Configuration is valid\"\r\n    };\r\n  } catch (error) {\r\n    console.error(`Error validating configuration for attorney ${attorneyId}:`, error);\r\n    return {\r\n      valid: false,\r\n      error: error.message,\r\n      message: `Error validating configuration: ${error.message}`\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Ensure preview matches deployment\r\n *\r\n * This function checks if the preview in the dashboard matches what will be\r\n * deployed to the attorney's subdomain. It checks for discrepancies between\r\n * the attorney's profile in Supabase, the Vapi assistant, and the subdomain\r\n * configuration.\r\n *\r\n * @param {Object} params - The parameters for the function\r\n * @param {string} params.attorneyId - The ID of the attorney\r\n * @returns {Object} The consistency check result\r\n */\r\nexport const checkPreviewConsistency = async (params) => {\r\n  const { attorneyId } = params;\r\n\r\n  console.log(`Checking preview consistency for attorney ${attorneyId}`);\r\n\r\n  try {\r\n    // Get attorney data from Supabase (source of truth)\r\n    const attorney = await getAttorneyById(attorneyId);\r\n\r\n    if (!attorney) {\r\n      return {\r\n        consistent: false,\r\n        errors: [\"Attorney not found\"],\r\n        message: \"Cannot check consistency for non-existent attorney\"\r\n      };\r\n    }\r\n\r\n    // Check Vapi assistant consistency\r\n    const vapiAssistantId = attorney.vapi_assistant_id;\r\n\r\n    if (!vapiAssistantId) {\r\n      return {\r\n        consistent: false,\r\n        errors: [\"No Vapi assistant ID found\"],\r\n        message: \"Attorney record is missing Vapi assistant ID\"\r\n      };\r\n    }\r\n\r\n    // Get Vapi assistant data\r\n    let vapiAssistant;\r\n    try {\r\n      vapiAssistant = await getVapiAssistant(vapiAssistantId);\r\n    } catch (error) {\r\n      return {\r\n        consistent: false,\r\n        errors: [`Error fetching Vapi assistant: ${error.message}`],\r\n        message: \"Failed to fetch Vapi assistant\"\r\n      };\r\n    }\r\n\r\n    // Compare attorney data with Vapi assistant\r\n    const vapiDiscrepancies = {};\r\n\r\n    // Map of attorney fields to Vapi assistant fields\r\n    const vapiFieldMappings = {\r\n      firm_name: \"name\",\r\n      welcome_message: \"firstMessage\",\r\n      vapi_instructions: \"instructions\"\r\n    };\r\n\r\n    // Check for discrepancies in mapped fields\r\n    Object.entries(vapiFieldMappings).forEach(([attorneyField, vapiField]) => {\r\n      if (attorney[attorneyField] !== vapiAssistant[vapiField]) {\r\n        vapiDiscrepancies[attorneyField] = {\r\n          attorney: attorney[attorneyField],\r\n          vapi: vapiAssistant[vapiField]\r\n        };\r\n      }\r\n    });\r\n\r\n    // Check voice configuration\r\n    if (attorney.voice_provider !== vapiAssistant.voice?.provider ||\r\n        attorney.voice_id !== vapiAssistant.voice?.voiceId) {\r\n      vapiDiscrepancies.voice = {\r\n        attorney: {\r\n          provider: attorney.voice_provider,\r\n          voiceId: attorney.voice_id\r\n        },\r\n        vapi: vapiAssistant.voice\r\n      };\r\n    }\r\n\r\n    // If Vapi discrepancies found, fix them\r\n    if (Object.keys(vapiDiscrepancies).length > 0) {\r\n      console.log(`Found Vapi discrepancies for attorney ${attorneyId}:`, vapiDiscrepancies);\r\n\r\n      // Ensure profile persistence to fix discrepancies\r\n      const syncResult = await ensureProfilePersistence({\r\n        attorneyId: attorney.id,\r\n        forceUpdate: true\r\n      });\r\n\r\n      return {\r\n        consistent: false,\r\n        discrepancies: { vapi: vapiDiscrepancies },\r\n        action: \"fixed\",\r\n        syncResult,\r\n        message: \"Vapi assistant discrepancies found and fixed\"\r\n      };\r\n    }\r\n\r\n    // Everything is consistent\r\n    return {\r\n      consistent: true,\r\n      message: \"Preview is consistent with deployment\"\r\n    };\r\n  } catch (error) {\r\n    console.error(`Error checking preview consistency for attorney ${attorneyId}:`, error);\r\n    return {\r\n      consistent: false,\r\n      error: error.message,\r\n      message: `Error checking preview consistency: ${error.message}`\r\n    };\r\n  }\r\n};\r\n"], "names": ["syncAttorneyProfile", "params", "attorneyId", "forceUpdate", "result", "ensureProfilePersistence", "error", "manageAuthState", "authData", "action", "user", "session", "attorney", "getAttorneyByEmail", "updateAttorney", "createAttorney", "syncResult", "refreshed<PERSON><PERSON><PERSON><PERSON>", "getAttorneyByAuthId", "refreshSyncResult", "validateConfiguration", "configData", "getAttorneyById", "requiredFields", "mergedConfig", "missingFields", "hasErrors", "section", "fields", "missing", "field", "validationErrors", "getValidVoicesForProvider", "checkPreviewConsistency", "vapiAssistantId", "vapiAssistant", "getVapiAssistant", "vapiDiscrepancies", "<PERSON><PERSON><PERSON>", "vapiField"], "mappings": "yJAmCY,MAACA,EAAsB,MAAOC,GAAW,CACnD,KAAM,CAAE,WAAAC,EAAY,YAAAC,EAAc,EAAK,EAAKF,EAE5C,QAAQ,IAAI,gCAAgCC,CAAU,kBAAkBC,CAAW,EAAE,EAErF,GAAI,CAEF,MAAMC,EAAS,MAAMC,EAAyB,CAC5C,WAAAH,EACA,YAAAC,CACN,CAAK,EAGD,eAAQ,IAAI,kCAAkCD,CAAU,IAAK,CAC3D,OAAQE,EAAO,OACf,QAASA,EAAO,QAChB,QAASA,EAAO,OACtB,CAAK,EAGM,CACL,OAAQA,EAAO,OACf,YAAaA,EAAO,YAAY,YAChC,QAASA,EAAO,QAChB,QAASA,EAAO,QAChB,QAASA,EAAO,QAChB,cAAeA,EAAO,YAAY,eAAiB,IACzD,CACG,OAAQE,EAAO,CACd,eAAQ,MAAM,sCAAsCJ,CAAU,IAAKI,CAAK,EACjE,CACL,OAAQ,QACR,MAAOA,EAAM,QACb,QAAS,mCAAmCA,EAAM,OAAO,EAC/D,CACG,CACH,EAcaC,EAAkB,MAAON,GAAW,CAC/C,KAAM,CAAE,SAAAO,EAAU,OAAAC,CAAQ,EAAGR,EAE7B,QAAQ,IAAI,mCAAmCQ,CAAM,EAAE,EAEvD,GAAI,CACF,OAAQA,EAAM,CACZ,IAAK,QAEH,KAAM,CAAE,KAAAC,EAAM,QAAAC,CAAS,EAAGH,EAE1B,GAAI,CAACE,GAAQ,CAACA,EAAK,MACjB,MAAO,CACL,OAAQ,QACR,QAAS,GACT,QAAS,sCACrB,EAIQ,IAAIE,EAAW,MAAMC,EAAmBH,EAAK,KAAK,EAE7CE,EAQOA,EAAS,UAEnB,QAAQ,IAAI,sDAAsDF,EAAK,KAAK,EAAE,EAC9EE,EAAW,MAAME,EAAeF,EAAS,GAAI,CAC3C,QAASF,EAAK,EAC1B,CAAW,IAXD,QAAQ,IAAI,oCAAoCA,EAAK,KAAK,EAAE,EAC5DE,EAAW,MAAMG,EAAe,CAC9B,MAAOL,EAAK,MACZ,KAAMA,EAAK,eAAe,MAAQA,EAAK,MAAM,MAAM,GAAG,EAAE,CAAC,EACzD,QAASA,EAAK,EAC1B,CAAW,GAUH,QAAQ,IAAI,qBAAqBE,EAAS,EAAE,sBAAsB,EAClE,MAAMI,EAAa,MAAMX,EAAyB,CAChD,WAAYO,EAAS,GACrB,UAAWA,EACX,YAAa,EACvB,CAAS,EAED,MAAO,CACL,OAAQ,QACR,QAAS,GACT,SAAAA,EACA,WAAAI,EACA,QAAAL,EACA,QAAS,mCACnB,EAEM,IAAK,SAGH,MAAO,CACL,OAAQ,SACR,QAAS,GACT,QAAS,8BACnB,EAEM,IAAK,UAEH,GAAI,CAACH,EAAS,KACZ,MAAO,CACL,OAAQ,UACR,QAAS,GACT,QAAS,mCACrB,EAIQ,MAAMS,EAAoB,MAAMC,EAAoBV,EAAS,KAAK,EAAE,EAEpE,GAAI,CAACS,EACH,MAAO,CACL,OAAQ,UACR,QAAS,GACT,QAAS,4CACrB,EAIQ,QAAQ,IAAI,6CAA6CA,EAAkB,EAAE,aAAa,EAC1F,MAAME,EAAoB,MAAMd,EAAyB,CACvD,WAAYY,EAAkB,GAC9B,UAAWA,CACrB,CAAS,EAED,MAAO,CACL,OAAQ,UACR,QAAS,GACT,SAAUA,EACV,WAAYE,EACZ,QAAS,iDACnB,EAEM,QACE,MAAO,CACL,OAAQ,UACR,QAAS,GACT,QAAS,mBAAmBV,CAAM,EAC5C,CACK,CACF,OAAQH,EAAO,CACd,eAAQ,MAAM,wCAAwCG,CAAM,IAAKH,CAAK,EAC/D,CACL,OAAAG,EACA,QAAS,GACT,MAAOH,EAAM,QACb,QAAS,8BAA8BA,EAAM,OAAO,EAC1D,CACG,CACH,EAaac,EAAwB,MAAOnB,GAAW,CACrD,KAAM,CAAE,WAAAC,EAAY,WAAAmB,CAAY,EAAGpB,EAEnC,QAAQ,IAAI,yCAAyCC,CAAU,EAAE,EACjE,QAAQ,IAAI,eAAgBmB,CAAU,EAEtC,GAAI,CAEF,MAAMT,EAAW,MAAMU,EAAgBpB,CAAU,EAEjD,GAAI,CAACU,EACH,MAAO,CACL,MAAO,GACP,OAAQ,CAAC,oBAAoB,EAC7B,QAAS,yDACjB,EAII,MAAMW,EAAiB,CACrB,QAAS,CAAC,OAAQ,OAAO,EACzB,WAAY,CAAC,WAAW,EACxB,MAAO,CAAC,kBAAmB,mBAAmB,EAC9C,MAAO,CAAC,iBAAkB,UAAU,CAC1C,EAGUC,EAAe,CAAE,GAAGZ,EAAU,GAAGS,CAAU,EAG3CI,EAAgB,CAAA,EACtB,IAAIC,EAAY,GAEhB,OAAO,QAAQH,CAAc,EAAE,QAAQ,CAAC,CAACI,EAASC,CAAM,IAAM,CAC5D,MAAMC,EAAUD,EAAO,OAAOE,GAAS,CAACN,EAAaM,CAAK,CAAC,EACvDD,EAAQ,OAAS,IACnBJ,EAAcE,CAAO,EAAIE,EACzBH,EAAY,GAEpB,CAAK,EAGD,MAAMK,EAAmB,CAAA,EAGzB,OAAIP,EAAa,iBAAmBA,EAAa,gBAAgB,OAAS,MACxEO,EAAiB,KAAK,0DAA0D,EAChFL,EAAY,IAIVF,EAAa,gBAAkBA,EAAa,YAC1B,MAAMQ,EAA0BR,EAAa,cAAc,GAC9D,SAASA,EAAa,QAAQ,IAC7CO,EAAiB,KAAK,aAAaP,EAAa,QAAQ,gCAAgCA,EAAa,cAAc,GAAG,EACtHE,EAAY,KAIT,CACL,MAAO,CAACA,EACR,cAAe,OAAO,KAAKD,CAAa,EAAE,OAAS,EAAIA,EAAgB,KACvE,iBAAkBM,EAAiB,OAAS,EAAIA,EAAmB,KACnE,QAASL,EACL,kCACA,wBACV,CACG,OAAQpB,EAAO,CACd,eAAQ,MAAM,+CAA+CJ,CAAU,IAAKI,CAAK,EAC1E,CACL,MAAO,GACP,MAAOA,EAAM,QACb,QAAS,mCAAmCA,EAAM,OAAO,EAC/D,CACG,CACH,EAca2B,EAA0B,MAAOhC,GAAW,CACvD,KAAM,CAAE,WAAAC,CAAY,EAAGD,EAEvB,QAAQ,IAAI,6CAA6CC,CAAU,EAAE,EAErE,GAAI,CAEF,MAAMU,EAAW,MAAMU,EAAgBpB,CAAU,EAEjD,GAAI,CAACU,EACH,MAAO,CACL,WAAY,GACZ,OAAQ,CAAC,oBAAoB,EAC7B,QAAS,oDACjB,EAII,MAAMsB,EAAkBtB,EAAS,kBAEjC,GAAI,CAACsB,EACH,MAAO,CACL,WAAY,GACZ,OAAQ,CAAC,4BAA4B,EACrC,QAAS,8CACjB,EAII,IAAIC,EACJ,GAAI,CACFA,EAAgB,MAAMC,EAAiBF,CAAe,CACvD,OAAQ5B,EAAO,CACd,MAAO,CACL,WAAY,GACZ,OAAQ,CAAC,kCAAkCA,EAAM,OAAO,EAAE,EAC1D,QAAS,gCACjB,CACK,CAGD,MAAM+B,EAAoB,CAAA,EAgC1B,GAtBA,OAAO,QAPmB,CACxB,UAAW,OACX,gBAAiB,eACjB,kBAAmB,cACzB,CAGoC,EAAE,QAAQ,CAAC,CAACC,EAAeC,CAAS,IAAM,CACpE3B,EAAS0B,CAAa,IAAMH,EAAcI,CAAS,IACrDF,EAAkBC,CAAa,EAAI,CACjC,SAAU1B,EAAS0B,CAAa,EAChC,KAAMH,EAAcI,CAAS,CACvC,EAEA,CAAK,GAGG3B,EAAS,iBAAmBuB,EAAc,OAAO,UACjDvB,EAAS,WAAauB,EAAc,OAAO,WAC7CE,EAAkB,MAAQ,CACxB,SAAU,CACR,SAAUzB,EAAS,eACnB,QAASA,EAAS,QACnB,EACD,KAAMuB,EAAc,KAC5B,GAIQ,OAAO,KAAKE,CAAiB,EAAE,OAAS,EAAG,CAC7C,QAAQ,IAAI,yCAAyCnC,CAAU,IAAKmC,CAAiB,EAGrF,MAAMrB,EAAa,MAAMX,EAAyB,CAChD,WAAYO,EAAS,GACrB,YAAa,EACrB,CAAO,EAED,MAAO,CACL,WAAY,GACZ,cAAe,CAAE,KAAMyB,CAAmB,EAC1C,OAAQ,QACR,WAAArB,EACA,QAAS,8CACjB,CACK,CAGD,MAAO,CACL,WAAY,GACZ,QAAS,uCACf,CACG,OAAQV,EAAO,CACd,eAAQ,MAAM,mDAAmDJ,CAAU,IAAKI,CAAK,EAC9E,CACL,WAAY,GACZ,MAAOA,EAAM,QACb,QAAS,uCAAuCA,EAAM,OAAO,EACnE,CACG,CACH"}