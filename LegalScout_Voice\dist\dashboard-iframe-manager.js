/**
 * Dashboard Iframe Manager
 * 
 * Manages preview iframe communication and ensures proper loading/messaging
 */

(function() {
  'use strict';

  // Prevent multiple loads
  if (window.__DASHBOARD_IFRAME_MANAGER_LOADED) {
    console.log('[DashboardIframeManager] Already loaded');
    return;
  }

  window.__DASHBOARD_IFRAME_MANAGER_LOADED = true;

  // Configuration
  const config = {
    iframeSelector: '#preview-iframe, .preview-iframe, iframe[title="Agent Preview"]',
    messageTimeout: 5000,
    retryDelay: 1000,
    maxRetries: 3
  };

  // Logging utility
  function log(message, level = 'info') {
    const prefix = '[DashboardIframeManager]';
    console[level](`${prefix} ${message}`);
  }

  // Find preview iframes
  function findPreviewIframes() {
    const iframes = document.querySelectorAll(config.iframeSelector);
    log(`Found ${iframes.length} potential preview iframes`);
    
    // Filter for accessible iframes
    const accessibleIframes = Array.from(iframes).filter(iframe => {
      try {
        return iframe.contentWindow && iframe.src;
      } catch (error) {
        log(`Iframe not accessible: ${error.message}`, 'warn');
        return false;
      }
    });

    log(`Found ${accessibleIframes.length} accessible preview iframes`);
    return accessibleIframes;
  }

  // Send message to iframe with retry logic
  function sendMessageToIframe(iframe, message, retries = 0) {
    return new Promise((resolve, reject) => {
      if (!iframe || !iframe.contentWindow) {
        reject(new Error('Iframe or contentWindow not available'));
        return;
      }

      try {
        const targetOrigin = new URL(iframe.src).origin;
        
        // Set up response listener
        const responseHandler = (event) => {
          if (event.origin !== targetOrigin) return;
          
          if (event.data && event.data.type === `${message.type}_RESPONSE`) {
            window.removeEventListener('message', responseHandler);
            clearTimeout(timeoutId);
            log(`Received response for ${message.type}`);
            resolve(event.data);
          }
        };

        window.addEventListener('message', responseHandler);

        // Set up timeout
        const timeoutId = setTimeout(() => {
          window.removeEventListener('message', responseHandler);
          
          if (retries < config.maxRetries) {
            log(`Message timeout, retrying (${retries + 1}/${config.maxRetries})`, 'warn');
            setTimeout(() => {
              sendMessageToIframe(iframe, message, retries + 1)
                .then(resolve)
                .catch(reject);
            }, config.retryDelay);
          } else {
            reject(new Error(`Message timeout after ${config.maxRetries} retries`));
          }
        }, config.messageTimeout);

        // Send the message
        iframe.contentWindow.postMessage(message, targetOrigin);
        log(`Sent message ${message.type} to iframe`);

      } catch (error) {
        reject(error);
      }
    });
  }

  // Send configuration to all preview iframes
  function sendConfigToPreviewIframes(config) {
    const iframes = findPreviewIframes();
    
    if (iframes.length === 0) {
      log('No preview iframes found to send config to', 'warn');
      return Promise.resolve([]);
    }

    const message = {
      type: 'PREVIEW_CONFIG_UPDATE',
      config: config,
      timestamp: Date.now()
    };

    const promises = iframes.map(iframe => {
      return sendMessageToIframe(iframe, message)
        .then(response => {
          log(`Config sent successfully to iframe: ${iframe.src}`);
          return { iframe, success: true, response };
        })
        .catch(error => {
          log(`Failed to send config to iframe: ${error.message}`, 'error');
          return { iframe, success: false, error: error.message };
        });
    });

    return Promise.allSettled(promises);
  }

  // Wait for iframe to be ready
  function waitForIframeReady(iframe, timeout = 10000) {
    return new Promise((resolve, reject) => {
      if (!iframe) {
        reject(new Error('No iframe provided'));
        return;
      }

      // Check if already loaded
      if (iframe.contentDocument && iframe.contentDocument.readyState === 'complete') {
        log('Iframe already ready');
        resolve(iframe);
        return;
      }

      const timeoutId = setTimeout(() => {
        iframe.removeEventListener('load', loadHandler);
        reject(new Error('Iframe load timeout'));
      }, timeout);

      const loadHandler = () => {
        clearTimeout(timeoutId);
        log('Iframe loaded successfully');
        resolve(iframe);
      };

      iframe.addEventListener('load', loadHandler);
    });
  }

  // Enhanced iframe finder with DOM observation
  function setupIframeObserver() {
    // Wait for DOM to be ready
    function initObserver() {
      // Ensure document.body exists
      if (!document.body) {
        log('Document body not ready, retrying in 100ms...');
        setTimeout(initObserver, 100);
        return null;
      }

      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1) { // Element node
                // Check if the node itself is an iframe
                if (node.tagName === 'IFRAME' && node.matches && node.matches(config.iframeSelector)) {
                  log('New preview iframe detected');
                  handleNewIframe(node);
                }

                // Check for iframes within the added node
                const iframes = node.querySelectorAll && node.querySelectorAll(config.iframeSelector);
                if (iframes && iframes.length > 0) {
                  log(`Found ${iframes.length} new preview iframes in added content`);
                  iframes.forEach(handleNewIframe);
                }
              }
            });
          }
        });
      });

      try {
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        log('Iframe observer set up successfully');
        return observer;
      } catch (error) {
        log(`Error setting up iframe observer: ${error.message}`, 'error');
        return null;
      }
    }

    return initObserver();
  }

  // Handle new iframe detection
  function handleNewIframe(iframe) {
    log(`Handling new iframe: ${iframe.src || 'no src'}`);
    
    // Wait for iframe to be ready, then notify
    waitForIframeReady(iframe)
      .then(() => {
        // Dispatch custom event for components
        const event = new CustomEvent('previewIframeReady', {
          detail: { iframe }
        });
        window.dispatchEvent(event);
      })
      .catch(error => {
        log(`Error waiting for iframe ready: ${error.message}`, 'error');
      });
  }

  // Public API
  window.DashboardIframeManager = {
    findPreviewIframes,
    sendConfigToPreviewIframes,
    sendMessageToIframe,
    waitForIframeReady,
    
    // Utility method for components
    sendConfigToPreview: function(previewConfig) {
      log('Sending config to preview iframes');
      return sendConfigToPreviewIframes(previewConfig);
    },

    // Check if any iframes are available
    hasPreviewIframes: function() {
      return findPreviewIframes().length > 0;
    },

    // Get iframe count
    getIframeCount: function() {
      return findPreviewIframes().length;
    }
  };

  // Initialize
  log('Initializing Dashboard Iframe Manager');
  
  // Set up iframe observer
  setupIframeObserver();

  // Check for existing iframes
  const existingIframes = findPreviewIframes();
  existingIframes.forEach(handleNewIframe);

  // Listen for preview ready messages
  window.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'PREVIEW_READY') {
      log('Received PREVIEW_READY message from iframe');
      
      // Dispatch custom event
      const customEvent = new CustomEvent('previewReady', {
        detail: { 
          iframe: event.source.frameElement,
          origin: event.origin 
        }
      });
      window.dispatchEvent(customEvent);
    }
  });

  log('Dashboard Iframe Manager initialized successfully');

})();
