# Agent Page Integration Example

This document provides an example of how to integrate the enhanced Vapi components into the agent page while keeping the original components for reference.

## Overview

The agent page is where users interact with the attorney's AI assistant. The enhanced Vapi components provide a more modern and consistent UI for these interactions, with better speech visualization and call control.

## Integration Steps

### 1. Import the Enhanced Components

```jsx
// In your agent page component
import EnhancedVapiCall from '../components/EnhancedVapiCall';
import EnhancedCallController from '../components/call/EnhancedCallController';
import { useState } from 'react';
```

### 2. Add State for Component Selection and Call State

```jsx
// Add state to toggle between original and enhanced components
const [useEnhancedComponents, setUseEnhancedComponents] = useState(false);
// Add state to track if a call is active
const [callActive, setCallActive] = useState(false);
```

### 3. Add a Toggle Button (for Development Only)

```jsx
<div className="component-toggle">
  <button 
    className={`toggle-button ${useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(true)}
  >
    Use Enhanced Components
  </button>
  <button 
    className={`toggle-button ${!useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(false)}
  >
    Use Original Components
  </button>
</div>
```

### 4. Conditionally Render Components

```jsx
// In your render function
{!callActive ? (
  // Render call controller
  useEnhancedComponents ? (
    <EnhancedCallController 
      assistantId={attorney.vapi_assistant_id}
      onCallStart={() => setCallActive(true)}
      showTranscript={true}
      showVisualization={true}
    />
  ) : (
    <OriginalCallController 
      assistantId={attorney.vapi_assistant_id}
      onCallStart={() => setCallActive(true)}
    />
  )
) : (
  // Render full call component
  useEnhancedComponents ? (
    <EnhancedVapiCall 
      assistantId={attorney.vapi_assistant_id}
      onEndCall={() => setCallActive(false)}
      customInstructions={{
        firmName: attorney.firm_name,
        welcomeMessage: attorney.welcome_message,
        voiceId: attorney.voice_id,
        voiceProvider: attorney.voice_provider
      }}
    />
  ) : (
    <VapiCall 
      assistantId={attorney.vapi_assistant_id}
      onEndCall={() => setCallActive(false)}
      customInstructions={{
        firmName: attorney.firm_name,
        welcomeMessage: attorney.welcome_message,
        voiceId: attorney.voice_id,
        voiceProvider: attorney.voice_provider
      }}
    />
  )
)}
```

### 5. Complete Example

Here's a complete example of how to integrate the enhanced components into the agent page:

```jsx
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import EnhancedVapiCall from '../components/EnhancedVapiCall';
import EnhancedCallController from '../components/call/EnhancedCallController';
import VapiCall from '../components/legacy/VapiCall';
import OriginalCallController from '../components/legacy/CallController';
import { getAttorneyBySubdomain } from '../utils/attorneyUtils';
import EnhancedSpeechParticles from '../components/EnhancedSpeechParticles';

const AgentPage = () => {
  const { subdomain } = useParams();
  const [attorney, setAttorney] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [callActive, setCallActive] = useState(false);
  const [useEnhancedComponents, setUseEnhancedComponents] = useState(true); // Default to enhanced

  useEffect(() => {
    const fetchAttorney = async () => {
      try {
        setLoading(true);
        const data = await getAttorneyBySubdomain(subdomain);
        setAttorney(data);
        setLoading(false);
      } catch (err) {
        setError('Failed to load attorney data');
        setLoading(false);
      }
    };

    fetchAttorney();
  }, [subdomain]);

  if (loading) {
    return <div className="loading">Loading...</div>;
  }

  if (error || !attorney) {
    return <div className="error">{error || 'Attorney not found'}</div>;
  }

  return (
    <div className="agent-page">
      <div className="agent-header">
        <h1>{attorney.firm_name}</h1>
        {/* Toggle button for development only - remove in production */}
        <div className="component-toggle">
          <button 
            className={`toggle-button ${useEnhancedComponents ? 'active' : ''}`}
            onClick={() => setUseEnhancedComponents(true)}
          >
            Use Enhanced Components
          </button>
          <button 
            className={`toggle-button ${!useEnhancedComponents ? 'active' : ''}`}
            onClick={() => setUseEnhancedComponents(false)}
          >
            Use Original Components
          </button>
        </div>
      </div>
      
      <div className="agent-content">
        <div className="agent-description">
          <p>{attorney.practice_description}</p>
        </div>
        
        {!callActive ? (
          // Render call controller
          <div className="call-controller-container">
            {useEnhancedComponents ? (
              <>
                <EnhancedSpeechParticles className="background-particles" />
                <EnhancedCallController 
                  assistantId={attorney.vapi_assistant_id}
                  onCallStart={() => setCallActive(true)}
                  showTranscript={true}
                  showVisualization={true}
                  customInstructions={{
                    firmName: attorney.firm_name,
                    welcomeMessage: attorney.welcome_message,
                    voiceId: attorney.voice_id,
                    voiceProvider: attorney.voice_provider
                  }}
                />
              </>
            ) : (
              <OriginalCallController 
                assistantId={attorney.vapi_assistant_id}
                onCallStart={() => setCallActive(true)}
                customInstructions={{
                  firmName: attorney.firm_name,
                  welcomeMessage: attorney.welcome_message,
                  voiceId: attorney.voice_id,
                  voiceProvider: attorney.voice_provider
                }}
              />
            )}
          </div>
        ) : (
          // Render full call component
          <div className="call-container">
            {useEnhancedComponents ? (
              <EnhancedVapiCall 
                assistantId={attorney.vapi_assistant_id}
                onEndCall={() => setCallActive(false)}
                customInstructions={{
                  firmName: attorney.firm_name,
                  welcomeMessage: attorney.welcome_message,
                  voiceId: attorney.voice_id,
                  voiceProvider: attorney.voice_provider
                }}
              />
            ) : (
              <VapiCall 
                assistantId={attorney.vapi_assistant_id}
                onEndCall={() => setCallActive(false)}
                customInstructions={{
                  firmName: attorney.firm_name,
                  welcomeMessage: attorney.welcome_message,
                  voiceId: attorney.voice_id,
                  voiceProvider: attorney.voice_provider
                }}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AgentPage;
```

## Styling

You can add the following CSS to style the agent page:

```css
.agent-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  position: relative;
}

.agent-header {
  padding: 1rem;
  background-color: var(--primary-color, #3b82f6);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
}

.agent-description {
  max-width: 800px;
  margin-bottom: 2rem;
  text-align: center;
}

.call-controller-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  display: flex;
  justify-content: center;
}

.call-container {
  position: relative;
  width: 100%;
  height: 100%;
  flex: 1;
  display: flex;
  justify-content: center;
}

.background-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* Toggle button styles (for development only) */
.component-toggle {
  display: flex;
  gap: 0.5rem;
}

.toggle-button {
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-button.active {
  background-color: rgba(255, 255, 255, 0.4);
}

.toggle-button:hover {
  opacity: 0.9;
}
```

## Next Steps

1. **Test the Integration**: Test the integration to ensure that both the original and enhanced components work as expected.

2. **Remove Development Toggle**: Once you're satisfied with the enhanced components, remove the toggle button and use only the enhanced components.

3. **Optimize for Mobile**: Ensure that the agent page works well on mobile devices, as many users will access it from their phones.
