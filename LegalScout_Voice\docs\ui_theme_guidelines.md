# UI Theme Guidelines

This document outlines the design system and UI guidelines for the LegalScout interface, focusing on creating a modern, elegant, and cohesive user experience.

## Design Principles

Our UI design follows these core principles:

1. **Minimalism**: Clean layouts with sufficient whitespace for better focus
2. **Elegance**: Sophisticated typography and color schemes for a professional look
3. **Legibility**: Clear visual hierarchy and readable text across all screen sizes
4. **Consistency**: Uniform patterns across the interface for intuitive use
5. **Global Theme Respect**: All components respect the application's dark/light mode setting

## Color System

### Primary Colors

- **Primary**: `#4B74AA` - Blue-gray shade, used for primary actions and highlighting
- **Secondary**: `#607D8B` - Blue-grey shade, used for secondary elements
- **Text Primary**: `#37474F` - Dark blue-grey, used for headings and important text
- **Text Secondary**: `#607D8B` - Lighter blue-grey, used for secondary text
- **Border Color**: `#E0E7EF` - Light blue-grey, used for borders and dividers
- **Background**: `#FAFBFD` - Very light blue-grey, used for the main background

### Derived Colors

- **Primary Hover**: Darker variation of primary color
- **Secondary Hover**: Darker variation of secondary color
- **Success**: `#4CAF50` - Green, used for success states
- **Danger**: `#e74c3c` - Red, used for destructive actions

### Global Theme Integration

All components should respect the global theme set at the application level:

- `[data-theme="dark"]` - Dark mode selector
- `[data-theme="light"]` - Light mode selector

Component styles should inherit from App.css global variables:
- `--bg-primary`: Background primary color
- `--bg-secondary`: Background secondary color
- `--text-primary`: Primary text color
- `--text-secondary`: Secondary text color
- `--accent-primary`: Primary accent color
- `--accent-secondary`: Secondary accent color

## Typography

- **Font Family**: System fonts stack (system-ui, -apple-system, etc.)
- **Font Weights**:
  - Normal: 400
  - Medium: 500
  - Semibold: 600
  - Bold: 700
- **Font Sizes**:
  - Large Title: 1.4rem
  - Title: 1.25rem
  - Subtitle: 1.125rem
  - Body: 0.95rem
  - Small: 0.85rem
  - Caption: 0.8rem

## Spacing and Layout

- **Containers**: Maximum width of 1200px with auto margins
- **Padding**: Standard padding of 1.5rem to 2rem
- **Gap**: Use smaller gaps (0.5rem) for inline items, larger gaps (1.5rem) for sections
- **Margins**: Standard section margins of 1.5rem

## Components

### Buttons

- **Primary**: Solid background with the primary color, white text
- **Secondary**: White background with border, text primary color
- **Small**: Reduced padding, smaller font size
- **Danger**: Red background for destructive actions
- **Hover States**: Slight elevation effect with transform and shadow

### Inputs

- Consistent padding and border-radius
- Focus states with primary color outline
- Clear typography with good contrast
- Helper text for additional context

### Cards & Panels

- Light background with subtle border
- Border-radius for rounded corners
- Soft shadows for subtle elevation
- Hover states with increased elevation

### Tabs

- Clean, minimal styling with underline for active state
- Consistent spacing and padding
- Icon + text combination for better recognition

### Tags

- Pill-shaped design with primary color theming
- Compact but legible text
- Remove button integrated within the tag

### Color Pickers

- **Circular Design**: Round color dots for intuitive color selection
- **Inline Labels**: Labels positioned next to controls to save vertical space
- **Hover Effects**: Scale animation (1.1x) when hovering over color dots
- **Tooltip Hints**: Tooltips appear on hover to show the purpose of each color picker
- **Accessibility**: Proper ARIA labels for screen readers
- **Integration**: Color pickers paired with related opacity controls for unified experience

### Sliders

- **Modern Minimal Design**: Clean, thin track with a prominent circular thumb
- **Visual Feedback**: Percentage indicators showing the current value
- **Smooth Interaction**: Subtle hover and active states for better usability
- **Compact Layout**: Sliders designed to fit inline with related controls
- **Consistency**: Common styling across all opacity controls

## Shadows and Elevation

- **Soft Shadow**: `0 4px 12px rgba(0, 20, 50, 0.04)` - Used for cards and panels
- **Medium Shadow**: `0 8px 24px rgba(0, 20, 50, 0.06)` - Used for hover states and elevated elements
- **Button Shadow**: `0 2px 5px rgba(0, 0, 0, 0.1)` - Used for buttons

## Radius System

- **Small**: 8px - Used for input fields, buttons
- **Medium**: 12px - Used for cards, panels
- **Large**: 16px - Used for modal dialogs
- **Full Circle**: 50% - Used for color picker dots and slider thumbs

## Animations & Transitions

- Standard transition: `all 0.3s ease`
- Minimal, purpose-driven animations
- Hover effects that enhance usability
- Fade and slide animations for content appearing/disappearing
- Scale transforms for interactive elements (1.05x-1.1x)

## Responsive Design

- Mobile-first approach with appropriate breakpoints
- Simplified layouts on smaller screens
- Touch-friendly targets for mobile users
- Stacked controls on smaller screens, inline on larger screens

## Space Efficiency

- **Inline Controls**: Position related controls horizontally where possible
- **Compact Groups**: Group related controls together with minimal spacing
- **Progressive Disclosure**: Show additional options only when needed
- **Information Density**: Balance between information density and readability

## Accessibility

- Sufficient color contrast ratios
- Keyboard navigation support
- Proper focus indicators
- Semantic HTML structure
- ARIA labels for custom controls

## Implementation Guidelines

1. Use CSS variables for consistent theming
2. Follow class naming conventions for clarity
3. Prioritize performance with efficient CSS
4. Test across different screen sizes and devices
5. Ensure all interactive elements have proper focus and hover states

## Dark Mode Considerations

- Predefine dark mode color schemes
- Ensure sufficient contrast in dark mode
- Adjust shadows for dark backgrounds
- Maintain consistent hierarchy in both modes
- Customize control styles appropriately for dark backgrounds

---

By following these guidelines, we maintain a cohesive, elegant, and professional appearance across all interface components while ensuring usability and accessibility for all users. 