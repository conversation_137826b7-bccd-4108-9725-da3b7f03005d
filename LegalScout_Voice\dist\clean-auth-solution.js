/**
 * CLEAN AUTHENTICATION SOLUTION
 * 
 * This script removes all conflicting fetch interceptors and lets Supabase
 * handle its own authentication properly. This is the correct approach.
 */

(function() {
  console.log('🧹 [CleanAuthSolution] Starting clean authentication solution...');

  // Prevent multiple installations
  if (window.__CLEAN_AUTH_SOLUTION_INSTALLED) {
    console.log('🧹 [CleanAuthSolution] Already installed, skipping...');
    return;
  }

  // Step 1: Restore original fetch function
  function restoreOriginalFetch() {
    console.log('🧹 [CleanAuthSolution] Restoring original fetch function...');
    
    // Find the original fetch (before any interceptors)
    const originalFetch = window.originalFetchBeforeInterceptors || 
                         window.__ORIGINAL_FETCH__ || 
                         window.fetch;

    // Store a clean reference
    window.__ORIGINAL_FETCH__ = originalFetch;
    
    // Restore clean fetch
    window.fetch = originalFetch;
    
    console.log('✅ [CleanAuthSolution] Original fetch restored');
  }

  // Step 2: Ensure Supabase client is properly configured
  function ensureSupabaseClient() {
    console.log('🧹 [CleanAuthSolution] Ensuring Supabase client is properly configured...');
    
    // Wait for Supabase to be available
    const checkSupabase = () => {
      if (typeof supabase !== 'undefined' && supabase.createClient) {
        const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
        
        // Only create if not already exists
        if (!window.supabase) {
          window.supabase = supabase.createClient(supabaseUrl, supabaseKey, {
            auth: {
              autoRefreshToken: true,
              persistSession: true,
              detectSessionInUrl: true
            }
          });
          console.log('✅ [CleanAuthSolution] Supabase client created');
        } else {
          console.log('✅ [CleanAuthSolution] Supabase client already exists');
        }
        
        return true;
      }
      return false;
    };

    // Try immediately
    if (checkSupabase()) {
      return;
    }

    // Wait for Supabase to load
    const interval = setInterval(() => {
      if (checkSupabase()) {
        clearInterval(interval);
      }
    }, 100);

    // Clear interval after 10 seconds
    setTimeout(() => {
      clearInterval(interval);
      console.warn('🧹 [CleanAuthSolution] Timeout waiting for Supabase');
    }, 10000);
  }

  // Step 3: Clear any corrupted authentication state
  function clearCorruptedAuthState() {
    console.log('🧹 [CleanAuthSolution] Checking for corrupted auth state...');
    
    try {
      // Check for expired tokens in localStorage
      const authKeys = [
        'sb-utopqxsvudgrtiwenlzl-auth-token',
        'supabase.auth.token'
      ];

      authKeys.forEach(key => {
        const authData = localStorage.getItem(key);
        if (authData) {
          try {
            const parsed = JSON.parse(authData);
            if (parsed.access_token) {
              // Decode JWT to check expiration
              const payload = JSON.parse(atob(parsed.access_token.split('.')[1]));
              const now = Math.floor(Date.now() / 1000);
              
              if (payload.exp && payload.exp < now) {
                console.log(`🧹 [CleanAuthSolution] Removing expired token: ${key}`);
                localStorage.removeItem(key);
              }
            }
          } catch (e) {
            console.log(`🧹 [CleanAuthSolution] Removing malformed token: ${key}`);
            localStorage.removeItem(key);
          }
        }
      });
    } catch (e) {
      console.log('🧹 [CleanAuthSolution] Error checking auth state:', e);
    }
  }

  // Step 4: Set up proper auth state monitoring
  function setupAuthStateMonitoring() {
    console.log('🧹 [CleanAuthSolution] Setting up auth state monitoring...');
    
    // Wait for Supabase client to be available
    const setupMonitoring = () => {
      if (window.supabase && window.supabase.auth) {
        window.supabase.auth.onAuthStateChange((event, session) => {
          console.log('🧹 [CleanAuthSolution] Auth state changed:', event);
          
          if (event === 'SIGNED_OUT') {
            console.log('🧹 [CleanAuthSolution] User signed out, clearing local state');
          } else if (event === 'SIGNED_IN' && session) {
            console.log('🧹 [CleanAuthSolution] User signed in:', session.user.email);
          } else if (event === 'TOKEN_REFRESHED' && session) {
            console.log('🧹 [CleanAuthSolution] Token refreshed successfully');
          }
        });
        
        console.log('✅ [CleanAuthSolution] Auth state monitoring set up');
        return true;
      }
      return false;
    };

    // Try immediately
    if (setupMonitoring()) {
      return;
    }

    // Wait for Supabase client
    const interval = setInterval(() => {
      if (setupMonitoring()) {
        clearInterval(interval);
      }
    }, 100);

    setTimeout(() => {
      clearInterval(interval);
    }, 10000);
  }

  // Step 5: Disable all conflicting scripts
  function disableConflictingScripts() {
    console.log('🧹 [CleanAuthSolution] Disabling conflicting scripts...');
    
    // Mark conflicting fixes as disabled
    window.__FINAL_SUPABASE_FIX_DISABLED = true;
    window.__FIX_KEY_CONFUSION_DISABLED = true;
    window.__CONSOLIDATED_DASHBOARD_FIX_DISABLED = true;
    window.__VAPI_NETWORK_INTERCEPTOR_DISABLED = true;
    
    console.log('✅ [CleanAuthSolution] Conflicting scripts disabled');
  }

  // Step 6: Test the solution
  function testSolution() {
    console.log('🧹 [CleanAuthSolution] Testing the solution...');
    
    setTimeout(async () => {
      if (window.supabase) {
        try {
          // Test a simple query
          const { data, error } = await window.supabase
            .from('attorneys')
            .select('id')
            .limit(1);

          if (error) {
            console.error('🧹 [CleanAuthSolution] Test failed:', error.message);
          } else {
            console.log('✅ [CleanAuthSolution] Test passed - Supabase working correctly');
          }
        } catch (e) {
          console.error('🧹 [CleanAuthSolution] Test error:', e.message);
        }
      }
    }, 2000);
  }

  // Execute the solution
  function executeSolution() {
    console.log('🧹 [CleanAuthSolution] Executing clean authentication solution...');
    
    // Execute steps in order
    disableConflictingScripts();
    restoreOriginalFetch();
    clearCorruptedAuthState();
    ensureSupabaseClient();
    setupAuthStateMonitoring();
    testSolution();
    
    // Mark as installed
    window.__CLEAN_AUTH_SOLUTION_INSTALLED = true;
    
    console.log('✅ [CleanAuthSolution] Clean authentication solution complete');
  }

  // Run the solution
  executeSolution();

})();
