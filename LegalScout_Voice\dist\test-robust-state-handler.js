/**
 * Test Robust State Handler Implementation
 * 
 * This script tests the robust state handler pattern to ensure:
 * 1. New accounts get default assistants auto-created
 * 2. Existing accounts load from Vapi
 * 3. UI reflects current Vapi configuration
 * 4. No duplicate assistant creation
 */

(function testRobustStateHandler() {
  console.log('🧪 [TestRobustStateHandler] Starting comprehensive tests...');
  
  // Wait for dependencies
  let checkInterval = setInterval(() => {
    if (window.resolveAttorneyState && window.supabase) {
      clearInterval(checkInterval);
      console.log('🔗 [TestRobustStateHandler] Dependencies ready, running tests...');
      runTests();
    }
  }, 1000);
  
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[TestRobustStateHandler] Timed out waiting for dependencies');
  }, 10000);
  
  async function runTests() {
    console.log('🚀 [TestRobustStateHandler] Running robust state handler tests...');
    
    // Test 1: New account scenario
    await testNewAccountScenario();
    
    // Test 2: Existing account scenario
    await testExistingAccountScenario();
    
    // Test 3: Duplicate prevention
    await testDuplicatePrevention();
    
    console.log('✅ [TestRobustStateHandler] All tests completed');
  }
  
  async function testNewAccountScenario() {
    console.log('🧪 [TestRobustStateHandler] Test 1: New account scenario');
    
    try {
      // Simulate new account email
      const testEmail = '<EMAIL>';
      
      console.log(`Testing new account: ${testEmail}`);
      
      // This should:
      // 1. Create attorney record with defaults
      // 2. Auto-create default assistant in Vapi
      // 3. Save assistant ID to Supabase
      // 4. Return complete state
      const result = await window.resolveAttorneyState(testEmail);
      
      console.log('New account test result:', result);
      
      if (result.success && result.attorney && result.selectedAssistant) {
        console.log('✅ [TestRobustStateHandler] New account test PASSED');
        console.log('  - Attorney created:', result.attorney.firm_name);
        console.log('  - Assistant created:', result.selectedAssistant.id);
        console.log('  - No creation needed:', !result.needsCreation);
      } else {
        console.error('❌ [TestRobustStateHandler] New account test FAILED');
        console.error('  - Result:', result);
      }
      
    } catch (error) {
      console.error('❌ [TestRobustStateHandler] New account test ERROR:', error);
    }
  }
  
  async function testExistingAccountScenario() {
    console.log('🧪 [TestRobustStateHandler] Test 2: Existing account scenario');
    
    try {
      // Test with known existing account
      const testEmail = '<EMAIL>';
      
      console.log(`Testing existing account: ${testEmail}`);
      
      // This should:
      // 1. Find existing attorney record
      // 2. Load existing assistant from Vapi
      // 3. Return current state
      const result = await window.resolveAttorneyState(testEmail);
      
      console.log('Existing account test result:', result);
      
      if (result.success && result.attorney && result.selectedAssistant) {
        console.log('✅ [TestRobustStateHandler] Existing account test PASSED');
        console.log('  - Attorney found:', result.attorney.firm_name);
        console.log('  - Assistant loaded:', result.selectedAssistant.id);
        console.log('  - No creation needed:', !result.needsCreation);
      } else {
        console.error('❌ [TestRobustStateHandler] Existing account test FAILED');
        console.error('  - Result:', result);
      }
      
    } catch (error) {
      console.error('❌ [TestRobustStateHandler] Existing account test ERROR:', error);
    }
  }
  
  async function testDuplicatePrevention() {
    console.log('🧪 [TestRobustStateHandler] Test 3: Duplicate prevention');
    
    try {
      // Test calling resolveAttorneyState multiple times for same email
      const testEmail = '<EMAIL>';
      
      console.log(`Testing duplicate prevention for: ${testEmail}`);
      
      // Call multiple times rapidly
      const promises = [
        window.resolveAttorneyState(testEmail),
        window.resolveAttorneyState(testEmail),
        window.resolveAttorneyState(testEmail)
      ];
      
      const results = await Promise.all(promises);
      
      console.log('Duplicate prevention test results:', results);
      
      // All should succeed and return same assistant ID
      const assistantIds = results
        .filter(r => r.success && r.selectedAssistant)
        .map(r => r.selectedAssistant.id);
      
      const uniqueIds = [...new Set(assistantIds)];
      
      if (uniqueIds.length === 1) {
        console.log('✅ [TestRobustStateHandler] Duplicate prevention test PASSED');
        console.log('  - All calls returned same assistant ID:', uniqueIds[0]);
      } else {
        console.error('❌ [TestRobustStateHandler] Duplicate prevention test FAILED');
        console.error('  - Multiple assistant IDs returned:', uniqueIds);
      }
      
    } catch (error) {
      console.error('❌ [TestRobustStateHandler] Duplicate prevention test ERROR:', error);
    }
  }
  
  // Expose test function globally for manual testing
  window.testRobustStateHandler = runTests;
  
})();
