/**
 * Attorney-Specific Vapi Webhook Handler
 *
 * Supports both:
 * 1. Attorney-specific URLs: https://damon.legalscout.net/api/vapi-webhook-direct
 * 2. Legacy URL with assistant lookup: https://legalscout.net/api/vapi-webhook-direct
 */

export default async function handler(req, res) {
  try {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS, GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Vapi-Signature');

    // Handle OPTIONS request for CORS
    if (req.method === 'OPTIONS') {
      return res.status(200).json({ message: 'CORS preflight successful' });
    }

    // Extract attorney subdomain from request
    const attorneySubdomain = extractAttorneySubdomain(req);

    // Handle GET request for testing
    if (req.method === 'GET') {
      const response = {
        message: 'Attorney-specific Vapi webhook endpoint is working',
        timestamp: new Date().toISOString(),
        method: 'GET',
        status: 'healthy',
        attorneySubdomain: attorneySubdomain || 'none detected'
      };

      // Add debug info if requested
      if (req.query.debug === 'true') {
        const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
        const supabaseKey = process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY;
        const correctUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';

        response.debug = {
          supabaseUrl: supabaseUrl,
          keyLength: supabaseKey?.length,
          isCorrectUrl: supabaseUrl === correctUrl,
          correctUrl: correctUrl,
          hostname: req.headers.host,
          attorneySubdomain: attorneySubdomain
        };
      }

      return res.status(200).json(response);
    }

    // Handle POST request (actual webhook)
    if (req.method === 'POST') {
      const callData = req.body;

      console.log('📞 Attorney-specific webhook received call:', callData?.id, 'status:', callData?.status, 'subdomain:', attorneySubdomain);

      // Basic validation
      if (!callData || !callData.id || !callData.assistant_id) {
        return res.status(400).json({ error: 'Invalid call data' });
      }

      // Try to process with Supabase
      try {
        const result = await processCallWithSupabase(callData, attorneySubdomain);

        return res.status(200).json({
          success: true,
          message: 'Call processed successfully',
          timestamp: new Date().toISOString(),
          callId: callData.id,
          attorneySubdomain: attorneySubdomain,
          result: result
        });
      } catch (supabaseError) {
        console.error('Supabase processing failed:', supabaseError);

        // Return success anyway to not break Vapi, but log the issue
        return res.status(200).json({
          success: true,
          message: 'Webhook received but Supabase processing failed',
          timestamp: new Date().toISOString(),
          callId: callData.id,
          attorneySubdomain: attorneySubdomain,
          error: supabaseError.message
        });
      }
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });

  } catch (error) {
    console.error('Error in direct webhook handler:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Extract attorney subdomain from request
 * @param {Object} req - The request object
 * @returns {string|null} The attorney subdomain or null if not found
 */
function extractAttorneySubdomain(req) {
  try {
    const hostname = req.headers.host || req.headers['x-forwarded-host'];
    if (!hostname) return null;

    const parts = hostname.split('.');

    // For attorney subdomains like damon.legalscout.net
    if (parts.length >= 3 && parts[0] !== 'www' && parts[0] !== 'api') {
      return parts[0].toLowerCase();
    }

    return null;
  } catch (error) {
    console.error('Error extracting subdomain:', error);
    return null;
  }
}

/**
 * Process call data with Supabase
 * @param {Object} callData - The call data from Vapi
 * @param {string|null} attorneySubdomain - The attorney subdomain (if any)
 */
async function processCallWithSupabase(callData, attorneySubdomain = null) {
  // Dynamic import to avoid module loading issues
  const { createClient } = await import('@supabase/supabase-js');

  // Use the correct Supabase configuration with SERVICE ROLE key for webhook operations
  const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
  const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczODk0ODAwNywiZXhwIjoyMDU0NTI0MDA3fQ.Noq994xfKMoQipfGli9fZcgQYig9fZovjqdEnpBe7CM';

  if (!supabaseUrl || !supabaseKey) {
    throw new Error(`Supabase configuration missing - URL: ${supabaseUrl ? 'Set' : 'Missing'}, Key: ${supabaseKey ? 'Set' : 'Missing'}`);
  }

  console.log('Supabase config:', {
    fullUrl: supabaseUrl,
    keyLength: supabaseKey?.length,
    isCorrectUrl: supabaseUrl === 'https://utopqxsvudgrtiwenlzl.supabase.co',
    keyType: supabaseKey?.includes('service_role') ? 'service_role' : 'anon'
  });

  // Create Supabase client with SERVICE ROLE key for webhook operations
  const supabase = createClient(supabaseUrl, supabaseKey);

  let attorney = null;
  let attorneyError = null;

  // Try subdomain-based lookup first (more efficient)
  if (attorneySubdomain) {
    console.log(`Looking up attorney by subdomain: ${attorneySubdomain}`);
    const result = await supabase
      .from('attorneys')
      .select('id, subdomain, firm_name')
      .eq('subdomain', attorneySubdomain)
      .single();

    attorney = result.data;
    attorneyError = result.error;

    if (attorney) {
      console.log(`Found attorney by subdomain: ${attorney.firm_name} (${attorney.id})`);
    }
  }

  // Fallback to assistant ID lookup if subdomain lookup failed
  if (!attorney) {
    console.log(`Looking up attorney by assistant ID: ${callData.assistant_id}`);
    const result = await supabase
      .from('attorneys')
      .select('id, subdomain, firm_name')
      .eq('vapi_assistant_id', callData.assistant_id)
      .single();

    attorney = result.data;
    attorneyError = result.error;

    if (attorney) {
      console.log(`Found attorney by assistant ID: ${attorney.firm_name} (${attorney.id})`);
    }
  }

  if (attorneyError || !attorney) {
    const lookupMethod = attorneySubdomain ? `subdomain: ${attorneySubdomain}` : `assistant ID: ${callData.assistant_id}`;
    throw new Error(`Attorney not found for ${lookupMethod}`);
  }

  const attorneyId = attorney.id;

  console.log('Creating consultation record for Briefs page (bypassing call_records)...');

  // Skip call_records table (has foreign key issues) and go directly to consultations
  // The Briefs page only reads from the consultations table anyway

  // Create consultation for any call that has meaningful data
  if (callData.status && (callData.status === 'completed' || callData.status === 'ended' || callData.transcripts?.length > 0)) {
    const consultationRecord = {
      attorney_id: attorneyId,
      client_name: 'Anonymous Client',
      client_email: null,
      client_phone: callData.customer?.phone_number,
      summary: `Call with ${callData.customer?.phone_number || 'unknown'} on ${new Date().toLocaleString()}. Duration: ${Math.round((callData.duration || 0) / 60)} minutes.`,
      transcript: generateTranscript(callData),
      duration: callData.duration,
      practice_area: null,
      location: null,
      location_data: {},
      metadata: {
        call_id: callData.id,
        assistant_id: callData.assistant_id,
        webhook_processed: true
      },
      status: 'new'
    };

    const { data: consultationResult, error: consultationError } = await supabase
      .from('consultations')
      .insert(consultationRecord)
      .select();

    if (consultationError) {
      throw new Error(`Failed to create consultation: ${consultationError.message}`);
    }

    return {
      callRecord: callResult,
      consultation: consultationResult
    };
  }

  return {
    callRecord: callResult,
    consultation: null
  };
}

/**
 * Generate transcript from call data
 */
function generateTranscript(callData) {
  if (!callData.transcripts || !Array.isArray(callData.transcripts)) {
    return 'No transcript available';
  }

  return callData.transcripts
    .map(transcript => {
      const speaker = transcript.role === 'assistant' ? 'Assistant' : 'Client';
      const timestamp = transcript.timestamp ? new Date(transcript.timestamp).toLocaleTimeString() : 'Unknown time';
      return `[${timestamp}] ${speaker}: ${transcript.text || transcript.message || ''}`;
    })
    .join('\n\n');
}
