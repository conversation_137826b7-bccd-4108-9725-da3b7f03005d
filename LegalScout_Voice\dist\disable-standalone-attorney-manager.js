/**
 * Disable Standalone Attorney Manager
 * 
 * This script prevents the old StandaloneAttorneyManager from loading
 * to avoid conflicts with the new unified attorney manager.
 */

(function() {
  console.log('[DisableStandaloneAttorneyManager] Preventing StandaloneAttorneyManager from loading...');

  // Create a placeholder that prevents the real StandaloneAttorneyManager from initializing
  window.standaloneAttorneyManager = {
    disabled: true,
    message: 'StandaloneAttorneyManager has been replaced by UnifiedAttorneyManager',
    
    // Provide stub methods to prevent errors
    initialize: () => {
      console.warn('[DisableStandaloneAttorneyManager] StandaloneAttorneyManager.initialize() called but manager is disabled');
      return Promise.resolve();
    },
    
    loadAttorneyBySubdomain: () => {
      console.warn('[DisableStandaloneAttorneyManager] StandaloneAttorneyManager.loadAttorneyBySubdomain() called but manager is disabled');
      return Promise.resolve();
    },
    
    subscribe: () => {
      console.warn('[DisableStandaloneAttorneyManager] StandaloneAttorneyManager.subscribe() called but manager is disabled');
      return () => {}; // Return unsubscribe function
    },
    
    unsubscribe: () => {
      console.warn('[DisableStandaloneAttorneyManager] StandaloneAttorneyManager.unsubscribe() called but manager is disabled');
    },
    
    updateAttorney: () => {
      console.warn('[DisableStandaloneAttorneyManager] StandaloneAttorneyManager.updateAttorney() called but manager is disabled');
      return Promise.resolve();
    },
    
    getAttorney: () => {
      console.warn('[DisableStandaloneAttorneyManager] StandaloneAttorneyManager.getAttorney() called but manager is disabled');
      return null;
    },
    
    attorney: null,
    isInitialized: false,
    isLoading: false,
    isSaving: false,
    lastError: null
  };

  console.log('[DisableStandaloneAttorneyManager] StandaloneAttorneyManager disabled successfully');
})();
