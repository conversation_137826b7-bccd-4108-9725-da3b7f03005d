# Enhanced Vapi Components

This document provides an overview of the enhanced Vapi components that have been implemented in the LegalScout Voice project. These components provide a more modern and consistent UI for voice interactions with Vapi assistants.

## Overview

The enhanced Vapi components are designed to be drop-in replacements for the existing components, with improved functionality and a more consistent UI. They use the `use-vapi` hook for consistent functionality and are styled to match the existing UI design.

## Components

### 1. `use-vapi` Hook

**File:** `src/hooks/use-vapi.ts`

A unified hook that provides consistent access to Vapi functionality throughout the application. It handles:

- Volume level tracking
- Session state management
- Conversation history
- Speaker detection
- Dossier data collection

### 2. EnhancedSpeechParticles

**File:** `src/components/EnhancedSpeechParticles.jsx`

A visualization component that shows audio levels with different colors for user and assistant speech. It uses the `updateAudioSource` function to control:

- Amplitude (volume level)
- Frequency (pitch)
- Speaker (user or assistant)

The component is styled with:
- Light blue for assistant speech
- Green for user speech
- Transparent background
- Wave-like appearance

### 3. EnhancedCallController

**File:** `src/components/call/EnhancedCallController.jsx`

A complete call control interface with:
- Status indicators
- Call button
- Transcript display
- Speech visualization

### 4. EnhancedVapiCall

**File:** `src/components/EnhancedVapiCall.jsx`

A full-featured call component that replaces the existing VapiCall component. It includes:
- Speech visualization
- Transcript display
- Dossier data collection
- Call controls

### 5. EnhancedAgentPreview

**File:** `src/components/EnhancedAgentPreview.jsx`

A preview component for the dashboard that shows how the attorney's AI assistant will appear and function for potential clients. It can:
- Switch between controller view and full call view
- Use the attorney's actual Vapi assistant configuration
- Show speech visualization

### 6. EnhancedPreviewTab

**File:** `src/components/dashboard/EnhancedPreviewTab.jsx`

A dashboard tab component that contains the enhanced agent preview and controls for toggling theme and visibility.

### 7. VapiDemo Page

**File:** `src/pages/VapiDemo.jsx`

A page that showcases all the enhanced components, allowing you to see them in action and test them with your Vapi assistants. It includes tabs for:
- Call Controller
- Meeting Scheduler
- Speech Visualization

## Integration

To integrate these components into your application, see the following documents:

- [Dashboard Integration Example](./DASHBOARD_INTEGRATION_EXAMPLE.md)
- [Agent Page Integration Example](./AGENT_PAGE_INTEGRATION_EXAMPLE.md)

## Original Components

The original components have been moved to the `src/components/legacy` directory for reference. These include:

- `src/components/legacy/VapiCall.jsx`
- `src/components/legacy/SpeechParticles.jsx`

## Styling

The enhanced components are styled to match the existing UI design, with:

- Light blue for assistant speech
- Green for user speech
- Transparent background for speech particles
- Light thin blue piping around dossier component edges
- Transparent scrollbars with light thin blue piping

## Configuration

The speech particles visualization can be configured using the tool at https://codepen.io/Damon-Kost/pen/KwwjmNx. This allows you to:

- Adjust the amplitude and frequency response
- Change the colors for user and assistant speech
- Modify the wave appearance

## Next Steps

1. **Integration Testing**: Test these components with real Vapi assistants to ensure they work as expected.

2. **Dashboard Integration**: Replace the existing preview tab in the dashboard with the `EnhancedPreviewTab` component.

3. **Agent Page Integration**: Replace the existing call button on the agent page with the `EnhancedCallController` component.

4. **Route Configuration**: Add the VapiDemo page to your routes to showcase all the enhanced components.

5. **Documentation Review**: Review the documentation to ensure it's up-to-date and accurate.

## Technical Details

### Vapi SDK Communication

The Vapi SDK communicates audio levels and transcripts through iframe messages with the following format:

```javascript
{
  what: 'iframe-call-message',
  action: 'remote-participants-audio-level',
  participantsAudioLevel: {
    // Audio levels for each participant
  }
}
```

### Speech Animation

The speech animation uses the `updateAudioSource` function to control:

```javascript
window.updateAudioSource(amplitude, frequency, speaker);
```

Where:
- `amplitude` is a number between 0 and 1 representing the volume level
- `frequency` is a number representing the pitch (in Hz)
- `speaker` is a string, either 'user' or 'assistant'

### MCP Server

The Vapi MCP server provides a consistent way to interact with Vapi services through tool calls. It exposes all Vapi endpoints as tools for any LLM or agent framework.

## Resources

- [Vapi Blocks](https://vapiblocks.com) - A resource for Vapi implementation
- [Vapi MCP Server Documentation](https://docs.vapi.ai/mcp-server) - Documentation for the Vapi MCP server
- [Speech Particles Configuration Tool](https://codepen.io/Damon-Kost/pen/KwwjmNx) - Tool for configuring speech particles
