/**
 * Vapi MCP Test Endpoint
 * Provides diagnostic testing for Vapi MCP integration
 */

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { action } = req.body;
    const vapiPrivateKey = process.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

    console.log('[Vapi MCP Test] Action:', action);

    switch (action) {
      case 'list_assistants':
        return await testListAssistants(res, vapiPrivateKey);
      
      case 'test_mcp':
        return await testMcpConnection(res);
      
      case 'test_config':
        return await testConfiguration(res);
      
      default:
        return res.status(400).json({ error: 'Unknown action' });
    }
  } catch (error) {
    console.error('[Vapi MCP Test] Error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
}

/**
 * Test direct Vapi API call
 */
async function testListAssistants(res, apiKey) {
  try {
    console.log('[Vapi MCP Test] Testing direct API call...');
    
    const response = await fetch('https://api.vapi.ai/assistant', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Vapi API error: ${response.status} ${response.statusText}`);
    }

    const assistants = await response.json();
    
    console.log('[Vapi MCP Test] Found assistants:', assistants.length);
    
    return res.status(200).json({
      success: true,
      method: 'direct_api',
      count: assistants.length,
      assistants: assistants.map(a => ({
        id: a.id,
        name: a.name,
        createdAt: a.createdAt,
        updatedAt: a.updatedAt
      }))
    });
  } catch (error) {
    console.error('[Vapi MCP Test] Direct API error:', error);
    return res.status(500).json({
      success: false,
      method: 'direct_api',
      error: error.message
    });
  }
}

/**
 * Test MCP connection
 */
async function testMcpConnection(res) {
  try {
    console.log('[Vapi MCP Test] Testing MCP connection...');
    
    // Try to import and test MCP service
    let mcpResult = null;
    try {
      // This would normally import the MCP service
      // For now, we'll simulate the test
      mcpResult = {
        available: false,
        reason: 'MCP server not available in serverless environment'
      };
    } catch (mcpError) {
      mcpResult = {
        available: false,
        error: mcpError.message
      };
    }
    
    return res.status(200).json({
      success: true,
      method: 'mcp_test',
      mcp: mcpResult
    });
  } catch (error) {
    console.error('[Vapi MCP Test] MCP test error:', error);
    return res.status(500).json({
      success: false,
      method: 'mcp_test',
      error: error.message
    });
  }
}

/**
 * Test configuration
 */
async function testConfiguration(res) {
  try {
    console.log('[Vapi MCP Test] Testing configuration...');
    
    const config = {
      hasPrivateKey: !!process.env.VITE_VAPI_PRIVATE_KEY,
      hasPublicKey: !!process.env.VITE_VAPI_PUBLIC_KEY,
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
      runtime: 'serverless'
    };
    
    return res.status(200).json({
      success: true,
      method: 'config_test',
      config
    });
  } catch (error) {
    console.error('[Vapi MCP Test] Config test error:', error);
    return res.status(500).json({
      success: false,
      method: 'config_test',
      error: error.message
    });
  }
}
