import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  preview_instances: defineTable({
    firmName: v.string(),
    attorneyName: v.string(),
    practiceAreas: v.array(v.string()),
    state: v.string(),
    logoUrl: v.optional(v.string()),
    backgroundColor: v.string(),
    templateColors: v.object({
      primary: v.string(),
      secondary: v.string(),
    }),
    scrapedData: v.optional(v.object({
      url: v.string(),
      content: v.array(v.any()),
      timestamp: v.number(),
    })),
    expiresAt: v.number(),
    customInstructions: v.optional(v.string()),
  }).index("by_expiration", ["expiresAt"]),
  
  // ... existing tables
}); 