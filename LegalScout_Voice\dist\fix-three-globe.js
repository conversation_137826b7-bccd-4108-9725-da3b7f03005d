/**
 * Fix Three Globe
 * 
 * This script ensures that Three.js is properly loaded before three-globe tries to use it.
 */

(function() {
  console.log('[ThreeGlobeFix] Starting Three.js Globe fix...');
  
  // Check if THREE is already defined
  if (typeof THREE === 'undefined') {
    console.log('[ThreeGlobeFix] THREE is not defined, creating global object');
    
    // Create a placeholder THREE object
    window.THREE = window.THREE || {};
    
    // Load Three.js dynamically
    const loadThreeJs = () => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/three@0.174.0/build/three.min.js';
        script.onload = () => {
          console.log('[ThreeGlobeFix] Three.js loaded successfully');
          resolve();
        };
        script.onerror = (error) => {
          console.error('[ThreeGlobeFix] Error loading Three.js:', error);
          reject(error);
        };
        document.head.appendChild(script);
      });
    };
    
    // Load OrbitControls
    const loadOrbitControls = () => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/three@0.174.0/examples/js/controls/OrbitControls.js';
        script.onload = () => {
          console.log('[ThreeGlobeFix] OrbitControls loaded successfully');
          resolve();
        };
        script.onerror = (error) => {
          console.error('[ThreeGlobeFix] Error loading OrbitControls:', error);
          reject(error);
        };
        document.head.appendChild(script);
      });
    };
    
    // Load Three Globe
    const loadThreeGlobe = () => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/three-globe@2.42.1/dist/three-globe.min.js';
        script.onload = () => {
          console.log('[ThreeGlobeFix] Three Globe loaded successfully');
          resolve();
        };
        script.onerror = (error) => {
          console.error('[ThreeGlobeFix] Error loading Three Globe:', error);
          reject(error);
        };
        document.head.appendChild(script);
      });
    };
    
    // Load scripts in the correct order
    loadThreeJs()
      .then(() => loadOrbitControls())
      .then(() => loadThreeGlobe())
      .then(() => {
        console.log('[ThreeGlobeFix] All Three.js dependencies loaded successfully');
      })
      .catch((error) => {
        console.error('[ThreeGlobeFix] Error loading dependencies:', error);
      });
  } else {
    console.log('[ThreeGlobeFix] THREE is already defined');
  }
  
  // Patch the ThreeGlobe constructor to ensure THREE is available
  const patchThreeGlobe = () => {
    if (typeof ThreeGlobe !== 'undefined') {
      console.log('[ThreeGlobeFix] Patching ThreeGlobe constructor');
      
      const originalThreeGlobe = ThreeGlobe;
      
      window.ThreeGlobe = function(...args) {
        // Ensure THREE.Group exists
        if (!THREE.Group) {
          console.log('[ThreeGlobeFix] Creating THREE.Group');
          THREE.Group = function() {
            this.type = 'Group';
            this.children = [];
            this.add = function(obj) { this.children.push(obj); return this; };
            this.remove = function(obj) {
              const index = this.children.indexOf(obj);
              if (index !== -1) this.children.splice(index, 1);
              return this;
            };
          };
        }
        
        return new originalThreeGlobe(...args);
      };
    } else {
      // If ThreeGlobe is not defined yet, try again later
      setTimeout(patchThreeGlobe, 100);
    }
  };
  
  // Start patching
  patchThreeGlobe();
  
  console.log('[ThreeGlobeFix] Three.js Globe fix initialized');
})();
