/**
 * Fix MCP Connection Issues
 * 
 * This script fixes the MCP connection issues by ensuring the Vapi MCP service
 * falls back to direct API mode when MCP connections fail.
 */

console.log('[FixMcpConnection] Starting fix...');

// Function to create a mock MCP service that always uses direct API
function createMockMcpService() {
  return {
    invoke: async function(toolName, args = {}) {
      console.log('[MockMcpService] Invoking tool:', toolName, 'with args:', args);
      
      // Use the Vapi private key for API calls
      const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
      
      try {
        switch (toolName) {
          case 'list_assistants_vapi-mcp-server':
            const assistantsResponse = await fetch('https://api.vapi.ai/assistant', {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
              }
            });
            
            if (assistantsResponse.ok) {
              const assistants = await assistantsResponse.json();
              console.log('[MockMcpService] Listed assistants:', assistants.length);
              return assistants;
            } else {
              console.warn('[MockMcpService] Failed to list assistants:', assistantsResponse.status);
              return [];
            }
            
          case 'get_assistant_vapi-mcp-server':
            const assistantId = args.assistantId;
            if (!assistantId) {
              throw new Error('Assistant ID is required');
            }
            
            const assistantResponse = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
              }
            });
            
            if (assistantResponse.ok) {
              const assistant = await assistantResponse.json();
              console.log('[MockMcpService] Got assistant:', assistant.id);
              return assistant;
            } else if (assistantResponse.status === 404) {
              console.warn('[MockMcpService] Assistant not found:', assistantId);
              return null;
            } else {
              throw new Error(`Failed to get assistant: ${assistantResponse.status}`);
            }
            
          case 'create_assistant_vapi-mcp-server':
            const createResponse = await fetch('https://api.vapi.ai/assistant', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify(args)
            });
            
            if (createResponse.ok) {
              const newAssistant = await createResponse.json();
              console.log('[MockMcpService] Created assistant:', newAssistant.id);
              return newAssistant;
            } else {
              const errorText = await createResponse.text();
              throw new Error(`Failed to create assistant: ${createResponse.status} - ${errorText}`);
            }
            
          default:
            console.warn('[MockMcpService] Unknown tool:', toolName);
            throw new Error(`Unknown tool: ${toolName}`);
        }
      } catch (error) {
        console.error('[MockMcpService] Error invoking tool:', toolName, error);
        throw error;
      }
    }
  };
}

// Function to apply the fix
function applyFix() {
  try {
    // Create a mock MCP service that uses direct API calls
    const mockMcp = createMockMcpService();
    
    // Set it as the global MCP service
    window.mcp = mockMcp;
    
    console.log('[FixMcpConnection] Mock MCP service created and set as global');
    
    // Also fix the Vapi MCP service if it exists
    if (window.vapiMcpService) {
      const service = window.vapiMcpService;
      
      // Force it to use direct API mode
      service.useDirect = true;
      service.directApiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
      service.directApiUrl = 'https://api.vapi.ai';
      service.connected = true;
      
      console.log('[FixMcpConnection] Forced Vapi MCP service to use direct API mode');
    }
    
    // Fix the standalone attorney manager's Vapi config initialization
    if (window.standaloneAttorneyManager) {
      const manager = window.standaloneAttorneyManager;
      
      // Override the initializeVapiConfig method to use the mock MCP
      const originalInitializeVapiConfig = manager.initializeVapiConfig;
      manager.initializeVapiConfig = async function() {
        try {
          console.log('[FixMcpConnection] Using mock MCP for Vapi config initialization');
          
          // Use the mock MCP to get assistants
          const assistants = await mockMcp.invoke('list_assistants_vapi-mcp-server', {});
          
          if (assistants && assistants.length > 0) {
            const config = this.extractVapiConfig(assistants);
            
            // Cache the config
            localStorage.setItem('vapi_config', JSON.stringify(config));
            localStorage.setItem('vapi_config_timestamp', Date.now().toString());
            
            console.log('[FixMcpConnection] Vapi config initialized via mock MCP');
            return config;
          }
        } catch (error) {
          console.warn('[FixMcpConnection] Error initializing Vapi config via mock MCP:', error);
        }
        
        // Fall back to default config
        const config = this.getDefaultVapiConfig();
        localStorage.setItem('vapi_config', JSON.stringify(config));
        localStorage.setItem('vapi_config_timestamp', Date.now().toString());
        
        console.log('[FixMcpConnection] Using default Vapi config');
        return config;
      };
      
      console.log('[FixMcpConnection] Enhanced standalone attorney manager with mock MCP');
    }
    
    console.log('[FixMcpConnection] Fix applied successfully');
    
  } catch (error) {
    console.error('[FixMcpConnection] Error applying fix:', error);
  }
}

// Apply the fix immediately
applyFix();

// Also apply the fix when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFix);
} else {
  // DOM is already ready, apply fix after a short delay
  setTimeout(applyFix, 100);
}

// Apply the fix when services become available
let checkCount = 0;
const maxChecks = 50; // Check for up to 5 seconds

function checkForServices() {
  checkCount++;
  
  if (window.standaloneAttorneyManager || window.vapiMcpService) {
    console.log('[FixMcpConnection] Services found, applying fix');
    applyFix();
    return;
  }
  
  if (checkCount < maxChecks) {
    setTimeout(checkForServices, 100);
  } else {
    console.log('[FixMcpConnection] Services not found after waiting, but mock MCP is available');
  }
}

// Start checking for services
checkForServices();

console.log('[FixMcpConnection] Fix script loaded');
