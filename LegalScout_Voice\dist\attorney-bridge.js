/**
 * Attorney Bridge
 * 
 * This script bridges all attorney management systems to ensure they stay in sync
 * and provides a unified API for the application to use.
 */

(function() {
  console.log('[AttorneyBridge] Initializing...');
  
  // Create the bridge object
  const bridge = {
    // Current attorney data
    attorney: null,
    
    // State flags
    isLoading: false,
    isSaving: false,
    isSyncing: false,
    
    // Error tracking
    lastError: null,
    
    // Subscribers
    subscribers: [],
    
    /**
     * Initialize the bridge
     */
    initialize: async function() {
      console.log('[AttorneyBridge] Starting initialization...');
      
      try {
        this.isLoading = true;
        
        // Load attorney from localStorage first for quick access
        this.loadFromLocalStorage();
        
        // Then try to load from Supabase for most up-to-date data
        await this.loadFromSupabase();
        
        // If we still don't have an attorney, create a default one
        if (!this.attorney) {
          console.log('[AttorneyBridge] No attorney found, creating default');
          this.attorney = this.createDefaultAttorney();
          this.saveToAll(this.attorney);
        }
        
        // Sync with Vapi
        if (this.attorney && this.attorney.id) {
          this.syncWithVapi().catch(error => {
            console.warn('[AttorneyBridge] Initial Vapi sync failed:', error);
          });
        }
        
        console.log('[AttorneyBridge] Initialization complete');
        this.notifySubscribers();
      } catch (error) {
        console.error('[AttorneyBridge] Initialization failed:', error);
        this.lastError = error;
      } finally {
        this.isLoading = false;
      }
    },
    
    /**
     * Load attorney from localStorage
     */
    loadFromLocalStorage: function() {
      try {
        const attorneyJson = localStorage.getItem('attorney');
        
        if (attorneyJson) {
          const attorney = JSON.parse(attorneyJson);
          
          if (attorney && attorney.id) {
            console.log('[AttorneyBridge] Loaded attorney from localStorage:', attorney.id);
            this.attorney = attorney;
            this.notifySubscribers();
            return attorney;
          }
        }
        
        return null;
      } catch (error) {
        console.error('[AttorneyBridge] Error loading from localStorage:', error);
        return null;
      }
    },
    
    /**
     * Load attorney from Supabase
     */
    loadFromSupabase: async function() {
      try {
        // Check if Supabase is available
        if (!window.supabase) {
          console.warn('[AttorneyBridge] Supabase not available');
          return null;
        }
        
        // Get attorney ID from localStorage or current attorney
        const attorneyId = this.attorney?.id || localStorage.getItem('attorney_id');
        
        if (!attorneyId) {
          console.warn('[AttorneyBridge] No attorney ID available');
          return null;
        }
        
        console.log('[AttorneyBridge] Loading attorney from Supabase:', attorneyId);
        
        const { data, error } = await window.supabase
          .from('attorneys')
          .select('*')
          .eq('id', attorneyId)
          .single();
        
        if (error) {
          console.warn('[AttorneyBridge] Error loading from Supabase:', error);
          return null;
        }
        
        if (data) {
          console.log('[AttorneyBridge] Loaded attorney from Supabase:', data.id);
          this.attorney = data;
          this.saveToLocalStorage(data);
          this.notifySubscribers();
          return data;
        }
        
        return null;
      } catch (error) {
        console.error('[AttorneyBridge] Error loading from Supabase:', error);
        return null;
      }
    },
    
    /**
     * Save attorney to all storage systems
     * @param {Object} attorney - The attorney to save
     */
    saveToAll: function(attorney) {
      if (!attorney || !attorney.id) {
        console.warn('[AttorneyBridge] Cannot save invalid attorney');
        return;
      }
      
      try {
        this.isSaving = true;
        
        // Save to localStorage
        this.saveToLocalStorage(attorney);
        
        // Save to Supabase
        this.saveToSupabase(attorney).catch(error => {
          console.warn('[AttorneyBridge] Error saving to Supabase:', error);
        });
        
        // Update standalone attorney manager if available
        if (window.standaloneAttorneyManager) {
          window.standaloneAttorneyManager.attorney = attorney;
          window.standaloneAttorneyManager.notifySubscribers();
        }
        
        // Update attorney state manager if available
        if (window.attorneyStateManager) {
          window.attorneyStateManager.attorney = attorney;
          window.attorneyStateManager.notifySubscribers();
        }
        
        // Update local state
        this.attorney = attorney;
        this.notifySubscribers();
      } catch (error) {
        console.error('[AttorneyBridge] Error saving attorney:', error);
        this.lastError = error;
      } finally {
        this.isSaving = false;
      }
    },
    
    /**
     * Save attorney to localStorage
     * @param {Object} attorney - The attorney to save
     */
    saveToLocalStorage: function(attorney) {
      if (!attorney || !attorney.id) {
        console.warn('[AttorneyBridge] Cannot save invalid attorney to localStorage');
        return;
      }
      
      try {
        localStorage.setItem('attorney', JSON.stringify(attorney));
        localStorage.setItem('attorney_id', attorney.id);
        localStorage.setItem('attorney_version', Date.now().toString());
        
        console.log('[AttorneyBridge] Saved attorney to localStorage:', attorney.id);
      } catch (error) {
        console.error('[AttorneyBridge] Error saving to localStorage:', error);
      }
    },
    
    /**
     * Save attorney to Supabase
     * @param {Object} attorney - The attorney to save
     */
    saveToSupabase: async function(attorney) {
      if (!attorney || !attorney.id) {
        console.warn('[AttorneyBridge] Cannot save invalid attorney to Supabase');
        return null;
      }
      
      try {
        // Check if Supabase is available
        if (!window.supabase) {
          console.warn('[AttorneyBridge] Supabase not available');
          return null;
        }
        
        console.log('[AttorneyBridge] Saving attorney to Supabase:', attorney.id);
        
        const { data, error } = await window.supabase
          .from('attorneys')
          .upsert(attorney, { onConflict: 'id' })
          .select()
          .single();
        
        if (error) {
          console.warn('[AttorneyBridge] Error saving to Supabase:', error);
          return null;
        }
        
        if (data) {
          console.log('[AttorneyBridge] Saved attorney to Supabase:', data.id);
          return data;
        }
        
        return null;
      } catch (error) {
        console.error('[AttorneyBridge] Error saving to Supabase:', error);
        return null;
      }
    },
    
    /**
     * Sync attorney with Vapi
     */
    syncWithVapi: async function() {
      if (!this.attorney || !this.attorney.id) {
        console.warn('[AttorneyBridge] Cannot sync invalid attorney with Vapi');
        return { action: 'error', error: 'Invalid attorney' };
      }
      
      try {
        this.isSyncing = true;
        
        // Use the simplified Vapi integration if available
        if (window.vapiIntegration) {
          console.log('[AttorneyBridge] Using simplified Vapi integration');
          const result = await window.vapiIntegration.syncAttorney(this.attorney);
          
          // If the sync created or updated the assistant ID, save the changes
          if (result.action === 'created' && result.assistant && result.assistant.id) {
            const updatedAttorney = {
              ...this.attorney,
              vapi_assistant_id: result.assistant.id,
              updated_at: new Date().toISOString()
            };
            
            this.saveToAll(updatedAttorney);
          }
          
          return result;
        }
        
        // Fall back to standalone attorney manager if available
        if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.syncWithVapi) {
          console.log('[AttorneyBridge] Using standalone attorney manager for Vapi sync');
          return window.standaloneAttorneyManager.syncWithVapi(this.attorney);
        }
        
        // Fall back to attorney state manager if available
        if (window.attorneyStateManager && window.attorneyStateManager.syncWithVapi) {
          console.log('[AttorneyBridge] Using attorney state manager for Vapi sync');
          return window.attorneyStateManager.syncWithVapi({ force: true });
        }
        
        console.warn('[AttorneyBridge] No Vapi sync method available');
        return { action: 'none', error: 'No sync method available' };
      } catch (error) {
        console.error('[AttorneyBridge] Error syncing with Vapi:', error);
        this.lastError = error;
        return { action: 'error', error: error.message };
      } finally {
        this.isSyncing = false;
      }
    },
    
    /**
     * Update attorney
     * @param {Object} updates - The updates to apply
     */
    updateAttorney: function(updates) {
      if (!this.attorney || !this.attorney.id) {
        console.warn('[AttorneyBridge] Cannot update invalid attorney');
        return null;
      }
      
      try {
        // Create updated attorney
        const updatedAttorney = {
          ...this.attorney,
          ...updates,
          updated_at: new Date().toISOString()
        };
        
        // Ensure ID doesn't change
        updatedAttorney.id = this.attorney.id;
        
        // Save to all storage systems
        this.saveToAll(updatedAttorney);
        
        // Check if we need to sync with Vapi
        const needsVapiSync = [
          'firm_name',
          'name',
          'welcome_message',
          'vapi_instructions',
          'voice_provider',
          'voice_id'
        ].some(key => updates[key] !== undefined);
        
        if (needsVapiSync) {
          // Schedule a sync with Vapi
          setTimeout(() => {
            this.syncWithVapi().catch(error => {
              console.error('[AttorneyBridge] Error syncing with Vapi:', error);
            });
          }, 0);
        }
        
        return updatedAttorney;
      } catch (error) {
        console.error('[AttorneyBridge] Error updating attorney:', error);
        this.lastError = error;
        return null;
      }
    },
    
    /**
     * Create a default attorney
     * @param {Object} overrides - Properties to override in the default attorney
     */
    createDefaultAttorney: function(overrides = {}) {
      const defaultAttorney = {
        id: this.generateUUID(),
        subdomain: 'default',
        firm_name: 'Your Law Firm',
        name: 'Your Name',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        voice_provider: '11labs',
        voice_id: 'sarah',
        welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
        information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
        vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
        vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Default assistant ID
      };
      
      return { ...defaultAttorney, ...overrides };
    },
    
    /**
     * Generate a UUID v4
     */
    generateUUID: function() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    
    /**
     * Subscribe to attorney changes
     * @param {Function} callback - The callback to call when attorney changes
     */
    subscribe: function(callback) {
      if (typeof callback !== 'function') {
        console.warn('[AttorneyBridge] Cannot subscribe with non-function callback');
        return () => {};
      }
      
      this.subscribers.push(callback);
      
      // Call immediately with current state
      if (this.attorney) {
        callback(this.attorney);
      }
      
      // Return unsubscribe function
      return () => {
        this.subscribers = this.subscribers.filter(cb => cb !== callback);
      };
    },
    
    /**
     * Notify all subscribers of attorney changes
     */
    notifySubscribers: function() {
      if (!this.attorney) return;
      
      this.subscribers.forEach(callback => {
        try {
          callback(this.attorney);
        } catch (error) {
          console.error('[AttorneyBridge] Error in subscriber callback:', error);
        }
      });
    }
  };
  
  // Make the bridge available globally
  window.attorneyBridge = bridge;
  
  // Initialize the bridge
  bridge.initialize().catch(error => {
    console.error('[AttorneyBridge] Error in initialization:', error);
  });
  
  console.log('[AttorneyBridge] Bridge created and initialization started');
})();
