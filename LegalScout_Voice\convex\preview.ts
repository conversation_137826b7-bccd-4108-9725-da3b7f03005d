import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Create a new preview instance
export const createPreviewInstance = mutation({
  args: {
    firmName: v.string(),
    attorneyName: v.string(),
    practiceAreas: v.array(v.string()),
    state: v.string(),
    logoUrl: v.optional(v.string()),
    backgroundColor: v.string(),
    templateColors: v.object({
      primary: v.string(),
      secondary: v.string(),
    }),
    customInstructions: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const PREVIEW_EXPIRY_HOURS = 24;
    const expiresAt = Date.now() + PREVIEW_EXPIRY_HOURS * 60 * 60 * 1000;

    return await ctx.db.insert("preview_instances", {
      ...args,
      expiresAt,
    });
  },
});

// Get a preview instance by ID
export const getPreviewInstance = query({
  args: { id: v.id("preview_instances") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Update scraped data for a preview instance
export const updateScrapedData = mutation({
  args: {
    id: v.id("preview_instances"),
    url: v.string(),
    content: v.array(v.any()),
  },
  handler: async (ctx, args) => {
    const instance = await ctx.db.get(args.id);
    if (!instance) {
      throw new Error("Preview instance not found");
    }

    await ctx.db.patch(args.id, {
      scrapedData: {
        url: args.url,
        content: args.content,
        timestamp: Date.now(),
      },
    });
  },
});

// Update preview instance customizations
export const updatePreviewCustomizations = mutation({
  args: {
    id: v.id("preview_instances"),
    updates: v.object({
      firmName: v.optional(v.string()),
      attorneyName: v.optional(v.string()),
      practiceAreas: v.optional(v.array(v.string())),
      state: v.optional(v.string()),
      logoUrl: v.optional(v.string()),
      backgroundColor: v.optional(v.string()),
      templateColors: v.optional(v.object({
        primary: v.string(),
        secondary: v.string(),
      })),
      customInstructions: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const instance = await ctx.db.get(args.id);
    if (!instance) {
      throw new Error("Preview instance not found");
    }

    await ctx.db.patch(args.id, args.updates);
  },
}); 