/**
 * Fix Loading Flicker
 * 
 * This script adds safeguards to prevent loading flicker in the dashboard.
 * It patches various components and functions to ensure smooth loading.
 */

(function() {
  console.log('[FixLoadingFlicker] Starting fix...');

  // Track loading state to prevent rapid changes
  let isCurrentlyLoading = false;
  let lastLoadingChange = Date.now();
  const MIN_LOADING_DURATION = 1000; // Minimum time to show loading state (ms)

  // Wait for React to be available
  const waitForReact = () => {
    if (window.React) {
      applyFixes();
    } else {
      console.log('[FixLoadingFlicker] Waiting for React...');
      setTimeout(waitForReact, 100);
    }
  };

  // Apply fixes to prevent loading flicker
  const applyFixes = () => {
    try {
      console.log('[FixLoadingFlicker] Applying fixes...');

      // Patch useState to debounce loading state changes
      patchUseState();

      // Add global loading state manager
      addGlobalLoadingManager();

      console.log('[FixLoadingFlicker] Fixes applied successfully');
    } catch (error) {
      console.error('[FixLoadingFlicker] Error applying fixes:', error);
    }
  };

  // Patch React.useState to debounce loading state changes
  const patchUseState = () => {
    try {
      // Store the original useState function
      const originalUseState = window.React.useState;

      // Replace with our patched version
      window.React.useState = function(initialState) {
        // Call the original useState
        const [state, setState] = originalUseState(initialState);

        // If this isn't a boolean state, return the original state and setter
        if (typeof state !== 'boolean') {
          return [state, setState];
        }

        // Create a debounced setState function for boolean states
        // This helps prevent rapid loading state changes
        const debouncedSetState = (newState) => {
          // If this is a loading state change (based on naming convention)
          const callerName = new Error().stack.split('\n')[2]?.trim() || '';
          const isLoadingState = callerName.includes('loading') || 
                                callerName.includes('Loading') || 
                                callerName.toLowerCase().includes('setloading');

          if (isLoadingState) {
            const now = Date.now();
            
            // If we're changing from loading to not loading
            if (isCurrentlyLoading && !newState) {
              // Ensure minimum loading duration
              const timeInLoadingState = now - lastLoadingChange;
              if (timeInLoadingState < MIN_LOADING_DURATION) {
                console.log(`[FixLoadingFlicker] Delaying loading state change (${timeInLoadingState}ms < ${MIN_LOADING_DURATION}ms)`);
                setTimeout(() => {
                  setState(newState);
                  isCurrentlyLoading = newState;
                  lastLoadingChange = Date.now();
                }, MIN_LOADING_DURATION - timeInLoadingState);
                return;
              }
            }
            
            // Update tracking variables
            isCurrentlyLoading = newState;
            lastLoadingChange = now;
          }
          
          // Call the original setState
          setState(newState);
        };

        return [state, debouncedSetState];
      };

      console.log('[FixLoadingFlicker] Patched React.useState');
    } catch (error) {
      console.error('[FixLoadingFlicker] Error patching React.useState:', error);
    }
  };

  // Add global loading state manager
  const addGlobalLoadingManager = () => {
    try {
      // Create global loading manager
      window.loadingManager = {
        loadingStates: {},
        
        // Start loading for a component
        startLoading: function(componentId) {
          this.loadingStates[componentId] = true;
          this.updateGlobalLoadingState();
        },
        
        // End loading for a component
        endLoading: function(componentId) {
          // Ensure minimum loading duration
          const now = Date.now();
          const startTime = this.loadingStates[componentId + '_startTime'] || 0;
          const timeInLoadingState = now - startTime;
          
          if (timeInLoadingState < MIN_LOADING_DURATION) {
            console.log(`[FixLoadingFlicker] Delaying global loading state change for ${componentId}`);
            setTimeout(() => {
              this.loadingStates[componentId] = false;
              this.updateGlobalLoadingState();
            }, MIN_LOADING_DURATION - timeInLoadingState);
          } else {
            this.loadingStates[componentId] = false;
            this.updateGlobalLoadingState();
          }
        },
        
        // Update global loading state
        updateGlobalLoadingState: function() {
          // If any component is loading, the global state is loading
          const isLoading = Object.values(this.loadingStates).some(state => state === true);
          
          // Update loading indicator
          const loadingIndicator = document.querySelector('.dashboard-loading');
          if (loadingIndicator) {
            if (isLoading) {
              loadingIndicator.style.display = 'flex';
            } else {
              // Fade out loading indicator
              loadingIndicator.style.opacity = '0';
              setTimeout(() => {
                loadingIndicator.style.display = 'none';
                loadingIndicator.style.opacity = '1';
              }, 300);
            }
          }
        }
      };

      console.log('[FixLoadingFlicker] Added global loading manager');
    } catch (error) {
      console.error('[FixLoadingFlicker] Error adding global loading manager:', error);
    }
  };

  // Start the fix
  waitForReact();
})();
