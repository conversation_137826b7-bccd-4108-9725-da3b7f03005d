<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{FIRM_NAME}} - Legal Consultation</title>
    <meta name="description" content="Get instant legal consultation with {{FIRM_NAME}}'s AI-powered assistant.">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            padding: 60px 0;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        .main-content {
            background: white;
            border-radius: 20px;
            padding: 60px;
            margin: 40px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin: 60px 0;
        }

        .feature {
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            background: #f8f9fa;
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .feature h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .feature p {
            color: #666;
            line-height: 1.6;
        }

        .cta-section {
            text-align: center;
            padding: 60px 0;
            background: linear-gradient(135deg, #4B74AA 0%, #2C3E50 100%);
            color: white;
            border-radius: 20px;
            margin: 40px 0;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }

        .cta-section p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .voice-call-info {
            background: #e8f4fd;
            border: 2px solid #4B74AA;
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            text-align: center;
        }

        .voice-call-info h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .voice-call-info p {
            color: #666;
            margin-bottom: 10px;
        }

        .footer {
            text-align: center;
            padding: 40px 0;
            color: white;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 30px;
                margin: 20px 0;
            }
            
            .features {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>{{FIRM_NAME}}</h1>
            <p>Get instant legal consultation with our AI-powered assistant. Available 24/7 to answer your legal questions and connect you with our attorneys.</p>
        </header>

        <main class="main-content">
            <div class="voice-call-info">
                <h3>🎤 Voice Consultation Available</h3>
                <p>Click the voice button in the bottom-right corner to start a conversation with our AI legal assistant.</p>
                <p>Our assistant can help with initial legal questions and schedule consultations with our attorneys.</p>
            </div>

            <div class="features">
                <div class="feature">
                    <div class="feature-icon">⚖️</div>
                    <h3>Expert Legal Guidance</h3>
                    <p>Get immediate answers to your legal questions from our AI assistant, trained on legal expertise and {{FIRM_NAME}}'s practice areas.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">🕒</div>
                    <h3>24/7 Availability</h3>
                    <p>Our AI assistant is available around the clock to provide initial legal guidance and schedule consultations with our attorneys.</p>
                </div>

                <div class="feature">
                    <div class="feature-icon">🔒</div>
                    <h3>Confidential & Secure</h3>
                    <p>All conversations are confidential and secure. Your privacy and legal privilege are our top priorities.</p>
                </div>
            </div>

            <div class="cta-section">
                <h2>Ready to Get Started?</h2>
                <p>Click the voice button to begin your consultation with our AI legal assistant.</p>
                <p><strong>It's free, confidential, and available right now.</strong></p>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 {{FIRM_NAME}}. All rights reserved. | Powered by LegalScout Voice</p>
        </footer>
    </div>

    <!-- Vapi Widget Integration -->
    <script src="/subdomain-vapi-widget.js"></script>
    <script>
        // Initialize the Vapi widget for this subdomain
        document.addEventListener('DOMContentLoaded', function() {
            // Extract subdomain from URL or use data attribute
            const subdomain = '{{SUBDOMAIN}}' || window.location.hostname.split('.')[0];
            
            console.log('Initializing Vapi widget for subdomain:', subdomain);
            
            // Initialize with subdomain-specific configuration
            initializeVapiWidget({
                subdomain: subdomain,
                assistantId: '{{ASSISTANT_ID}}', // Will be replaced with attorney's assistant ID
                customConfig: {
                    idle: {
                        title: "Have a question for {{FIRM_NAME}}?",
                        subtitle: "Talk with our AI legal assistant"
                    }
                }
            });
        });

        // Handle widget events
        window.addEventListener('message', function(event) {
            if (event.data.type === 'vapi-call-start') {
                console.log('Voice call started');
                // You can add analytics tracking here
            } else if (event.data.type === 'vapi-call-end') {
                console.log('Voice call ended');
                // You can add analytics tracking here
            } else if (event.data.type === 'vapi-call-error') {
                console.error('Voice call error:', event.data.error);
                // You can add error handling here
            }
        });
    </script>
</body>
</html>
