# Attorney Search Integration

## Overview
The Attorney Search feature allows the LegalScout voice assistant to find and display attorneys based on the user's location and legal needs. It first checks for LegalScout partner attorneys, then falls back to external searches using the Apify API.

## Key Components

### AttorneyMatchService
This service handles:
* Searching Convex database for LegalScout partner attorneys
* Calling external attorney search APIs when needed
* Formatting attorney data for display

### ApifyService
A dedicated service for interacting with the Apify API:
* Constructs proper search queries
* Handles API authentication
* Parses and normalizes results

### AttorneyResults Component
UI component that displays:
* List of matching attorneys
* Contact options
* Attorney details and distance

## Workflow

### Urgent Legal Needs Path
1. Voice assistant determines case is urgent
2. System checks for LegalScout attorney matches in Convex
3. If matches found, displays LegalScout attorneys
4. If no matches or user declines, performs external search via Apify
5. Displays results on map with attorney cards

### Attorney Liaison Process
If user selects an attorney:
1. System can initiate outbound call to attorney
2. Liaison Scout introduces LegalScout and summarizes legal issue
3. If attorney accepts, they can be merged into call
4. System offers to send transcript/summary to both parties

## Technical Requirements

### Apify Integration
* API Key: `**********************************************`
* Actor ID: `1lSvMAaRcadrM1Vgv`
* Google Maps Actor: `lukaskrivka/google-maps-with-contact-details`

### Data Structure for Attorneys 