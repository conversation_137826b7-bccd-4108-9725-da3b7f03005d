# LegalScout Documentation Hub

Welcome to the LegalScout Documentation Hub. This comprehensive resource contains all the documentation for the LegalScout project, organized by category for easy navigation.

## 📋 Table of Contents

- [Project Overview](#project-overview)
- [Technical Architecture](#technical-architecture)
- [Development Workflow](#development-workflow)
- [Feature Documentation](#feature-documentation)
- [Project Status and Tasks](#project-status-and-tasks)
- [User Experience](#user-experience)
- [Integration Guides](#integration-guides)

---

## 🚀 Project Overview

### Project Vision

LegalScout aims to democratize access to legal services by providing an intelligent voice-guided platform that helps users navigate legal issues, find appropriate attorneys, and visualize relevant information through interactive maps and dossiers. The system serves as both an initial legal consultant and a matchmaker between clients and attorneys, enhancing the legal support experience through modern technology.

### Project Description

LegalScout is a voice-guided legal consultation platform that connects users with attorneys based on their legal needs. The platform uses Vapi.ai for voice interactions and provides interactive map visualizations for attorney recommendations. The system includes a customizable attorney dashboard that allows legal professionals to configure their AI assistant, manage consultations, and integrate with external services.

### Target Users

- Individuals with urgent legal questions
- People searching for attorneys in specialized fields
- Clients needing guidance through complex legal jurisdictions
- Legal service providers looking to connect with potential clients

### Market Differentiation

Unlike traditional legal directories or chatbots, LegalScout combines voice interaction, visual representation, and attorney matching in a single platform. The voice-guided map feature creates an intuitive way to explore legal information that bridges the gap between text-based research and in-person consultation.

### Success Metrics

- **User Engagement**: Average consultation duration > 5 minutes
- **Conversion Rate**: >30% of users view attorney matches
- **Attorney Matching**: >15% of users contact recommended attorneys
- **Satisfaction**: >85% positive feedback on voice experience
- **Retention**: >40% of users return for additional consultations

### Regulatory Considerations

- All consultations include appropriate disclaimers about not constituting legal advice
- Attorney data is sourced ethically and in compliance with bar association rules
- User data is handled in accordance with privacy regulations
- Clear distinction between partner attorneys and externally sourced listings

---

## 🏗️ Technical Architecture

### System Architecture Overview

LegalScout is built on a modern web architecture with a React frontend, Supabase backend, and Vapi.ai integration for voice capabilities. The system uses the Model Context Protocol (MCP) for service integrations and follows a component-based design pattern.

### Core Technology Stack

#### Frontend Framework
- **React 18.2.0** with functional components and hooks
- **Vite 4.4.5** for development and build processes
- **React Router v7** for navigation
- **TailwindCSS** for styling

#### Backend Services
- **Supabase** for database and authentication
- **Vapi.ai** for voice AI integration
- **Model Context Protocol (MCP)** for service integrations

#### Key Integrations
- **Vapi MCP Server** for programmatic control of voice assistants
- **Gmail OAuth** for authentication
- **Supabase Storage** for file uploads (logos, voice samples)
- **Vercel** for deployment
- **Apify** for external attorney searches
- **Leaflet.js** for mapping capabilities
- **Google Maps** for location data and geocoding

### Frontend Architecture

#### Component Structure

The frontend follows a hierarchical component structure:

```
App
├── AnimatedBackground
├── Navbar
├── Routes
│   ├── Home
│   │   └── VapiCall
│   ├── Dashboard
│   │   ├── ProfileTab
│   │   ├── AgentTab
│   │   ├── CustomFieldsTab
│   │   ├── AutomationTab
│   │   ├── ConsultationsTab
│   │   └── IntegrationsTab
│   ├── SimpleDemoPage
│   ├── PreviewPage
│   └── AboutPage
└── AuthOverlay
```

#### State Management

- **React Context API** for global state (Auth, Theme)
- **Local component state** with useState for component-specific state
- **localStorage** for persistent settings and caching

### Backend Architecture

#### Supabase Integration

The application uses Supabase for:
- **Database**: PostgreSQL database for storing attorney profiles, call records, etc.
- **Authentication**: User authentication and session management
- **Storage**: File storage for logos, voice samples, etc.
- **Row-Level Security**: Data access control based on user roles

#### Database Schema

Main tables:

1. **attorneys**
   - Basic information (name, firm, contact)
   - Branding (logo, colors)
   - Voice configuration
   - AI assistant settings
   - Subdomain configuration

2. **call_records**
   - Consultation history
   - Timestamps
   - Client information
   - Assistant ID reference

3. **custom_fields**
   - Attorney-specific data collection fields
   - Field types and validation rules
   - Display order

### Voice AI Integration

#### Vapi.ai Architecture

- Custom assistant creation for each attorney
- Voice selection and customization
- Real-time transcription
- Conversation management
- Post-call summaries

#### MCP Integration

The Model Context Protocol (MCP) is used to integrate with various services:
- Programmatic control of Vapi assistants
- Call creation and management
- Assistant configuration
- Voice customization

### Authentication Flow

- Gmail OAuth integration
- Supabase authentication
- Row-level security policies

### Deployment Architecture

- GitHub repository for version control
- Vercel CI/CD pipeline
- Vercel Edge Network for hosting
- Custom domains and subdomains

---

## 🔄 Development Workflow

### Development Environment Setup

#### Prerequisites
- Node.js (version specified in package.json)
- npm or yarn
- Git
- VSCode (recommended)

#### Initial Setup
1. Clone the repository
2. Install dependencies with `npm install`
3. Create a `.env` file based on `.env.example`
4. Configure environment variables
5. Start the development server with `npm run dev`

### Branch Strategy
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/feature-name` - Feature branches
- `fix/bug-name` - Bug fix branches

### Feature Development Process
1. Create a new feature branch from `develop`
2. Implement your feature, following coding standards
3. Test your changes locally
4. Commit with descriptive messages
5. Push your branch to GitHub
6. Create a pull request to merge into `develop`
7. After review and approval, merge the pull request

### Code Review Guidelines
- Check for adherence to coding standards
- Verify functionality works as expected
- Ensure proper error handling
- Look for potential performance issues
- Validate accessibility compliance
- Review documentation updates

### Testing
- Run the development server for local testing
- Test in different browsers
- Test responsive design using browser dev tools
- Verify voice functionality with Vapi
- Run automated tests with `npm test`

### Deployment
- Merge changes into `develop` for staging deployment
- Create a pull request from `develop` to `main` for production
- Vercel automatically deploys to production after merge
- Verify deployment in production environment

### Supabase Database Changes
- Create SQL migration files in `supabase/migrations/`
- Test migrations locally
- Apply migrations to development database
- Document changes in documentation

### Vapi Integration Changes
- Update assistant configuration in services
- Test changes locally
- Document changes in documentation

---

## 📱 Feature Documentation

### Attorney Dashboard

The Attorney Dashboard is a central feature of LegalScout that allows attorneys to customize their AI assistant, manage their profile, view consultation history, and configure integrations.

#### Dashboard Structure

The dashboard is organized into several tabs:

1. **Profile** - Basic attorney and firm information
2. **Agent** - AI assistant configuration
3. **Custom Fields** - Data collection customization
4. **Automation** - Workflow automation rules
5. **Consultations** - History of client interactions
6. **Integrations** - Third-party service connections

#### Component Architecture

```
DashboardNew
├── ProfileTab
├── AgentTab
├── CustomFieldsTab
├── AutomationTab
├── ConsultationsTab
├── IntegrationsTab
└── PreviewPane
    └── SimplifiedPreview
        └── VapiCall
```

#### Data Flow

1. **User Authentication**
   - Attorney logs in via Gmail OAuth
   - Supabase authenticates and retrieves attorney record
   - Dashboard loads with attorney's configuration

2. **Configuration Updates**
   - Attorney makes changes in dashboard tabs
   - Changes are saved to local state
   - Preview updates in real-time
   - Changes are saved to Supabase
   - Vapi assistant is updated if necessary

3. **Preview Interaction**
   - Attorney can test their configuration
   - VapiCall component connects to Vapi
   - AI assistant responds based on configuration
   - Results are displayed in preview pane

### Vapi Integration

LegalScout integrates with Vapi.ai to provide voice-guided legal consultations. This integration enables natural language conversations between users and an AI assistant, with customization options for attorneys to personalize their voice assistant experience.

#### Key Components

1. **VapiCall Component** - React component for voice interaction
2. **Vapi Service** - Service layer for Vapi API communication
3. **Vapi MCP Service** - Model Context Protocol integration for programmatic control
4. **Vapi Assistant Service** - Service for creating and managing assistants

#### Assistant Configuration

Basic configuration includes:
- Name and instructions
- First message and mode
- LLM provider and model
- Voice provider and voice ID
- Transcriber configuration

Advanced configuration includes:
- Analysis configuration for summaries
- Structured data collection
- Custom fields integration

#### Voice Customization

Attorneys can select from predefined voices or create custom voices:
- **11labs** - High-quality voices with emotion
- **PlayHT** - Voice cloning capabilities
- **OpenAI** - TTS voices from OpenAI

#### Call Handling

The call lifecycle includes:
1. **Initialization** - Create Vapi instance, set up event listeners
2. **Call Start** - Connect to Vapi, start the call
3. **During Call** - Process user speech, handle assistant responses
4. **Call End** - Stop the call, process call summary

### Subdomain System

The LegalScout subdomain system allows each attorney to have a custom branded experience at their own subdomain (e.g., `attorneyname.legalscout.net`). This system dynamically loads attorney-specific configuration, branding, and AI assistant settings based on the subdomain.

#### System Architecture

The subdomain system consists of several components:
1. **Subdomain Detection** - Identifies the current subdomain
2. **Attorney Configuration Loading** - Loads attorney data from Supabase
3. **Dynamic Rendering** - Renders the UI based on attorney configuration
4. **Vapi Integration** - Connects to the attorney's custom AI assistant

#### DNS Configuration

The subdomain system uses Vercel's domain configuration:
1. Main domain (`legalscout.net`) is configured in Vercel
2. Wildcard subdomain (`*.legalscout.net`) points to the same Vercel project
3. Vercel handles routing based on the hostname

#### Testing and Development

For local development, the system provides several testing utilities:
1. **Test Subdomain Setting**: Set a test subdomain in localStorage
2. **Subdomain Test Page**: UI for testing different subdomains
3. **Mock Data**: Fallback data for development without Supabase

### Custom Fields

The Custom Fields feature allows attorneys to define custom data collection fields for their AI assistant. These fields enable attorneys to gather specific information from potential clients during consultations, tailored to their practice areas and requirements.

#### Key Features

1. **Field Creation and Management**
   - Create, edit, and delete custom fields
   - Reorder fields to control the flow of data collection
   - Group fields into logical sections

2. **Field Types**
   - Text input (short and long form)
   - Multiple choice (single and multiple selection)
   - Date picker
   - Numeric input
   - File upload

3. **Field Validation**
   - Required fields
   - Format validation (email, phone, etc.)
   - Custom validation rules

4. **Integration with AI Assistant**
   - Dynamic prompting based on field definitions
   - Structured data collection
   - Conditional field display

#### Database Schema

Custom fields are stored in the Supabase database with the following structure:
- Field name and type
- Validation rules
- Display order
- Section grouping
- Conditional logic

### Map Visualization

The Map Visualization feature provides an interactive way to explore attorney locations and legal information. It uses Leaflet.js for mapping capabilities and integrates with the case dossier system.

#### Key Features

1. **Interactive Map**
   - Pan and zoom controls
   - Attorney markers with information popups
   - Location-based filtering

2. **3D Globe Option**
   - Alternative visualization for global perspective
   - Animated transitions between views

3. **Case Dossier Integration**
   - Synchronized display of case information
   - Location-based data visualization
   - Attorney recommendations based on location

---

## 📊 Project Status and Tasks

### Current Status (Updated: June 2024)

LegalScout is a voice-guided legal consultation platform that connects users with attorneys based on their legal needs. The platform uses Vapi.ai for voice interactions and provides interactive map visualizations for attorney recommendations.

### Implemented Features

#### Core Platform
- ✅ Voice-guided AI consultations via Vapi.ai
- ✅ Interactive map visualization with case dossier
- ✅ Attorney search and matching based on location and practice area
- ✅ Basic call handling and UI transitions
- ✅ Subdomain system for attorney-specific experiences
- ✅ Gmail OAuth authentication

#### Attorney Dashboard
- ✅ Profile management (firm info, branding, contact details)
- ✅ AI agent configuration (voice, instructions, welcome messages)
- ✅ Custom fields setup (basic implementation)
- ✅ Consultation history viewing
- ✅ Preview functionality for testing changes

#### Voice Integration
- ✅ Vapi.ai integration with custom assistants
- ✅ Voice selection and customization
- ✅ Real-time transcription
- ✅ Structured data collection
- ✅ Post-call summaries

#### User Experience
- ✅ Responsive design for different devices
- ✅ Dark/light theme support
- ✅ Animated background and transitions
- ✅ Interactive UI elements
- ✅ Error handling and recovery

### Known Issues

#### Technical Issues
1. **React Ref Warning in MapDossierView**
   - Warning appears in console when using MapDossierView component
   - Needs update to use React.forwardRef properly

2. **End Call Button Visibility**
   - End call button sometimes not visible in top right during call
   - Z-index and styling adjustments needed

3. **Message Chunking**
   - Message chunks are too short, causing choppy display
   - Optimization needed for smoother experience

4. **Supabase RLS Policy Issues**
   - Persistent Row Level Security policy issues when uploading files
   - Needs configuration adjustment

### Development Roadmap

#### Short-Term (1-3 Months)
- 🔄 Fix React ref warning in MapDossierView
- 🔄 Optimize message chunking for smoother display
- 🔄 Fix end call button visibility
- 🔄 Resolve Supabase RLS policy issues
- 🔄 Complete custom fields implementation
- 🔄 Enhance voice customization options
- 🔄 Improve consultation history with filtering and sorting

#### Medium-Term (3-6 Months)
- 📅 Multi-party calls with attorney liaison
- 📅 Document upload and analysis
- 📅 Enhanced attorney search with advanced filters
- 📅 Calendar integration for appointment scheduling
- 📅 Basic payment processing for consultations

#### Long-Term (6-12 Months)
- 🔮 Legal process navigator with step-by-step guidance
- 🔮 Document relationship mapping
- 🔮 Case history tracking and follow-ups
- 🔮 Advanced payment processing and subscription options
- 🔮 Client portal for case management

### Technical Debt

#### Code Quality
- Refactor message handling logic
- Clean up unused CSS
- Optimize component rendering
- Add error boundaries throughout the application
- Improve test coverage

#### Architecture
- Standardize state management approach
- Improve component modularity
- Enhance error handling strategy
- Optimize data fetching patterns
- Implement proper caching

---

## 👤 User Experience

### Client Flow

1. **Initial Engagement**
   - User visits attorney subdomain or main site
   - Views attorney branding and information
   - Clicks "Start Consultation" button

2. **Voice Consultation**
   - Engages with voice assistant
   - Provides information about legal needs
   - Assistant asks relevant questions based on practice area

3. **Information Collection**
   - Assistant collects structured data
   - User provides details about their legal situation
   - Information is organized into a case dossier

4. **Attorney Recommendations**
   - System analyzes user needs
   - Matches with appropriate attorneys
   - Displays recommendations on map
   - Provides attorney profiles and contact information

5. **Follow-up**
   - User receives consultation summary
   - Can connect with recommended attorney
   - Option to schedule appointment or continue research

### Attorney Flow

1. **Onboarding**
   - Attorney signs up through platform
   - Completes profile with firm information
   - Configures branding and subdomain

2. **Dashboard Configuration**
   - Configures profile and branding
   - Sets up AI assistant parameters
   - Customizes data collection fields
   - Configures automation rules

3. **Client Management**
   - Reviews consultation history
   - Manages client communications
   - Tracks conversion metrics
   - Updates availability and specialties

4. **Integration Management**
   - Connects external services
   - Configures calendar integration
   - Sets up document management
   - Manages payment processing

### UI Components

#### Home Page
- Animated background with subtle motion
- Prominent call-to-action button
- Clear value proposition
- Navigation to other sections

#### Voice Interface
- Microphone status indicator
- Real-time transcription display
- Visual feedback for voice levels
- Clear call controls

#### Map Visualization
- Interactive map with attorney markers
- Information popups for attorneys
- Filtering controls for specialties
- Toggle between map and list views

#### Attorney Dashboard
- Left sidebar for navigation (collapsible)
- Right content area for configuration
- Real-time preview of changes
- Responsive design for all devices

---

## 🔌 Integration Guides

### Supabase Integration

#### Setup and Configuration
1. Create a Supabase project
2. Configure database tables
3. Set up authentication
4. Configure storage buckets
5. Implement Row-Level Security policies

#### Database Schema
- attorneys table
- call_records table
- custom_fields table
- Other supporting tables

#### Authentication Flow
1. User signs in with Gmail OAuth
2. Supabase handles authentication
3. JWT token is stored for session management
4. Row-Level Security enforces data access

### Vapi Integration

#### API Setup
1. Create a Vapi account
2. Generate API keys
3. Configure webhook endpoints
4. Set up default assistant

#### Assistant Creation
1. Define assistant parameters
2. Configure voice settings
3. Set up instructions and context
4. Configure analysis options

#### Call Handling
1. Initialize Vapi SDK
2. Set up event listeners
3. Handle call lifecycle
4. Process transcription and responses

### MCP Integration

#### Setup
1. Configure MCP client
2. Connect to Vapi MCP server
3. Set up authentication
4. Configure service connections

#### Service Integration
1. Gmail integration for notifications
2. Calendar integration for scheduling
3. Document processing for legal forms
4. Payment processing integration

### Vercel Deployment

#### Setup
1. Link GitHub repository to Vercel
2. Configure environment variables
3. Set up domain and subdomain routing
4. Configure build settings

#### Deployment Process
1. Push changes to GitHub
2. Vercel automatically builds and deploys
3. Preview deployments for pull requests
4. Production deployment from main branch

---

## 📚 Additional Resources

### External Documentation
- [Vapi Documentation](https://docs.vapi.ai/)
- [Supabase Documentation](https://supabase.io/docs)
- [React Documentation](https://reactjs.org/docs)
- [Vercel Documentation](https://vercel.com/docs)

### Internal References
- [GitHub Repository](https://github.com/damonkost/LegalScout_Voice)
- [Design System](link-to-design-system)
- [API Documentation](link-to-api-docs)
- [User Guides](link-to-user-guides)

### Support Channels
- GitHub Issues for bug reports
- Team communication channels
- Support email for urgent issues

---

## 🔄 Documentation Updates

This documentation is regularly updated to reflect the current state of the project. Last updated: June 2024.

### Recent Updates
- Added comprehensive documentation for Custom Fields feature
- Updated Technical Architecture with latest system design
- Added detailed Vapi Integration documentation
- Updated Project Status with current roadmap
- Added User Experience flows and UI components

### Planned Documentation
- Comprehensive API documentation
- User manuals and help guides
- Database schema and relationships in detail
- Attorney portal usage guide
- Deployment and operations guide
- Troubleshooting and FAQ documentation
