/**
 * Fix for profile editing issues in the dashboard
 * This script addresses issues with subdomain config loading and UI blocking
 */
(function() {
  console.log('[ProfileEditFix] Initializing profile editing fixes');

  // Fix for JSON parsing errors
  const originalFetch = window.fetch;
  window.fetch = function(url, options) {
    // If it's a subdomain config request that might fail
    if (url && typeof url === 'string' && url.includes('subdomain_config.json')) {
      console.log('[ProfileEditFix] Intercepting subdomain config request:', url);

      // First try the normal fetch
      return originalFetch(url, options)
        .then(response => {
          // Check if response is valid
          if (!response.ok) {
            console.warn('[ProfileEditFix] Subdomain config response not OK, returning fallback');
            return {
              ok: true,
              status: 200,
              json: () => Promise.resolve({
                "default": {
                  "firmName": "LegalScout",
                  "logo": "/PRIMARY CLEAR.png",
                  "mascot": "/PRIMARY CLEAR.png",
                  "vapiInstructions": "You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney.",
                  "vapiContext": "",
                  "interactionDepositUrl": "",
                  "practiceAreas": []
                }
              })
            };
          }

          // Check if content type is JSON
          const contentType = response.headers.get('content-type');
          if (!contentType || !contentType.includes('application/json')) {
            console.warn('[ProfileEditFix] Subdomain config response not JSON, returning fallback');
            return {
              ok: true,
              status: 200,
              json: () => Promise.resolve({
                "default": {
                  "firmName": "LegalScout",
                  "logo": "/PRIMARY CLEAR.png",
                  "mascot": "/PRIMARY CLEAR.png",
                  "vapiInstructions": "You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney.",
                  "vapiContext": "",
                  "interactionDepositUrl": "",
                  "practiceAreas": []
                }
              })
            };
          }

          // If everything is OK, return the original response
          return response;
        })
        .catch(error => {
          console.warn('[ProfileEditFix] Error fetching subdomain config:', error);
          return {
            ok: true,
            status: 200,
            json: () => Promise.resolve({
              "default": {
                "firmName": "LegalScout",
                "logo": "/PRIMARY CLEAR.png",
                "mascot": "/PRIMARY CLEAR.png",
                "vapiInstructions": "You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney.",
                "vapiContext": "",
                "interactionDepositUrl": "",
                "practiceAreas": []
              }
            })
          };
        });
    }

    // Otherwise, proceed with the original fetch
    return originalFetch(url, options);
  };

  // Fix for UI blocking in profile preview
  document.addEventListener('DOMContentLoaded', function() {
    // Function to optimize event handlers
    const optimizeEventHandlers = () => {
      // Find the profile preview element
      const profilePreview = document.querySelector('.preview-content.profile-preview');
      if (profilePreview) {
        console.log('[ProfileEditFix] Found profile preview element, optimizing event handlers');

        // Add a debounce function for event handlers
        const debounce = (func, delay) => {
          let timeout;
          return function() {
            const context = this;
            const args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(context, args), delay);
          };
        };

        // Replace all event listeners with debounced versions
        const originalAddEventListener = profilePreview.addEventListener;
        profilePreview.addEventListener = function(type, listener, options) {
          if (type === 'input' || type === 'change' || type === 'keyup' || type === 'keydown') {
            console.log(`[ProfileEditFix] Debouncing ${type} event listener`);
            originalAddEventListener.call(this, type, debounce(listener, 50), options);
          } else {
            originalAddEventListener.call(this, type, listener, options);
          }
        };
      }
    };

    // Run optimization immediately
    optimizeEventHandlers();

    // Also run after a short delay to catch dynamically added elements
    setTimeout(optimizeEventHandlers, 1000);

    // And run whenever the active tab changes
    const tabButtons = document.querySelectorAll('.tab-button');
    if (tabButtons) {
      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          setTimeout(optimizeEventHandlers, 500);
        });
      });
    }
  });

  // Fix for form submission issues
  const enhanceFormSubmission = () => {
    // Find all forms in the dashboard
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      // Override the submit event
      form.addEventListener('submit', function(event) {
        // Get all input fields
        const inputs = form.querySelectorAll('input, textarea, select');

        // Log the form data for debugging
        const formData = {};
        inputs.forEach(input => {
          formData[input.name] = input.value;
        });
        console.log('[ProfileEditFix] Form submission data:', formData);

        // Continue with the normal submission
      });
    });
  };

  // Run form enhancement after DOM is loaded
  document.addEventListener('DOMContentLoaded', enhanceFormSubmission);

  console.log('[ProfileEditFix] Profile editing fixes applied successfully');
})();
