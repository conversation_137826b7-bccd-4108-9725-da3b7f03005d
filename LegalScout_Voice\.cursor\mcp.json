{"servers": {"browser_tools": {"command": "cmd", "args": ["/c", "npx", "browser-tools-mcp", "serve", "--no-discovery", "--expose-identity", "--host", "127.0.0.1", "--port", "4000"]}, "vapi-mcp-server": {"command": "npx", "args": ["-y", "@vapi-ai/mcp-server"], "env": {"VAPI_TOKEN": "310f0d43-27c2-47a5-a76d-e55171d024f7"}}, "ai-meta-mcp": {"command": "node", "args": ["C:\\Users\\<USER>\\Documents\\LegalScout_VS_REPO\\LegalScout_Voice\\ai-meta-mcp-server\\build\\index.js"], "env": {"ALLOW_JS_EXECUTION": "true", "ALLOW_PYTHON_EXECUTION": "false", "ALLOW_SHELL_EXECUTION": "false", "PERSIST_TOOLS": "true", "TOOLS_DB_PATH": "./tools.json"}}}}