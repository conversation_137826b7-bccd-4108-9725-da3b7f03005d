/**
 * API Route: /api/sync-tools/manage-auth-state
 *
 * This endpoint handles authentication state management across systems.
 */

import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client - use server environment variables only
const supabaseUrl = process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;

// Debug environment variables
console.log('[manage-auth-state] Environment check:', {
  hasSupabaseUrl: !!supabaseUrl,
  hasServiceKey: !!supabaseServiceKey,
  nodeEnv: process.env.NODE_ENV,
  availableEnvVars: Object.keys(process.env).filter(key => key.includes('SUPABASE')).length
});

let supabase = null;
let initError = null;

try {
  if (supabaseUrl && supabaseServiceKey) {
    // Use clean, simple Supabase client configuration
    supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    console.log('[manage-auth-state] Supabase client initialized successfully');
  } else {
    const errorMsg = `Missing Supabase configuration: URL=${!!supabaseUrl}, Key=${!!supabaseServiceKey}`;
    console.warn('[manage-auth-state]', errorMsg);
    initError = errorMsg;
  }
} catch (error) {
  console.error('[manage-auth-state] Error initializing Supabase client:', error);
  initError = error.message;
}

// Server-side implementation
const manageAuthState = async ({ authData, action }) => {
  console.log('Server-side manageAuthState called with:', { authData: !!authData, action });

  if (!supabase) {
    return {
      success: false,
      error: 'Supabase not configured'
    };
  }

  try {
    if ((action === 'login' || action === 'refresh') && authData?.user?.email) {
      // Find attorney by email for both login and refresh actions
      const { data: attorney, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('email', authData.user.email)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return {
        success: true,
        message: 'Auth state managed successfully',
        action,
        attorney: attorney || null
      };
    }

    return {
      success: true,
      message: 'Auth state managed successfully',
      action
    };
  } catch (error) {
    console.error('Error managing auth state:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export default async function handler(req, res) {
  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  // Early return if Supabase not configured
  if (!supabase && initError) {
    console.warn('[manage-auth-state] Supabase not configured, returning fallback response');
    return res.status(200).json({
      success: true,
      result: {
        success: true,
        action: 'fallback',
        hasAttorney: true,
        message: 'Client-side fallback: Supabase not configured on server',
        fallback: true,
        error: initError
      }
    });
  }

  try {
    console.log('[manage-auth-state] Received request');
    console.log('[manage-auth-state] Request method:', req.method);
    console.log('[manage-auth-state] Request headers:', req.headers);

    // Parse request body if it's a string
    let body = req.body;
    if (typeof body === 'string') {
      try {
        body = JSON.parse(body);
      } catch (parseError) {
        console.error('[manage-auth-state] Error parsing request body:', parseError);
        return res.status(400).json({
          success: false,
          error: 'Invalid JSON in request body'
        });
      }
    }

    const { authData, action } = body || {};
    console.log('[manage-auth-state] Parsed body:', { action, hasAuthData: !!authData });

    // Log request details for debugging
    console.log('Request body:', {
      action,
      authData: authData ? {
        hasUser: !!authData.user,
        hasSession: !!authData.session,
        userEmail: authData.user?.email,
        userId: authData.user?.id
      } : null
    });

    // Validate required parameters
    if (!authData || !action) {
      console.log('[manage-auth-state] Missing required parameters');
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: authData and action are required'
      });
    }

    // Call the manageAuthState function
    console.log(`[manage-auth-state] Calling manageAuthState with action: ${action}`);

    try {
      const result = await manageAuthState({ authData, action });

      // Log the result for debugging
      console.log('[manage-auth-state] manageAuthState result:', {
        success: result.success,
        action: result.action,
        hasAttorney: !!result.attorney,
        message: result.message
      });

      // Ensure we always return a valid response
      const response = {
        success: true,
        result: result || { success: false, error: 'No result returned' }
      };

      console.log('[manage-auth-state] Sending response:', response);
      return res.status(200).json(response);
    } catch (innerError) {
      console.error('[manage-auth-state] Inner error in manageAuthState:', innerError);

      // Return a detailed error response
      const errorResponse = {
        success: false,
        error: innerError.message || 'Error in manageAuthState function',
        errorDetails: {
          message: innerError.message,
          stack: innerError.stack,
          name: innerError.name
        }
      };

      console.log('[manage-auth-state] Sending error response:', errorResponse);
      return res.status(500).json(errorResponse);
    }
  } catch (error) {
    console.error('Error managing auth state:', error);

    // Create a detailed error object
    let errorDetails;
    if (error instanceof Error) {
      errorDetails = {
        message: error.message,
        stack: error.stack,
        name: error.name
      };
    } else if (typeof error === 'object') {
      try {
        errorDetails = JSON.stringify(error);
      } catch (e) {
        errorDetails = 'Error object could not be stringified';
      }
    } else {
      errorDetails = String(error);
    }

    // Return a proper error response with detailed information
    return res.status(500).json({
      success: false,
      error: error.message || 'An unknown error occurred',
      errorDetails
    });
  }
}
