/**
 * Fix for EnhanceAttorneyManager
 * 
 * This script fixes issues with the EnhanceAttorneyManager:
 * 1. Improves loading state management to prevent repeated loading attempts
 * 2. Adds a debounce mechanism for state changes
 * 3. Ensures loading flags are properly reset
 */

(function() {
  console.log('[FixEnhanceAttorneyManager] Starting fix...');

  // Wait for the StandaloneAttorneyManager to be available
  const waitForManager = () => {
    if (window.standaloneAttorneyManager) {
      applyFix();
    } else {
      console.log('[FixEnhanceAttorneyManager] Waiting for StandaloneAttorneyManager...');
      setTimeout(waitForManager, 100);
    }
  };

  // Apply the fix to the StandaloneAttorneyManager
  const applyFix = () => {
    console.log('[FixEnhanceAttorneyManager] Applying fix to StandaloneAttorneyManager...');

    // Add a debounce mechanism for state changes
    const originalNotifySubscribers = window.standaloneAttorneyManager.notifySubscribers;
    let notifyTimeout = null;

    window.standaloneAttorneyManager.notifySubscribers = function() {
      // Clear any existing timeout
      if (notifyTimeout) {
        clearTimeout(notifyTimeout);
      }

      // Set a new timeout to debounce notifications
      notifyTimeout = setTimeout(() => {
        originalNotifySubscribers.call(this);
      }, 50); // 50ms debounce
    };

    // Fix the loadAttorneyForUser method to properly handle loading states
    if (window.standaloneAttorneyManager.loadAttorneyForUser) {
      const originalLoadAttorneyForUser = window.standaloneAttorneyManager.loadAttorneyForUser;
      
      window.standaloneAttorneyManager.loadAttorneyForUser = async function(userId) {
        console.log(`[FixEnhanceAttorneyManager] Fixed loadAttorneyForUser called with userId: ${userId}`);
        
        // If we already have an attorney with this user ID, return it immediately
        if (this.attorney && this.attorney.user_id === userId) {
          console.log('[FixEnhanceAttorneyManager] Already have attorney with this user ID, returning current attorney');
          return this.attorney;
        }
        
        // If we're already loading, return the current attorney or a promise that will resolve when loading is complete
        if (this.isLoadingByUserId) {
          console.log('[FixEnhanceAttorneyManager] Already loading by user ID, returning current attorney');
          return this.attorney;
        }
        
        // Set loading flags
        this.isLoadingByUserId = true;
        this.isLoading = true;
        
        try {
          // Call the original method
          const result = await originalLoadAttorneyForUser.call(this, userId);
          return result;
        } catch (error) {
          console.error('[FixEnhanceAttorneyManager] Error in loadAttorneyForUser:', error);
          throw error;
        } finally {
          // Always reset loading flags
          setTimeout(() => {
            this.isLoadingByUserId = false;
            this.isLoading = false;
          }, 100);
        }
      };
    }

    // Fix the loadAttorneyByEmail method to properly handle loading states
    if (window.standaloneAttorneyManager.loadAttorneyByEmail) {
      const originalLoadAttorneyByEmail = window.standaloneAttorneyManager.loadAttorneyByEmail;
      
      window.standaloneAttorneyManager.loadAttorneyByEmail = async function(email) {
        console.log(`[FixEnhanceAttorneyManager] Fixed loadAttorneyByEmail called with email: ${email}`);
        
        // If we already have an attorney with this email, return it immediately
        if (this.attorney && this.attorney.email === email) {
          console.log('[FixEnhanceAttorneyManager] Already have attorney with this email, returning current attorney');
          return this.attorney;
        }
        
        // If we're already loading, return the current attorney or a promise that will resolve when loading is complete
        if (this.isLoadingByEmail) {
          console.log('[FixEnhanceAttorneyManager] Already loading by email, returning current attorney');
          return this.attorney;
        }
        
        // Set loading flags
        this.isLoadingByEmail = true;
        this.isLoading = true;
        
        try {
          // Call the original method
          const result = await originalLoadAttorneyByEmail.call(this, email);
          return result;
        } catch (error) {
          console.error('[FixEnhanceAttorneyManager] Error in loadAttorneyByEmail:', error);
          throw error;
        } finally {
          // Always reset loading flags
          setTimeout(() => {
            this.isLoadingByEmail = false;
            this.isLoading = false;
          }, 100);
        }
      };
    }

    console.log('[FixEnhanceAttorneyManager] Fix applied successfully');
  };

  // Start the fix process
  waitForManager();
})();
