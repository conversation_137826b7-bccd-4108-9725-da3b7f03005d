/**
 * Agent Tab Fix
 *
 * This script ensures that the Agent tab can properly access the attorney ID.
 */

(function() {
  console.log('[AgentTabFix] Initializing agent tab fix');

  // Function to get attorney data from localStorage
  const getStoredAttorney = () => {
    try {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        const parsedAttorney = JSON.parse(storedAttorney);
        
        // Handle the case where the attorney is an array or has numeric keys (multiple attorneys)
        if (Array.isArray(parsedAttorney) || (typeof parsedAttorney === 'object' && parsedAttorney !== null && Object.keys(parsedAttorney).some(key => !isNaN(parseInt(key))))) {
          console.log('[AgentTabFix] Found multiple attorneys in localStorage, normalizing');
          
          // If it's an array, use the first element
          if (Array.isArray(parsedAttorney)) {
            return parsedAttorney[0];
          }
          
          // If it has numeric keys, use the first one
          const numericKeys = Object.keys(parsedAttorney).filter(key => !isNaN(parseInt(key)));
          if (numericKeys.length > 0) {
            // Create a new object with only the non-numeric properties and the first attorney's properties
            const firstAttorney = parsedAttorney[numericKeys[0]];
            const normalizedAttorney = { ...firstAttorney };
            
            // Copy over any non-numeric properties
            Object.keys(parsedAttorney).forEach(key => {
              if (isNaN(parseInt(key))) {
                normalizedAttorney[key] = parsedAttorney[key];
              }
            });
            
            return normalizedAttorney;
          }
        }
        
        return parsedAttorney;
      }
    } catch (error) {
      console.error('[AgentTabFix] Error parsing stored attorney:', error);
    }
    return null;
  };

  // Function to get user ID from Supabase auth data
  const getUserIdFromAuth = () => {
    try {
      const authData = localStorage.getItem('supabase.auth.token');
      if (authData) {
        const parsedData = JSON.parse(authData);
        return parsedData?.currentSession?.user?.id;
      }
    } catch (error) {
      console.error('[AgentTabFix] Error getting user ID from localStorage:', error);
    }
    return null;
  };

  // Function to patch the AgentTab component
  const patchAgentTab = () => {
    // Wait for React to be available
    if (!window.React) {
      setTimeout(patchAgentTab, 100);
      return;
    }

    // Monitor for AgentTab component rendering
    const originalCreateElement = window.React.createElement;
    window.React.createElement = function(type, props, ...children) {
      // Check if this is the AgentTab component
      if (type && typeof type === 'function' && type.name === 'AgentTab') {
        // Create a wrapper function that adds the attorney ID
        const wrappedType = function(props) {
          // Before rendering, ensure attorney has an ID
          if (props.attorney && !props.attorney.id) {
            console.log('[AgentTabFix] Attorney object is missing ID, adding it');
            
            // Get the user ID from auth
            const userId = getUserIdFromAuth();
            if (userId) {
              props.attorney.id = userId;
              console.log('[AgentTabFix] Added ID to attorney:', userId);
            }
          }
          
          // If attorney is still null or undefined, create a fallback
          if (!props.attorney) {
            console.log('[AgentTabFix] Attorney object is null, creating fallback');
            
            // Get the stored attorney or create a fallback
            const storedAttorney = getStoredAttorney();
            if (storedAttorney) {
              props.attorney = storedAttorney;
              console.log('[AgentTabFix] Using stored attorney:', storedAttorney);
            } else {
              // Get the user ID from auth
              const userId = getUserIdFromAuth();
              if (userId) {
                props.attorney = {
                  id: userId,
                  user_id: userId,
                  vapi_assistant_id: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
                  welcome_message: "Hello! I'm Scout, your legal assistant. How can I help you today?",
                  information_gathering: "Tell me about your situation, and I'll help find the right solution for you.",
                  vapi_instructions: "You are a legal assistant. Help the user with their legal questions and concerns.",
                  primary_color: '#4B74AA',
                  secondary_color: '#2C3E50',
                  background_color: '#1a1a1a',
                  voice_provider: 'playht',
                  voice_id: 'ranger',
                  fallback: true
                };
                console.log('[AgentTabFix] Created fallback attorney with ID:', userId);
              }
            }
          }
          
          // Call the original component
          return type.call(this, props);
        };
        
        // Copy over any static properties
        Object.assign(wrappedType, type);
        wrappedType.displayName = type.displayName || type.name;
        
        // Call the original createElement with our wrapped component
        return originalCreateElement.call(this, wrappedType, props, ...children);
      }
      
      // For all other components, call the original createElement
      return originalCreateElement.call(this, type, props, ...children);
    };
    
    console.log('[AgentTabFix] Patched React.createElement to fix AgentTab');
  };

  // Patch the updateAttorney function to ensure it has an ID
  const patchUpdateAttorney = () => {
    // Monitor for updateAttorney function calls
    const observer = new MutationObserver((mutations) => {
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length) {
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Look for the Save Changes button in the Agent tab
              const saveButton = node.querySelector('button.dashboard-button');
              if (saveButton && saveButton.textContent.includes('Save Changes')) {
                // Add a click handler to ensure the attorney ID is set
                saveButton.addEventListener('click', () => {
                  setTimeout(() => {
                    // Get the attorney from localStorage
                    const attorney = getStoredAttorney();
                    if (attorney && !attorney.id) {
                      // Get the user ID from auth
                      const userId = getUserIdFromAuth();
                      if (userId) {
                        // Update the attorney with the user ID
                        attorney.id = userId;
                        localStorage.setItem('attorney', JSON.stringify(attorney));
                        console.log('[AgentTabFix] Updated attorney ID in localStorage:', userId);
                      }
                    }
                  }, 100);
                });
                
                console.log('[AgentTabFix] Added click handler to Save Changes button');
              }
            }
          }
        }
      }
    });
    
    // Start observing the document
    observer.observe(document.body, { childList: true, subtree: true });
    
    console.log('[AgentTabFix] Started monitoring for Save Changes button');
  };

  // Run the patches
  patchAgentTab();
  patchUpdateAttorney();

  console.log('[AgentTabFix] Agent tab fix initialized');
})();
