<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Health Check</title>
    <script>
        // Redirect to JSON health endpoint for API calls
        if (window.location.search.includes('json') || 
            document.referrer.includes('vapi') ||
            navigator.userAgent.includes('vapi')) {
            
            const healthData = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                service: 'LegalScout Voice API (Development)',
                version: '1.0.0',
                environment: 'development',
                checks: {
                    server: 'ok',
                    vapi: 'ready'
                }
            };
            
            // Set content type and return JSON
            document.write('<pre>' + JSON.stringify(healthData, null, 2) + '</pre>');
        }
    </script>
</head>
<body>
    <h1>LegalScout Voice - Health Check</h1>
    <p>Status: <span style="color: green;">Healthy</span></p>
    <p>Timestamp: <span id="timestamp"></span></p>
    <p>Service: LegalScout Voice API (Development)</p>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toISOString();
        
        // If this is an AJAX request or from Vapi, return JSON
        if (window.XMLHttpRequest && 
            (document.referrer.includes('vapi') || 
             window.location.search.includes('json'))) {
            
            const healthData = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                service: 'LegalScout Voice API (Development)',
                version: '1.0.0',
                environment: 'development',
                checks: {
                    server: 'ok',
                    vapi: 'ready'
                }
            };
            
            document.body.innerHTML = '<pre>' + JSON.stringify(healthData, null, 2) + '</pre>';
        }
    </script>
</body>
</html>
