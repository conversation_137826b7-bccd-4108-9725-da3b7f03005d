/**
 * Browser-compatible test for conflict resolution
 * Run this in the browser console to test the new logic
 */

window.testConflictResolution = async function() {
  console.log('🔍 Testing Vapi Assistant Conflict Resolution...\n');

  try {
    // Import required modules
    const { supabase } = await import('/src/lib/supabase.js');
    const { checkAssistantIdConflicts, ensureProfilePersistence } = await import('/src/services/syncHelpers.js');

    // Get your attorney record
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (error) {
      console.error('❌ Error fetching attorney:', error);
      return;
    }

    console.log('👤 Attorney found:', {
      email: attorney.email,
      firmName: attorney.firm_name,
      subdomain: attorney.subdomain,
      assistantId: attorney.vapi_assistant_id
    });

    // Check for conflicts
    console.log('\n🔍 Checking for assistant ID conflicts...');
    const conflictCheck = await checkAssistantIdConflicts(attorney.vapi_assistant_id, attorney.id);

    if (conflictCheck.hasConflicts) {
      console.log('⚠️ Conflicts detected!');
      console.log('Conflicting attorneys:', conflictCheck.conflicts);

      console.log('\n🔧 Resolving conflicts using enhanced sync...');
      const syncResult = await ensureProfilePersistence({
        attorneyId: attorney.id,
        localData: attorney,
        forceUpdate: true
      });

      console.log('✅ Sync result:', syncResult);

      if (syncResult.success && syncResult.vapiResult?.action === 'created_new_to_resolve_conflict') {
        console.log('🎉 New assistant created to resolve conflict!');
        console.log('New assistant ID:', syncResult.vapiResult.assistantId);
        
        // Update localStorage if available
        if (typeof localStorage !== 'undefined') {
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            const parsedAttorney = JSON.parse(storedAttorney);
            parsedAttorney.vapi_assistant_id = syncResult.vapiResult.assistantId;
            localStorage.setItem('attorney', JSON.stringify(parsedAttorney));
            console.log('📱 Updated localStorage with new assistant ID');
          }
        }
      }
    } else {
      console.log('✅ No conflicts found - assistant ID is unique to this attorney');
    }

    return {
      success: true,
      conflictCheck,
      attorney
    };

  } catch (error) {
    console.error('❌ Error during test:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

console.log('🧪 Conflict resolution test loaded! Run: testConflictResolution()');
