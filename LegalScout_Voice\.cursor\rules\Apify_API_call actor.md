
Install Apify CLI
Using Homebrew:

brew install apify-cli

Using NPM:

npm install -g apify-cli

Having problems? Read the installation guide
Log in to Apify
You will need to provide your Apify API token to complete this action.

apify login

Run this Actor from the command line
apify call 1lSvMAaRcadrM1Vgv

Learn more about Apify CLI


**********************************************

API CLIENTS: import { ApifyClient } from 'apify-client';

// Initialize the ApifyClient with API token
const client = new ApifyClient({
    token: '<YOUR_API_TOKEN>',
});

// Prepare Actor input
const input = {
    "actors": [
        "apify/instagram-scraper",
        "apify/rag-web-browser",
        "lukaskrivka/google-maps-with-contact-details"
    ],
    "enableActorAutoLoading": false,
    "maxActorMemoryBytes": 4096,
    "debugActor": "apify/rag-web-browser",
    "debugActorInput": {
        "query": "hello world"
    }
};

(async () => {
    // Run the Actor and wait for it to finish
    const run = await client.actor("1lSvMAaRcadrM1Vgv").call(input);

    // Fetch and print Actor results from the run's dataset (if any)
    console.log('Results from dataset');
    const { items } = await client.dataset(run.defaultDatasetId).listItems();
    items.forEach((item) => {
        console.dir(item);
    });
})();