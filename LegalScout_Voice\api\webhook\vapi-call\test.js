/**
 * Simple Test Webhook Handler
 * 
 * A minimal webhook handler to test basic functionality
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS, GET');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Vapi-Signature');

  // Handle OPTIONS request for CORS
  if (req.method === 'OPTIONS') {
    return res.status(200).json({ message: 'CORS preflight successful' });
  }

  // Handle GET request for testing
  if (req.method === 'GET') {
    return res.status(200).json({ 
      message: 'Webhook test endpoint is working',
      timestamp: new Date().toISOString(),
      method: 'GET'
    });
  }

  // Handle POST request
  if (req.method === 'POST') {
    try {
      const callData = req.body;
      
      return res.status(200).json({ 
        success: true,
        message: 'Webhook POST received successfully',
        timestamp: new Date().toISOString(),
        receivedData: {
          id: callData?.id,
          assistant_id: callData?.assistant_id,
          status: callData?.status
        }
      });
    } catch (error) {
      return res.status(500).json({ 
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  // Method not allowed
  return res.status(405).json({ error: 'Method not allowed' });
}
