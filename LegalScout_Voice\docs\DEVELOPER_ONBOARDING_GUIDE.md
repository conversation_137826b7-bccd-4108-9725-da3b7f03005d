# 🚀 LegalScout Developer Onboarding Guide

**Welcome to LegalScout!** This guide will get you up and running quickly and help you understand our revolutionary legal AI platform.

## 🎯 What is LegalScout?

LegalScout is transforming legal services from expensive luxury to accessible commodity through AI-powered multi-agent, multi-human orchestration. We're building the "Operating System for Professional Services" with legal as our beachhead market.

**Current Status:** Phase 0 Complete (Voice AI + MCP Infrastructure) → Moving to Phase 1 (Session Templates MVP)

## 🏗️ Architecture Overview

```mermaid
graph TB
    A[Client Layer] --> B[Orchestration Layer]
    B --> C[MCP Layer]
    C --> D[Service Layer]
    
    A --> A1[Voice Interface<br/>Vapi Web SDK]
    A --> A2[Web Dashboard<br/>React + Vite]
    A --> A3[Mobile App<br/>Future]
    
    B --> B1[Session Template Engine]
    B --> B2[AI/Human Coordinators]
    B --> B3[Multi-Agent Workflows]
    
    C --> C1[Vapi MCP Server<br/>Voice AI]
    C --> C2[Browser MCP<br/>Automation]
    C --> C3[Legal DB MCP<br/>Research]
    C --> C4[Document MCP<br/>Generation]
    
    D --> D1[Supabase<br/>Database]
    D --> D2[Vercel<br/>Hosting]
    D --> D3[Payment<br/>Stripe]
    D --> D4[SMS<br/>Notifications]
```

## 🛠️ Tech Stack

### Frontend
- **React 18** with Vite for fast development
- **Tailwind CSS** for styling
- **Framer Motion** for animations (with fallbacks)
- **React Router** for navigation
- **Vapi Web SDK** for voice interactions

### Backend
- **Vercel Serverless Functions** (`/api/index.js`)
- **Supabase** for database and auth
- **MCP (Model Context Protocol)** for AI tool integration
- **Express.js** for API routing

### AI & Voice
- **Vapi.ai** for voice AI assistants
- **OpenAI GPT-4** and **Claude Sonnet** for AI agents
- **11Labs** and **OpenAI** for voice synthesis
- **MCP Servers** for tool grounding

### Development Tools
- **ESLint** for code quality
- **Vitest** for testing
- **Concurrently** for running dev servers
- **Cross-env** for environment variables

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone the repository
git clone https://github.com/damonkost/LegalScout_Voice.git
cd LegalScout_Voice

# Install dependencies
npm install

# Copy environment template
cp .env.example .env.local
```

### 2. Environment Variables

Create `.env.local` with these required variables:

```env
# Vapi Configuration
VITE_VAPI_PUBLIC_KEY=310f0d43-27c2-47a5-a76d-e55171d024f7
VAPI_PRIVATE_KEY=6734febc-fc65-4669-93b0-929b31ff6564

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Development
NODE_ENV=development
VITE_API_BASE_URL=http://localhost:3001
```

### 3. Start Development

```bash
# Start both API server and frontend
npm run dev:full

# Or start individually
npm run dev:api    # API server on :3001
npm run dev        # Frontend on :5174
```

### 4. Access the Application

- **Frontend:** http://localhost:5174
- **API:** http://localhost:3001
- **Dashboard:** http://localhost:5174/dashboard

## 📁 Project Structure

```
LegalScout_Voice/
├── src/
│   ├── components/          # React components
│   │   ├── dashboard/       # Dashboard-specific components
│   │   ├── call/           # Voice call components
│   │   └── common/         # Shared components
│   ├── pages/              # Route pages
│   ├── services/           # Business logic & API calls
│   ├── hooks/              # Custom React hooks
│   ├── contexts/           # React contexts
│   ├── config/             # Configuration files
│   └── utils/              # Utility functions
├── api/                    # Serverless API functions
│   ├── index.js           # Main API handler
│   ├── vapi-mcp-server/   # Vapi MCP integration
│   └── webhook/           # Webhook handlers
├── docs/                   # Documentation
├── scripts/               # Build & utility scripts
└── tests/                 # Test files
```

## 🔑 Key Concepts

### 1. One-Way Sync Pattern
**UI → Supabase → Vapi**
- Always save to Supabase first
- Then sync to Vapi
- Never sync from Vapi back to Supabase

### 2. MCP (Model Context Protocol)
- Connects AI agents to external tools
- Provides grounded, accurate responses
- Prevents AI hallucination through citations

### 3. Session Templates
- Multi-agent, multi-human workflows
- Standardized legal processes
- Fixed pricing for predictable outcomes

### 4. Attorney Subdomains
- Each attorney gets their own subdomain
- Customizable branding and configuration
- Embedded voice interface

## 🎯 Current Development Focus

### Phase 1: Session Template MVP (Next 4 Weeks)

**Week 1: Database & UI Foundation**
- [ ] Create session template database schema
- [ ] Build SessionTemplateManager UI component
- [ ] Implement session creation flow
- [ ] Add payment integration

**Week 2: Multi-Agent Orchestration**
- [ ] Build SessionOrchestrator class
- [ ] Create AI agent framework
- [ ] Implement communication hub
- [ ] Add handoff management

**Week 3: Workflow Engine**
- [ ] Complete workflow execution logic
- [ ] Add phase dependency management
- [ ] Implement deliverable tracking
- [ ] Build monitoring dashboard

**Week 4: Production Launch**
- [ ] Security & compliance implementation
- [ ] Monitoring & analytics setup
- [ ] Pilot attorney onboarding
- [ ] First 50 sessions completed

## 🧪 Testing Strategy

### Running Tests

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:vapi-mcp      # Test Vapi MCP connection
npm run test:vapi-key      # Test Vapi API key

# Run with coverage
npm test -- --coverage
```

### Test Structure
- **Unit Tests:** Individual component testing
- **Integration Tests:** Service interaction testing
- **E2E Tests:** Complete workflow testing

## 🔧 Development Patterns

### 1. Component Structure
```jsx
// Standard component pattern
import React from 'react';
import { useVapi } from '../hooks/useVapi';
import './ComponentName.css';

export const ComponentName = ({ prop1, prop2 }) => {
  const { vapiState, vapiActions } = useVapi();
  
  return (
    <div className="component-name">
      {/* Component content */}
    </div>
  );
};
```

### 2. Service Pattern
```javascript
// Service pattern for business logic
export class ServiceName {
  constructor(dependencies) {
    this.dependencies = dependencies;
  }
  
  async performAction(params) {
    try {
      // Business logic
      return result;
    } catch (error) {
      console.error('Service error:', error);
      throw error;
    }
  }
}
```

### 3. Hook Pattern
```javascript
// Custom hook pattern
export const useCustomHook = (params) => {
  const [state, setState] = useState(initialState);
  
  const actions = useMemo(() => ({
    action1: () => { /* implementation */ },
    action2: () => { /* implementation */ }
  }), [dependencies]);
  
  return { state, actions };
};
```

## 🚨 Common Pitfalls & Solutions

### 1. Vapi Integration Issues
**Problem:** Assistant creation loops or duplicates
**Solution:** Use creation guards and check existing assistants first

### 2. Authentication Flow
**Problem:** Users not redirecting to dashboard after login
**Solution:** Check AuthContext and routing configuration

### 3. MCP Connection Failures
**Problem:** MCP servers not connecting
**Solution:** Verify environment variables and server status

### 4. Framer Motion Errors
**Problem:** Animation library causing build issues
**Solution:** Use fallback components and conditional imports

## 📚 Essential Reading

1. **[LEGALSCOUT_MASTER_PLAN.md](./LEGALSCOUT_MASTER_PLAN.md)** - Complete vision & strategy
2. **[IMPLEMENTATION_ROADMAP.md](./IMPLEMENTATION_ROADMAP.md)** - 4-week to 5-year execution plan
3. **[VAPI_IMPLEMENTATION_GUIDELINES.md](./VAPI_IMPLEMENTATION_GUIDELINES.md)** - Vapi integration best practices
4. **[sessionTemplates.js](../src/config/sessionTemplates.js)** - Multi-agent workflow architecture

## 🤝 Working with the AI Assistant

When working with the AI assistant (me), here are some tips:

### Effective Communication
- **Be specific** about what you want to accomplish
- **Provide context** about which part of the codebase you're working on
- **Ask for explanations** if something isn't clear
- **Request tests** to validate your implementations

### Development Workflow
1. **Plan first** - Always start with a clear plan
2. **Review existing code** - Understand current patterns
3. **Implement incrementally** - Small, testable changes
4. **Test thoroughly** - Write tests for new functionality
5. **Document changes** - Update relevant documentation

### Getting Help
- Use `@` mentions to get my attention
- Provide error messages and logs when debugging
- Share relevant code snippets for context
- Ask for architectural guidance on complex features

## 🎯 Success Metrics

### Development Quality
- **Code Coverage:** >80% for new features
- **Build Time:** <2 minutes for full build
- **Test Pass Rate:** 100% for CI/CD
- **Documentation:** All new features documented

### Business Impact
- **Session Completion Rate:** >95%
- **Attorney Satisfaction:** >4.5/5
- **Client Satisfaction:** >4.5/5
- **Revenue per Session:** $199-$2,999

## 🚀 Next Steps

1. **Complete this onboarding** by setting up your development environment
2. **Review the master plan** to understand our vision
3. **Examine session templates** to understand our architecture
4. **Pick a task** from the current sprint backlog
5. **Start coding** with confidence!

## 🔍 Debugging & Troubleshooting

### Common Issues & Solutions

#### 1. Vapi Connection Issues
```bash
# Test Vapi MCP connection
npm run test:vapi-mcp

# Test Vapi API key
npm run test:vapi-key

# Check Vapi assistant status
node scripts/get-all-assistants.js
```

#### 2. Supabase Connection Issues
```bash
# Test Supabase connection
node test-supabase-connection.js

# Verify environment variables
node scripts/verify-vapi-setup.js
```

#### 3. Build Issues
```bash
# Clear cache and reinstall
npm run clean
npm install

# Check for conflicting dependencies
npm ls --depth=0
```

### Development Tools

#### Browser DevTools
- **Console:** Check for JavaScript errors
- **Network:** Monitor API calls and responses
- **Application:** Inspect localStorage and sessionStorage

#### VS Code Extensions
- **ES7+ React/Redux/React-Native snippets**
- **Tailwind CSS IntelliSense**
- **ESLint**
- **Prettier**
- **Thunder Client** (API testing)

## 📋 Development Checklist

### Before Starting a Task
- [ ] Pull latest changes from main branch
- [ ] Review related documentation
- [ ] Understand the existing code patterns
- [ ] Set up test data if needed
- [ ] Create a feature branch

### During Development
- [ ] Follow existing code patterns
- [ ] Write tests for new functionality
- [ ] Update documentation as needed
- [ ] Test in multiple browsers
- [ ] Check mobile responsiveness

### Before Submitting PR
- [ ] Run all tests (`npm test`)
- [ ] Check code coverage
- [ ] Lint code (`npm run lint`)
- [ ] Test build process (`npm run build`)
- [ ] Update CHANGELOG if applicable

## 🎓 Learning Resources

### LegalScout Specific
1. **Session Templates Deep Dive** - Understanding multi-agent workflows
2. **Vapi Integration Patterns** - Voice AI best practices
3. **MCP Protocol** - Tool grounding and citations
4. **Supabase Patterns** - Database and auth patterns

### General Development
1. **React Patterns** - Component composition and hooks
2. **Serverless Architecture** - Vercel functions and edge computing
3. **Voice UI Design** - Conversational interface principles
4. **Legal Tech Compliance** - Security and privacy requirements

## 🤖 AI Assistant Collaboration Tips

### Effective Prompts
```
Good: "Help me implement a session template component that displays
available legal workflows with pricing and duration estimates"

Better: "I need to create a SessionTemplateCard component in
src/components/dashboard/ that shows template name, description,
estimated duration, pricing, and a 'Start Session' button.
It should match our existing card design patterns."
```

### Code Review Requests
```
"Please review this SessionOrchestrator class for:
1. Proper error handling
2. Memory management
3. Integration with existing services
4. Test coverage gaps"
```

### Architecture Discussions
```
"I'm implementing the handoff system between AI agents and human
attorneys. What's the best pattern for managing state transitions
and ensuring data consistency across the workflow?"
```

## 🚀 Advanced Development

### Performance Optimization
- **Code Splitting:** Use React.lazy for route-based splitting
- **Bundle Analysis:** `npm run build` and analyze bundle size
- **Caching:** Implement proper caching strategies
- **Monitoring:** Use Vercel Analytics for performance tracking

### Security Best Practices
- **Environment Variables:** Never commit secrets
- **API Security:** Validate all inputs and sanitize outputs
- **Authentication:** Use Supabase RLS policies
- **CORS:** Configure proper CORS policies

### Deployment Strategies
- **Feature Flags:** Use environment variables for feature toggles
- **Blue-Green Deployment:** Use Vercel preview deployments
- **Rollback Strategy:** Keep previous deployments available
- **Monitoring:** Set up alerts for critical failures

---

**Welcome to the team!** You're now part of building the future of legal services. Let's revolutionize how legal expertise is delivered in the AI age! 🎉

## 📞 Getting Help

- **Technical Issues:** Create GitHub issues with detailed descriptions
- **Architecture Questions:** Discuss in team meetings or Slack
- **Urgent Bugs:** Use the bug reporter in the app
- **General Questions:** Ask in the team chat

Remember: There are no stupid questions. We're all learning and building something revolutionary together!
