# Subdomain Testing Utility

This document explains how to use the subdomain testing utility in the LegalScout application for local development.

## Overview

The subdomain testing utility allows developers to simulate different subdomains in local development without needing actual DNS configuration. This is useful for testing subdomain-specific features, such as attorney profiles, branding, and content.

## Features

- Simulate different subdomains in local development
- Switch between available subdomains using a convenient UI panel
- Automatically load the appropriate attorney profile based on the selected subdomain
- Persist subdomain selection between page refreshes using localStorage
- Visual indicators in the UI showing the current subdomain and development status

## How to Use

### Automatic UI Panel

When running the application in development mode, a floating panel will appear in the bottom-right corner of the screen. This panel allows you to switch between available subdomains:

1. Click on any subdomain button to switch to that subdomain
2. The page will automatically refresh with the new subdomain applied
3. The current subdomain is highlighted in blue
4. Click "default" to clear any test subdomain

### Programmatic Usage

You can also use the subdomain testing utility programmatically in your code:

```javascript
import { 
  getCurrentSubdomain, 
  setTestSubdomain, 
  clearTestSubdomain 
} from './utils/subdomainTester';

// Get the current subdomain (either from localStorage or hostname)
const subdomain = getCurrentSubdomain();

// Set a test subdomain (persists in localStorage)
setTestSubdomain('test-attorney');

// Clear the test subdomain
clearTestSubdomain();
```

## Available Subdomains

The available subdomains are defined in the `subdomain_config.json` file at the root of the project. Each subdomain has its own configuration, including:

- Logo URL
- Mascot URL
- VAPI URL
- Firm name
- VAPI instructions
- VAPI context
- Interaction deposit URL
- Practice areas (for some subdomains)

Current available subdomains include:

- `example-subdomain` - Basic example configuration
- `test-attorney` - Test attorney profile for development
- `family-law` - Family law practice
- `criminal-defense` - Criminal defense practice
- `personal-injury` - Personal injury practice

## Adding New Test Subdomains

To add a new test subdomain:

1. Open the `subdomain_config.json` file
2. Add a new entry with the subdomain as the key
3. Configure the subdomain properties (logo, mascot, etc.)
4. Save the file
5. Restart the development server

Example:

```json
{
  "new-subdomain": {
    "logo": "https://example.com/logo.png",
    "mascot": "https://example.com/mascot.png",
    "vapi_url": "https://example.com/vapi",
    "firmName": "New Subdomain Firm",
    "vapiInstructions": "Custom instructions",
    "vapiContext": "Custom context",
    "interactionDepositUrl": "https://example.com/deposit",
    "practiceAreas": ["Area 1", "Area 2"]
  }
}
```

## Troubleshooting

- If the subdomain selector doesn't appear, make sure you're running in development mode
- If switching subdomains doesn't work, check the browser console for errors
- If the subdomain persists after clearing, try clearing your browser's localStorage
- If the attorney profile doesn't load correctly, verify the subdomain configuration in `subdomain_config.json`

## Implementation Details

The subdomain testing utility consists of:

- `src/utils/subdomainTester.js` - Core utility functions
- Integration in `src/App.jsx` - UI integration and subdomain handling
- `subdomain_config.json` - Subdomain configuration data

The utility uses localStorage to persist the selected subdomain between page refreshes, making it easy to test subdomain-specific features without constantly switching. 