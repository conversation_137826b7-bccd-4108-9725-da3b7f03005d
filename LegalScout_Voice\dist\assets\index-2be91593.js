import{R as pe,r,j as e,aI as st,aB as Ve,aH as St,aL as xt,aM as Nt,aN as L,aO as qe,aP as xe,aQ as Vt,aR as Pt}from"./vendor-react-5a73fa04.js";import{w as Re,g as _e,B as ot,V as it,p as Mt,E as Tt,a as Ce,m as et,i as Le,s as Rt,b as U,v as jt,c as ue,_ as ge,u as at,S as Dt,A as Lt,d as kt,L as Ut,e as Ot,f as zt,D as Ft,C as Bt,h as Jt,j as Yt,k as Xt,l as Ht,n as Kt,t as ke,o as Gt,q as $t,r as Zt,x as Wn,T as Qt,y as Wt}from"./pages-26099600.js";import{L as fe,o as qt,p as en,q as tn,d as nn,w as sn,C as es,S as ts}from"./vendor-94dbccd3.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const a of i)if(a.type==="childList")for(const c of a.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&s(c)}).observe(document,{childList:!0,subtree:!0});function n(i){const a={};return i.integrity&&(a.integrity=i.integrity),i.referrerPolicy&&(a.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?a.credentials="include":i.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function s(i){if(i.ep)return;i.ep=!0;const a=n(i);fetch(i.href,a)}})();if(typeof window<"u"){window.React||(window.React={},console.log("[ReactPolyfill] Created window.React object"));const t=(window.React||{}).createContext;Object.keys(pe).forEach(c=>{window.React[c]===void 0&&(window.React[c]=pe[c],console.log(`[ReactPolyfill] Added ${c} to window.React`))}),window.React.createContext||(window.React.createContext=t||pe.createContext,console.log("[ReactPolyfill] Added createContext to window.React")),["useState","useEffect","useLayoutEffect","useRef","useCallback","useMemo","useContext","forwardRef","createElement","cloneElement","createRef","Component","PureComponent","Fragment","Children","isValidElement"].forEach(c=>{!window.React[c]&&pe[c]&&(window.React[c]=pe[c],console.log(`[ReactPolyfill] Added ${c} from React`))});const s=window.React.createContext,a=setInterval(function(){window.React&&!window.React.createContext&&s&&(console.log("[ReactPolyfill] Restoring React.createContext"),window.React.createContext=s)},100);setTimeout(()=>{clearInterval(a),console.log("[ReactPolyfill] Stopped monitoring React.createContext")},1e4),window.LayoutGroupContext||(window.LayoutGroupContext={Provider:function(c){return c.children||null},Consumer:function(c){return c.children?c.children({}):null}},console.log("[ReactPolyfill] Created global LayoutGroupContext")),console.log("[ReactPolyfill] Enhanced React polyfill applied successfully")}const on=({isActive:o,onTransitionComplete:t,buttonPosition:n,mascotUrl:s})=>{const[i,a]=r.useState(!1),[c,p]=r.useState(!1),[u,h]=r.useState(!1),[d,w]=r.useState(!1),[f,I]=r.useState(!1),[R,y]=r.useState(!1),[M,J]=r.useState(!1);r.useState({});const[F,Y]=r.useState([]),[m,E]=r.useState([]),[C,P]=r.useState([]),S=r.useRef(null),x=r.useRef(null);r.useEffect(()=>{o?W():ee()},[o]);const W=()=>{a(!0),setTimeout(()=>h(!0),100),setTimeout(()=>te(),300),setTimeout(()=>w(!0),600),setTimeout(()=>I(!0),800),setTimeout(()=>{y(!0),j()},1e3),setTimeout(()=>J(!0),1300),setTimeout(()=>{t&&t()},2300)},ee=()=>{a(!1),p(!1),h(!1),w(!1),I(!1),y(!1),J(!1),Y([]),E([]),P([])},te=()=>{const T=[];for(let ie=0;ie<30;ie++){const se=Math.random()*Math.PI*2,K=100+Math.random()*150,le=1+Math.random()*2,oe=Math.random()*.5,ne=2+Math.random()*4,G={id:ie,style:{top:"50%",left:"50%",width:`${ne}px`,height:`${ne}px`,transform:`translate(-50%, -50%) translate(${Math.cos(se)*K}px, ${Math.sin(se)*K}px)`,opacity:0,animation:`particleMove ${le}s ease-out forwards`,animationDelay:`${oe}s`}};T.push(G)}Y(T)},j=()=>{if(!x.current)return;const T=x.current,{width:B,height:ie}=T.getBoundingClientRect(),se=25,K=[],le=B/2,oe=ie/2;for(let G=0;G<se;G++){const O=G/se*Math.PI*2+(Math.random()*.5-.25),$=50+Math.random()*(Math.min(B,ie)*.35),de=le+Math.cos(O)*$,k=oe+Math.sin(O)*$,z=3+Math.random()*6,q=Math.random()*3,ae=1.5+Math.random()*2;K.push({id:G,x:de,y:k,size:z,style:{top:`${k}px`,left:`${de}px`,width:`${z}px`,height:`${z}px`,animationDelay:`${q}s`,animationDuration:`${ae}s`,boxShadow:`0 0 ${z*2}px rgba(41, 121, 255, 0.8)`,background:"radial-gradient(circle, rgba(41, 121, 255, 1) 0%, rgba(41, 121, 255, 0.7) 70%, rgba(41, 121, 255, 0) 100%)"}})}E(K);const ne=[];for(let G=0;G<K.length;G++){const O=K[G],$=K.filter(k=>k.id!==O.id).map(k=>{const z=k.x-O.x,q=k.y-O.y;return{node:k,distance:Math.sqrt(z*z+q*q)}}).sort((k,z)=>k.distance-z.distance),de=2+Math.floor(Math.random()*2);for(let k=0;k<Math.min(de,$.length);k++){const z=$[k].node,q=z.x-O.x,ae=z.y-O.y,v=Math.sqrt(q*q+ae*ae),_=Math.atan2(ae,q)*(180/Math.PI),re=.3+Math.random()*.4,Ae=Math.random()*2,me=1+Math.random()*3;ne.push({id:`${O.id}-${z.id}`,style:{top:`${O.y+O.size/2}px`,left:`${O.x+O.size/2}px`,width:`${v}px`,transform:`rotate(${_}deg)`,opacity:re,animationDelay:`${Ae}s`,animationDuration:`${me}s`,height:"1px",background:`linear-gradient(90deg, rgba(41, 121, 255, ${re}) 0%, rgba(41, 121, 255, ${re*.5}) 100%)`}})}}P(ne)};return e.jsxs("div",{ref:S,className:`call-transition-overlay ${i?"active":""}`,children:[u&&e.jsx("div",{className:`expanding-circle ${u?"active":""}`}),e.jsx("div",{className:"particles-container",children:F.map(T=>e.jsx("div",{className:"particle",style:T.style},`particle-${T.id}`))}),e.jsxs("div",{ref:x,className:`neural-network ${R?"active":""}`,children:[m.map(T=>e.jsx("div",{className:"node pulse",style:T.style},`node-${T.id}`)),C.map(T=>e.jsx("div",{className:"connection-line",style:T.style},`connection-${T.id}`))]}),e.jsxs("div",{className:`connection-text ${d?"active":""}`,children:["Connecting to LegalScout AI",e.jsxs("span",{className:`loading-dots-inline ${f?"active":""}`,children:[e.jsx("span",{className:"dot",children:"."}),e.jsx("span",{className:"dot",children:"."}),e.jsx("span",{className:"dot",children:"."})]})]}),e.jsxs("div",{className:`status-indicator ${M?"active":""}`,children:[e.jsx("div",{className:"status-icon"}),e.jsx("div",{className:"status-text",children:"Establishing secure connection"})]})]})};delete fe.Icon.Default.prototype._getIconUrl;fe.Icon.Default.mergeOptions({iconUrl:qt,iconRetinaUrl:en,shadowUrl:tn});let Ue,Oe;try{Ue=new URL("/attorney-marker.svg",self.location).href,Oe=new URL("/location-marker.svg",self.location).href}catch(o){console.error("Error loading SVG icons:",o),Ue="https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png",Oe="https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png"}const an=[39.8283,-98.5795],rn=4,cn=fe.icon({iconUrl:Ue,iconSize:[32,32],iconAnchor:[16,32],popupAnchor:[0,-32]}),ln=fe.icon({iconUrl:Oe,iconSize:[32,32],iconAnchor:[16,32],popupAnchor:[0,-32]});document.addEventListener("DOMContentLoaded",()=>{document.querySelectorAll(".attorney-marker-icon img").forEach(n=>{n.onerror=function(){this.src="/attorney-marker.png"}}),document.querySelectorAll(".location-marker-icon img").forEach(n=>{n.onerror=function(){this.src="/location-marker.png"}})});const dn=({isVisible:o,location:t,attorneys:n=[]})=>{const s=r.useRef(null),i=r.useRef(null),a=r.useRef({attorneys:[],clientLocation:null});return r.useEffect(()=>{if(!(!o||!s.current))return i.current||(i.current=fe.map(s.current,{center:an,zoom:rn,zoomControl:!0,attributionControl:!1}),fe.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{maxZoom:19,className:"dark-tiles"}).addTo(i.current)),t&&t.lat&&t.lng&&(i.current.setView([t.lat,t.lng],11),!a.current.clientLocation&&t.lat&&t.lng&&(a.current.clientLocation=fe.marker([t.lat,t.lng],{icon:ln}).addTo(i.current).bindPopup(t.address||"Your Location"))),()=>{i.current&&(i.current.remove(),i.current=null,a.current={attorneys:[],clientLocation:null})}},[o,t]),r.useEffect(()=>{!i.current||!n.length||(a.current.attorneys.forEach(c=>{i.current&&i.current.removeLayer(c)}),a.current.attorneys=[],n.forEach(c=>{if(c.latitude&&c.longitude){const p=fe.marker([c.latitude,c.longitude],{icon:cn}).addTo(i.current).bindPopup(`<strong>${c.name}</strong><br>${c.practiceArea||""}`);a.current.attorneys.push(p)}}))},[n]),e.jsx("div",{className:"map-dossier-view",children:e.jsx("div",{ref:s,className:"map-container"})})},un=({attorney:o})=>{const[t,n]=r.useState(!0),s=o?.location||{lat:40.7128,lng:-74.006},i=o?[{name:o.name||"Attorney",practiceArea:o.practiceArea||"General Practice",specialty:o.specialty,latitude:o.location?.lat,longitude:o.location?.lng,distance:o.distance||"< 5",phone:o.phone,email:o.email,website:o.website,address:o.address}]:[];return e.jsxs("div",{className:"map-view-container",children:[e.jsx("div",{className:"map-controls",children:e.jsx("button",{className:"toggle-map-button",onClick:()=>n(!t),children:t?"Hide Map":"Show Map"})}),e.jsx(dn,{isVisible:t,location:s,attorneys:i})]})},pn=Re(un,{displayName:"MapView",type:"container",description:"Container component for the map display with attorney information",responsibleFor:["map visibility toggle","attorney data formatting"]}),gn=({attorney:o})=>o?e.jsxs("div",{className:"attorney-dossier",children:[e.jsx("h2",{children:"Attorney Information"}),e.jsxs("div",{className:"dossier-section",children:[e.jsx("h3",{children:o.name||"Attorney"}),e.jsx("p",{className:"practice-area",children:o.practiceArea||o.specialty||"General Practice"}),o.distance&&e.jsxs("p",{className:"distance",children:[e.jsx("strong",{children:"Distance:"})," ",o.distance," miles"]})]}),e.jsxs("div",{className:"dossier-section",children:[e.jsx("h4",{children:"Contact Information"}),e.jsxs("ul",{className:"contact-list",children:[o.phone&&e.jsxs("li",{children:[e.jsx("strong",{children:"Phone:"})," ",e.jsx("a",{href:`tel:${o.phone}`,children:o.phone})]}),o.email&&e.jsxs("li",{children:[e.jsx("strong",{children:"Email:"})," ",e.jsx("a",{href:`mailto:${o.email}`,children:o.email})]}),o.website&&e.jsxs("li",{children:[e.jsx("strong",{children:"Website:"})," ",e.jsx("a",{href:o.website,target:"_blank",rel:"noopener noreferrer",children:"Visit Website"})]})]})]}),o.address&&e.jsxs("div",{className:"dossier-section",children:[e.jsx("h4",{children:"Office Address"}),e.jsx("address",{children:o.address})]}),o.bio&&e.jsxs("div",{className:"dossier-section",children:[e.jsx("h4",{children:"About"}),e.jsx("p",{children:o.bio})]}),o.expertise&&o.expertise.length>0&&e.jsxs("div",{className:"dossier-section",children:[e.jsx("h4",{children:"Areas of Expertise"}),e.jsx("ul",{className:"expertise-list",children:o.expertise.map((t,n)=>e.jsx("li",{children:t},n))})]}),e.jsx("div",{className:"dossier-actions",children:e.jsx("button",{className:"contact-button",children:"Contact This Attorney"})})]}):e.jsxs("div",{className:"attorney-dossier empty-dossier",children:[e.jsx("h2",{children:"No Attorney Selected"}),e.jsx("p",{children:"Please select an attorney from the map or list."})]}),mn=Re(gn,{displayName:"AttorneyDossier",type:"display",description:"Displays detailed information about a selected attorney",responsibleFor:["attorney profile display","contact information","expertise listing"]}),hn=({data:o})=>o?e.jsxs("div",{className:"call-summary",children:[e.jsx("h2",{children:"Call Summary"}),e.jsxs("div",{className:"summary-section",children:[e.jsx("h3",{children:"Thank you for using LegalScout"}),e.jsx("p",{children:"We've gathered the following information about your case:"})]}),e.jsxs("div",{className:"summary-details",children:[o.issue&&e.jsxs("div",{className:"summary-item",children:[e.jsx("strong",{children:"Legal Issue:"})," ",o.issue]}),o.practiceArea&&e.jsxs("div",{className:"summary-item",children:[e.jsx("strong",{children:"Practice Area:"})," ",o.practiceArea]}),o.location?.address&&e.jsxs("div",{className:"summary-item",children:[e.jsx("strong",{children:"Location:"})," ",o.location.address]}),o.noteworthy&&e.jsxs("div",{className:"summary-item",children:[e.jsx("strong",{children:"Case Details:"})," ",o.noteworthy]}),o.urgency&&e.jsxs("div",{className:"summary-item",children:[e.jsx("strong",{children:"Urgency Level:"})," ",o.urgency]})]}),e.jsxs("div",{className:"next-steps",children:[e.jsx("h3",{children:"Next Steps"}),o.urgency==="High"?e.jsx("p",{children:'Based on the urgency of your case, we recommend speaking with an attorney as soon as possible. You can use the "Start New Consultation" button below to connect with an available attorney.'}):e.jsx("p",{children:"Thank you for your consultation. We've sent additional resources to your email (if provided). You can start a new consultation anytime if you need further assistance."})]}),e.jsxs("div",{className:"summary-actions",children:[e.jsx("button",{className:"email-summary-button",children:"Email Summary"}),e.jsx("button",{className:"download-summary-button",children:"Download PDF"})]})]}):e.jsxs("div",{className:"call-summary-empty",children:[e.jsx("h2",{children:"No Call Summary Available"}),e.jsx("p",{children:"There is no summary data available for this call."})]}),fn=Re(hn,{displayName:"CallSummary",type:"display",description:"Displays a summary of the call details and next steps",responsibleFor:["call summary display","case information display","next steps guidance"]}),In=({isDarkTheme:o})=>{const[t,n]=r.useState(!1),s=st(),i=r.useRef(null),a=_e(),c=a!=="default"&&a!=="www"&&a!==""&&a!==null,p=()=>{n(!t)};return e.jsxs("div",{className:`nav-container ${t?"menu-active":""}`,ref:i,children:[e.jsxs("button",{className:`hamburger-menu ${t?"active":""}`,onClick:p,"aria-label":"Toggle navigation menu","aria-expanded":t,children:[e.jsx("span",{}),e.jsx("span",{}),e.jsx("span",{})]}),e.jsx("nav",{className:`main-nav ${t?"active":""}`,children:e.jsxs("ul",{children:[!c&&e.jsxs(e.Fragment,{children:[e.jsx("li",{children:e.jsxs(Ve,{to:"/home",className:s.pathname==="/home"||s.pathname==="/"?"active":"",onClick:()=>n(!1),"data-text":"Home",children:[e.jsx("i",{className:"nav-icon fas fa-home"}),"Home"]})}),e.jsx("li",{children:e.jsxs(Ve,{to:"/demo",className:s.pathname==="/demo"?"active":"",onClick:()=>n(!1),"data-text":"Agent",children:[e.jsx("i",{className:"nav-icon fas fa-gavel"}),"Agent"]})})]}),e.jsx("li",{children:e.jsxs(Ve,{to:"/about",className:s.pathname==="/about"?"active":"",onClick:()=>n(!1),"data-text":"About",children:[e.jsx("i",{className:"nav-icon fas fa-info-circle"}),"About"]})})]})}),e.jsx("div",{className:`nav-overlay ${t?"active":""}`,onClick:()=>n(!1),"aria-hidden":"true"})]})};window.THREE?.OrbitControls;const yn=()=>e.jsx("div",{className:"gradient-bg",children:e.jsxs("div",{className:"gradients-container",children:[e.jsx("div",{className:"bubble",style:{width:"150px",height:"150px",left:"30%",top:"40%",background:"radial-gradient(circle, rgba(var(--color2), 0.8) 0%, rgba(var(--color2), 0) 50%)"}}),e.jsx("div",{className:"bubble",style:{width:"200px",height:"200px",left:"50%",top:"60%",background:"radial-gradient(circle, rgba(var(--color3), 0.8) 0%, rgba(var(--color3), 0) 50%)"}}),e.jsx("div",{className:"bubble",style:{width:"180px",height:"180px",left:"70%",top:"20%",background:"radial-gradient(circle, rgba(var(--color4), 0.8) 0%, rgba(var(--color4), 0) 50%)"}}),e.jsx("div",{className:"bubble",style:{width:"130px",height:"130px",left:"20%",top:"70%",background:"radial-gradient(circle, rgba(var(--color5), 0.8) 0%, rgba(var(--color5), 0) 50%)"}}),e.jsx("div",{className:"bubble",style:{width:"220px",height:"220px",left:"40%",top:"30%",background:"radial-gradient(circle, rgba(var(--color1), 0.8) 0%, rgba(var(--color1), 0) 50%)",animationDelay:"2s"}}),e.jsx("div",{className:"bubble",style:{width:"170px",height:"170px",left:"60%",top:"40%",background:"radial-gradient(circle, rgba(var(--color4), 0.8) 0%, rgba(var(--color4), 0) 50%)",animationDelay:"4s"}})]})});const An=({isDark:o,onToggle:t})=>e.jsx("div",{className:"theme-toggle-container",children:e.jsxs("button",{className:`theme-toggle ${o?"dark":"light"}`,onClick:t,"aria-label":"Toggle theme",children:[e.jsxs("div",{className:"toggle-icons",children:[e.jsxs("svg",{className:"sun-icon",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"5"}),e.jsx("line",{x1:"12",y1:"1",x2:"12",y2:"3"}),e.jsx("line",{x1:"12",y1:"21",x2:"12",y2:"23"}),e.jsx("line",{x1:"4.22",y1:"4.22",x2:"5.64",y2:"5.64"}),e.jsx("line",{x1:"18.36",y1:"18.36",x2:"19.78",y2:"19.78"}),e.jsx("line",{x1:"1",y1:"12",x2:"3",y2:"12"}),e.jsx("line",{x1:"21",y1:"12",x2:"23",y2:"12"}),e.jsx("line",{x1:"4.22",y1:"19.78",x2:"5.64",y2:"18.36"}),e.jsx("line",{x1:"18.36",y1:"5.64",x2:"19.78",y2:"4.22"})]}),e.jsx("svg",{className:"moon-icon",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:e.jsx("path",{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"})})]}),e.jsx("div",{className:"toggle-circle"})]})});const wn=({onClick:o})=>o?e.jsx("button",{className:"sign-in-button",onClick:o,children:"Sign In/Sign Up"}):e.jsx(Ve,{to:"/login",className:"sign-in-button",children:"Sign In/Sign Up"});const En=({firmName:o,logoUrl:t,mascot:n,buttonImageUrl:s,primaryColor:i,secondaryColor:a,buttonColor:c,backgroundColor:p,backgroundOpacity:u,buttonText:h,buttonOpacity:d,practiceAreaBackgroundOpacity:w,textBackgroundColor:f,practiceDescription:I,welcomeMessage:R,informationGathering:y,officeAddress:M,schedulingLink:J,practiceAreas:F,vapiInstructions:Y,vapiContext:m,vapi_assistant_id:E,voiceId:C,aiModel:P,subdomain:S="default",customFields:x,summaryPrompt:W,structuredDataPrompt:ee,structuredDataSchema:te,theme:j="dark"})=>{const[T,B]=r.useState(!1),[ie,se]=r.useState(!1),[K,le]=r.useState(!0),[oe,ne]=r.useState(!1),[G,O]=r.useState(0),$=v=>{v=v.replace("#","");const _=parseInt(v.substring(0,2),16),re=parseInt(v.substring(2,4),16),Ae=parseInt(v.substring(4,6),16);return`${_}, ${re}, ${Ae}`},de=v=>{let _=v.replace("#","");const re=parseInt(_.substring(0,2),16),Ae=parseInt(_.substring(2,4),16),me=parseInt(_.substring(4,6),16);return(.299*re+.587*Ae+.114*me)/255>.5?"#000000":"#ffffff"},k=()=>{if(oe||T){console.log("[SimplifiedPreview] Call already starting or active, ignoring duplicate startCall");return}console.log("[SimplifiedPreview] Starting call with assistant ID:",E),ne(!0),se(!0),O(v=>v+1),setTimeout(()=>{B(!0),se(!1),setTimeout(()=>{ne(!1)},2e3)},500)},z=()=>{B(!1)},q=v=>v&&typeof v=="string"&&v.includes("legalscout.ai/static/media")?(console.log("Converting production URL to local asset:",v),"/PRIMARY CLEAR.png"):Mt(v)||"/PRIMARY CLEAR.png";r.useEffect(()=>{le(!0),console.log("SimplifiedPreview: Always showing firm name header for proper branding")},[]),r.useEffect(()=>{console.log("SimplifiedPreview props:",{firmName:o,primaryColor:i,secondaryColor:a,buttonColor:c,practiceDescription:I?I.substring(0,100)+"...":"None",showPreviewHeader:K})},[o,i,a,c,I,K]);const ae={"--primary-color":i,"--primary-color-rgb":$(i),"--secondary-color":a,"--secondary-color-rgb":$(a),"--button-color":c,"--button-color-rgb":$(c),"--button-text-color":de(c),"--background-color":`rgba(${$(p)}, ${u})`,"--text-background-color":`rgba(${$(f)}, ${w})`,"--button-opacity":d};return e.jsx("div",{className:`simplified-preview ${j}-theme`,style:ae,children:e.jsx("div",{className:"preview-content",children:T?e.jsx("div",{className:"preview-call-container",children:e.jsx(it,{onEndCall:z,subdomain:S,isDemo:!E,attorneyData:E?(()=>{const v={firm_name:o,vapi_instructions:Y,vapi_context:m,welcome_message:R,vapi_assistant_id:E,voice_id:C,ai_model:P,subdomain:S,primary_color:i,secondary_color:a,custom_fields:x,summary_prompt:W,structured_data_prompt:ee,structured_data_schema:te};return console.log("🎨 SimplifiedPreview: Passing attorney data with colors and subdomain:",{primary_color:i,secondary_color:a,firmName:o,subdomain:S}),v})():null,customInstructions:{firmName:o,vapiInstructions:Y,vapiContext:m,initialMessage:R||`Hello, I'm Scout from ${o}. How can I help you today?`,assistantId:E,voiceId:C,aiModel:P}},G)}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"preview-practice-description",children:[e.jsx("div",{className:"practice-description-content",children:e.jsx(St,{remarkPlugins:[nn],rehypePlugins:[sn],components:{h1:({node:v,..._})=>e.jsx("h1",{style:{color:i||"#4B74AA"},..._}),h2:({node:v,..._})=>e.jsx("h2",{style:{color:a||"#2C3E50"},..._}),h3:({node:v,..._})=>e.jsx("h3",{style:{color:a||"#2C3E50"},..._}),a:({node:v,..._})=>e.jsx("a",{style:{color:c||"#3498db"},..._}),strong:({node:v,..._})=>e.jsx("strong",{style:{color:i||"#4B74AA"},..._}),em:({node:v,..._})=>e.jsx("em",{style:{fontStyle:"italic"},..._}),blockquote:({node:v,..._})=>e.jsx("blockquote",{style:{borderLeft:`4px solid ${a||"#3498db"}`,paddingLeft:"1rem",marginLeft:0,fontStyle:"italic"},..._}),ul:({node:v,..._})=>e.jsx("ul",{style:{marginLeft:"1.5rem"},..._}),ol:({node:v,..._})=>e.jsx("ol",{style:{marginLeft:"1.5rem"},..._}),li:({node:v,..._})=>e.jsx("li",{style:{marginBottom:"0.5rem"},..._})},children:I})}),F&&F.length>0&&e.jsxs("div",{className:"practice-areas-section",children:[e.jsx("h3",{children:"Practice Areas"}),e.jsx("ul",{className:"practice-areas-list",children:F.map((v,_)=>e.jsx("li",{children:v},_))})]}),M&&e.jsxs("div",{className:"office-address-section",children:[e.jsx("h3",{children:"Office Location"}),e.jsx("p",{children:M})]}),J&&e.jsxs("div",{className:"scheduling-section",children:[e.jsx("h3",{children:"Schedule a Consultation"}),e.jsx("a",{href:J,target:"_blank",rel:"noopener noreferrer",className:"scheduling-link",children:"Book an Appointment"})]})]}),e.jsx("div",{className:"preview-button-container",children:e.jsx(ot,{onClick:k,label:h||"Start Consultation",mascot:q(s||n),isLoading:ie,buttonColor:c,buttonOpacity:d})})]})})})},vn=()=>{const[o,t]=r.useState(null),[n,s]=r.useState(!0),[i,a]=r.useState(null),[c,p]=r.useState(!1),[u,h]=r.useState(!1);r.useEffect(()=>{(async()=>{try{console.log("[PreviewFrameLoader] Loading attorney configuration...");const f=_e();console.log("[PreviewFrameLoader] Current subdomain:",f),console.log("[PreviewFrameLoader] Loading config for subdomain:",f);const I=await Ce(f);if(console.log("[PreviewFrameLoader] Raw attorney config loaded:",{hasConfig:!!I,firmName:I?.firmName,vapi_assistant_id:I?.vapi_assistant_id,id:I?.id,subdomain:I?.subdomain,isFallback:I?.isFallback,configKeys:I?Object.keys(I):[]}),I){const R=I.isFallback||I.firmName==="Your Law Firm"&&!I.id||!I.id&&f==="damon";console.log("[PreviewFrameLoader] Config analysis:",{isFallbackConfig:R,hasId:!!I.id,firmName:I.firmName,subdomain:f});const y=et(I);console.log("[PreviewFrameLoader] Mapped preview config:",{firmName:y.firmName,titleText:y.titleText,vapiAssistantId:y.vapiAssistantId,welcomeMessage:y.welcomeMessage}),t({...y,vapiAssistantId:I.vapi_assistant_id||y.vapiAssistantId,...I,isFallbackConfig:R}),R&&f==="damon"&&(console.log("[PreviewFrameLoader] Fallback config detected for damon, showing activate button"),p(!0))}else console.warn("[PreviewFrameLoader] No attorney config found, using defaults"),t({firmName:"Your Law Firm",titleText:"Your Law Firm",primaryColor:"#4B74AA",secondaryColor:"#2C3E50",backgroundColor:"#1a1a1a",backgroundOpacity:.9,buttonText:"Start Consultation",buttonOpacity:1,practiceAreaBackgroundOpacity:.1,textBackgroundColor:"#634C38",welcomeMessage:"Hello! I'm Scout, your legal assistant. How can I help you today?",informationGathering:"Tell me about your situation, and I'll help find the right solution for you.",logoUrl:"/PRIMARY CLEAR.png",mascot:"/PRIMARY CLEAR.png",theme:"dark",vapiAssistantId:null})}catch(f){console.error("[PreviewFrameLoader] Error loading attorney config:",f),a(f.message),t({firmName:"Your Law Firm",titleText:"Your Law Firm",primaryColor:"#4B74AA",secondaryColor:"#2C3E50",backgroundColor:"#1a1a1a",backgroundOpacity:.9,buttonText:"Start Consultation",buttonOpacity:1,practiceAreaBackgroundOpacity:.1,textBackgroundColor:"#634C38",welcomeMessage:"Hello! I'm Scout, your legal assistant. How can I help you today?",informationGathering:"Tell me about your situation, and I'll help find the right solution for you.",logoUrl:"/PRIMARY CLEAR.png",mascot:"/PRIMARY CLEAR.png",theme:"dark",vapiAssistantId:null})}finally{s(!1)}})()},[]);const d=async()=>{h(!0);try{console.log("[PreviewFrameLoader] Activating assistant for subdomain:",_e());const w=_e(),f=await Ce(w);if(console.log("[PreviewFrameLoader] Activation - loaded config:",{hasConfig:!!f,firmName:f?.firmName,assistantId:f?.vapi_assistant_id,id:f?.id}),f&&f.vapi_assistant_id){const I=et(f);t({...I,vapiAssistantId:f.vapi_assistant_id,...f,isFallbackConfig:!1}),p(!1),console.log("[PreviewFrameLoader] Assistant activated successfully")}else console.warn("[PreviewFrameLoader] Activation failed - no valid config or assistant ID found")}catch(w){console.error("[PreviewFrameLoader] Error activating assistant:",w)}finally{h(!1)}};return n?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",backgroundColor:"#1a1a1a",color:"white"},children:[e.jsx("div",{style:{width:"40px",height:"40px",border:"4px solid #333",borderTop:"4px solid #4B74AA",borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("p",{style:{marginTop:"20px"},children:"Loading preview..."}),e.jsx("style",{children:`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `})]}):i?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",backgroundColor:"#1a1a1a",color:"#ff6b6b",padding:"20px",textAlign:"center"},children:[e.jsx("h2",{children:"Preview Error"}),e.jsxs("p",{children:["Failed to load attorney configuration: ",i]}),e.jsx("p",{style:{color:"#ccc",marginTop:"20px"},children:"Using default configuration instead."})]}):e.jsxs("div",{style:{position:"relative",width:"100%",height:"100vh"},children:[e.jsx(Tt,{...o}),c&&e.jsxs("div",{style:{position:"fixed",bottom:window.innerWidth<=768?"20px":"auto",top:window.innerWidth<=768?"auto":"20px",left:window.innerWidth<=768?"50%":"auto",right:window.innerWidth<=768?"auto":"20px",transform:window.innerWidth<=768?"translateX(-50%)":"none",zIndex:9999,backgroundColor:"rgba(173, 216, 230, 0.1)",padding:window.innerWidth<=768?"12px 20px":"15px",borderRadius:window.innerWidth<=768?"25px":"8px",border:"1px solid rgba(173, 216, 230, 0.8)",color:"#87CEEB",textAlign:"center",maxWidth:window.innerWidth<=768?"90vw":"250px",minWidth:window.innerWidth<=768?"280px":"auto",boxShadow:"0 2px 15px rgba(173, 216, 230, 0.2)",backdropFilter:"blur(15px)"},children:[e.jsx("div",{style:{marginBottom:window.innerWidth<=768?"8px":"10px",fontSize:window.innerWidth<=768?"16px":"14px",fontWeight:"bold"},children:"Assistant Not Active"}),window.innerWidth>768&&e.jsx("div",{style:{marginBottom:"15px",fontSize:"12px",color:"#ccc"},children:"Your assistant configuration is not synced with this subdomain."}),e.jsx("button",{onClick:d,disabled:u,style:{backgroundColor:u?"rgba(173, 216, 230, 0.2)":"rgba(173, 216, 230, 0.15)",color:u?"#B0C4DE":"#87CEEB",border:"1px solid rgba(173, 216, 230, 0.6)",padding:window.innerWidth<=768?"12px 24px":"8px 16px",borderRadius:window.innerWidth<=768?"20px":"4px",cursor:u?"not-allowed":"pointer",fontSize:window.innerWidth<=768?"14px":"12px",fontWeight:"bold",minWidth:window.innerWidth<=768?"120px":"auto",transition:"all 0.3s ease",backdropFilter:"blur(10px)"},children:u?"Activating...":window.innerWidth<=768?"Activate":"Activate Assistant"})]})]})},bn=({onActivated:o})=>{const[t,n]=r.useState(!1),[s,i]=r.useState(!1),[a,c]=r.useState(!1);r.useEffect(()=>{const u=()=>{const h=window.innerWidth<=768;c(h)};return u(),window.addEventListener("resize",u),()=>window.removeEventListener("resize",u)},[]),r.useEffect(()=>{a?(async()=>{try{const h=_e();if(h==="damon"){const d=await Ce(h),w=!d||d.isFallback||d.firmName==="Your Law Firm"&&!d.id||!d.id&&h==="damon";console.log("[MobileActivateAssistant] Activation check:",{subdomain:h,needsActivation:w,hasConfig:!!d,hasId:!!d?.id,firmName:d?.firmName}),n(w&&a)}}catch(h){console.error("[MobileActivateAssistant] Error checking activation:",h)}})():n(!1)},[a]);const p=async()=>{i(!0);try{console.log("[MobileActivateAssistant] Activating assistant...");const u=_e(),h=await Ce(u);console.log("[MobileActivateAssistant] Activation result:",{hasConfig:!!h,firmName:h?.firmName,assistantId:h?.vapi_assistant_id,id:h?.id}),h&&h.vapi_assistant_id?(n(!1),console.log("[MobileActivateAssistant] Assistant activated successfully"),o&&o(h),setTimeout(()=>{window.location.reload()},1e3)):(console.warn("[MobileActivateAssistant] Activation failed - no valid config found"),alert("Activation failed. Please try again or contact support."))}catch(u){console.error("[MobileActivateAssistant] Error activating assistant:",u),alert("Error activating assistant. Please try again.")}finally{i(!1)}};return!a||!t?null:e.jsxs("div",{style:{position:"fixed",bottom:"20px",left:"50%",transform:"translateX(-50%)",zIndex:1e4,backgroundColor:"rgba(173, 216, 230, 0.1)",padding:"12px 20px",borderRadius:"25px",border:"1px solid rgba(173, 216, 230, 0.8)",color:"#87CEEB",textAlign:"center",maxWidth:"90vw",minWidth:"280px",boxShadow:"0 2px 15px rgba(173, 216, 230, 0.2)",backdropFilter:"blur(15px)",animation:"slideUp 0.3s ease-out"},children:[e.jsx("style",{children:`
        @keyframes slideUp {
          from {
            transform: translateX(-50%) translateY(100px);
            opacity: 0;
          }
          to {
            transform: translateX(-50%) translateY(0);
            opacity: 1;
          }
        }
      `}),e.jsx("div",{style:{marginBottom:"8px",fontSize:"16px",fontWeight:"bold"},children:"🚀 Assistant Not Active"}),e.jsx("div",{style:{marginBottom:"12px",fontSize:"13px",color:"#ccc",lineHeight:"1.3"},children:"Tap to sync your assistant configuration"}),e.jsx("button",{onClick:p,disabled:s,style:{backgroundColor:s?"rgba(173, 216, 230, 0.2)":"rgba(173, 216, 230, 0.15)",color:s?"#B0C4DE":"#87CEEB",border:"1px solid rgba(173, 216, 230, 0.6)",padding:"12px 24px",borderRadius:"20px",cursor:s?"not-allowed":"pointer",fontSize:"14px",fontWeight:"bold",minWidth:"120px",transition:"all 0.3s ease",boxShadow:s?"none":"0 2px 10px rgba(173, 216, 230, 0.2)",backdropFilter:"blur(10px)"},children:s?"⏳ Activating...":"✨ Activate Now"})]})};const _n=({isOpen:o,onClose:t,onSuccess:n})=>{const[s,i]=r.useState(null),[a,c]=r.useState(""),[p,u]=r.useState(""),[h,d]=r.useState(""),[w,f]=r.useState(""),[I,R]=r.useState(!1),[y,M]=r.useState(null),[J,F]=r.useState("methods");r.useEffect(()=>{o&&(console.log("AuthOverlay opened"),i(null),c(""),u(""),d(""),f(""),M(null),F("methods"))},[o]);const Y=async()=>{R(!0),M(null);const S=!1;if(console.log("Current environment mode:","production"),!Le()&&!S){console.error("Supabase not configured for Google sign-in"),M("Authentication is not configured. Please contact the administrator."),R(!1);return}try{await Rt()}catch(x){console.error("Google sign-in error:",x),M("Failed to sign in with Google. Please try again."),R(!1)}},m=async S=>{if(S.preventDefault(),R(!0),M(null),!Le()){M("Authentication is not configured. Please contact the administrator."),R(!1);return}try{if(!a||!p||!h||!w)throw new Error("All fields are required");if(!/^[a-z0-9-]+$/.test(w))throw new Error("Subdomain can only contain lowercase letters, numbers, and hyphens");const{data:x,error:W}=await U.from("attorneys").select("id").eq("subdomain",w).single();if(x)throw new Error("This subdomain is already taken. Please choose another one.");const{data:ee,error:te}=await U.auth.signUp({email:a,password:p});if(console.log("AuthOverlay: supabase.auth.signUp result:",{authData:ee,authError:te}),te)throw te;const{data:j,error:T}=await U.from("attorneys").insert([{subdomain:w,firm_name:h,email:a,is_active:!0,user_id:ee.user.id,address:"",vapi_instructions:`You are a legal assistant for ${h}. Help potential clients understand their legal needs and collect relevant information for consultation.`}]).select("*").single();if(console.log('AuthOverlay: supabase.from("attorneys").insert result:',{attorneyData:j,attorneyError:T}),T)throw T;try{const B=await jt.createAssistantForAttorney(j);console.log("Created Vapi assistant for attorney:",B)}catch(B){console.error("Error creating Vapi assistant:",B)}localStorage.setItem("attorney",JSON.stringify(j)),localStorage.setItem("attorney_id",j.id),localStorage.setItem("currentAttorneyId",j.id),n?n(j):window.location.href="/dashboard"}catch(x){console.error("Email sign-up error:",x),M(x.message||"Failed to create account. Please try again.")}finally{R(!1)}},E=S=>{S.target.classList.contains("auth-overlay")&&t()};if(!o)return null;const C=!1,P=!Le()&&!C;return e.jsx("div",{className:"auth-overlay",onClick:E,children:e.jsxs("div",{className:"auth-overlay-content",children:[e.jsx("button",{className:"close-button",onClick:t,children:"×"}),P&&e.jsxs("div",{className:"error-message",children:[e.jsx("h3",{children:"Authentication Not Configured"}),e.jsx("p",{children:"The authentication system is not properly configured. This is expected in development mode."}),e.jsxs("div",{className:"dev-mode-options",children:[e.jsx("h4",{children:"Development Options:"}),e.jsx("button",{className:"dev-mode-button",onClick:()=>{console.log("Using mock authentication for development");const S={id:"dev-"+Date.now(),firm_name:"Development Law Firm",subdomain:"devmode",email:"<EMAIL>",is_active:!0,created_at:new Date().toISOString(),vapi_instructions:"You are a legal assistant for Development Law Firm. Help potential clients understand their legal needs and collect relevant information for consultation."};localStorage.setItem("attorney",JSON.stringify(S)),n?n(S):window.location.href="/dashboard"},children:"Continue in Development Mode"}),e.jsxs("div",{className:"setup-instructions",children:[e.jsx("p",{children:e.jsx("strong",{children:"To configure Supabase:"})}),e.jsxs("ol",{children:[e.jsxs("li",{children:["Create a ",e.jsx("a",{href:"https://supabase.com/",target:"_blank",rel:"noopener noreferrer",children:"Supabase"})," account and project"]}),e.jsxs("li",{children:["Create a ",e.jsx("code",{children:".env"})," file in the project root with:"]}),e.jsxs("pre",{children:["VITE_SUPABASE_URL=your-supabase-url",e.jsx("br",{}),"VITE_SUPABASE_KEY=your-supabase-anon-key"]}),e.jsxs("li",{children:["Create an ",e.jsx("code",{children:"attorneys"})," table in Supabase with the required fields"]})]})]})]})]}),!P&&J==="methods"&&e.jsxs(e.Fragment,{children:[e.jsx("h2",{children:"Create Your Attorney Account"}),e.jsx("p",{children:"Choose a sign-up method to get started with your customized legal assistant."}),e.jsxs("div",{className:"auth-methods",children:[e.jsxs("button",{className:"auth-method-button google-button",onClick:Y,disabled:I,children:[e.jsx("img",{src:"/google-icon.svg",alt:"Google"}),"Continue with Google"]}),e.jsx("div",{className:"divider",children:e.jsx("span",{children:"or"})}),e.jsxs("button",{className:"auth-method-button email-button",onClick:()=>F("email"),disabled:I,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("rect",{x:"2",y:"4",width:"20",height:"16",rx:"2"}),e.jsx("path",{d:"M22 7l-10 7L2 7"})]}),"Continue with Email"]})]})]}),!P&&J==="email"&&e.jsxs(e.Fragment,{children:[e.jsx("h2",{children:"Create Your Attorney Account"}),e.jsx("p",{children:"Fill in your details to set up your customized legal assistant."}),e.jsxs("form",{onSubmit:m,className:"auth-form",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"firmName",children:"Law Firm Name"}),e.jsx("input",{type:"text",id:"firmName",value:h,onChange:S=>d(S.target.value),placeholder:"Your Law Firm, LLC",required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"subdomain",children:"Subdomain"}),e.jsxs("div",{className:"subdomain-input",children:[e.jsx("input",{type:"text",id:"subdomain",value:w,onChange:S=>f(S.target.value.toLowerCase()),placeholder:"yourfirm",required:!0}),e.jsx("span",{className:"subdomain-suffix",children:".legalscout.ai"})]}),e.jsxs("small",{children:["This will be your unique URL: https://",w||"yourfirm",".legalscout.ai"]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"email",children:"Email Address"}),e.jsx("input",{type:"email",id:"email",value:a,onChange:S=>c(S.target.value),placeholder:"<EMAIL>",required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{htmlFor:"password",children:"Password"}),e.jsx("input",{type:"password",id:"password",value:p,onChange:S=>u(S.target.value),placeholder:"Create a secure password",required:!0,minLength:"8"})]}),y&&e.jsx("div",{className:"error-message",children:y}),e.jsxs("div",{className:"form-actions",children:[e.jsx("button",{type:"button",className:"back-button",onClick:()=>F("methods"),disabled:I,children:"Back"}),e.jsx("button",{type:"submit",className:"submit-button",disabled:I,children:I?"Creating Account...":"Create Account"})]})]})]}),!P&&J==="success"&&e.jsxs("div",{className:"success-message",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"64",height:"64",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),e.jsx("polyline",{points:"22 4 12 14.01 9 11.01"})]}),e.jsx("h2",{children:"Account Created Successfully!"}),e.jsx("p",{children:"Redirecting to your dashboard..."}),e.jsx("div",{className:"loading-spinner"})]})]})})};function Cn(){const o="https://utopqxsvudgrtiwenlzl.supabase.co",t=!!"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU";return console.log("=== SUPABASE CONFIG TEST ==="),console.log("Supabase URL configured:",!!o),console.log("Supabase Key configured:",t),console.log("Supabase URL:",o),{urlConfigured:!!o,keyConfigured:t}}async function rt(){console.log("🔍 Verifying Supabase configuration..."),console.log("Environment variables:",{VITE_SUPABASE_URL:"https://utopqxsvudgrtiwenlzl.supabase.co",REACT_APP_SUPABASE_URL:"https://utopqxsvudgrtiwenlzl.supabase.co",VITE_SUPABASE_KEY:"[HIDDEN]",VITE_SUPABASE_ANON_KEY:"[HIDDEN]",REACT_APP_SUPABASE_KEY:"[HIDDEN]",REACT_APP_SUPABASE_ANON_KEY:"[HIDDEN]"});const t="https://utopqxsvudgrtiwenlzl.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU",s=t==="your-supabase-url",i=n==="your-anon-key";try{console.log("Testing Supabase connection...");const{data:a,error:c}=await U.from("attorneys").select("*").limit(1);return c?c.message==="Invalid API key"?(console.error("❌ Invalid Supabase API key detected"),console.warn("The application will use mock data instead of real Supabase data"),console.warn("To fix this, update your Supabase API key in the .env file"),{success:!1,error:"Invalid API key - using mock data instead",usingFallback:!0,useMockData:!0}):(console.error("❌ Supabase connection test failed:",c.message),{success:!1,error:c.message,usingFallback:s||i}):(console.log("✅ Supabase connection test successful!"),{success:!0,data:a,usingFallback:s||i})}catch(a){return console.error("❌ Unexpected error testing Supabase:",a.message),{success:!1,error:a.message,usingFallback:s||i}}}function Sn(){(window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1")&&(console.log("🔧 Development mode detected, initializing Supabase configuration..."),rt().then(t=>{t.success?console.log("✅ Supabase configuration verified and working"):console.error("❌ Supabase configuration verification failed:",t.error)}))}const xn=()=>e.jsx("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",backgroundColor:"#f0f0f0",color:"#333",padding:"20px",textAlign:"center"},children:e.jsxs("div",{style:{maxWidth:"600px",backgroundColor:"white",padding:"30px",borderRadius:"10px",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)"},children:[e.jsx("h1",{style:{color:"#4B74AA"},children:"LegalScout Test Page"}),e.jsx("p",{children:"If you can see this page, React is working correctly."}),e.jsx("p",{children:"This is a simple React component to test if the application can render without Framer Motion."}),e.jsxs("p",{children:["Current time: ",new Date().toLocaleString()]})]})});class Nn{constructor(){this.useMock=!1,this.apiKey=null,this.initialized=!1,this.mcpService=ue}async initialize(t,n=!1,s={}){const{isAttorneyDashboard:i=!1,isPreview:a=!1,isProduction:c=!1,forceMcpMode:p=!1,forceDirect:u=!1}=s,h=typeof window<"u"&&window.FAST_LOADING_MODE;console.log("[VapiServiceManager] Initializing with API key:",t?"****":"none",`(Attorney Dashboard: ${i}, Preview: ${a}, Production: ${c}, Force MCP: ${p}, Force Direct: ${u}, Fast Loading: ${h})`),this.apiKey=t,this.isAttorneyDashboard=i,this.isPreview=a,this.isProduction=c,this.forceMcpMode=p,n&&console.error("💀 [VapiServiceManager] Mock mode requested but MOCKS ARE DEAD! Using real service only.");try{console.log("[VapiServiceManager] Trying to connect to real Vapi service");let d=!1;if(h||u?(console.log("[VapiServiceManager] Fast loading mode - using direct API immediately"),await ue.connect(t,!0)&&(console.log("[VapiServiceManager] Connected to Vapi using direct API (fast loading)"),this.useMock=!1,this.mcpService=ue,d=!0)):this.forceMcpMode?(console.log("[VapiServiceManager] Forcing MCP mode, skipping direct API"),await ue.connect(t)&&(console.log("[VapiServiceManager] Connected to Vapi MCP server"),this.useMock=!1,this.mcpService=ue,d=!0)):await ue.connect(t)?(console.log("[VapiServiceManager] Connected to Vapi MCP server"),this.useMock=!1,this.mcpService=ue,d=!0):(console.log("[VapiServiceManager] MCP connection failed, trying direct API"),await ue.connect(t,!0)&&(console.log("[VapiServiceManager] Connected to Vapi using direct API"),this.useMock=!1,this.mcpService=ue,d=!0)),!d)throw console.error("💀 [VapiServiceManager] Failed to connect to Vapi - NO MOCKS ALLOWED!"),this.connectionError=!0,this.connectionWarning="Critical: Unable to connect to voice service. Please check your API key and network connection.",new Error("Failed to connect to Vapi service - no fallback available");return this.initialized=!0,!0}catch(d){throw console.error("💀 [VapiServiceManager] Error initializing Vapi service - NO MOCKS ALLOWED!",d),this.connectionError=!0,this.connectionWarning="Critical error connecting to voice service. Please check your configuration.",d}}getConnectionWarning(){return this.connectionWarning||null}getMcpService(){return this.mcpService}createVapiInstance(t,n={}){if(console.log("💀 [VapiServiceManager] Creating real Vapi instance - NO MOCKS!"),typeof window<"u"&&window.Vapi)return window.Vapi.create(t,n);if(typeof Vapi<"u")return Vapi.create(t,n);throw console.error("💀 [VapiServiceManager] Vapi not available - NO MOCKS ALLOWED!"),new Error("Vapi SDK not available - please ensure it is loaded")}isUsingMock(){return this.useMock}getConnectionStatus(){return{initialized:this.initialized,useMock:!1,connected:this.mcpService.connected,connectionMode:this.mcpService.useDirect?"direct":"mcp"}}}const A=new Nn;class Vn{constructor(){this.currentAttorney=null,this.subscription=null,this.listeners=new Set,this.isInitialized=!1,this.lastSyncTime=null,this.syncStatus={consistent:!1,message:"Not synchronized yet"},this.isRecoveryMode=!1,this.recoveryAttempts=0,this.MAX_RECOVERY_ATTEMPTS=3,this.pendingUpdates=new Map,this.initPromise=null,this.initialize=this.initialize.bind(this),this.handleAttorneyUpdate=this.handleAttorneyUpdate.bind(this),this.checkVapiSynchronization=this.checkVapiSynchronization.bind(this),this.forceSynchronization=this.forceSynchronization.bind(this),this.autoInitializeFromLocalStorage()}autoInitializeFromLocalStorage(){try{const t=this.loadFromLocalStorage();t&&t.id&&(console.log("[AttorneyProfileManager] Auto-initializing from localStorage"),this.currentAttorney=t,this.setupRealtimeSubscription(t.id),setTimeout(async()=>{try{let n=t;if(t.id&&typeof t.id=="string"&&!t.id.startsWith("dev-"))try{const s=await this.loadAttorneyById(t.id);s&&(console.log("[AttorneyProfileManager] Refreshed attorney data for auto-sync:",{id:s.id,vapi_assistant_id:s.vapi_assistant_id}),n=s,this.currentAttorney=s,this.saveToLocalStorage(s))}catch(s){console.warn("[AttorneyProfileManager] Could not refresh attorney data for auto-sync:",s)}console.log("[AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)")}catch(n){console.error("[AttorneyProfileManager] Auto-sync check error:",n)}},2e3))}catch(t){console.error("[AttorneyProfileManager] Auto-initialize error:",t)}}async initialize(t,n=null){if(this.initPromise)return this.initPromise;this.initPromise=this._initialize(t,n);try{return await this.initPromise}finally{this.initPromise=null}}async _initialize(t,n=null){if(console.log("[AttorneyProfileManager] Initializing with userId:",t,"email:",n),this.isInitialized&&this.currentAttorney)return console.log("[AttorneyProfileManager] Already initialized with attorney:",this.currentAttorney.id),this.currentAttorney;try{let s=null;if(t)try{s=await this.loadAttorneyByUserId(t),s&&console.log("[AttorneyProfileManager] Found attorney by userId:",s.id)}catch(i){console.warn("[AttorneyProfileManager] Error loading by userId:",i)}if(!s&&n)try{s=await this.loadAttorneyByEmail(n),s&&(console.log("[AttorneyProfileManager] Found attorney by email:",s.id),s.vapi_assistant_id&&!s.vapi_assistant_id.includes("mock")&&console.log("[AttorneyProfileManager] ✅ Attorney already has valid assistant:",s.vapi_assistant_id),t&&(!s.user_id||s.user_id!==t)&&(console.log("[AttorneyProfileManager] Linking attorney to userId:",t),s=await this.updateAttorneyInSupabase({id:s.id,user_id:t})))}catch(i){console.warn("[AttorneyProfileManager] Error loading by email:",i)}if(!s){const i=this.loadFromLocalStorage();if(i&&i.id){console.log("[AttorneyProfileManager] Found attorney in localStorage:",i.id);try{s=await this.loadAttorneyById(i.id),s&&(console.log("[AttorneyProfileManager] Verified attorney from localStorage exists in Supabase"),t&&(!s.user_id||s.user_id!==t)&&(console.log("[AttorneyProfileManager] Linking attorney to userId:",t),s=await this.updateAttorneyInSupabase({id:s.id,user_id:t})))}catch(a){console.warn("[AttorneyProfileManager] Error verifying localStorage attorney:",a),s=i}}}if(!s&&(t||n)){console.log("[AttorneyProfileManager] No attorney found, creating new attorney profile");const i=n||`user-${t}@example.com`,a=i.split("@")[0],c=i.split("@")[1],p=c?`${c.split(".")[0]} Legal`:"My Law Firm";try{const{supabase:u}=await ge(()=>import("./pages-26099600.js").then(f=>f.O),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"]),h={subdomain:`${a}-${Date.now()}`.toLowerCase().replace(/[^a-z0-9-]/g,"-"),firm_name:p,name:a,email:i,user_id:t,is_active:!0,vapi_instructions:`You are a legal assistant for ${p}. Help potential clients understand their legal needs and collect relevant information for consultation.`,welcome_message:`Hello! I'm Scout from ${p}. How can I help you with your legal needs today?`,information_gathering:"Tell me about your legal situation, and I'll help connect you with the right resources and guidance.",primary_color:"#4B74AA",secondary_color:"#2C3E50",background_color:"#1a1a1a",voice_provider:"playht",voice_id:"ranger",ai_model:"gpt-4o"},{data:d,error:w}=await u.from("attorneys").insert([h]).select().single();if(!w&&d)console.log("[AttorneyProfileManager] Created new attorney in Supabase:",d.id),s=d;else throw console.warn("[AttorneyProfileManager] Failed to create attorney in Supabase:",w),w}catch(u){console.warn("[AttorneyProfileManager] Supabase creation failed, creating local attorney:",u),s={id:`dev-${Date.now()}`,user_id:t,email:i,firm_name:p,name:a,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),vapi_instructions:`You are a legal assistant for ${p}. Help potential clients understand their legal needs and collect relevant information for consultation.`,welcome_message:`Hello! I'm Scout from ${p}. How can I help you with your legal needs today?`,is_development:!0}}console.log("[AttorneyProfileManager] Created development attorney:",s.id)}if(s){if(this.currentAttorney=s,this.saveToLocalStorage(s),s.id&&typeof s.id=="string"&&!s.id.startsWith("dev-")?this.setupRealtimeSubscription(s.id):console.log("[AttorneyProfileManager] Development attorney or invalid ID, skipping Realtime subscription"),s.id&&typeof s.id=="string"&&!s.id.startsWith("dev-"))try{const i=await this.loadAttorneyById(s.id);i&&(console.log("[AttorneyProfileManager] Refreshed attorney data before Vapi sync:",{id:i.id,vapi_assistant_id:i.vapi_assistant_id}),s=i,this.currentAttorney=i,this.saveToLocalStorage(i))}catch(i){console.warn("[AttorneyProfileManager] Could not refresh attorney data:",i)}return console.log("[AttorneyProfileManager] 🛑 MVP FIX: Skipping automatic Vapi sync to prevent duplicate assistants"),s.vapi_assistant_id&&!s.vapi_assistant_id.includes("mock")&&console.log("[AttorneyProfileManager] ✅ Attorney has valid assistant:",s.vapi_assistant_id),this.isInitialized=!0,this.notifyListeners(),s}return console.warn("[AttorneyProfileManager] No attorney profile found and could not create development attorney"),null}catch(s){console.error("[AttorneyProfileManager] Initialization error:",s);const i=this.loadFromLocalStorage();if(i)return console.log("[AttorneyProfileManager] Recovering from localStorage"),this.currentAttorney=i,this.notifyListeners(),i;throw s}}async loadAttorneyByUserId(t){try{console.log("[AttorneyProfileManager] Loading attorney by userId:",t);const{data:n,error:s}=await U.from("attorneys").select("*").eq("user_id",t).single();if(s){if(s.code==="PGRST116")return null;throw s}return n}catch(n){throw console.error("[AttorneyProfileManager] Error loading attorney by userId:",n),n}}async loadAttorneyByEmail(t){try{console.log("[AttorneyProfileManager] Loading attorney by email:",t);const{data:n,error:s}=await U.from("attorneys").select("*").eq("email",t).order("updated_at",{ascending:!1});if(s)throw s;if(n&&n.length>0){if(n.length>1){console.warn(`[AttorneyProfileManager] Found ${n.length} attorneys for email ${t}`);const i=n.find(a=>a.vapi_assistant_id&&!a.vapi_assistant_id.includes("mock")&&!a.vapi_assistant_id.includes("duplicate"));if(i)return console.log(`[AttorneyProfileManager] Using attorney with valid assistant ID: ${i.vapi_assistant_id}`),i}return n[0]}return null}catch(n){throw console.error("[AttorneyProfileManager] Error loading attorney by email:",n),n}}async loadAttorneyById(t){try{console.log("[AttorneyProfileManager] Loading attorney by id:",t);const{data:n,error:s}=await U.from("attorneys").select("*").eq("id",t).single();if(s){if(s.code==="PGRST116")return null;throw s}return n}catch(n){throw console.error("[AttorneyProfileManager] Error loading attorney by id:",n),n}}setupRealtimeSubscription(t){try{console.log("[AttorneyProfileManager] Setting up Realtime subscription for attorney:",t),typeof U.channel=="function"?(this.channel&&(U.removeChannel(this.channel),this.channel=null),this.channel=U.channel(`attorneys:id=${t}`).on("postgres_changes",{event:"UPDATE",schema:"public",table:"attorneys",filter:`id=eq.${t}`},n=>this.handleAttorneyUpdate(n)).on("postgres_changes",{event:"DELETE",schema:"public",table:"attorneys",filter:`id=eq.${t}`},n=>this.handleAttorneyDelete(n)).subscribe(),console.log("[AttorneyProfileManager] Realtime subscription set up using channel API")):typeof U.from=="function"&&typeof U.from("attorneys").on=="function"?(this.subscription&&(U.removeSubscription(this.subscription),this.subscription=null),this.subscription=U.from(`attorneys:id=eq.${t}`).on("UPDATE",n=>this.handleAttorneyUpdate(n)).on("DELETE",n=>this.handleAttorneyDelete(n)).subscribe(),console.log("[AttorneyProfileManager] Realtime subscription set up using from().on() API")):(console.warn("[AttorneyProfileManager] Supabase Realtime API not available, using polling instead"),this.pollingInterval&&clearInterval(this.pollingInterval),this.pollingInterval=setInterval(async()=>{try{if(this.currentAttorney&&this.currentAttorney.id){const{data:n,error:s}=await U.from("attorneys").select("*").eq("id",this.currentAttorney.id).single();if(s)throw s;n&&JSON.stringify(n)!==JSON.stringify(this.currentAttorney)&&(console.log("[AttorneyProfileManager] Attorney updated via polling"),this.handleAttorneyUpdate({new:n}))}}catch(n){console.error("[AttorneyProfileManager] Error polling for attorney updates:",n)}},1e4),console.log("[AttorneyProfileManager] Polling fallback set up"))}catch(n){console.error("[AttorneyProfileManager] Error setting up Realtime subscription:",n),setTimeout(()=>{this.currentAttorney&&this.currentAttorney.id&&this.setupRealtimeSubscription(this.currentAttorney.id)},5e3)}}async handleAttorneyUpdate(t){console.log("[AttorneyProfileManager] Received attorney update from Supabase:",t);try{const n=t.new,s=n.id+"_"+n.updated_at;this.pendingUpdates.has(s)?(console.log("[AttorneyProfileManager] This is a pending update we initiated, skipping sync check"),this.pendingUpdates.delete(s)):console.log("[AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)"),this.currentAttorney=n,this.saveToLocalStorage(n),this.notifyListeners()}catch(n){console.error("[AttorneyProfileManager] Error handling attorney update:",n)}}handleAttorneyDelete(t){console.log("[AttorneyProfileManager] Received attorney delete from Supabase:",t),this.currentAttorney=null;try{localStorage.removeItem("attorney"),localStorage.removeItem("attorney_last_updated")}catch(n){console.error("[AttorneyProfileManager] Error clearing localStorage:",n)}this.notifyListeners()}shouldSyncWithVapi(t){if(!this.currentAttorney)return t.vapi_assistant_id?(console.log("[AttorneyProfileManager] No current attorney but has assistant ID - checking if assistant exists"),!0):(console.log("[AttorneyProfileManager] No current attorney and no assistant ID - sync needed to create assistant"),!0);const n=["firm_name","welcome_message","vapi_instructions","voice_provider","voice_id","ai_model","vapi_assistant_id"],s=["logo_url","profile_image","button_image","primary_color","secondary_color","button_color","background_color","address","phone","practice_areas","practice_description","scheduling_link","custom_fields","summary_prompt","structured_data_prompt","structured_data_schema"];for(const a of n)if(this.currentAttorney[a]!==t[a])return console.log(`[AttorneyProfileManager] Vapi-relevant field changed: ${a}`),console.log(`  Old: ${this.currentAttorney[a]}`),console.log(`  New: ${t[a]}`),!0;const i=[];for(const a in t)this.currentAttorney[a]!==t[a]&&i.push(a);if(i.length>0){const a=i.filter(c=>s.includes(c));a.length>0&&console.log(`[AttorneyProfileManager] Only non-Vapi fields changed: ${a.join(", ")}`)}return!1}async checkVapiSynchronization(t){if(!t){console.warn("[AttorneyProfileManager] No attorney to check Vapi synchronization");return}if(Array.isArray(t))if(console.log("[AttorneyProfileManager] Attorney passed as array, extracting first element"),t.length>0&&t[0]&&t[0].id)t=t[0],console.log("[AttorneyProfileManager] Using attorney from array:",t.id);else{console.error("[AttorneyProfileManager] Array is empty or invalid:",t),this.syncStatus={consistent:!1,message:"Invalid attorney array data",lastChecked:new Date,error:"Attorney array is empty or invalid"};return}if(!t.id){console.error("[AttorneyProfileManager] Attorney missing ID, cannot sync with Vapi:",t),this.syncStatus={consistent:!1,message:"Attorney missing ID, cannot sync with voice service",lastChecked:new Date,error:"Invalid attorney data"};return}console.log("[AttorneyProfileManager] Checking Vapi synchronization for attorney:",t.id);try{const n=A.getMcpService();if(!await n.ensureConnection()){console.warn("[AttorneyProfileManager] Vapi service not connected, skipping synchronization"),this.syncStatus={consistent:!0,message:"Voice service not available, using local data",lastChecked:new Date},this.lastSyncTime=new Date;return}if(t.vapi_assistant_id&&t.vapi_assistant_id.startsWith("mock-")){console.warn("[AttorneyProfileManager] Mock assistant ID detected, attempting to fix:",t.vapi_assistant_id);const i=typeof window<"u"&&(window.location.hostname==="dashboard.legalscout.net"||window.location.hostname.endsWith(".legalscout.net"));try{const c=(await n.listAssistants()).find(p=>p.name&&p.name.includes(t.firm_name)&&!p.id.startsWith("mock-"));if(c){console.log("[AttorneyProfileManager] Found existing real assistant:",c.id),await this.updateAttorneyInSupabase({id:t.id,vapi_assistant_id:c.id}),this.syncStatus={consistent:!0,message:"Fixed mock assistant ID with existing real assistant",lastChecked:new Date},this.lastSyncTime=new Date;return}else if(i){console.log("[AttorneyProfileManager] No existing assistant found, creating new one");const p=await this.createVapiAssistant(t);if(p&&p.id&&!p.id.startsWith("mock-")){await this.updateAttorneyInSupabase({id:t.id,vapi_assistant_id:p.id}),this.syncStatus={consistent:!0,message:"Created new real assistant to replace mock",lastChecked:new Date},this.lastSyncTime=new Date;return}}}catch(a){console.error("[AttorneyProfileManager] Error fixing mock assistant ID:",a)}this.syncStatus={consistent:!1,message:"Mock assistant ID detected - needs real assistant",lastChecked:new Date,warning:"Voice assistant needs to be properly configured"},this.lastSyncTime=new Date;return}if(t.vapi_assistant_id){let i;try{i=await A.getMcpService().getAssistant(t.vapi_assistant_id)}catch(a){console.error("[AttorneyProfileManager] Error getting Vapi assistant:",a),a.message.includes("MCP server")||a.message.includes("connection")?(console.warn("[AttorneyProfileManager] MCP server not available, using mock assistant"),i={id:t.vapi_assistant_id,name:`${t.firm_name} Assistant`,instructions:t.vapi_instructions,firstMessage:t.welcome_message,mock:!0}):i=null}if(i){const a=this.findDiscrepancies(t,i);Object.keys(a).length>0?(console.log("[AttorneyProfileManager] Found discrepancies, updating assistant:",a),await this.updateVapiAssistant(t),this.syncStatus={consistent:!0,message:"Updated Vapi assistant",lastChecked:new Date}):(console.log("[AttorneyProfileManager] No discrepancies found, assistant is up to date"),this.syncStatus={consistent:!0,message:"Vapi assistant is up to date",lastChecked:new Date})}else{console.log("[AttorneyProfileManager] Assistant not found, creating new one");const a=await this.createVapiAssistant(t);await this.updateAttorneyInSupabase({id:t.id,vapi_assistant_id:a.id}),this.syncStatus={consistent:!0,message:"Created replacement Vapi assistant",lastChecked:new Date}}}else{console.log("[AttorneyProfileManager] Attorney has no Vapi assistant ID, creating new assistant");const i=await this.createVapiAssistant(t);i&&i.id&&!i.id.startsWith("mock-")?(await this.updateAttorneyInSupabase({id:t.id,vapi_assistant_id:i.id}),this.syncStatus={consistent:!0,message:"Created new Vapi assistant",lastChecked:new Date}):(console.warn("[AttorneyProfileManager] Mock assistant created, not saving to database"),this.syncStatus={consistent:!1,message:"Mock assistant created, Vapi service may be unavailable",lastChecked:new Date,warning:"Voice assistant will be created when service is available"})}this.lastSyncTime=new Date,this.isRecoveryMode=!1,this.recoveryAttempts=0}catch(n){console.error("[AttorneyProfileManager] Error checking Vapi synchronization:",n),n.message.includes("MCP server")||n.message.includes("connection")?(console.warn("[AttorneyProfileManager] MCP server not available, marking as consistent but noting issue"),this.syncStatus={consistent:!0,message:"Vapi MCP service not available, using local data",lastChecked:new Date,warning:n.message},this.lastSyncTime=new Date):(this.syncStatus={consistent:!1,message:`Sync error: ${n.message}`,lastChecked:new Date,error:n.message},this.isRecoveryMode||(this.isRecoveryMode=!0,this.recoveryAttempts=0,this.attemptRecovery()))}}async attemptRecovery(){if(this.recoveryAttempts>=this.MAX_RECOVERY_ATTEMPTS){console.warn("[AttorneyProfileManager] Max recovery attempts reached, giving up"),this.isRecoveryMode=!1;return}this.recoveryAttempts++,console.log(`[AttorneyProfileManager] Recovery attempt ${this.recoveryAttempts}/${this.MAX_RECOVERY_ATTEMPTS}`);try{switch(this.recoveryAttempts){case 1:if(this.currentAttorney&&this.currentAttorney.id){const t=await this.loadAttorneyById(this.currentAttorney.id);t&&(this.currentAttorney=t,this.saveToLocalStorage(t),await this.checkVapiSynchronization(t))}break;case 2:if(this.currentAttorney&&this.currentAttorney.id){const t=await this.createVapiAssistant(this.currentAttorney);await this.updateAttorneyInSupabase({id:this.currentAttorney.id,vapi_assistant_id:t.id})}else console.warn("[AttorneyProfileManager] Cannot create assistant: current attorney missing or has no ID");break;case 3:this.currentAttorney&&this.currentAttorney.id?await this.updateAttorneyInSupabase({id:this.currentAttorney.id,vapi_assistant_id:null}):console.warn("[AttorneyProfileManager] Cannot reset assistant ID: current attorney missing or has no ID");break}}catch(t){console.error(`[AttorneyProfileManager] Recovery attempt ${this.recoveryAttempts} failed:`,t),setTimeout(()=>{this.attemptRecovery()},5e3*this.recoveryAttempts)}}async forceSynchronization(){console.log("[AttorneyProfileManager] Forcing synchronization");try{if(!this.currentAttorney)throw new Error("No attorney profile loaded");if(!this.currentAttorney.id)throw new Error("Current attorney missing ID, cannot sync");let t;try{console.log("[AttorneyProfileManager] Fetching latest data from Supabase for attorney:",this.currentAttorney.id);const{data:n,error:s}=await U.from("attorneys").select("*").eq("id",this.currentAttorney.id).single();if(s)throw s;t=n,console.log("[AttorneyProfileManager] Fetched Supabase data:",{id:n.id,firm_name:n.firm_name,vapi_assistant_id:n.vapi_assistant_id,voice_provider:n.voice_provider,voice_id:n.voice_id}),this.currentAttorney=n,this.saveToLocalStorage(n)}catch(n){console.error("[AttorneyProfileManager] Error getting attorney from Supabase:",n),console.warn("[AttorneyProfileManager] Continuing with current attorney data:",{id:this.currentAttorney.id,firm_name:this.currentAttorney.firm_name,vapi_assistant_id:this.currentAttorney.vapi_assistant_id}),t=this.currentAttorney}if(t.vapi_assistant_id)try{await this.updateVapiAssistant(t)}catch(n){console.error("[AttorneyProfileManager] Error updating Vapi assistant:",n),n.message.includes("MCP server")||n.message.includes("connection")?(console.warn("[AttorneyProfileManager] MCP server not available, continuing with local data"),this.syncStatus={consistent:!0,message:"Voice service not available, using local data",lastChecked:new Date,warning:n.message}):this.syncStatus={consistent:!1,message:`Voice service sync error: ${n.message}`,lastChecked:new Date,error:n.message}}else{console.warn("[AttorneyProfileManager] No Vapi assistant ID available, skipping Vapi sync");try{console.log("[AttorneyProfileManager] Attempting to create new Vapi assistant");const n=await this.createVapiAssistant(t);n&&n.id&&(await this.updateAttorneyInSupabase({id:t.id,vapi_assistant_id:n.id}),console.log("[AttorneyProfileManager] Created and saved new Vapi assistant:",n.id),this.syncStatus={consistent:!0,message:"Created new voice assistant",lastChecked:new Date})}catch(n){console.error("[AttorneyProfileManager] Error creating new Vapi assistant:",n),this.syncStatus={consistent:!1,message:"No voice assistant available and could not create one",lastChecked:new Date,warning:"Voice assistant will be created on next sync attempt"}}}return this.lastSyncTime=new Date,(!this.syncStatus||!this.syncStatus.lastChecked||this.syncStatus.lastChecked<this.lastSyncTime)&&(this.syncStatus={consistent:!0,message:"Forced synchronization successful",lastChecked:new Date}),this.notifyListeners(),{success:!0,attorney:t}}catch(t){return console.error("[AttorneyProfileManager] Error forcing synchronization:",t),this.syncStatus={consistent:!1,message:`Sync error: ${t.message}`,lastChecked:new Date,error:t.message},this.notifyListeners(),{success:!1,error:t.message}}}async createVapiAssistant(t){try{if(!t)throw new Error("Attorney object is required");const n=t.id||t.firm_name||"unknown";console.log("[AttorneyProfileManager] Creating Vapi assistant for attorney:",n),t.firm_name||(t.firm_name="Your Law Firm");const s=typeof window<"u"&&(window.location.hostname==="dashboard.legalscout.net"||window.location.hostname.endsWith(".legalscout.net")),i=A.getMcpService();await i.ensureConnection();const a={name:`${t.firm_name} Assistant`,firstMessage:t.welcome_message||`Hello, I'm Scout from ${t.firm_name}. How can I help you today?`,firstMessageMode:"assistant-speaks-first",model:{provider:"openai",model:t.ai_model||"gpt-4o",messages:[{role:"system",content:t.vapi_instructions||`You are a legal assistant for ${t.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`}]},voice:{provider:t.voice_provider||"11labs",voiceId:t.voice_id||"sarah"},transcriber:{provider:"deepgram",model:"nova-3"}},c=await i.createAssistant(a);if(c&&c.id&&c.id.startsWith("mock-")){if(s)throw new Error("Mock assistant created in production environment");console.warn("[AttorneyProfileManager] Mock assistant created, this should not happen in production")}return console.log("[AttorneyProfileManager] Created Vapi assistant:",c.id),c}catch(n){if(console.error("[AttorneyProfileManager] Error creating Vapi assistant:",n),typeof window<"u"&&(window.location.hostname==="dashboard.legalscout.net"||window.location.hostname.endsWith(".legalscout.net")))throw n;if(n.message.includes("MCP server")||n.message.includes("connection"))return console.warn("[AttorneyProfileManager] MCP server not available, creating mock assistant"),{id:"mock-"+Date.now(),name:`${t.firm_name} Assistant`,instructions:t.vapi_instructions,firstMessage:t.welcome_message,mock:!0};throw n}}async updateVapiAssistant(t){try{if(!t)throw new Error("Attorney object is required");const n=t.id||t.firm_name||"unknown";if(console.log("[AttorneyProfileManager] Updating Vapi assistant for attorney:",n),!t.vapi_assistant_id)throw new Error("No Vapi assistant ID");const s=A.getMcpService();if(!await s.ensureConnection())return console.warn("[AttorneyProfileManager] Vapi service not connected, returning mock updated assistant"),{id:t.vapi_assistant_id,name:`${t.firm_name} Assistant`,instructions:t.vapi_instructions,firstMessage:t.welcome_message,mock:!0};let a;try{a=await s.getAssistant(t.vapi_assistant_id)}catch(d){if(console.error("[AttorneyProfileManager] Error getting assistant:",d),d.message.includes("MCP server")||d.message.includes("connection"))return console.warn("[AttorneyProfileManager] MCP server error getting assistant, returning mock assistant"),{id:t.vapi_assistant_id,name:`${t.firm_name} Assistant`,instructions:t.vapi_instructions,firstMessage:t.welcome_message,mock:!0};if(d.message.includes("not found"))return console.warn("[AttorneyProfileManager] Assistant not found, creating a new one"),await this.createVapiAssistant(t);throw d}if(!a)return console.warn("[AttorneyProfileManager] Assistant not found, creating a new one"),await this.createVapiAssistant(t);let c=t.voice_provider||(a.voice?a.voice.provider:"11labs"),p=t.voice_id||(a.voice?a.voice.voiceId:"sarah");c==="playht"&&p==="sarah"&&(console.warn("[AttorneyProfileManager] Fixing voice mismatch: sarah is not available for playht, switching to 11labs"),c="11labs");const u={name:`${t.firm_name} Assistant`,firstMessage:t.welcome_message||`Hello, I'm Scout from ${t.firm_name}. How can I help you today?`,firstMessageMode:"assistant-speaks-first",instructions:t.vapi_instructions||`You are a legal assistant for ${t.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`,llm:{...a.llm||a.model||{provider:"openai",model:"gpt-4o"},model:t.ai_model||(a.llm?a.llm.model:a.model?a.model.model:"gpt-4o")},voice:{...a.voice||{provider:"11labs",voiceId:"sarah"},provider:c,voiceId:p}};console.log("[AttorneyProfileManager] Sending update config to Vapi:",JSON.stringify(u,null,2));const h=await s.updateAssistant(t.vapi_assistant_id,u);return console.log("[AttorneyProfileManager] Updated Vapi assistant:",h.id),h}catch(n){if(console.error("[AttorneyProfileManager] Error updating Vapi assistant:",n),n.message.includes("400")&&(console.error("[AttorneyProfileManager] 400 Error Details:"),console.error("- Assistant ID:",t.vapi_assistant_id),console.error("- Attorney Data:",JSON.stringify({firm_name:t.firm_name,welcome_message:t.welcome_message,vapi_instructions:t.vapi_instructions,voice_id:t.voice_id,voice_provider:t.voice_provider,ai_model:t.ai_model},null,2))),n.message.includes("MCP server")||n.message.includes("connection"))return console.warn("[AttorneyProfileManager] MCP server not available, returning mock updated assistant"),{id:t.vapi_assistant_id,name:`${t.firm_name} Assistant`,instructions:t.vapi_instructions,firstMessage:t.welcome_message,mock:!0};throw n}}async updateAttorneyInSupabase(t){try{if(!t)throw new Error("No data provided for attorney update");if(!t.id)throw console.error("[AttorneyProfileManager] No ID provided in update data:",t),new Error("Attorney ID is required for Supabase update");if(console.log("[AttorneyProfileManager] Updating attorney in Supabase:",t.id),t.id&&t.id.startsWith("dev-")){console.log("[AttorneyProfileManager] Development ID detected, updating locally only");const c={...this.currentAttorney,...t,updated_at:new Date().toISOString()};return this.currentAttorney=c,this.saveToLocalStorage(c),this.notifyListeners(),c}const n={...t,updated_at:new Date().toISOString()},s=t.id+"_"+n.updated_at;this.pendingUpdates.set(s,n);const{data:i,error:a}=await U.from("attorneys").update(n).eq("id",t.id).select().single();if(a)throw a;return console.log("[AttorneyProfileManager] Updated attorney in Supabase successfully"),i}catch(n){console.error("[AttorneyProfileManager] Error updating attorney in Supabase:",n);const s=t.id+"_"+(t.updated_at||new Date().toISOString());throw this.pendingUpdates.delete(s),n}}saveToLocalStorage(t){if(!t){console.warn("[AttorneyProfileManager] Cannot save undefined attorney to localStorage");return}try{localStorage.setItem("attorney",JSON.stringify(t)),localStorage.setItem("attorney_last_updated",new Date().toISOString()),console.log("[AttorneyProfileManager] Saved attorney to localStorage:",t.id||"unknown")}catch(n){console.error("[AttorneyProfileManager] Error saving attorney to localStorage:",n)}}loadFromLocalStorage(){try{const t=localStorage.getItem("attorney");if(!t)return null;const n=JSON.parse(t);return console.log("[AttorneyProfileManager] Loaded attorney from localStorage:",n.id),n}catch(t){return console.error("[AttorneyProfileManager] Error loading attorney from localStorage:",t),null}}findDiscrepancies(t,n){const s={};return n.name!==`${t.firm_name} Assistant`&&(s.name={current:n.name,expected:`${t.firm_name} Assistant`}),t.vapi_instructions&&n.instructions!==t.vapi_instructions&&(s.instructions={current:n.instructions,expected:t.vapi_instructions}),t.welcome_message&&n.firstMessage!==t.welcome_message&&(s.firstMessage={current:n.firstMessage,expected:t.welcome_message}),t.voice_id&&n.voice&&n.voice.voiceId!==t.voice_id&&(s.voiceId={current:n.voice?n.voice.voiceId:null,expected:t.voice_id}),s}addListener(t){if(this.listeners.add(t),this.currentAttorney)try{t(this.currentAttorney)}catch(n){console.error("[AttorneyProfileManager] Error in initial listener notification:",n)}}removeListener(t){this.listeners.delete(t)}notifyListeners(){for(const t of this.listeners)try{t(this.currentAttorney)}catch(n){console.error("[AttorneyProfileManager] Error in attorney update listener:",n)}}cleanup(){if(this.subscription){try{U.removeSubscription(this.subscription)}catch(t){console.warn("[AttorneyProfileManager] Error removing subscription:",t)}this.subscription=null}if(this.channel){try{U.removeChannel(this.channel)}catch(t){console.warn("[AttorneyProfileManager] Error removing channel:",t)}this.channel=null}this.pollingInterval&&(clearInterval(this.pollingInterval),this.pollingInterval=null),this.listeners.clear(),console.log("[AttorneyProfileManager] Resources cleaned up")}}const X=new Vn;typeof window<"u"&&(window.attorneyProfileManager=X);const Pn=()=>{const{user:o,isAuthenticated:t}=at(),[n,s]=r.useState(X.currentAttorney),[i,a]=r.useState(!n),[c,p]=r.useState(null),[u,h]=r.useState(X.syncStatus),[d,w]=r.useState(X.lastSyncTime);r.useEffect(()=>{t&&o?.id&&(a(!0),p(null),X.initialize(o.id,o.email).then(y=>{a(!1)}).catch(y=>{console.error("[useAttorneyProfile] Error initializing attorney profile:",y),p(y.message),a(!1)}))},[t,o?.id,o?.email]),r.useEffect(()=>{const y=M=>{s(M),h(X.syncStatus),w(X.lastSyncTime)};return X.addListener(y),()=>{X.removeListener(y)}},[]);const f=r.useCallback(async()=>{try{return a(!0),await X.forceSynchronization(),h(X.syncStatus),w(X.lastSyncTime),{success:!0}}catch(y){return console.error("[useAttorneyProfile] Error forcing synchronization:",y),p(y.message),{success:!1,error:y.message}}finally{a(!1)}},[]),I=r.useCallback(async()=>{try{if(!n)throw new Error("No attorney profile loaded");return await X.checkVapiSynchronization(n),h(X.syncStatus),w(X.lastSyncTime),{success:!0}}catch(y){return console.error("[useAttorneyProfile] Error checking sync status:",y),p(y.message),{success:!1,error:y.message}}},[n]),R=r.useCallback(async y=>{try{if(!n)throw new Error("No attorney profile loaded");return a(!0),{success:!0,attorney:await X.updateAttorneyInSupabase({...y,id:n.id})}}catch(M){return console.error("[useAttorneyProfile] Error updating attorney:",M),p(M.message),{success:!1,error:M.message}}finally{a(!1)}},[n]);return{attorney:n,loading:i,error:c,syncStatus:u,lastSyncTime:d,forceSync:f,checkSyncStatus:I,updateAttorney:R}},Mn=()=>{const{attorney:o,loading:t,error:n,syncStatus:s,lastSyncTime:i,forceSync:a,checkSyncStatus:c,updateAttorney:p}=Pn(),[u,h]=r.useState([]),d=(m,E,C)=>{h(P=>[{name:m,success:E,message:C,timestamp:new Date().toISOString()},...P])},w=async()=>{try{const m=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY||localStorage.getItem("vapi_api_key");if(!m){d("Vapi MCP Connection",!1,"No API key found");return}await A.initialize(m);const E=A.getMcpService();if(A.isUsingMock()){d("Vapi MCP Connection",!0,"Connected successfully (using mock service)");return}E.connected&&!E.useDirect?d("Vapi MCP Connection",!0,"Connected successfully"):d("Vapi MCP Connection",!1,"Failed to connect")}catch(m){d("Vapi MCP Connection",!1,`Error: ${m.message}`)}},f=async()=>{try{const m=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY||localStorage.getItem("vapi_api_key");if(!m){d("Vapi Direct API",!1,"No API key found");return}const E=A.getMcpService();if(A.isUsingMock()){d("Vapi Direct API",!0,"Connected successfully (using mock service)");return}await E.connect(m,!0)&&E.useDirect?d("Vapi Direct API",!0,"Connected successfully using direct API"):d("Vapi Direct API",!1,"Failed to connect using direct API")}catch(m){d("Vapi Direct API",!1,`Error: ${m.message}`)}},I=async()=>{try{const m=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY||localStorage.getItem("vapi_api_key");if(!m){d("Vapi Mock Mode",!1,"No API key found");return}if(await A.initialize(m,!0),A.isUsingMock()){d("Vapi Mock Mode",!0,"Mock mode enabled successfully");const E=await A.getMcpService().listAssistants();console.log("Mock assistants:",E);const C=await A.getMcpService().createAssistant({name:"Test Mock Assistant",instructions:"This is a test assistant created in mock mode",firstMessage:"Hello from mock mode!"});console.log("Created mock assistant:",C)}else d("Vapi Mock Mode",!1,"Failed to enable mock mode")}catch(m){d("Vapi Mock Mode",!1,`Error: ${m.message}`)}},R=async()=>{try{const m=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY||localStorage.getItem("vapi_api_key");if(!m){d("Attorney Dashboard Mode",!1,"No API key found");return}await A.initialize(m,!1,{isAttorneyDashboard:!0,isPreview:!1});const E=A.getConnectionStatus(),C=A.getConnectionWarning();C?d("Attorney Dashboard Mode",!1,`Warning: ${C}`):A.isUsingMock()?d("Attorney Dashboard Mode",!0,"Connected in mock mode (fallback)"):d("Attorney Dashboard Mode",!0,`Connected using ${E.connectionMode} mode`);try{const P=await A.getMcpService().listAssistants();console.log("Assistants in attorney dashboard mode:",P),P&&P.length>0?d("List Assistants (Dashboard)",!0,`Found ${P.length} assistants${A.isUsingMock()?" (mock)":""}`):d("List Assistants (Dashboard)",!0,"No assistants found")}catch(P){d("List Assistants (Dashboard)",!1,`Error: ${P.message}`)}}catch(m){d("Attorney Dashboard Mode",!1,`Error: ${m.message}`)}},y=async()=>{try{const E=await A.getMcpService().listAssistants();E&&E.length>0?d("List Assistants",!0,`Found ${E.length} assistants${A.isUsingMock()?" (mock)":""}`):d("List Assistants",!0,"No assistants found")}catch(m){d("List Assistants",!1,`Error: ${m.message}`)}},M=async()=>{try{const m=await a();m.success?d("Force Sync",!0,"Sync successful"):d("Force Sync",!1,`Sync failed: ${m.error}`)}catch(m){d("Force Sync",!1,`Error: ${m.message}`)}},J=async()=>{try{const m=await c();m.success?d("Check Sync Status",!0,"Check successful"):d("Check Sync Status",!1,`Check failed: ${m.error}`)}catch(m){d("Check Sync Status",!1,`Error: ${m.message}`)}},F=async()=>{try{if(!o){d("Update Attorney",!1,"No attorney to update");return}const m=await p({welcome_message:`Hello! I'm Scout from ${o.firm_name}. How can I help you today? (Updated: ${new Date().toISOString()})`});m.success?d("Update Attorney",!0,"Update successful"):d("Update Attorney",!1,`Update failed: ${m.error}`)}catch(m){d("Update Attorney",!1,`Error: ${m.message}`)}},Y=m=>{if(!m)return"Never";const C=new Date-m,P=Math.floor(C/1e3),S=Math.floor(P/60),x=Math.floor(S/60);return P<60?"Just now":S<60?`${S} minute${S===1?"":"s"} ago`:x<24?`${x} hour${x===1?"":"s"} ago`:m.toLocaleString()};return e.jsxs("div",{className:"attorney-profile-test",children:[e.jsx("h2",{children:"Attorney Profile Test"}),e.jsxs("div",{className:"test-section",children:[e.jsx("h3",{children:"Current State"}),e.jsxs("div",{className:"test-info",children:[e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Loading:"}),e.jsx("span",{className:"test-info-value",children:t?"Yes":"No"})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Error:"}),e.jsx("span",{className:"test-info-value",children:n||"None"})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Sync Status:"}),e.jsx("span",{className:`test-info-value ${s?.consistent?"status-ok":"status-error"}`,children:s?.consistent?"Synchronized":"Out of Sync"})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Last Sync:"}),e.jsx("span",{className:"test-info-value",children:i?Y(i):"Never"})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Sync Message:"}),e.jsx("span",{className:"test-info-value",children:s?.message||"None"})]}),s?.warning&&e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Sync Warning:"}),e.jsx("span",{className:"test-info-value warning",children:s.warning})]})]})]}),e.jsxs("div",{className:"test-section",children:[e.jsx("h3",{children:"Vapi Connection Status"}),e.jsxs("div",{className:"test-info",children:[A.isUsingMock()?e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Mode:"}),e.jsx("span",{className:"test-info-value status-ok",children:"Mock Mode (Simulated Vapi)"})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Connected:"}),e.jsx("span",{className:`test-info-value ${A.getMcpService().connected?"status-ok":"status-error"}`,children:A.getMcpService().connected?"Yes":"No"})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Connection Mode:"}),e.jsx("span",{className:"test-info-value",children:A.getMcpService().useDirect?"Direct API":A.getMcpService().connected?"MCP Server":"Not Connected"})]})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"API Key Available:"}),e.jsx("span",{className:`test-info-value ${A.getMcpService().apiKey?"status-ok":"status-error"}`,children:A.getMcpService().apiKey?"Yes":"No"})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Connection Attempts:"}),e.jsx("span",{className:"test-info-value",children:A.getMcpService().connectionAttempts||0})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Last Connection Time:"}),e.jsx("span",{className:"test-info-value",children:A.getMcpService().lastConnectionTime?Y(new Date(A.getMcpService().lastConnectionTime)):"Never"})]}),A.getConnectionWarning()&&e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Warning:"}),e.jsx("span",{className:"test-info-value warning",children:A.getConnectionWarning()})]}),e.jsxs("div",{className:"test-info-item",children:[e.jsx("span",{className:"test-info-label",children:"Context:"}),e.jsx("span",{className:"test-info-value",children:A.isAttorneyDashboard?"Attorney Dashboard":A.isPreview?"Preview Mode":"Regular Mode"})]})]})]}),e.jsxs("div",{className:"test-section",children:[e.jsx("h3",{children:"Attorney Profile"}),o?e.jsxs("div",{className:"attorney-info",children:[e.jsxs("div",{className:"attorney-info-item",children:[e.jsx("span",{className:"attorney-info-label",children:"ID:"}),e.jsx("span",{className:"attorney-info-value",children:o.id})]}),e.jsxs("div",{className:"attorney-info-item",children:[e.jsx("span",{className:"attorney-info-label",children:"Firm Name:"}),e.jsx("span",{className:"attorney-info-value",children:o.firm_name})]}),e.jsxs("div",{className:"attorney-info-item",children:[e.jsx("span",{className:"attorney-info-label",children:"Vapi Assistant ID:"}),e.jsx("span",{className:"attorney-info-value",children:o.vapi_assistant_id||"None"})]}),e.jsxs("div",{className:"attorney-info-item",children:[e.jsx("span",{className:"attorney-info-label",children:"Welcome Message:"}),e.jsx("span",{className:"attorney-info-value",children:o.welcome_message||"None"})]})]}):e.jsx("div",{className:"no-attorney",children:"No attorney profile loaded"})]}),e.jsxs("div",{className:"test-section",children:[e.jsx("h3",{children:"Test Actions"}),e.jsxs("div",{className:"test-buttons",children:[e.jsx("button",{onClick:w,disabled:t,children:"Test Vapi MCP Connection"}),e.jsx("button",{onClick:f,disabled:t,children:"Test Vapi Direct API"}),e.jsx("button",{onClick:I,disabled:t,children:"Test Vapi Mock Mode"}),e.jsx("button",{onClick:R,disabled:t,children:"Test Attorney Dashboard Mode"}),e.jsx("button",{onClick:y,disabled:t,children:"Test List Assistants"}),e.jsx("button",{onClick:M,disabled:t,children:"Test Force Sync"}),e.jsx("button",{onClick:J,disabled:t,children:"Test Check Sync Status"}),e.jsx("button",{onClick:F,disabled:t,children:"Test Update Attorney"})]})]}),e.jsxs("div",{className:"test-section",children:[e.jsx("h3",{children:"Test Results"}),u.length>0?e.jsx("div",{className:"test-results",children:u.map((m,E)=>e.jsxs("div",{className:`test-result ${m.success?"success":"failure"}`,children:[e.jsxs("div",{className:"test-result-header",children:[e.jsx("span",{className:"test-result-name",children:m.name}),e.jsx("span",{className:"test-result-status",children:m.success?"Success":"Failure"})]}),e.jsx("div",{className:"test-result-message",children:m.message}),e.jsx("div",{className:"test-result-timestamp",children:new Date(m.timestamp).toLocaleTimeString()})]},E))}):e.jsx("div",{className:"no-results",children:"No test results yet"})]}),e.jsx("style",{jsx:!0,children:`
        .attorney-profile-test {
          padding: 20px;
          max-width: 800px;
          margin: 0 auto;
        }

        .test-section {
          margin-bottom: 20px;
          padding: 15px;
          border: 1px solid #ddd;
          border-radius: 5px;
          background-color: #f9f9f9;
        }

        .test-section h3 {
          margin-top: 0;
          margin-bottom: 15px;
          border-bottom: 1px solid #eee;
          padding-bottom: 10px;
        }

        .test-info-item, .attorney-info-item {
          margin-bottom: 8px;
          display: flex;
        }

        .test-info-label, .attorney-info-label {
          font-weight: bold;
          width: 150px;
          flex-shrink: 0;
        }

        .status-ok {
          color: green;
        }

        .status-error {
          color: red;
        }

        .warning {
          color: orange;
        }

        .test-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
        }

        .test-buttons button {
          padding: 8px 16px;
          background-color: #4B74AA;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
        }

        .test-buttons button:hover {
          background-color: #3A5A8C;
        }

        .test-buttons button:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }

        .test-results {
          display: flex;
          flex-direction: column;
          gap: 10px;
        }

        .test-result {
          padding: 10px;
          border-radius: 4px;
        }

        .test-result.success {
          background-color: #d4edda;
          border: 1px solid #c3e6cb;
        }

        .test-result.failure {
          background-color: #f8d7da;
          border: 1px solid #f5c6cb;
        }

        .test-result-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 5px;
        }

        .test-result-name {
          font-weight: bold;
        }

        .test-result-status {
          font-weight: bold;
        }

        .test-result-message {
          margin-bottom: 5px;
        }

        .test-result-timestamp {
          font-size: 0.8em;
          color: #666;
        }

        .no-attorney, .no-results {
          font-style: italic;
          color: #666;
        }
      `})]})},Q=Yt("App"),Tn=r.lazy(()=>ge(()=>import("./pages-26099600.js").then(o=>o.U),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"])),Rn=r.lazy(()=>{try{return ge(()=>import("./pages-26099600.js").then(o=>o.W),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"])}catch(o){return console.error("Error loading SimpleDemoPage:",o),Promise.resolve({default:Dt})}}),jn=r.lazy(()=>ge(()=>import("./pages-26099600.js").then(o=>o.X),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"])),tt=({isLoading:o,callActive:t,showAttorneyInfo:n,showCallSummary:s,attorneyProfile:i,startCall:a,endCall:c,callData:p,subdomain:u,setShowAttorneyInfo:h,setShowCallSummary:d,buttonText:w,isAttorneySubdomain:f,hideCreateAgentButton:I=!1,isDarkTheme:R,vapiCallKey:y})=>{const[M,J]=r.useState(!1),[F,Y]=r.useState(null),m=r.useRef(null),[E,C]=r.useState(null);r.useEffect(()=>{f&&i&&!o&&i.firmName&&u&&u!=="default"&&ge(()=>import("./pages-26099600.js").then(x=>x.Q),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"]).then(x=>{C(x)})},[f,i,o,u]);const P=()=>{if(m.current){const x=m.current.getBoundingClientRect();Y({top:x.top,left:x.left,width:x.width,height:x.height})}J(!0),Q.log("Call transition started",{timestamp:new Date().toISOString()}),ke("call_transition_started")},S=()=>{J(!1),setTimeout(()=>{a()},100)};if(console.log("🔍 [App.jsx] Condition check:",{isAttorneySubdomain:f,hasAttorneyProfile:!!i,isLoading:o,firmName:i?.firmName,firm_name:i?.firm_name,subdomain:u,subdomainNotDefault:u!=="default"}),f&&i&&!o&&(i.firmName||i.firm_name)&&u&&u!=="default"){if(console.log("🎯 [App.jsx] Condition met! Rendering iframe for subdomain:",u),pe.lazy(()=>ge(()=>import("./pages-26099600.js").then(W=>W.R),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"])),!E)return e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",backgroundColor:"#f5f7fa"},children:[e.jsx("div",{style:{width:"40px",height:"40px",border:"4px solid #f3f3f3",borderTop:"4px solid #4B9CD3",borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("p",{style:{marginTop:"20px",color:"#666"},children:"Loading attorney profile..."}),e.jsx("style",{children:`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `})]});const x=`/simple-preview?subdomain=${encodeURIComponent(u)}&theme=${R?"dark":"light"}&useEnhancedPreview=true`;return e.jsx("div",{className:"attorney-subdomain-page",style:{width:"100%",height:"100vh",margin:0,padding:0},children:e.jsx("iframe",{ref:W=>{if(W){const ee=async te=>{if(te.data&&te.data.type==="PREVIEW_READY"){console.log("🎯 [App.jsx] Received PREVIEW_READY from subdomain iframe, sending config...");try{if(!W.contentWindow)return;const{previewConfig:j}=await ge(()=>import("./pages-26099600.js").then(T=>T.P),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"]).then(T=>T.createAttorneyPreviewConfig(i));j.theme=R?"dark":"light",j.useEnhancedPreview=!0,i.vapi_assistant_id&&(j.vapi_assistant_id=i.vapi_assistant_id,j.vapiAssistantId=i.vapi_assistant_id),W.contentWindow.postMessage({type:"UPDATE_PREVIEW_CONFIG",config:j},"*"),console.log("🎯 [App.jsx] Sent complete config to subdomain iframe via postMessage:",j)}catch(j){console.error("🚨 [App.jsx] Error sending config via postMessage:",j)}window.removeEventListener("message",ee)}};window.addEventListener("message",ee),W.onload=()=>{console.log("🎯 [App.jsx] Subdomain iframe loaded, waiting for PREVIEW_READY message...")}}},src:x,style:{width:"100%",height:"100%",border:"none",margin:0,padding:0},title:`${i.firm_name||"Attorney"} Preview`})})}return e.jsx(e.Fragment,{children:o?e.jsx("div",{className:"loading-indicator",children:"Loading..."}):e.jsxs(e.Fragment,{children:[!t&&!n&&!s&&e.jsx("div",{className:"start-button-container",ref:m,children:e.jsx(ot,{onClick:P,label:"Get Started",mascot:"/PRIMARY CLEAR.png",isLoading:!1})}),e.jsx(on,{isActive:M,onTransitionComplete:S,buttonPosition:F,mascotUrl:"/PRIMARY CLEAR.png"}),t&&e.jsx("div",{className:`call-card-container ${t?"active":""}`,children:e.jsx("div",{className:"call-card",children:e.jsx(it,{onEndCall:c,subdomain:u,assistantId:Xt,forceDefaultAssistant:!0,initializationDelay:1200,showDebugPanel:!1},y)})}),e.jsx(Dn,{showAttorneyInfo:n,callData:p,showCallSummary:s,setShowAttorneyInfo:h,setShowCallSummary:d})]})})},Dn=({showAttorneyInfo:o,callData:t,showCallSummary:n,setShowAttorneyInfo:s,setShowCallSummary:i})=>e.jsxs(e.Fragment,{children:[o&&t&&e.jsxs("div",{className:"attorney-info-container",children:[e.jsxs("div",{className:"map-dossier-container",children:[e.jsx(pn,{attorney:t.attorney}),e.jsx(mn,{attorney:t.attorney})]}),e.jsx("button",{className:"back-button",onClick:()=>s(!1),children:"Back to Start"})]}),n&&t&&e.jsxs("div",{className:"call-summary-container",children:[e.jsx(fn,{data:t.summary}),e.jsx("button",{className:"back-button",onClick:()=>i(!1),children:"Back to Start"})]})]});function Ln(){const o=st(),{user:t}=at(),[n,s]=r.useState(!1),[i,a]=r.useState(!1),[c,p]=r.useState(!1),[u,h]=r.useState(null),[d,w]=r.useState(null),[f,I]=r.useState(!1),[R,y]=r.useState(null),[M,J]=r.useState(!1),[F,Y]=r.useState(!0),[m,E]=r.useState(["default"]),[C,P]=r.useState(!0);r.useState(!1);const[S,x]=r.useState(""),[W,ee]=r.useState(!1),[te,j]=r.useState("firm"),[T,B]=r.useState(!1),[ie,se]=r.useState("url"),[K,le]=r.useState("Smith & Associates, LLP"),[oe,ne]=r.useState(""),[G,O]=r.useState(""),[$,de]=r.useState("#2c3e50"),[k,z]=r.useState("#3498db"),[q,ae]=r.useState("#3498db"),[v,_]=r.useState("#f0f4f8"),[re,Ae]=r.useState(.3),[me,je]=r.useState("Start Consultation"),[ze,lt]=r.useState(1),[dt,ut]=r.useState(600),[Fe,De]=r.useState(`**Welcome to our legal practice**

Our team of experienced attorneys is dedicated to providing you with exceptional legal representation. We combine expertise with a client-focused approach to help you navigate complex legal challenges.

### How we can help:
- Personalized legal solutions
- Clear communication throughout your case
- Decades of combined experience`),[Be,Se]=r.useState("Hello, I'm an AI assistant from Smith & Associates. How can I help you today?"),[Je,Pe]=r.useState("To better assist you, I'll need a few details about your situation."),[Ye,Xe]=r.useState("John Smith"),[He,pt]=r.useState(.2),[Ke,gt]=r.useState("#634C38"),[Me,we]=r.useState(""),[mt,Ge]=r.useState(!1),ht=r.useRef(null),he={"Personal Injury":{questions:"I want to know the circumstances of their injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?",practiceDescription:`**Our firm specializes in personal injury law**, and we have a proven track record of success in obtaining favorable settlements and verdicts for our clients.

### Our Services:
- Car accident claims
- Slip and fall cases
- Medical malpractice
- Workplace injuries

We work on a contingency basis - *you don't pay unless we win*.`,welcomeMessage:"Welcome to our personal injury consultation. I'm your virtual legal assistant and I'm here to help gather information about your case.",informationGathering:"I want to know the circumstances of your injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?"},"Family Law":{questions:"I need to understand the nature of their family law issue. Are they seeking a divorce, child custody, child support, alimony, or a modification of an existing order?",practiceDescription:`**Our firm is dedicated to helping families** navigate the complexities of family law. We handle all aspects of divorce, including property division, child custody and visitation.

### Practice Areas:
- Divorce proceedings
- Child custody & support
- Spousal support/alimony
- Prenuptial agreements

> We approach each case with compassion and understanding during difficult times.`,welcomeMessage:"Welcome to our family law consultation. I'm your virtual legal assistant and I'm here to help with your family law matters.",informationGathering:"I need to understand the nature of your family law issue. Are you seeking a divorce, child custody, child support, alimony, or a modification of an existing order?"},"Criminal Defense":{questions:"I need to know the charges against the client, where they are in the criminal process, and if there are any deadlines or court dates already scheduled.",practiceDescription:`## Criminal Defense Experts

Our **experienced criminal defense attorneys** provide aggressive representation for all criminal matters, from misdemeanors to serious felony charges.

### Our Approach:
1. Thorough case evaluation
2. Strategic defense planning
3. Aggressive courtroom advocacy
4. Pursuit of best possible outcomes

[Contact us](#) immediately if you've been charged with a crime.`,welcomeMessage:"Thank you for considering our firm for your criminal defense needs. I'm here to gather some initial information about your case.",informationGathering:"Please tell me about the charges you're facing and where you are in the legal process. All information is confidential and protected by attorney-client privilege."}},ft=g=>{const b=g.target.value;x(b),b&&he[b]&&(Se(he[b].welcomeMessage),Pe(he[b].informationGathering),De(he[b].practiceDescription))},It=g=>{const b=g.target.files[0];if(b){const D=new FileReader;D.onloadend=()=>{const l=Ht(D.result);console.log("Stored image with ID:",l),ne(l)},D.readAsDataURL(b)}},yt=()=>{ne("")},At=()=>{ee(!0)},Ee=async g=>{g&&g.preventDefault&&g.preventDefault();let b=Me;if(g&&g.detail&&g.detail.url&&(console.log("Using URL from event:",g.detail.url),b=g.detail.url,we(b)),!b){const D=localStorage.getItem("pendingUrlAutoConfig");D&&(console.log("Using URL from localStorage:",D),b=D,we(b))}if(!b){alert("Please enter a URL");return}console.log("Processing URL:",b),Ge(!0),xe.info("Auto-configuring from website...",{position:"top-center",autoClose:!1,hideProgressBar:!1,closeOnClick:!1,pauseOnHover:!0,draggable:!1,progress:void 0,toastId:"auto-configure-toast"});try{const{scrapeWebsite:D}=await ge(()=>import("./websiteScraper-71ea97cc.js"),[]),l=await D(Me);if(console.log("Extracted website data:",l),l&&(le(l.firmName||"Your Law Firm"),Xe(l.attorneyName||""),l.logo&&l.logo.url&&ne(l.logo.url),l.colors&&(de(l.colors.primary||"#4B74AA"),z(l.colors.secondary||"#2C3E50"),ae(l.colors.accent||l.colors.secondary||"#3498db"),_(l.colors.background||"#1a1a1a")),De(l.contentAnalysis?.services?.join(", ")||"Specializing in various legal matters with a client-focused approach."),Se(l.welcomeMessage||"Hello, I'm an AI assistant from "+l.firmName+". How can I help you today?"),Pe(l.informationGathering||"To better assist you, I'll need a few details about your situation."),je(l.buttonText||"Start Consultation"),l.address&&l.address.state&&O(l.address.state),l.practiceAreas&&l.practiceAreas.length>0)){const N={"personal injury":"Personal Injury",accident:"Personal Injury",family:"Family Law",divorce:"Family Law",criminal:"Criminal Defense",defense:"Criminal Defense"};for(const Z of l.practiceAreas){const V=Z.toLowerCase();for(const[ce,ve]of Object.entries(N))if(V.includes(ce)&&he[ve]){x(ve),l.welcomeMessage||Se(he[ve].welcomeMessage),l.informationGathering||Pe(he[ve].informationGathering);break}}}}catch(D){console.error("Error scraping website:",D);const N=Me.replace(/^https?:\/\//,"").replace(/^www\./,"").split("/")[0].split(/[.-]/).map(Z=>Z.charAt(0).toUpperCase()+Z.slice(1)).join(" ")+" Law";le(N),Se("Hello, I'm an AI assistant from "+N+". How can I help you today?"),xe.dismiss("auto-configure-toast"),xe.warning("Could not fully analyze the website. Basic information has been extracted.",{position:"top-center",autoClose:5e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0}),se("manual")}finally{Ge(!1),setTimeout(()=>{xe.dismiss("auto-configure-toast"),ie==="url"&&(xe.success("Website auto-configuration complete!",{position:"top-center",autoClose:3e3,hideProgressBar:!1,closeOnClick:!0,pauseOnHover:!0,draggable:!0}),se("manual"))},1e3)}};r.useEffect(()=>{document.documentElement.setAttribute("data-theme",C?"dark":"light"),C?document.body.classList.add("dark-theme"):document.body.classList.remove("dark-theme")},[C]);const wt=()=>{P(g=>!g)};r.useEffect(()=>{Q.log("App component mounted");const g=localStorage.getItem("pendingUrlAutoConfig"),b=localStorage.getItem("urlAutoConfigTimestamp");if(g&&b){const V=Date.now(),ce=parseInt(b,10);!isNaN(ce)&&V-ce<5e3?(console.log("Found pending URL auto-configuration:",g),we(g),localStorage.removeItem("pendingUrlAutoConfig"),localStorage.removeItem("urlAutoConfigTimestamp"),setTimeout(()=>{Ee()},500)):(localStorage.removeItem("pendingUrlAutoConfig"),localStorage.removeItem("urlAutoConfigTimestamp"))}Sn(),Cn(),rt().then(V=>{V.success?(console.log("Supabase is properly configured and connected!",V.data),V.usingFallback&&console.warn("Using fallback Supabase configuration. Update your .env.development file for a better development experience.")):(console.warn("Supabase connection failed:",V.error),V.useMockData&&(console.info("The application will use mock data for development"),console.info("This is fine for local development, but you should fix the Supabase connection for production"),window.USING_MOCK_DATA=!0))}),window.__REACT_DEVTOOLS_GLOBAL_HOOK__&&(window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout={});const D=window.location.hostname==="localhost"||window.location.hostname==="127.0.0.1";J(D);const l=_e();Q.log("Subdomain detected:",l),w(l);const N=Kt(l);I(N),Q.log("Is attorney subdomain:",N,"Subdomain value:",l),N||y(null);const Z=async()=>{Y(!0),console.log("🚀 [App.jsx] Starting attorney profile load for subdomain:",l),Q.log("🚀 Starting attorney profile load for subdomain:",l);try{if(console.log("📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain:",l),Q.log("📞 Calling getAttorneyConfigAsync with subdomain:",l),typeof Ce!="function")throw new Error("getAttorneyConfigAsync is not a function");const V=await Ce(l);console.log("✅ [App.jsx] Attorney profile loaded successfully:",{hasProfile:!!V,firmName:V?.firmName,id:V?.id,subdomain:V?.subdomain,isFallback:V?.isFallback}),Q.log("✅ Attorney profile loaded successfully:",{hasProfile:!!V,firmName:V?.firmName,id:V?.id,subdomain:V?.subdomain,isFallback:V?.isFallback}),y(V),!V||!V.firmName?(console.log("⚠️ [App.jsx] No valid profile found, setting isAttorneySubdomain to false"),Q.log("⚠️ No valid profile found, setting isAttorneySubdomain to false"),I(!1)):(console.log("✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true"),Q.log("✅ Valid profile found, keeping isAttorneySubdomain as true"))}catch(V){console.error("❌ [App.jsx] Error loading attorney profile:",{message:V.message,stack:V.stack,subdomain:l,functionExists:typeof Ce=="function"}),I(!1),y(null)}finally{Y(!1),console.log("🏁 [App.jsx] Attorney profile loading complete"),Q.log("🏁 Attorney profile loading complete")}};return N?(console.log("🔍 [App.jsx] This is an attorney subdomain, loading profile"),Z()):(console.log("🏠 [App.jsx] This is not an attorney subdomain, skipping profile load"),Y(!1)),()=>{Q.log("App component unmounted")}},[]),r.useEffect(()=>{if(!M)return;(async()=>{try{const{supabase:b}=await ge(()=>import("./pages-26099600.js").then(Z=>Z.O),["assets/pages-26099600.js","assets/vendor-94dbccd3.js","assets/vendor-react-5a73fa04.js","assets/css/vendor-react-72929085.css","assets/css/vendor-4d8e306f.css","assets/css/pages-942cbb16.css"]),{data:D,error:l}=await b.from("attorneys").select("subdomain").not("subdomain","is",null);if(l){console.warn("Error fetching attorney subdomains:",l),E(["default"]);return}const N=D.map(Z=>Z.subdomain).filter(Boolean);Q.log("Available subdomains for testing:",N),E(["default",...N])}catch(b){console.warn("Error setting up subdomain testing, using default only:",b),E(["default"])}})()},[M]);const[Et,$e]=r.useState(!1),Ze=()=>{if(Q.log("Call started",{timestamp:new Date().toISOString()}),ke("call_started"),Et){console.log("[App.jsx] Already in the process of starting a call, ignoring duplicate startCall");return}$e(!0),a(!1),p(!1),window.vapiCallActive=!1;try{const g=document.querySelectorAll('iframe[title*="daily"]');g.length>0&&(console.log(`[App] Found ${g.length} lingering Daily.co iframes. Removing before starting new call...`),g.forEach(b=>{b.parentNode.removeChild(b)}))}catch(g){console.warn("[App] Error cleaning up iframes before starting call:",g)}Ct(g=>g+1),console.log("[App] Starting call - setting callActive to true"),s(!0),setTimeout(()=>{console.log("[App] Forcing call-card-container to be visible");const g=document.querySelector(".call-card-container");g&&(g.classList.add("active"),g.style.display="flex",g.style.visibility="visible",g.style.opacity="1",g.style.zIndex="1000"),setTimeout(()=>{$e(!1)},3e3)},500)},vt=()=>{console.log("handleGetStarted called in App.jsx"),B(!0)};r.useEffect(()=>{window.handleGetStarted=vt,window.handleUrlSubmit=Ee,window.setShowAuthOverlay=B;const g=l=>{console.log("urlAutoConfig event received:",l.detail),l.detail&&l.detail.url&&(we(l.detail.url),Ee({detail:{url:l.detail.url}}))},b=l=>{console.log("autoConfigureClicked event received:",l.detail),l.detail&&l.detail.url&&(we(l.detail.url),Ee({detail:{url:l.detail.url}}))},D=l=>{if(l.target&&(l.target.textContent.includes("Auto-Configure")||l.target.parentElement&&l.target.parentElement.textContent.includes("Auto-Configure"))){console.log("Auto-Configure button click detected");const N=document.getElementById("firmUrl");N&&N.value&&(console.log("Found URL input with value:",N.value),we(N.value),Ee({detail:{url:N.value}}))}};return document.addEventListener("urlAutoConfig",g),document.addEventListener("autoConfigureClicked",b),document.addEventListener("click",D,!0),()=>{delete window.handleGetStarted,delete window.handleUrlSubmit,delete window.setShowAuthOverlay,document.removeEventListener("urlAutoConfig",g),document.removeEventListener("autoConfigureClicked",b),document.removeEventListener("click",D,!0)}},[Ee]);const bt=g=>{console.log("Attorney account created:",g),g&&g.id&&g.id.toString().startsWith("dev-")&&(console.log("Development mode detected. Redirecting to dashboard..."),B(!1)),window.location.href="/dashboard"},[_t,Te]=r.useState(!1),[Qe,Ct]=r.useState(0),We=g=>{if(Q.log("Call ended",{timestamp:new Date().toISOString(),data:g}),ke("call_ended"),_t){console.log("[App.jsx] Already in the process of ending a call, ignoring duplicate endCall");return}Te(!0);const b=window.vapiCallActive===!0,D=g&&g.forcedWhileSpeaking;try{const l=document.querySelectorAll('iframe[title*="daily"]');l.length>0&&(console.log(`[App.jsx] Found ${l.length} lingering Daily.co iframes. Removing during endCall...`),l.forEach(N=>{N.parentNode.removeChild(N)}))}catch(l){console.warn("[App.jsx] Error cleaning up iframes during endCall:",l)}if(!g||Object.keys(g).length===0&&g.constructor===Object){console.log("[App.jsx] Received empty data in endCall, likely during initialization - resetting state"),s(!1),window.vapiCallActive=!1,setTimeout(()=>{Te(!1)},500);return}if(b||D){console.log("[App.jsx] Call is still active or ended while assistant was speaking - keeping interface visible"),console.log("[App.jsx] window.vapiCallActive =",window.vapiCallActive);const l=document.querySelector(".call-card-container");l&&(l.classList.add("active"),l.style.display="flex",l.style.visibility="visible",l.style.opacity="1",l.style.zIndex="1000",console.log("[App.jsx] Forced call-card-container to be visible during active call"));let N=0;const Z=10,V=setInterval(()=>{if(N++,console.log(`[App.jsx] Check ${N}/${Z} for call active status: ${window.vapiCallActive}`),window.vapiCallActive===!1||N>=Z){console.log("[App.jsx] Call is no longer active or max checks reached, cleaning up"),clearInterval(V),N>=Z&&(console.log("[App.jsx] Max checks reached, forcing window.vapiCallActive to false"),window.vapiCallActive=!1),s(!1),g&&g.attorney?(console.log("[App.jsx] Showing attorney info"),a(!0),p(!1),h(g)):g&&g.summary?(console.log("[App.jsx] Showing call summary"),a(!1),p(!0),h(g)):(console.log("[App.jsx] No attorney or summary data, returning to home state"),a(!1),p(!1));try{const ce=document.querySelectorAll('iframe[title*="daily"]');ce.length>0&&(console.log(`[App.jsx] Found ${ce.length} lingering Daily.co iframes after interval. Removing...`),ce.forEach(ve=>{ve.parentNode.removeChild(ve)}))}catch(ce){console.warn("[App.jsx] Error cleaning up iframes after interval:",ce)}Te(!1)}},1e3);return}s(!1),console.log("[App.jsx] Call ended with data:",g),g&&g.attorney?(console.log("[App.jsx] Showing attorney info"),a(!0),p(!1),h(g)):g&&g.summary?(console.log("[App.jsx] Showing call summary"),a(!1),p(!0),h(g)):(console.log("[App.jsx] No attorney or summary data, returning to home state"),a(!1),p(!1)),setTimeout(()=>{console.log("[App.jsx] Forcing re-render after call end"),s(l=>l===!0?!1:l),window.vapiCallActive=!1,console.log("[App.jsx] Double-checked window.vapiCallActive is false at end of endCall");try{const l=document.querySelectorAll('iframe[title*="daily"]');l.length>0&&(console.log(`[App.jsx] Found ${l.length} lingering Daily.co iframes in final cleanup. Removing...`),l.forEach(N=>{N.parentNode.removeChild(N)}))}catch(l){console.warn("[App.jsx] Error cleaning up iframes in final cleanup:",l)}Te(!1)},100)};return r.useEffect(()=>{oe&&console.log("[App.jsx] Current logoUrl:",oe)},[oe]),e.jsxs("div",{className:"app-wrapper",children:[e.jsx(yn,{}),e.jsx(xt,{position:"top-center",autoClose:5e3,hideProgressBar:!1,newestOnTop:!0,closeOnClick:!0,rtl:!1,pauseOnFocusLoss:!0,draggable:!0,pauseOnHover:!0,theme:C?"dark":"light"}),o.pathname!=="/dashboard"&&!f&&e.jsxs("header",{className:"header",children:[e.jsx("div",{className:"logo-container",children:e.jsx(Ve,{to:"/","aria-label":"Go to home page",children:e.jsx("img",{src:"/nav_logo.webp",alt:"LegalScout Logo",className:"logo"})})}),e.jsx(In,{isDarkTheme:C}),o.pathname==="/demo"&&e.jsx(wn,{onClick:()=>B(!0)}),e.jsx(An,{isDark:C,onToggle:wt})]}),e.jsx("main",{className:"main-content-layer",children:e.jsxs(Nt,{children:[e.jsx(L,{path:"/",element:d===null||F?e.jsxs("div",{className:"loading-container",style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",color:"#fff"},children:[e.jsx("div",{className:"loading-spinner",style:{border:"4px solid #f3f3f3",borderTop:"4px solid #3498db",borderRadius:"50%",width:"40px",height:"40px",animation:"spin 2s linear infinite",marginBottom:"20px"}}),e.jsx("p",{children:"Loading..."})]}):f?e.jsx(tt,{isLoading:F,callActive:n,showAttorneyInfo:i,showCallSummary:c,attorneyProfile:R,startCall:Ze,endCall:We,callData:u,subdomain:d,setShowAttorneyInfo:a,setShowCallSummary:p,buttonText:me,isAttorneySubdomain:f,hideCreateAgentButton:!0,isDarkTheme:C,vapiCallKey:Qe}):t?e.jsx(qe,{to:"/dashboard",replace:!0}):e.jsx(qe,{to:"/home",replace:!0})}),e.jsx(L,{path:"/home",element:e.jsx(tt,{isLoading:F,callActive:n,showAttorneyInfo:i,showCallSummary:c,attorneyProfile:R,startCall:Ze,endCall:We,callData:u,subdomain:d,setShowAttorneyInfo:a,setShowCallSummary:p,buttonText:me,isAttorneySubdomain:f,hideCreateAgentButton:!0,isDarkTheme:C,vapiCallKey:Qe})}),e.jsx(L,{path:"/about",element:e.jsx(Lt,{})}),e.jsx(L,{path:"/contact",element:e.jsx("div",{children:"Contact Page Coming Soon"})}),e.jsx(L,{path:"/auth/callback",element:e.jsx(kt,{})}),e.jsx(L,{path:"/login",element:e.jsx(Ut,{})}),e.jsx(L,{path:"/test",element:e.jsx(xn,{})}),e.jsx(L,{path:"/subdomain-test",element:e.jsx(Ot,{})}),e.jsx(L,{path:"/attorney-profile-test",element:e.jsx(Mn,{})}),e.jsx(L,{path:"/complete-profile",element:e.jsx(zt,{})}),e.jsx(L,{path:"/dashboard",element:e.jsx(Ft,{})}),e.jsx(L,{path:"/crm-demo",element:e.jsx(Bt,{})}),e.jsx(L,{path:"/call-control",element:e.jsx(r.Suspense,{fallback:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading call control interface..."})]}),children:e.jsx(jn,{})})}),e.jsx(L,{path:"/demo",element:e.jsx(r.Suspense,{fallback:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading demo interface..."})]}),children:e.jsx(Rn,{firmName:K,logoUrl:oe,state:G,primaryColor:$,secondaryColor:k,buttonColor:q,setButtonColor:ae,backgroundColor:v,backgroundOpacity:re,welcomeMessage:Be,informationGathering:Je,practiceDescription:Fe,previewHeight:dt,setPreviewHeight:ut,attorneyName:Ye,selectedPracticeArea:S,handlePracticeAreaChange:ft,showPreview:W,setShowPreview:ee,handleLogoUpload:It,handleRemoveLogo:yt,practiceAreas:he,activeConfigTab:te,setActiveConfigTab:j,buttonText:me,setButtonText:je,buttonOpacity:ze,setButtonOpacity:lt,practiceAreaBackgroundOpacity:He,setPracticeAreaBackgroundOpacity:pt,textBackgroundColor:Ke,setTextBackgroundColor:gt,goToPreview:At,setFirmName:le,setAttorneyName:Xe,setPracticeDescription:De,setState:O,setWelcomeMessage:Se,setInformationGathering:Pe,setPrimaryColor:de,setSecondaryColor:z,setBackgroundColor:_,setBackgroundOpacity:Ae,iframeRef:ht,firmUrl:Me,setFirmUrl:we,isLoading:mt,handleUrlSubmit:Ee,isDarkTheme:C,handleGetStarted:()=>B(!0)})})}),e.jsx(L,{path:"/demo/preview",element:e.jsx(r.Suspense,{fallback:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading preview..."})]}),children:e.jsx(Tn,{firmName:K,attorneyName:Ye,darkMode:C,onToggleDarkMode:()=>P(!C)})})}),e.jsx(L,{path:"/preview",element:e.jsx(r.Suspense,{fallback:e.jsxs("div",{className:"loading-container",children:[e.jsx("div",{className:"loading-spinner"}),e.jsx("p",{children:"Loading preview..."})]}),children:e.jsx(En,{firmName:K,primaryColor:$,secondaryColor:k,buttonColor:q,backgroundColor:v,backgroundOpacity:re,practiceDescription:Fe,welcomeMessage:Be,informationGathering:Je,theme:C?"dark":"light",logoUrl:oe||"/PRIMARY CLEAR.png",buttonText:me||"Start Consultation",buttonOpacity:ze,practiceAreaBackgroundOpacity:He,textBackgroundColor:Ke,mascot:"/PRIMARY CLEAR.png",vapiInstructions:"You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney."})})}),e.jsx(L,{path:"/simple-preview",element:e.jsx(Jt,{})}),e.jsx(L,{path:"/preview-frame",element:e.jsx(vn,{})}),e.jsx(L,{path:"/preview-frame-test",element:e.jsxs("div",{style:{padding:"20px",display:"flex",flexDirection:"column",alignItems:"center",gap:"20px"},children:[e.jsx("h1",{children:"Simple Preview Test"}),e.jsx("p",{children:"This page is for testing the simple preview route directly."}),e.jsx("div",{style:{width:"100%",maxWidth:"800px",height:"600px",border:"2px solid #4B74AA",borderRadius:"8px",overflow:"hidden"},children:e.jsx("iframe",{src:"/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true",title:"Simple Preview Test",style:{width:"100%",height:"100%",border:"none"}})}),e.jsxs("div",{children:[e.jsx("h2",{children:"Troubleshooting"}),e.jsxs("ul",{children:[e.jsx("li",{children:"If the preview appears here but not in the dashboard, there's likely a CSS or layout issue in the dashboard."}),e.jsx("li",{children:"If the preview doesn't appear here either, there might be an issue with the preview component itself."}),e.jsx("li",{children:"This test uses the same simple-preview route that all other previews now use."})]})]})]})}),e.jsx(L,{path:"/test-route",element:e.jsxs("div",{style:{padding:"20px",textAlign:"center"},children:[e.jsx("h1",{children:"Test Route Works!"}),e.jsx("p",{children:"If you can see this, routing is working correctly."})]})})]})}),e.jsx(_n,{isOpen:T,onClose:()=>B(!1),onSuccess:bt}),e.jsx(bn,{onActivated:g=>{console.log("[App] Assistant activated:",g),g&&g.id&&y(g)}})]})}const kn=Re(Ln,{displayName:"LegalScoutApp",type:"container",description:"Main application container that manages call state",responsibleFor:["call initiation","layout management","state control"]});class Un extends pe.Component{constructor(t){super(t),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(t){return{hasError:!0}}componentDidCatch(t,n){console.error("Error caught by ErrorBoundary:",t,n),this.setState({error:t,errorInfo:n})}render(){return this.state.hasError?e.jsxs("div",{className:"error-boundary-fallback",children:[e.jsx("h2",{children:"Something went wrong."}),this.props.showDetails&&e.jsxs("details",{style:{whiteSpace:"pre-wrap"},children:[e.jsx("summary",{children:"Error Details"}),e.jsx("p",{children:this.state.error&&this.state.error.toString()}),e.jsxs("p",{children:["Component Stack: ",this.state.errorInfo&&this.state.errorInfo.componentStack]})]}),this.props.onReset&&e.jsx("button",{onClick:()=>{this.setState({hasError:!1}),this.props.onReset()},children:"Try Again"})]}):this.props.children}}class On extends pe.Component{constructor(t){super(t),this.state={hasError:!1,error:null,errorInfo:null},this.fixFramerMotionIssues()}fixFramerMotionIssues(){try{typeof window<"u"&&(typeof window.React>"u"&&(console.log("[ErrorBoundary] Creating React placeholder"),window.React={}),typeof window.React.createContext>"u"&&(console.log("[ErrorBoundary] Adding createContext placeholder"),window.React.createContext=function(){return{Provider:function(){return null},Consumer:function(){return null}}}),["useState","useEffect","useLayoutEffect","useRef","useCallback","useMemo","useContext","forwardRef","createElement","cloneElement","createRef","Component","PureComponent","Fragment","Children","isValidElement"].forEach(n=>{typeof window.React[n]>"u"&&(console.log(`[ErrorBoundary] Adding ${n} placeholder`),window.React[n]=function(){return arguments[0]instanceof Function?arguments[0]():null})}),console.log("[ErrorBoundary] React polyfills applied"))}catch(t){console.error("[ErrorBoundary] Error applying React polyfills:",t)}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,n){if(console.error("ProductionErrorBoundary caught an error:",t,n),this.setState({errorInfo:n}),t.message&&(t.message.includes("createContext")||t.message.includes("framer-motion"))&&(console.log("[ErrorBoundary] Detected Framer Motion error, attempting to fix..."),this.fixFramerMotionIssues(),typeof window<"u")){const s={motion:{div:"div",span:"span",button:"button",a:"a",ul:"ul",li:"li",p:"p",h1:"h1",h2:"h2",h3:"h3",img:"img",section:"section"},AnimatePresence:function(i){return i.children||null}};try{if(window.__vite_plugin_react_preamble_installed__){console.log("[ErrorBoundary] Patching ESM imports for Framer Motion");const i=window.import;window.import=function(a){return a==="framer-motion"||a.includes("framer-motion/")?(console.log("[ErrorBoundary] Intercepted Framer Motion import:",a),Promise.resolve(s)):i.apply(this,arguments)}}console.log("[ErrorBoundary] Framer Motion disabled")}catch(i){console.error("[ErrorBoundary] Error patching module system:",i)}}}render(){return this.state.hasError?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100vh",padding:"20px",backgroundColor:"#f8f9fa",color:"#333",textAlign:"center",fontFamily:"Arial, sans-serif"},children:[e.jsx("h1",{style:{color:"#d9534f",marginBottom:"20px"},children:"Something went wrong"}),e.jsx("p",{style:{maxWidth:"600px",marginBottom:"20px"},children:"We're sorry, but there was an error loading the application. Please try refreshing the page."}),e.jsx("button",{onClick:()=>window.location.reload(),style:{backgroundColor:"#4B74AA",color:"white",border:"none",padding:"10px 20px",borderRadius:"4px",cursor:"pointer",fontSize:"16px"},children:"Refresh Page"}),!1]}):this.props.children}}const zn=({children:o})=>{const t=$t();return e.jsx(Zt,{syncTools:t,children:o})},Fn=({children:o})=>e.jsx(Gt,{children:e.jsx(zn,{children:o})}),Bn=["VITE_SUPABASE_URL","VITE_SUPABASE_KEY","VITE_VAPI_PUBLIC_KEY"],Jn=()=>{const o={allVariablesPresent:!0,missingVariables:[],variables:{}};return Bn.forEach(t=>{let n=null;try{n={VITE_SUPABASE_URL:"https://utopqxsvudgrtiwenlzl.supabase.co",VITE_SUPABASE_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU",VITE_FALLBACK_MODE:"false",VITE_VAPI_PUBLIC_KEY:"310f0d43-27c2-47a5-a76d-e55171d024f7",VITE_VAPI_SECRET_KEY:"6734febc-fc65-4669-93b0-929b31ff6564",VITE_APIFY_API_TOKEN:"**********************************************",VITE_GMAIL_CLIENT_ID:"your_gmail_client_id_here",VITE_GMAIL_REDIRECT_URI:"http://localhost:5173/auth/callback",VITE_GOOGLE_CLIENT_ID:"211827020409-0b877bga5m3retigi7ntb3nufq6csolo.apps.googleusercontent.com",VITE_GOOGLE_CLIENT_SECRET:"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",VITE_DEV_MODE:"false",VITE_DISABLE_FRAMER_MOTION:"true",VITE_USER_NODE_ENV:"development",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1,VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU",REACT_APP_SUPABASE_URL:"https://utopqxsvudgrtiwenlzl.supabase.co",REACT_APP_SUPABASE_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU",REACT_APP_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU"}[t]}catch{}const s=!!n,i=t.includes("KEY")||t.includes("TOKEN");o.variables[t]={isPresent:s,value:i?s?"****":void 0:n},s||(o.allVariablesPresent=!1,o.missingVariables.push(t))}),o},Yn=()=>{const o=Jn();return console.group("Environment Variable Verification"),console.log("All required variables present:",o.allVariablesPresent?"✅ Yes":"❌ No"),o.allVariablesPresent||console.warn("Missing variables:",o.missingVariables.join(", ")),console.log("Variables status:"),Object.entries(o.variables).forEach(([t,n])=>{console.log(`- ${t}: ${n.isPresent?"✅ Present":"❌ Missing"} ${n.value?`(${n.value})`:""}`)}),console.groupEnd(),o},Xn=()=>{const o=Yn();if(!o.allVariablesPresent){console.warn("Setting fallback values for missing environment variables");const t="https://utopqxsvudgrtiwenlzl.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU",s="310f0d43-27c2-47a5-a76d-e55171d024f7";let i=!1;try{i=!!"https://utopqxsvudgrtiwenlzl.supabase.co"}catch{}i||(window.VITE_SUPABASE_URL=t);let a=!1;try{a=!!"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU"}catch{}a||(window.VITE_SUPABASE_KEY=n);let c=!1;try{c=!!"310f0d43-27c2-47a5-a76d-e55171d024f7"}catch{}c||(window.VITE_VAPI_PUBLIC_KEY=s)}return o},Hn=async()=>{console.log("[initAttorneyProfileManager] Initializing attorney profile manager");try{const o=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY||localStorage.getItem("vapi_api_key");if(o){console.log("[initAttorneyProfileManager] Initializing Vapi service manager");try{const n=window.location.pathname.includes("/dashboard")||window.location.pathname.includes("/admin"),s=window.location.pathname.includes("/preview"),i=typeof window<"u"&&(window.location.hostname.includes(".com")||window.location.hostname.includes(".org")||window.location.hostname.includes(".net")||window.location.hostname.includes(".io")||!window.location.hostname.includes("localhost")),a=!1;await A.initialize(o,!1,{isAttorneyDashboard:n,isPreview:s,isProduction:i,forceMcpMode:a});const c=A.getConnectionStatus();console.log("[initAttorneyProfileManager] Vapi service manager initialized:",c);const p=A.getConnectionWarning();p&&(console.warn("[initAttorneyProfileManager] Vapi connection warning:",p),n&&!window.location.pathname.includes("/demo")?setTimeout(()=>{try{let u=document.getElementById("voice-connection-warning");u||(u=document.createElement("div"),u.id="voice-connection-warning",u.style.backgroundColor="#FFF3CD",u.style.color="#856404",u.style.padding="10px",u.style.margin="10px 0",u.style.borderRadius="4px",u.style.border="1px solid #FFEEBA",u.style.position="fixed",u.style.top="10px",u.style.right="10px",u.style.zIndex="9999",u.style.maxWidth="400px",document.body.appendChild(u)),u.textContent=p}catch(u){console.error("[initAttorneyProfileManager] Error showing warning banner:",u)}},1e3):console.log("[initAttorneyProfileManager] Voice service warning (hidden from UI):",p)),c.useMock?n||s?console.warn("[initAttorneyProfileManager] Using mock Vapi service in attorney dashboard/preview mode"):console.log("[initAttorneyProfileManager] Using mock Vapi service"):console.log(`[initAttorneyProfileManager] Connected to Vapi using ${c.connectionMode} mode`)}catch(n){console.warn("[initAttorneyProfileManager] Failed to initialize Vapi service manager:",n),console.log("[initAttorneyProfileManager] Continuing with initialization despite Vapi connection failure")}}else console.warn("[initAttorneyProfileManager] No Vapi API key found");const t=localStorage.getItem("attorney");if(t)try{const n=JSON.parse(t);n&&n.id&&console.log("[initAttorneyProfileManager] Found attorney in localStorage:",n.id)}catch(n){console.error("[initAttorneyProfileManager] Error parsing stored attorney:",n)}console.log("[initAttorneyProfileManager] Initialization complete")}catch(o){console.error("[initAttorneyProfileManager] Initialization error:",o)}};Hn();const ct={enabled:!0,logToConsole:!0,logToMemory:!0,maxLogEntries:100,networkLogging:{enabled:!0,includeHeaders:!0,includeBody:!0,maxBodyLength:500}},Ie=[],ye=[],Ne=[];let H={...ct};const Kn=(o={})=>{H={...ct,...o},typeof window<"u"&&(window.VapiMcpDebugger={config:H,connectionLogs:Ie,networkLogs:ye,errors:Ne,getLogs:()=>[...Ie],getNetworkLogs:()=>[...ye],getErrors:()=>[...Ne],clearLogs:()=>{Ie.length=0,console.log("Vapi MCP connection logs cleared")},clearNetworkLogs:()=>{ye.length=0,console.log("Vapi MCP network logs cleared")},clearErrors:()=>{Ne.length=0,console.log("Vapi MCP errors cleared")},clearAll:()=>{Ie.length=0,ye.length=0,Ne.length=0,console.log("All Vapi MCP logs cleared")},checkEnvironment:()=>Gn(),testConnection:async()=>{try{const t=await $n();return console.log("Vapi MCP connection test result:",t),t}catch(t){return console.error("Vapi MCP connection test failed:",t),{success:!1,error:t.message}}}})},be=(o,t,n={})=>{if(!H.enabled)return;const s={timestamp:new Date().toISOString(),status:o,message:t,details:{...n}};if(Ie.push(s),Ie.length>H.maxLogEntries&&Ie.splice(0,Ie.length-H.maxLogEntries),H.logToConsole){const i=o==="success"?"color: green; font-weight: bold":o==="error"?"color: red; font-weight: bold":o==="warning"?"color: orange; font-weight: bold":"color: blue; font-weight: bold";console.groupCollapsed(`%c[Vapi MCP] ${o.toUpperCase()}: ${t}`,i),console.log("Details:",n),console.log("Timestamp:",s.timestamp),console.groupEnd()}o==="error"&&Ne.push(s)},nt=(o,t,n,s,i,a)=>{if(!H.enabled||!H.networkLogging.enabled)return;const c={timestamp:new Date().toISOString(),type:o,url:t,method:n,status:s,headers:H.networkLogging.includeHeaders?i:void 0,body:H.networkLogging.includeBody?typeof a=="string"&&a.length>H.networkLogging.maxBodyLength?a.substring(0,H.networkLogging.maxBodyLength)+"...":a:void 0};if(ye.push(c),ye.length>H.maxLogEntries&&ye.splice(0,ye.length-H.maxLogEntries),H.logToConsole){const p=o==="request"?"color: blue":"color: green",u=s>=200&&s<300?"color: green":s>=400?"color: red":"color: orange";o==="request"?(console.groupCollapsed(`%c[Vapi MCP] ${n} ${t}`,p),console.log("Headers:",i),console.log("Body:",a),console.groupEnd()):(console.groupCollapsed(`%c[Vapi MCP] Response: %c${s}%c ${t}`,p,u,"color: black"),console.log("Headers:",i),console.log("Body:",a),console.groupEnd())}},Gn=()=>{const o={VITE_VAPI_PUBLIC_KEY:typeof import.meta<"u"?"Set":"Not set",VITE_VAPI_SECRET_KEY:typeof import.meta<"u"?"Set":"Not set",VAPI_TOKEN:typeof process<"u"&&process.env?.VAPI_TOKEN?"Set":"Not set",localStorage_vapi_api_key:typeof localStorage<"u"&&localStorage.getItem("vapi_api_key")?"Set":"Not set",isDevelopment:typeof import.meta<"u"&&!1,origin:typeof window<"u"?window.location.origin:"Unknown"};return H.logToConsole&&(console.group("[Vapi MCP] Environment Check"),console.table(o),console.groupEnd()),o},$n=async()=>{const o=typeof import.meta<"u"&&"310f0d43-27c2-47a5-a76d-e55171d024f7"||typeof window<"u"&&window.VITE_VAPI_PUBLIC_KEY||typeof localStorage<"u"&&localStorage.getItem("vapi_api_key");if(!o)return be("error","No API key available for connection test"),{success:!1,error:"No API key available"};try{be("info","Testing direct connection to Vapi API",{apiKey:`${o.substring(0,5)}...`});const t=["https://api.vapi.ai/v1/assistants?limit=1","https://api.vapi.ai/assistants?limit=1","https://api.vapi.ai/api/v1/assistants?limit=1","https://api.vapi.ai/api/assistants?limit=1"];for(const n of t)try{nt("request",n,"GET",null,{Authorization:`Bearer ${o}`},null);const s=await fetch(n,{method:"GET",headers:{Authorization:`Bearer ${o}`,"Content-Type":"application/json",Accept:"application/json"}}),i=await s.json();if(nt("response",n,"GET",s.status,s.headers,i),s.ok)return be("success",`Direct connection successful with endpoint: ${n}`),{success:!0,endpoint:n,mode:"direct",status:s.status,data:i};be("warning",`Endpoint ${n} returned status: ${s.status}`)}catch(s){be("warning",`Error with endpoint ${n}`,{error:s.message})}return be("error","All direct API endpoints failed"),{success:!1,error:"All direct API endpoints failed",mode:"direct"}}catch(t){return be("error","Error testing Vapi connection",{error:t.message}),{success:!1,error:t.message}}};Kn();const ns=window.fetch;Xn();Vt.createRoot(document.getElementById("root")).render(e.jsx(pe.StrictMode,{children:e.jsx(On,{children:e.jsx(Un,{showDetails:!0,onReset:()=>window.location.reload(),children:e.jsx(Pt,{children:e.jsx(Qt,{children:e.jsx(Wt,{children:e.jsx(Fn,{children:e.jsx(kn,{})})})})})})})}));
//# sourceMappingURL=index-2be91593.js.map
