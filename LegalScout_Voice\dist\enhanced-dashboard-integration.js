/**
 * Enhanced Dashboard Integration
 * 
 * Integrates the robust state handler with the dashboard to handle
 * any email state thrown at the system, including damonand<PERSON>rak<PERSON>@gmail.com
 */

(function enhancedDashboardIntegration() {
  console.log('🎯 [EnhancedDashboardIntegration] Initializing dashboard integration...');
  
  // Wait for dependencies
  let checkInterval = setInterval(() => {
    if (document.readyState === 'complete' && 
        window.supabase && 
        window.resolveAttorneyState && 
        window.createControlledAssistant) {
      clearInterval(checkInterval);
      initializeDashboardIntegration();
    }
  }, 100);
  
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[EnhancedDashboardIntegration] Timed out waiting for dependencies');
  }, 10000);
  
  function initializeDashboardIntegration() {
    console.log('🚀 [EnhancedDashboardIntegration] Starting dashboard integration...');
    
    // Enhanced attorney loading function
    window.loadAttorneyWithRobustHandling = async function(email) {
      console.log(`🔍 [EnhancedDashboardIntegration] Loading attorney with robust handling: ${email}`);
      
      try {
        // Use the robust state resolver
        const state = await window.resolveAttorneyState(email);
        
        if (!state.success) {
          throw new Error(state.error || 'Failed to resolve attorney state');
        }
        
        console.log('✅ [EnhancedDashboardIntegration] Attorney state resolved:', {
          attorneyId: state.attorney.id,
          assistantCount: state.assistants.length,
          needsCreation: state.needsCreation,
          message: state.message
        });
        
        // Update the UI state
        updateDashboardState(state);
        
        return state;
        
      } catch (error) {
        console.error('❌ [EnhancedDashboardIntegration] Error loading attorney:', error);
        
        // Show user-friendly error
        showUserMessage('error', `Unable to load attorney profile: ${error.message}`);
        
        throw error;
      }
    };
    
    // Enhanced assistant creation function
    window.createAssistantWithRobustHandling = async function(attorney) {
      console.log('🎯 [EnhancedDashboardIntegration] Creating assistant with robust handling...');
      
      try {
        // Show loading state
        showUserMessage('info', 'Creating your assistant...');
        
        // Use controlled creation to prevent duplicates
        const result = await window.createControlledAssistant(attorney);
        
        if (!result.success) {
          throw new Error(result.error || 'Failed to create assistant');
        }
        
        console.log('✅ [EnhancedDashboardIntegration] Assistant created successfully:', result.assistant.id);
        
        // Update the attorney record with the new assistant ID
        const updatedAttorney = await updateAttorneyAssistantId(attorney.id, result.assistant.id);
        
        // Refresh the dashboard state
        const newState = await window.resolveAttorneyState(attorney.email);
        updateDashboardState(newState);
        
        // Show success message
        showUserMessage('success', 'Assistant created successfully!');
        
        return result;
        
      } catch (error) {
        console.error('❌ [EnhancedDashboardIntegration] Error creating assistant:', error);
        
        // Show user-friendly error
        showUserMessage('error', `Failed to create assistant: ${error.message}`);
        
        throw error;
      }
    };
    
    // Update attorney assistant ID in database
    async function updateAttorneyAssistantId(attorneyId, assistantId) {
      console.log('💾 [EnhancedDashboardIntegration] Updating attorney assistant ID...');
      
      const { data, error } = await window.supabase
        .from('attorneys')
        .update({
          vapi_assistant_id: assistantId,
          updated_at: new Date().toISOString()
        })
        .eq('id', attorneyId)
        .select()
        .single();
      
      if (error) {
        console.error('[EnhancedDashboardIntegration] Error updating attorney:', error);
        throw error;
      }
      
      console.log('✅ [EnhancedDashboardIntegration] Attorney updated successfully');
      return data;
    }
    
    // Update dashboard UI state
    function updateDashboardState(state) {
      console.log('🎨 [EnhancedDashboardIntegration] Updating dashboard state...');
      
      // Store the current state globally for other components to access
      window.currentAttorneyState = state;
      
      // Trigger custom event for components to listen to
      const event = new CustomEvent('attorneyStateUpdated', {
        detail: state
      });
      document.dispatchEvent(event);
      
      // Update assistant dropdown if it exists
      updateAssistantDropdown(state);
      
      console.log('✅ [EnhancedDashboardIntegration] Dashboard state updated');
    }
    
    // Update assistant dropdown
    function updateAssistantDropdown(state) {
      // Find assistant dropdown elements
      const dropdowns = document.querySelectorAll('select[id*="assistant"], select.assistant-select');
      
      dropdowns.forEach(dropdown => {
        // Clear existing options
        dropdown.innerHTML = '';
        
        if (state.needsCreation || state.assistants.length === 0) {
          // No assistant state
          dropdown.innerHTML = `
            <option value="">No Assistant</option>
            <option value="create_new">+ Create New Assistant</option>
          `;
          dropdown.value = '';
        } else {
          // Has assistants - show them with latest selected
          state.assistants.forEach((assistant, index) => {
            const option = document.createElement('option');
            option.value = assistant.id;
            option.textContent = assistant.name || `Assistant ${assistant.id.slice(0, 8)}`;
            dropdown.appendChild(option);
          });
          
          // Add create option
          const createOption = document.createElement('option');
          createOption.value = 'create_new';
          createOption.textContent = '+ Create New Assistant';
          dropdown.appendChild(createOption);
          
          // Select the latest assistant
          if (state.selectedAssistant) {
            dropdown.value = state.selectedAssistant.id;
          }
        }
        
        console.log('🎨 [EnhancedDashboardIntegration] Updated assistant dropdown');
      });
    }
    
    // Show user messages
    function showUserMessage(type, message) {
      console.log(`📢 [EnhancedDashboardIntegration] ${type.toUpperCase()}: ${message}`);
      
      // Create notification element
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
      `;
      notification.textContent = message;
      
      document.body.appendChild(notification);
      
      // Remove after 5 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 5000);
    }
    
    // Override existing dashboard functions if they exist
    if (window.loadAttorney) {
      const originalLoadAttorney = window.loadAttorney;
      window.loadAttorney = function(email) {
        console.log('[EnhancedDashboardIntegration] Intercepting loadAttorney, using robust handling');
        return window.loadAttorneyWithRobustHandling(email);
      };
    }
    
    // Listen for assistant dropdown changes
    document.addEventListener('change', function(event) {
      if (event.target.matches('select[id*="assistant"], select.assistant-select')) {
        if (event.target.value === 'create_new') {
          console.log('[EnhancedDashboardIntegration] User selected create new assistant');
          
          // Get current attorney from state
          if (window.currentAttorneyState && window.currentAttorneyState.attorney) {
            window.createAssistantWithRobustHandling(window.currentAttorneyState.attorney);
          } else {
            showUserMessage('error', 'No attorney data available for assistant creation');
          }
        }
      }
    });
    
    console.log('✅ [EnhancedDashboardIntegration] Dashboard integration initialized');
  }
})();
