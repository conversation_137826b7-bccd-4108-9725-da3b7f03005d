/**
 * ULTRA-THINKING Optimized Sync API
 * 
 * Replaces the failing /api/sync-tools/manage-auth-state endpoint
 * with a streamlined, efficient sync system that leverages:
 * 1. Vercel-Supabase native integration
 * 2. Vapi MCP Server with streamable-HTTP
 * 3. Minimal API surface area
 * 4. Real-time sync capabilities
 */

import { createClient } from '@supabase/supabase-js';

// Initialize Supabase with clean, simple configuration
const supabase = createClient(
  process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL,
  process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

/**
 * Main API handler
 */
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const { action, data } = req.body;

    switch (action) {
      case 'sync_attorney':
        return await handleSyncAttorney(req, res, data);
      
      case 'validate_assistant':
        return await handleValidateAssistant(req, res, data);
      
      case 'get_sync_status':
        return await handleGetSyncStatus(req, res, data);
      
      case 'fix_conflicts':
        return await handleFixConflicts(req, res, data);
      
      default:
        return res.status(400).json({
          success: false,
          error: `Unknown action: ${action}`
        });
    }
  } catch (error) {
    console.error('[OptimizedSyncAPI] Error:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Sync attorney data between Supabase and Vapi
 */
async function handleSyncAttorney(req, res, data) {
  const { email, userId } = data;

  try {
    // Get attorney from Supabase
    let attorney = null;
    
    if (userId) {
      const { data: attorneyData, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('user_id', userId)
        .single();
      
      if (!error) attorney = attorneyData;
    }
    
    if (!attorney && email) {
      const { data: attorneyData, error } = await supabase
        .from('attorneys')
        .select('*')
        .eq('email', email)
        .single();
      
      if (!error) attorney = attorneyData;
    }

    if (!attorney) {
      return res.status(404).json({
        success: false,
        error: 'Attorney not found'
      });
    }

    // Validate assistant in Vapi if present
    let assistantValid = false;
    if (attorney.vapi_assistant_id) {
      assistantValid = await validateAssistantInVapi(attorney.vapi_assistant_id);
    }

    // Get assistant mappings
    const { data: assistantMappings } = await supabase
      .from('attorney_assistants')
      .select('*')
      .eq('attorney_id', attorney.id);

    return res.status(200).json({
      success: true,
      data: {
        attorney,
        assistantValid,
        assistantMappings: assistantMappings || [],
        syncTimestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[OptimizedSyncAPI] Sync attorney error:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Validate assistant exists in Vapi
 */
async function handleValidateAssistant(req, res, data) {
  const { assistantId } = data;

  if (!assistantId) {
    return res.status(400).json({
      success: false,
      error: 'Assistant ID is required'
    });
  }

  try {
    const isValid = await validateAssistantInVapi(assistantId);
    
    return res.status(200).json({
      success: true,
      data: {
        assistantId,
        valid: isValid,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[OptimizedSyncAPI] Validate assistant error:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Get sync status and metrics
 */
async function handleGetSyncStatus(req, res, data) {
  try {
    // Get basic stats from Supabase
    const { data: attorneyCount } = await supabase
      .from('attorneys')
      .select('id', { count: 'exact' });

    const { data: assistantMappingCount } = await supabase
      .from('attorney_assistants')
      .select('id', { count: 'exact' });

    // Get attorneys with assistant IDs
    const { data: attorneysWithAssistants } = await supabase
      .from('attorneys')
      .select('vapi_assistant_id')
      .not('vapi_assistant_id', 'is', null);

    return res.status(200).json({
      success: true,
      data: {
        totalAttorneys: attorneyCount?.length || 0,
        totalAssistantMappings: assistantMappingCount?.length || 0,
        attorneysWithAssistants: attorneysWithAssistants?.length || 0,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[OptimizedSyncAPI] Get sync status error:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Fix assistant conflicts automatically
 */
async function handleFixConflicts(req, res, data) {
  const { email } = data;

  try {
    // Get attorney
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('email', email)
      .single();

    if (error || !attorney) {
      return res.status(404).json({
        success: false,
        error: 'Attorney not found'
      });
    }

    // Get assistant mappings
    const { data: assistantMappings } = await supabase
      .from('attorney_assistants')
      .select('*')
      .eq('attorney_id', attorney.id);

    let fixedAssistantId = null;
    let action = 'none';

    // If attorney has no assistant ID but has mappings, use the first mapping
    if (!attorney.vapi_assistant_id && assistantMappings?.length > 0) {
      const firstMapping = assistantMappings[0];
      
      // Validate the mapped assistant exists in Vapi
      const isValid = await validateAssistantInVapi(firstMapping.assistant_id);
      
      if (isValid) {
        // Update attorney record
        const { error: updateError } = await supabase
          .from('attorneys')
          .update({ 
            vapi_assistant_id: firstMapping.assistant_id,
            updated_at: new Date().toISOString()
          })
          .eq('id', attorney.id);

        if (!updateError) {
          fixedAssistantId = firstMapping.assistant_id;
          action = 'assigned_from_mapping';
        }
      }
    }
    // If attorney has assistant ID, validate it
    else if (attorney.vapi_assistant_id) {
      const isValid = await validateAssistantInVapi(attorney.vapi_assistant_id);
      
      if (isValid) {
        action = 'validated_existing';
        fixedAssistantId = attorney.vapi_assistant_id;
      } else {
        // Try to find a valid assistant from mappings
        for (const mapping of assistantMappings || []) {
          const isValid = await validateAssistantInVapi(mapping.assistant_id);
          
          if (isValid) {
            // Update attorney record
            const { error: updateError } = await supabase
              .from('attorneys')
              .update({ 
                vapi_assistant_id: mapping.assistant_id,
                updated_at: new Date().toISOString()
              })
              .eq('id', attorney.id);

            if (!updateError) {
              fixedAssistantId = mapping.assistant_id;
              action = 'replaced_invalid';
              break;
            }
          }
        }
      }
    }

    return res.status(200).json({
      success: true,
      data: {
        email,
        action,
        fixedAssistantId,
        previousAssistantId: attorney.vapi_assistant_id,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('[OptimizedSyncAPI] Fix conflicts error:', error);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
}

/**
 * Validate assistant exists in Vapi using direct API call
 */
async function validateAssistantInVapi(assistantId) {
  if (!assistantId || assistantId.startsWith('mock-')) {
    return false;
  }

  try {
    const apiKey = process.env.VITE_VAPI_PRIVATE_KEY || process.env.VAPI_PRIVATE_KEY;
    
    if (!apiKey) {
      console.error('[OptimizedSyncAPI] No Vapi API key available');
      return false;
    }

    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    return response.ok;

  } catch (error) {
    console.error('[OptimizedSyncAPI] Vapi validation error:', error);
    return false;
  }
}
