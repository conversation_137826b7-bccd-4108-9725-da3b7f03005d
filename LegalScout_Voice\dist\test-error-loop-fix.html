<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Loop Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-error {
            color: #dc3545;
        }
        .log-warn {
            color: #ffc107;
        }
        .log-info {
            color: #17a2b8;
        }
        .log-success {
            color: #28a745;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Error Loop Fix Test</h1>
        <p>This page tests the error loop fixes and monitors for common issues.</p>
        
        <div id="status-container">
            <div class="status info">
                <span id="status-text">🔄 Initializing tests...</span>
            </div>
        </div>
        
        <div>
            <button onclick="runTests()">🧪 Run Tests</button>
            <button onclick="clearConsole()">🧹 Clear Console</button>
            <button onclick="checkForLoops()">🔍 Check for Loops</button>
        </div>
        
        <h3>📊 Test Results</h3>
        <div id="test-results"></div>
        
        <h3>📝 Console Output</h3>
        <div id="console-output"></div>
    </div>

    <!-- Load our error loop fix first -->
    <script src="/error-loop-fix.js"></script>
    
    <!-- Test scripts -->
    <script>
        let consoleOutput = document.getElementById('console-output');
        let testResults = document.getElementById('test-results');
        let statusText = document.getElementById('status-text');
        let originalConsole = {};
        
        // Capture console output
        function setupConsoleCapture() {
            ['log', 'warn', 'error', 'info'].forEach(method => {
                originalConsole[method] = console[method];
                console[method] = function(...args) {
                    originalConsole[method].apply(console, args);
                    logToPage(method, args.join(' '));
                };
            });
        }
        
        function logToPage(level, message) {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${level}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${level.toUpperCase()}: ${message}`;
            consoleOutput.appendChild(entry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearConsole() {
            consoleOutput.innerHTML = '';
        }
        
        function addTestResult(name, status, message) {
            const result = document.createElement('div');
            result.className = `status ${status}`;
            result.innerHTML = `<strong>${name}:</strong> ${message}`;
            testResults.appendChild(result);
        }
        
        function updateStatus(message, type = 'info') {
            statusText.textContent = message;
            statusText.parentElement.className = `status ${type}`;
        }
        
        async function runTests() {
            testResults.innerHTML = '';
            updateStatus('🧪 Running tests...', 'info');
            
            // Test 1: Check if error loop fix is applied
            if (window.ERROR_LOOP_FIX_APPLIED) {
                addTestResult('Error Loop Fix', 'success', '✅ Applied successfully');
            } else {
                addTestResult('Error Loop Fix', 'error', '❌ Not applied');
            }
            
            // Test 2: Check import.meta polyfill
            try {
                if (window.import && window.import.meta && window.import.meta.env) {
                    addTestResult('Import.meta Polyfill', 'success', '✅ Available');
                } else {
                    addTestResult('Import.meta Polyfill', 'warning', '⚠️ Not available');
                }
            } catch (e) {
                addTestResult('Import.meta Polyfill', 'error', `❌ Error: ${e.message}`);
            }
            
            // Test 3: Check for problematic scripts
            const problematicScripts = [
                'force-new-assistant.js',
                'aggressive-fix.js',
                'critical-fixes-v2.js'
            ];
            
            let foundProblematic = 0;
            problematicScripts.forEach(scriptName => {
                const scripts = document.querySelectorAll(`script[src*="${scriptName}"]`);
                if (scripts.length > 0) {
                    foundProblematic++;
                }
            });
            
            if (foundProblematic === 0) {
                addTestResult('Problematic Scripts', 'success', '✅ None found');
            } else {
                addTestResult('Problematic Scripts', 'warning', `⚠️ Found ${foundProblematic} problematic scripts`);
            }
            
            // Test 4: Check for multiple intervals
            const intervalCount = Object.keys(window).filter(key => key.includes('interval')).length;
            if (intervalCount < 5) {
                addTestResult('Interval Management', 'success', `✅ ${intervalCount} intervals found`);
            } else {
                addTestResult('Interval Management', 'warning', `⚠️ ${intervalCount} intervals found (may be excessive)`);
            }
            
            // Test 5: Check MutationObserver override
            if (window.MutationObserver && window.MutationObserver.toString().includes('ErrorLoopFix')) {
                addTestResult('MutationObserver Fix', 'success', '✅ Override applied');
            } else {
                addTestResult('MutationObserver Fix', 'warning', '⚠️ Override not detected');
            }
            
            // Test 6: Check for error boundaries
            let errorBoundaryActive = false;
            try {
                // Trigger a test error to see if it's caught
                const testError = new Error('Test error for boundary check');
                window.dispatchEvent(new ErrorEvent('error', { error: testError }));
                errorBoundaryActive = true;
            } catch (e) {
                // Error boundary should catch this
            }
            
            addTestResult('Error Boundaries', errorBoundaryActive ? 'success' : 'warning', 
                         errorBoundaryActive ? '✅ Active' : '⚠️ Status unknown');
            
            updateStatus('🎉 Tests completed!', 'success');
        }
        
        function checkForLoops() {
            updateStatus('🔍 Checking for loops...', 'info');
            
            // Monitor console for loop indicators
            let loopIndicators = 0;
            const originalLog = console.log;
            
            console.log = function(...args) {
                const message = args.join(' ');
                if (message.includes('Waiting for dependencies') || 
                    message.includes('RobustStateHandler') ||
                    message.includes('Initializing') && message.includes('again')) {
                    loopIndicators++;
                }
                originalLog.apply(console, args);
            };
            
            // Check for 5 seconds
            setTimeout(() => {
                console.log = originalLog;
                
                if (loopIndicators === 0) {
                    addTestResult('Loop Detection', 'success', '✅ No loops detected');
                    updateStatus('🎉 No loops detected!', 'success');
                } else {
                    addTestResult('Loop Detection', 'warning', `⚠️ ${loopIndicators} potential loop indicators found`);
                    updateStatus(`⚠️ ${loopIndicators} potential loops detected`, 'warning');
                }
            }, 5000);
        }
        
        // Initialize
        setupConsoleCapture();
        
        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
        
        // Listen for our fix completion event
        window.addEventListener('errorLoopFixComplete', (event) => {
            addTestResult('Fix Completion Event', 'success', `✅ Received: ${event.detail.fixes.join(', ')}`);
        });
    </script>
</body>
</html>
