/**
 * Fix for the "Configure Your Assistant" button
 * This script ensures the button is clickable and properly shows the auth overlay
 */

(function() {
  console.log('[ConfigureButtonFix] Script loaded');

  // Function to find and fix the button
  function fixConfigureButton() {
    // ONLY run on agent page
    const isAgentPage = window.location.pathname.includes('/agent');
    if (!isAgentPage) {
      console.log('[ConfigureButtonFix] Not on agent page, skipping button fix');
      return;
    }

    // Try to find the button by ID first
    let configureButton = document.getElementById('configure-assistant-button');

    // If not found by ID, try to find by text content
    if (!configureButton) {
      const buttons = document.querySelectorAll('button');
      for (const button of buttons) {
        if (button.textContent.includes('Configure Your Assistant')) {
          configureButton = button;
          break;
        }
      }
    }

    // If button is found, ensure it's clickable
    if (configureButton) {
      console.log('[ConfigureButtonFix] Found Configure Your Assistant button');

      // Remove any pointer-events: none style
      configureButton.style.pointerEvents = 'auto';

      // Ensure the button is visible
      configureButton.style.opacity = '1';
      configureButton.style.visibility = 'visible';

      // Add a direct click handler that shows the auth overlay
      configureButton.addEventListener('click', function(e) {
        console.log('[ConfigureButtonFix] Button clicked');

        // Prevent default and stop propagation to ensure our handler runs
        e.preventDefault();
        e.stopPropagation();

        // Try multiple methods to show the auth overlay
        if (typeof window.setShowAuthOverlay === 'function') {
          console.log('[ConfigureButtonFix] Using window.setShowAuthOverlay');
          window.setShowAuthOverlay(true);
        } else if (typeof window.handleGetStarted === 'function') {
          console.log('[ConfigureButtonFix] Using window.handleGetStarted');
          window.handleGetStarted();
        } else {
          console.log('[ConfigureButtonFix] Redirecting to /login');
          window.location.href = '/login';
        }

        return false;
      }, true);

      console.log('[ConfigureButtonFix] Button fixed successfully');
    } else {
      console.log('[ConfigureButtonFix] Configure Your Assistant button not found, will retry');

      // If button not found, try again after a short delay
      setTimeout(fixConfigureButton, 1000);
    }
  }

  // Function to create an emergency button if the original can't be fixed
  function createEmergencyButton() {
    // ONLY show on agent page, not on home page
    const isRelevantPage = window.location.pathname.includes('/agent');

    // Skip if not on agent page
    if (!isRelevantPage) {
      console.log('[ConfigureButtonFix] Not on agent page, skipping emergency button');
      return;
    }

    // Check if emergency button already exists
    if (document.getElementById('emergency-configure-button')) return;

    console.log('[ConfigureButtonFix] Creating emergency button');

    // Create the emergency button
    const emergencyButton = document.createElement('button');
    emergencyButton.id = 'emergency-configure-button';
    emergencyButton.textContent = 'Configure Your Assistant';
    emergencyButton.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      background-color: #D85722;
      color: white;
      border: none;
      border-radius: 30px;
      padding: 15px 40px;
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 6px 15px rgba(216, 87, 34, 0.4);
    `;

    // Add click handler
    emergencyButton.addEventListener('click', function() {
      console.log('[ConfigureButtonFix] Emergency button clicked');

      if (typeof window.setShowAuthOverlay === 'function') {
        window.setShowAuthOverlay(true);
      } else if (typeof window.handleGetStarted === 'function') {
        window.handleGetStarted();
      } else {
        window.location.href = '/login';
      }
    });

    // Add to the document
    document.body.appendChild(emergencyButton);
    console.log('[ConfigureButtonFix] Emergency button created');
  }

  // Run the fix when the DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      fixConfigureButton();
      setTimeout(createEmergencyButton, 5000); // Create emergency button after 5 seconds as a fallback
    });
  } else {
    fixConfigureButton();
    setTimeout(createEmergencyButton, 5000);
  }
})();
