/**
 * Debug Supabase Configuration
 * Shows exactly what Supabase config the webhook would use
 */

export default async function handler(req, res) {
  try {
    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseKey = process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY;
    
    const correctUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
    
    return res.status(200).json({
      message: 'Supabase configuration debug',
      config: {
        url: supabaseUrl,
        keyLength: supabaseKey?.length,
        isCorrectUrl: supabaseUrl === correctUrl,
        correctUrl: correctUrl,
        urlMatch: supabaseUrl === correctUrl ? 'MATCH' : 'MISMATCH'
      },
      envVars: {
        VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL ? 'Set' : 'Missing',
        SUPABASE_URL: process.env.SUPABASE_URL ? 'Set' : 'Missing',
        VITE_SUPABASE_KEY: process.env.VITE_SUPABASE_KEY ? 'Set' : 'Missing',
        SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY ? 'Set' : 'Missing'
      }
    });
  } catch (error) {
    return res.status(500).json({
      error: error.message
    });
  }
}
