# Workflow Tab Documentation

## Overview

The Workflow Tab is a new feature in the LegalScout dashboard that enables attorneys to create and manage multi-agent AI workflows using Vapi's Squad functionality. This feature allows for sophisticated legal consultation processes involving multiple specialized AI agents working together.

## Features

### 1. Workflow Templates

Pre-configured multi-agent workflows for common legal scenarios:

#### Legal Consultation Workflow
- **Intake Specialist**: Initial client screening and information gathering
- **Legal Analyst**: Case analysis and legal research  
- **Attorney Advisor**: Final consultation and recommendations

#### Contract Review Squad
- **Document Analyzer**: Initial document parsing and structure analysis
- **Risk Assessor**: Risk identification and compliance checking
- **Negotiation Advisor**: Negotiation strategy and recommendations

#### Litigation Support Team
- **Evidence Coordinator**: Evidence collection and organization
- **Research Specialist**: Legal research and precedent analysis
- **Strategy Advisor**: Litigation strategy and case planning

### 2. Squad Management

- Create squads from templates
- View active squads
- Start/stop workflows
- Monitor squad performance

### 3. Live Workflow Monitoring

- Real-time agent pipeline visualization
- Live transcript display
- Agent status indicators
- Workflow controls (pause, join, stop)

## Technical Implementation

### Components

#### WorkflowTab.jsx
Main component that handles:
- Template rendering
- Squad creation and management
- Workflow monitoring
- Integration with Vapi MCP service

#### Enhanced CSS Styles
- Responsive design
- Animated agent indicators
- Professional workflow visualization
- VapiBlocks-inspired UI components

### Vapi Integration

#### Squad Methods Added to vapiMcpService.js
- `listSquads()` - List all squads
- `getSquad(squadId)` - Get squad details
- `createSquad(squadConfig)` - Create new squad
- `updateSquad(squadId, squadConfig)` - Update squad
- `deleteSquad(squadId)` - Delete squad

#### API Endpoints
- Direct API: `https://api.vapi.ai/squad`
- MCP Server: `*_squad_vapi-mcp-server` tools

### Workflow Process

1. **Template Selection**: Attorney chooses a workflow template
2. **Squad Creation**: System creates specialized AI assistants and groups them into a squad
3. **Workflow Execution**: Squad handles client interaction with agent handoffs
4. **Monitoring**: Attorney can monitor progress and intervene if needed
5. **Completion**: Workflow completes with summary and next steps

## Usage Guide

### Creating a Workflow

1. Navigate to the Workflow tab in the dashboard
2. Select a template from the Templates section
3. Click "Create Squad" to instantiate the workflow
4. The squad will appear in the Squads section

### Starting a Workflow

1. Go to the Squads section
2. Find your desired squad
3. Click "Start" to begin a workflow session
4. The system will switch to the Active section for monitoring

### Monitoring Active Workflows

1. View the agent pipeline to see current progress
2. Monitor the live transcript for conversation flow
3. Use controls to pause, join, or stop the workflow
4. Intervene manually if needed

### Demo Mode

- Click "Try Demo Workflow" to see a simulated active workflow
- Perfect for testing and demonstration purposes
- Shows all monitoring features without creating actual calls

## Configuration

### Agent Configuration

Each agent in a workflow template includes:
- **Name**: Display name for the agent
- **Role**: Description of the agent's responsibilities
- **Model**: AI model to use (GPT-4, Claude, etc.)
- **Voice**: Voice configuration for speech synthesis
- **Tools**: Available tools and functions

### Squad Configuration

Squads are configured with:
- **Name**: Squad identifier
- **Members**: List of assistant IDs
- **Workflow Logic**: Rules for agent handoffs
- **Monitoring Settings**: Transcript and control options

## Best Practices

### Template Design

1. **Clear Role Definition**: Each agent should have a specific, well-defined role
2. **Logical Flow**: Design workflows with natural progression between agents
3. **Tool Specialization**: Assign relevant tools to each agent type
4. **Handoff Triggers**: Define clear criteria for agent transitions

### Monitoring

1. **Active Supervision**: Monitor workflows during execution
2. **Intervention Points**: Be ready to join calls when needed
3. **Quality Control**: Review transcripts for improvement opportunities
4. **Client Experience**: Ensure smooth transitions between agents

### Performance Optimization

1. **Agent Specialization**: Use different models for different tasks
2. **Tool Efficiency**: Optimize tool configurations for each agent
3. **Workflow Testing**: Test workflows before client use
4. **Feedback Integration**: Use client feedback to improve workflows

## Future Enhancements

### Planned Features

1. **Custom Workflow Builder**: Visual workflow designer
2. **Advanced Analytics**: Workflow performance metrics
3. **Client Feedback Integration**: Post-workflow surveys
4. **Workflow Templates Marketplace**: Share and download templates
5. **Integration with CRM**: Automatic case creation and updates

### Technical Improvements

1. **Real-time WebSocket Integration**: Live updates without polling
2. **Advanced Agent Orchestration**: More sophisticated handoff logic
3. **Multi-modal Support**: Video and document sharing capabilities
4. **Workflow Versioning**: Template version control and rollback

## Troubleshooting

### Common Issues

1. **Squad Creation Fails**: Check Vapi API key and connection
2. **Workflow Won't Start**: Verify assistant configurations
3. **Monitoring Not Working**: Check MCP server connection
4. **Agent Handoffs Fail**: Review workflow logic and triggers

### Debug Mode

Enable debug mode by setting `showDebugPanel={true}` in the WorkflowTab component to see:
- API call logs
- Squad configuration details
- Workflow state information
- Error messages and stack traces

## API Reference

### Squad Management

```javascript
// Create a squad from template
const squad = await vapiMcpService.createSquad({
  name: `${attorney.firm_name} - Legal Consultation Squad`,
  members: assistants.map(assistant => ({ assistantId: assistant.id }))
});

// Start a workflow
const call = await vapiMcpService.createCall({
  squadId: squad.id,
  customer: { phoneNumber: '+1234567890' }
});

// Monitor workflow
const squadDetails = await vapiMcpService.getSquad(squad.id);
```

### Workflow Events

The system listens for various workflow events:
- `workflow.started` - Workflow begins
- `agent.activated` - Agent becomes active
- `agent.handoff` - Control transfers between agents
- `workflow.completed` - Workflow ends
- `workflow.error` - Error occurs

## Security Considerations

1. **API Key Protection**: Never expose private API keys in client code
2. **Call Authentication**: Verify caller identity before starting workflows
3. **Data Privacy**: Ensure transcript data is properly secured
4. **Access Control**: Limit workflow access to authorized attorneys
5. **Audit Logging**: Log all workflow activities for compliance

## Performance Metrics

Track key metrics for workflow optimization:
- **Completion Rate**: Percentage of workflows that complete successfully
- **Average Duration**: Time from start to completion
- **Agent Utilization**: How often each agent type is used
- **Client Satisfaction**: Post-workflow feedback scores
- **Handoff Efficiency**: Success rate of agent transitions
