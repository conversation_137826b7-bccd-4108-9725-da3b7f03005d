# CourtListener Citation Lookup MCP Tool

This document provides instructions for integrating the CourtListener Citation Lookup API with your Vapi assistant using the Model Context Protocol (MCP).

## Overview

The CourtListener Citation Lookup API allows you to verify and lookup legal citations in text. This integration provides two main tools:

1. `lookup_legal_citations` - Verify and lookup all legal citations in a block of text
2. `verify_citation` - Quickly verify if a specific legal citation is valid

These tools help combat hallucinations in AI-generated legal content by providing a reliable way to verify citations against CourtListener's database of over 18 million citations.

## Prerequisites

- Node.js 14 or higher
- Vapi account and API key
- CourtListener API key (optional but recommended)

## Installation

1. Install the required dependencies:

```bash
npm install axios @modelcontextprotocol/sdk
```

2. Copy the tool files to your project:
   - `src/tools/courtListenerTool.js`
   - `src/tools/courtListenerMcpTool.js`

## Usage with Vapi MCP Server

### Option 1: Using the Standalone MCP Server

1. Set your CourtListener API key as an environment variable:

```bash
export COURTLISTENER_API_KEY=your_api_key_here
```

2. Run the example script:

```bash
node scripts/courtListenerMcpExample.js
```

3. Configure your Vapi assistant to use this MCP server.

### Option 2: Integrating with an Existing MCP Server

If you already have an MCP server, you can add the CourtListener tool to it:

```javascript
const { McpServer } = require('@modelcontextprotocol/sdk/server/mcp.js');
const { registerCourtListenerTool } = require('./src/tools/courtListenerMcpTool');

// Create your MCP server
const server = new McpServer({
  name: "your-mcp-server",
  version: "1.0.0",
});

// Register your existing tools
// ...

// Register the CourtListener tool
const courtListenerApiKey = process.env.COURTLISTENER_API_KEY || null;
registerCourtListenerTool(server, courtListenerApiKey);

// Connect your server
// ...
```

### Option 3: Using with Claude Desktop

1. Create a configuration file for Claude Desktop:

```json
{
  "mcpServers": {
    "courtlistener-mcp": {
      "command": "node",
      "args": [
        "scripts/courtListenerMcpExample.js"
      ],
      "env": {
        "COURTLISTENER_API_KEY": "your_api_key_here"
      }
    }
  }
}
```

2. Add this configuration to your Claude Desktop settings.

## Configuring Your Vapi Assistant

To configure your Vapi assistant to use the CourtListener tools, you'll need to:

1. Create or update your assistant with instructions on how to use the tools
2. Ensure your assistant has access to the MCP server

### Example Assistant Instructions

Here's an example of instructions you can add to your assistant:

```
You have access to the CourtListener Citation Lookup API through the following tools:

1. lookup_legal_citations - Use this tool to verify all legal citations in a block of text
2. verify_citation - Use this tool to quickly check if a specific citation is valid

When discussing legal cases or citing legal precedents, always verify citations using these tools before presenting them as facts. This helps prevent hallucinations and ensures accuracy.

Example usage:
- When a user asks about a specific case, use verify_citation to confirm the citation
- When analyzing a legal document, use lookup_legal_citations to verify all citations in the text
```

## Tool Descriptions

### lookup_legal_citations

Verifies and looks up legal citations in text using the CourtListener API.

**Parameters:**
- `text` (string, required): The text containing legal citations to verify (up to 64,000 characters)
- `format_results` (boolean, optional, default: true): Whether to format the results for human readability

**Example Response:**
```
Citation Verification Results:

Citation 1: "410 U.S. 113"
Normalized: 410 U.S. 113
Status: Valid ✓
Case: Roe v. Wade
Court: Supreme Court of the United States
Date: 1973-01-22
Link: https://www.courtlistener.com/opinion/108713/roe-v-wade/
```

### verify_citation

Quickly verifies if a specific legal citation is valid.

**Parameters:**
- `citation` (string, required): The legal citation to verify (e.g., "410 U.S. 113")

**Example Response:**
```
✓ Valid citation: "410 U.S. 113"
Case: Roe v. Wade
Court: Supreme Court of the United States
Date: 1973-01-22
Link: https://www.courtlistener.com/opinion/108713/roe-v-wade/
```

## Error Handling

The tools include error handling for common issues:

- Invalid citations
- API connection problems
- Rate limiting
- Authentication errors

Errors are returned in a structured format that your assistant can understand and communicate to users.

## Security Considerations

- The CourtListener API may require authentication for higher rate limits
- Consider storing your API key securely and not hardcoding it
- Be aware of rate limits when making frequent requests

## References

- [CourtListener Citation Lookup API Documentation](https://www.courtlistener.com/help/api/rest/v3/citation-lookup/)
- [Free Law Project Blog Post on Citation Lookup API](https://free.law/2024/04/16/citation-lookup-api)
- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
