/**
 * Call Issue Diagnostic Tool
 * 
 * This script diagnoses why Vapi calls aren't starting despite UI progression.
 * Following AI agent best practices: focus on the actual problem, not theoretical solutions.
 */

console.log('[CallDiagnostic] Starting call issue diagnosis...');

// Real-time call state monitoring
let callDiagnostics = {
  vapiInstanceCreated: false,
  apiKeyValid: false,
  assistantIdValid: false,
  callStartAttempted: false,
  callStartSuccessful: false,
  audioPermissions: false,
  networkConnectivity: false,
  errors: []
};

// Monitor Vapi instance creation
const originalVapi = window.Vapi;
if (originalVapi) {
  window.Vapi = function(apiKey) {
    console.log('[CallDiagnostic] ✅ Vapi constructor called with key:', apiKey?.substring(0, 8) + '...');
    callDiagnostics.vapiInstanceCreated = true;
    callDiagnostics.apiKeyValid = !!apiKey && apiKey.length > 10;
    
    const instance = new originalVapi(apiKey);
    
    // Monitor call start attempts
    const originalStart = instance.start;
    instance.start = function(assistantId, options) {
      console.log('[CallDiagnostic] 🚀 Call start attempted:', { assistantId, options });
      callDiagnostics.callStartAttempted = true;
      callDiagnostics.assistantIdValid = !!assistantId;
      
      try {
        const result = originalStart.call(this, assistantId, options);
        console.log('[CallDiagnostic] ✅ Call start returned:', result);
        callDiagnostics.callStartSuccessful = true;
        return result;
      } catch (error) {
        console.error('[CallDiagnostic] ❌ Call start failed:', error);
        callDiagnostics.errors.push(`Call start failed: ${error.message}`);
        throw error;
      }
    };
    
    // Monitor events
    const originalOn = instance.on;
    instance.on = function(event, callback) {
      console.log('[CallDiagnostic] 📡 Event listener added:', event);
      
      const wrappedCallback = function(...args) {
        console.log(`[CallDiagnostic] 🎯 Event fired: ${event}`, args);
        
        if (event === 'call-start') {
          callDiagnostics.callStartSuccessful = true;
        }
        
        if (event === 'error') {
          callDiagnostics.errors.push(`Vapi error: ${args[0]?.message || args[0]}`);
        }
        
        return callback.apply(this, args);
      };
      
      return originalOn.call(this, event, wrappedCallback);
    };
    
    return instance;
  };
  
  // Copy static properties
  Object.setPrototypeOf(window.Vapi, originalVapi);
  Object.assign(window.Vapi, originalVapi);
}

// Check audio permissions
async function checkAudioPermissions() {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    console.log('[CallDiagnostic] ✅ Audio permissions granted');
    callDiagnostics.audioPermissions = true;
    stream.getTracks().forEach(track => track.stop());
  } catch (error) {
    console.error('[CallDiagnostic] ❌ Audio permissions denied:', error);
    callDiagnostics.errors.push(`Audio permissions: ${error.message}`);
  }
}

// Check network connectivity to Vapi
async function checkNetworkConnectivity() {
  try {
    const response = await fetch('https://api.vapi.ai/health', { method: 'HEAD' });
    console.log('[CallDiagnostic] ✅ Network connectivity to Vapi:', response.status);
    callDiagnostics.networkConnectivity = response.ok;
  } catch (error) {
    console.error('[CallDiagnostic] ❌ Network connectivity failed:', error);
    callDiagnostics.errors.push(`Network: ${error.message}`);
  }
}

// Monitor DOM for call UI changes
function monitorCallUI() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) { // Element node
            // Look for call-related UI elements
            if (node.classList?.contains('call-active') || 
                node.classList?.contains('call-connecting') ||
                node.textContent?.includes('Connecting') ||
                node.textContent?.includes('Call active')) {
              console.log('[CallDiagnostic] 🎨 Call UI state changed:', {
                className: node.className,
                textContent: node.textContent?.substring(0, 50)
              });
            }
          }
        });
      }
      
      if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
        const target = mutation.target;
        if (target.className?.includes('call')) {
          console.log('[CallDiagnostic] 🎨 Call UI class changed:', target.className);
        }
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['class']
  });
}

// Generate diagnostic report
function generateDiagnosticReport() {
  const report = {
    timestamp: new Date().toISOString(),
    ...callDiagnostics,
    recommendations: []
  };
  
  // Add specific recommendations based on findings
  if (!report.vapiInstanceCreated) {
    report.recommendations.push('❌ Vapi instance not created - check if Vapi SDK is loaded');
  }
  
  if (!report.apiKeyValid) {
    report.recommendations.push('❌ Invalid API key - verify VITE_VAPI_PUBLIC_KEY');
  }
  
  if (!report.assistantIdValid) {
    report.recommendations.push('❌ Invalid assistant ID - verify assistant configuration');
  }
  
  if (!report.audioPermissions) {
    report.recommendations.push('❌ Audio permissions required - user must grant microphone access');
  }
  
  if (!report.networkConnectivity) {
    report.recommendations.push('❌ Network connectivity issue - check internet connection');
  }
  
  if (report.callStartAttempted && !report.callStartSuccessful) {
    report.recommendations.push('❌ Call start failed - check console for specific error');
  }
  
  if (report.errors.length > 0) {
    report.recommendations.push(`❌ Errors detected: ${report.errors.join(', ')}`);
  }
  
  return report;
}

// Auto-run diagnostics
async function runDiagnostics() {
  console.log('[CallDiagnostic] 🔍 Running comprehensive call diagnostics...');
  
  // Start monitoring
  monitorCallUI();
  
  // Check prerequisites
  await checkAudioPermissions();
  await checkNetworkConnectivity();
  
  // Wait a bit for any call attempts
  setTimeout(() => {
    const report = generateDiagnosticReport();
    console.log('[CallDiagnostic] 📊 DIAGNOSTIC REPORT:', report);
    
    // Store report globally for easy access
    window.callDiagnosticReport = report;
    
    // Show summary
    console.log('[CallDiagnostic] 📋 SUMMARY:');
    if (report.recommendations.length === 0) {
      console.log('✅ No issues detected - calls should be working');
    } else {
      console.log('❌ Issues found:');
      report.recommendations.forEach(rec => console.log('  ' + rec));
    }
    
    console.log('[CallDiagnostic] 💡 To get the full report: window.callDiagnosticReport');
  }, 5000);
}

// Start diagnostics when page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', runDiagnostics);
} else {
  runDiagnostics();
}

// Expose manual diagnostic function
window.diagnoseCallIssue = function() {
  console.log('[CallDiagnostic] 🔄 Running manual diagnostics...');
  return generateDiagnosticReport();
};

console.log('[CallDiagnostic] 🛠️ Call diagnostic tool loaded');
console.log('[CallDiagnostic] 💡 Run window.diagnoseCallIssue() anytime for current status');
