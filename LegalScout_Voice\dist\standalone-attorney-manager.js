/**
 * Standalone Attorney Manager
 *
 * A completely standalone implementation of the attorney state manager
 * that doesn't depend on React or any other library.
 *
 * Enhanced with Vapi configuration integration.
 */

(function() {
  console.log('[StandaloneAttorneyManager] Initializing...');

  // UUID validation regex
  const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

  // Storage keys
  const STORAGE_KEYS = {
    ATTORNEY: 'attorney',
    ATTORNEY_ID: 'attorney_id',
    ATTORNEY_VERSION: 'attorney_version',
    LAST_SYNC: 'attorney_last_sync',
    VAPI_CONFIG: 'vapi_config',
    VAPI_CONFIG_TIMESTAMP: 'vapi_config_timestamp'
  };

  // Vapi configuration cache
  let vapiConfigCache = null;

  /**
   * Standalone Attorney Manager
   */
  class StandaloneAttorneyManager {
    constructor() {
      this.attorney = null;
      this.isLoading = false;
      this.isSaving = false;
      this.isSyncing = false;
      this.lastError = null;
      this.subscribers = [];
      this.initialized = false;

      // Initialize immediately
      this.initialize();
    }

    /**
     * Initialize the manager
     */
    async initialize() {
      console.log('[StandaloneAttorneyManager] Initializing...');

      if (this.initialized) {
        console.log('[StandaloneAttorneyManager] Already initialized');
        return;
      }

      try {
        // Try to fetch Vapi configuration
        try {
          await this.fetchVapiConfig();
        } catch (configError) {
          console.warn('[StandaloneAttorneyManager] Failed to fetch Vapi configuration:', configError);
          // Use default config
          vapiConfigCache = this.getDefaultVapiConfig();
        }

        // Load attorney from localStorage
        this.loadFromLocalStorage();

        // If no attorney was loaded, create a default one
        if (!this.attorney) {
          console.log('[StandaloneAttorneyManager] No attorney found, creating default');
          this.attorney = this.createDefaultAttorney();
          this.saveToLocalStorage(this.attorney);
        }

        this.initialized = true;
        console.log('[StandaloneAttorneyManager] Initialization complete');
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Initialization failed:', error);
        this.lastError = error;
      }
    }

    /**
     * Validate a UUID
     * @param {string} uuid - The UUID to validate
     * @returns {boolean} Whether the UUID is valid
     */
    isValidUUID(uuid) {
      if (!uuid || typeof uuid !== 'string') return false;
      return UUID_REGEX.test(uuid);
    }

    /**
     * Generate a fallback UUID
     * @returns {string} A valid UUID
     */
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }

    /**
     * Fetch Vapi configuration using MCP
     * @returns {Promise<Object>} The Vapi configuration
     */
    async fetchVapiConfig() {
      try {
        // Check if we have a cached config
        const cachedConfig = localStorage.getItem(STORAGE_KEYS.VAPI_CONFIG);
        const cachedTimestamp = localStorage.getItem(STORAGE_KEYS.VAPI_CONFIG_TIMESTAMP);

        // Use cached config if it's less than 1 hour old
        if (cachedConfig && cachedTimestamp) {
          const now = Date.now();
          const timestamp = parseInt(cachedTimestamp, 10);

          if (now - timestamp < 60 * 60 * 1000) { // 1 hour
            console.log('[StandaloneAttorneyManager] Using cached Vapi configuration');
            try {
              vapiConfigCache = JSON.parse(cachedConfig);
              return vapiConfigCache;
            } catch (parseError) {
              console.warn('[StandaloneAttorneyManager] Error parsing cached Vapi configuration:', parseError);
              // Continue to fetch new config
            }
          }
        }

        // Check if MCP is available
        if (window.mcp) {
          try {
            console.log('[StandaloneAttorneyManager] Fetching Vapi configuration via MCP');

            // Get assistants via MCP
            const assistants = await window.mcp.invoke('list_assistants_vapi-mcp-server', {});

            // Extract configuration from assistants
            if (assistants && assistants.length > 0) {
              const config = this.extractVapiConfig(assistants);

              // Cache the config
              localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG, JSON.stringify(config));
              localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG_TIMESTAMP, Date.now().toString());

              vapiConfigCache = config;

              return config;
            }
          } catch (mcpError) {
            console.warn('[StandaloneAttorneyManager] Error fetching Vapi configuration via MCP:', mcpError);
          }
        }

        // Fall back to default config
        console.log('[StandaloneAttorneyManager] Using default Vapi configuration');
        const config = this.getDefaultVapiConfig();

        // Cache the config
        localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG, JSON.stringify(config));
        localStorage.setItem(STORAGE_KEYS.VAPI_CONFIG_TIMESTAMP, Date.now().toString());

        vapiConfigCache = config;

        return config;
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error fetching Vapi configuration:', error);

        // Return default config if we have no cache
        if (!vapiConfigCache) {
          vapiConfigCache = this.getDefaultVapiConfig();
        }

        return vapiConfigCache;
      }
    }

    /**
     * Extract Vapi configuration from assistants
     * @param {Array} assistants - The assistants
     * @returns {Object} The Vapi configuration
     */
    extractVapiConfig(assistants) {
      try {
        // Default configuration
        const config = this.getDefaultVapiConfig();

        // If no assistants, return default
        if (!assistants || !assistants.length) {
          return config;
        }

        // Map to track unique providers and voices
        const providers = new Map();
        const voiceCounts = {};

        // Process each assistant
        assistants.forEach(assistant => {
          if (assistant.voice && assistant.voice.provider && assistant.voice.voiceId) {
            const provider = assistant.voice.provider;
            const voiceId = assistant.voice.voiceId;

            // Track voice counts for finding the most common
            const key = `${provider}/${voiceId}`;
            voiceCounts[key] = (voiceCounts[key] || 0) + 1;

            // Add provider if not already in map
            if (!providers.has(provider)) {
              providers.set(provider, new Map());
            }

            // Add voice if not already in map
            const voices = providers.get(provider);
            if (!voices.has(voiceId)) {
              voices.set(voiceId, {
                id: voiceId,
                name: voiceId // Use ID as name for now
              });
            }
          }
        });

        // Get the most common voice
        let mostCommonVoice = null;
        let maxCount = 0;

        Object.entries(voiceCounts).forEach(([key, count]) => {
          if (count > maxCount) {
            maxCount = count;
            mostCommonVoice = key;
          }
        });

        // Update default voice if we found a common one
        if (mostCommonVoice) {
          const [provider, voiceId] = mostCommonVoice.split('/');
          config.defaultVoice = { provider, voiceId };
        }

        // Convert maps to arrays for voice providers
        const voiceProviders = [];

        for (const [providerId, voices] of providers.entries()) {
          // Skip if no voices
          if (voices.size === 0) continue;

          // Find existing provider in config
          const existingProvider = config.voiceProviders.find(p => p.id === providerId);

          // Create provider object
          const provider = {
            id: providerId,
            name: existingProvider ? existingProvider.name : providerId,
            voices: Array.from(voices.values())
          };

          // Add to providers array
          voiceProviders.push(provider);
        }

        // If we found providers, use them
        if (voiceProviders.length > 0) {
          config.voiceProviders = [
            ...voiceProviders,
            ...config.voiceProviders.filter(p => !voiceProviders.some(vp => vp.id === p.id))
          ];
        }

        return config;
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error extracting Vapi config:', error);
        return this.getDefaultVapiConfig();
      }
    }

    /**
     * Get default Vapi configuration
     * @returns {Object} The default Vapi configuration
     */
    getDefaultVapiConfig() {
      return {
        voiceProviders: [
          {
            id: '11labs',
            name: 'Eleven Labs',
            voices: [
              { id: 'sarah', name: 'Sarah' },
              { id: 'josh', name: 'Josh' },
              { id: 'rachel', name: 'Rachel' },
              { id: 'adam', name: 'Adam' }
            ]
          },
          {
            id: 'openai',
            name: 'OpenAI',
            voices: [
              { id: 'alloy', name: 'Alloy' },
              { id: 'echo', name: 'Echo' },
              { id: 'fable', name: 'Fable' },
              { id: 'onyx', name: 'Onyx' },
              { id: 'nova', name: 'Nova' },
              { id: 'shimmer', name: 'Shimmer' }
            ]
          }
        ],
        defaultVoice: {
          provider: '11labs',
          voiceId: 'sarah'
        }
      };
    }

    /**
     * Create a default attorney object
     * @param {Object} overrides - Properties to override in the default attorney
     * @returns {Object} A default attorney object
     */
    createDefaultAttorney(overrides = {}) {
      // Get default voice from Vapi config
      const defaultVoice = vapiConfigCache?.defaultVoice || { provider: '11labs', voiceId: 'sarah' };

      const defaultAttorney = {
        id: this.generateUUID(),
        subdomain: 'default',
        firm_name: 'Your Law Firm',
        name: 'Your Name',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        voice_provider: defaultVoice.provider,
        voice_id: defaultVoice.voiceId,
        welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
        information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
        vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
        vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
      };

      return { ...defaultAttorney, ...overrides };
    }

    /**
     * Load attorney from localStorage
     * @returns {Object|null} The attorney object or null if not found
     */
    loadFromLocalStorage() {
      try {
        // Try to get from localStorage
        const storedAttorney = localStorage.getItem(STORAGE_KEYS.ATTORNEY);
        if (storedAttorney) {
          const parsedAttorney = JSON.parse(storedAttorney);

          // Validate the attorney data
          if (parsedAttorney && this.isValidUUID(parsedAttorney.id)) {
            console.log('[StandaloneAttorneyManager] Loaded attorney from localStorage:', parsedAttorney.id);
            this.attorney = parsedAttorney;
            this.notifySubscribers();
            return parsedAttorney;
          }
        }

        // If not found or invalid, try to get just the ID
        const storedAttorneyId = localStorage.getItem(STORAGE_KEYS.ATTORNEY_ID);
        if (storedAttorneyId && this.isValidUUID(storedAttorneyId)) {
          console.log('[StandaloneAttorneyManager] Found attorney ID in localStorage:', storedAttorneyId);

          // Create a minimal attorney object with the ID
          const minimalAttorney = this.createDefaultAttorney({ id: storedAttorneyId });
          this.attorney = minimalAttorney;
          this.saveToLocalStorage(minimalAttorney);
          this.notifySubscribers();
          return minimalAttorney;
        }

        return null;
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error loading from localStorage:', error);
        return null;
      }
    }

    /**
     * Save attorney to localStorage
     * @param {Object} attorney - The attorney to save
     */
    saveToLocalStorage(attorney) {
      if (!attorney || !this.isValidUUID(attorney.id)) {
        console.warn('[StandaloneAttorneyManager] Cannot save invalid attorney to localStorage');
        return;
      }

      try {
        // Save full attorney object
        localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(attorney));

        // Save ID separately for redundancy
        localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, attorney.id);

        // Save version and timestamp
        localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());

        console.log('[StandaloneAttorneyManager] Saved attorney to localStorage:', attorney.id);
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error saving to localStorage:', error);
      }
    }

    /**
     * Update attorney
     * @param {Object} updates - The updates to apply to the attorney
     * @returns {Object} The updated attorney
     */
    updateAttorney(updates) {
      if (!this.attorney) {
        console.warn('[StandaloneAttorneyManager] Cannot update attorney: No attorney loaded');
        return null;
      }

      try {
        // Update the attorney
        const updatedAttorney = {
          ...this.attorney,
          ...updates,
          updated_at: new Date().toISOString()
        };

        // Ensure ID doesn't change
        updatedAttorney.id = this.attorney.id;

        // Save to localStorage
        this.saveToLocalStorage(updatedAttorney);

        // Update local state
        this.attorney = updatedAttorney;

        // Notify subscribers
        this.notifySubscribers();

        // Check if we need to sync with Vapi
        const vapiRelatedFields = [
          'firm_name', 'name', 'welcome_message', 'vapi_instructions',
          'voice_provider', 'voice_id'
        ];

        const needsVapiSync = Object.keys(updates).some(key =>
          vapiRelatedFields.includes(key)
        );

        if (needsVapiSync && updatedAttorney.vapi_assistant_id) {
          // Schedule a sync with Vapi
          setTimeout(() => {
            this.syncWithVapi(updatedAttorney).catch(error => {
              console.error('[StandaloneAttorneyManager] Error syncing with Vapi:', error);
            });
          }, 0);
        }

        return updatedAttorney;
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error updating attorney:', error);
        this.lastError = error;
        return null;
      }
    }

    /**
     * Sync attorney with Vapi using MCP
     * @param {Object} attorney - The attorney to sync (defaults to this.attorney)
     * @returns {Promise<Object>} The result of the sync
     */
    async syncWithVapi(attorney = null) {
      try {
        this.isSyncing = true;

        // Use provided attorney or current attorney
        const attorneyToSync = attorney || this.attorney;

        if (!attorneyToSync) {
          throw new Error('No attorney to sync');
        }

        // Check if MCP is available
        if (!window.mcp) {
          console.warn('[StandaloneAttorneyManager] MCP not available, falling back to default assistant');

          // Fall back to using a default assistant ID
          if (!attorneyToSync.vapi_assistant_id) {
            const updatedAttorney = {
              ...attorneyToSync,
              vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', // Default assistant ID
              updated_at: new Date().toISOString()
            };

            // Save to localStorage
            this.saveToLocalStorage(updatedAttorney);

            // Update local state
            this.attorney = updatedAttorney;

            // Notify subscribers
            this.notifySubscribers();
          }

          this.isSyncing = false;
          return { action: 'fallback', error: 'MCP not available' };
        }

        if (!attorneyToSync.vapi_assistant_id) {
          console.log('[StandaloneAttorneyManager] Creating new Vapi assistant via MCP');

          try {
            // Create a new assistant using MCP
            const assistantData = {
              name: `${attorneyToSync.name}'s Legal Assistant`,
              instructions: attorneyToSync.vapi_instructions || `You are a legal assistant for ${attorneyToSync.firm_name || 'a law firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
              firstMessage: attorneyToSync.welcome_message || 'Hello! I\'m your legal assistant. How can I help you today?',
              voice: {
                provider: attorneyToSync.voice_provider || '11labs',
                voiceId: attorneyToSync.voice_id || 'sarah'
              },
              llm: {
                provider: 'openai',
                model: 'gpt-4o-mini'
              }
            };

            // Call MCP to create assistant
            const newAssistant = await window.mcp.invoke('create_assistant_vapi-mcp-server', assistantData);

            // Update the attorney with the new assistant ID
            const updatedAttorney = {
              ...attorneyToSync,
              vapi_assistant_id: newAssistant.id,
              updated_at: new Date().toISOString()
            };

            // Save to localStorage
            this.saveToLocalStorage(updatedAttorney);

            // Update local state
            this.attorney = updatedAttorney;

            // Notify subscribers
            this.notifySubscribers();

            // Also save to Supabase if we have a user ID
            if (updatedAttorney.user_id) {
              this.saveAssistantMappingToSupabase(updatedAttorney.id, newAssistant.id, updatedAttorney.user_id)
                .catch(error => {
                  console.error('[StandaloneAttorneyManager] Error saving assistant mapping to Supabase:', error);
                });
            }

            console.log('[StandaloneAttorneyManager] Created new Vapi assistant via MCP:', newAssistant.id);

            this.isSyncing = false;
            return { action: 'created', assistant: newAssistant };
          } catch (createError) {
            console.error('[StandaloneAttorneyManager] Error creating Vapi assistant via MCP:', createError);

            // Fall back to using a default assistant ID
            const updatedAttorney = {
              ...attorneyToSync,
              vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c', // Default assistant ID
              updated_at: new Date().toISOString()
            };

            // Save to localStorage
            this.saveToLocalStorage(updatedAttorney);

            // Update local state
            this.attorney = updatedAttorney;

            // Notify subscribers
            this.notifySubscribers();

            this.isSyncing = false;
            return { action: 'fallback', error: createError.message };
          }
        } else {
          console.log('[StandaloneAttorneyManager] Updating existing Vapi assistant via MCP:', attorneyToSync.vapi_assistant_id);

          try {
            // First get the current assistant to check if it exists
            const existingAssistant = await window.mcp.invoke('get_assistant_vapi-mcp-server', {
              assistantId: attorneyToSync.vapi_assistant_id
            }).catch(() => null);

            // If assistant doesn't exist, create a new one
            if (!existingAssistant) {
              console.log('[StandaloneAttorneyManager] Assistant not found, creating new one');
              return this.syncWithVapi({
                ...attorneyToSync,
                vapi_assistant_id: null
              });
            }

            // For now, we can't update assistants via MCP, so we'll just return the existing one
            // In the future, when MCP supports updating assistants, we can implement that here

            console.log('[StandaloneAttorneyManager] Using existing Vapi assistant:', existingAssistant.id);

            this.isSyncing = false;
            return { action: 'existing', assistant: existingAssistant };
          } catch (updateError) {
            console.error('[StandaloneAttorneyManager] Error updating Vapi assistant via MCP:', updateError);
            this.isSyncing = false;
            return { action: 'failed', error: updateError.message };
          }
        }
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error syncing with Vapi:', error);
        this.lastError = error;
        this.isSyncing = false;
        return { action: 'error', error: error.message };
      }
    },

    /**
     * Save assistant mapping to Supabase
     * @param {string} attorneyId - The attorney ID
     * @param {string} assistantId - The Vapi assistant ID
     * @param {string} userId - The user ID
     * @returns {Promise<Object>} The result of the operation
     */
    async saveAssistantMappingToSupabase(attorneyId, assistantId, userId) {
      try {
        // Check if Supabase is available
        if (!window.supabase) {
          console.warn('[StandaloneAttorneyManager] Supabase not available, skipping assistant mapping save');
          return { success: false, error: 'Supabase not available' };
        }

        // Save the mapping to Supabase
        const { data, error } = await window.supabase
          .from('attorney_assistants')
          .upsert({
            attorney_id: attorneyId,
            assistant_id: assistantId,
            user_id: userId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, {
            onConflict: 'attorney_id',
            returning: 'minimal'
          });

        if (error) {
          throw error;
        }

        return { success: true, data };
      } catch (error) {
        console.error('[StandaloneAttorneyManager] Error saving assistant mapping to Supabase:', error);
        return { success: false, error: error.message };
      }
    }

    /**
     * Subscribe to attorney state changes
     * @param {Function} callback - The callback to call when state changes
     * @returns {Function} A function to unsubscribe
     */
    subscribe(callback) {
      if (typeof callback !== 'function') {
        console.warn('[StandaloneAttorneyManager] Cannot subscribe with non-function callback');
        return () => {};
      }

      this.subscribers.push(callback);

      // Call immediately with current state
      if (this.attorney) {
        callback(this.attorney);
      }

      // Return unsubscribe function
      return () => this.unsubscribe(callback);
    }

    /**
     * Unsubscribe from attorney state changes
     * @param {Function} callback - The callback to unsubscribe
     */
    unsubscribe(callback) {
      this.subscribers = this.subscribers.filter(cb => cb !== callback);
    }

    /**
     * Notify all subscribers of state changes
     */
    notifySubscribers() {
      if (!this.attorney) return;

      this.subscribers.forEach(callback => {
        try {
          callback(this.attorney);
        } catch (error) {
          console.error('[StandaloneAttorneyManager] Error in subscriber callback:', error);
        }
      });
    }
  }

  // Create global instance
  window.standaloneAttorneyManager = new StandaloneAttorneyManager();

  console.log('[StandaloneAttorneyManager] Global instance created');
})();
