/**
 * URL Submit Fix
 *
 * This script ensures that the submit button appears after entering a valid URL
 * and that clicking it properly triggers the URL submission process.
 */

(function() {
  // Function to fix URL submission
  function fixUrlSubmission() {
    // Get the URL input field
    const firmUrlInput = document.getElementById('firmUrl');
    if (!firmUrlInput) return;

    // Function to validate URL
    function validateUrl(url) {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    }

    // Function to show the submit button
    function showSubmitButton() {
      const url = firmUrlInput.value;
      const isValid = validateUrl(url);

      console.log('URL validation:', url, isValid);

      // Find the parent container (start-option)
      let container = firmUrlInput.closest('.start-option');
      if (!container) {
        container = firmUrlInput.closest('.glass-effect');
      }
      if (!container) {
        container = firmUrlInput.parentElement;
        while (container && !container.classList.contains('input-group')) {
          container = container.parentElement;
        }
        if (container) {
          container = container.parentElement;
        }
      }

      if (!container) {
        console.error('Could not find container for URL input');
        return;
      }

      // Check if a submit button already exists
      let submitButton = container.querySelector('.begin-config, .modern-button, button[type="submit"]');

      // We don't need to create a duplicate button anymore
      // Just check if the Auto-Configure button exists
      const autoConfigButton = Array.from(document.querySelectorAll('button')).find(
        button => button.textContent.includes('Auto-Configure')
      );

      if (isValid && !autoConfigButton) {
        console.log('Auto-Configure button not found, but URL is valid');
        // Instead of creating a duplicate button, we'll just log this
        // and rely on the auto-configure-fix.js script to handle it
      }
    }

    // Check immediately
    showSubmitButton();

    // Add input event listener to check when URL changes
    firmUrlInput.addEventListener('input', showSubmitButton);

    // Also check periodically
    setInterval(showSubmitButton, 1000);
  }

  // Run the fix when the DOM is loaded
  document.addEventListener('DOMContentLoaded', fixUrlSubmission);

  // Also run it immediately in case the DOM is already loaded
  if (document.readyState === 'interactive' || document.readyState === 'complete') {
    fixUrlSubmission();
  }

  // Run it again after a short delay to ensure it catches dynamically added elements
  setTimeout(fixUrlSubmission, 1000);
  setTimeout(fixUrlSubmission, 2000);
  setTimeout(fixUrlSubmission, 3000);
})();
