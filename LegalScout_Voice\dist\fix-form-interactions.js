/**
 * Fix for S(...).catch is not a function error during form field interactions
 * 
 * This script specifically targets form field interactions that trigger the error.
 */
(function() {
  console.log('[FormInteractionFix] Starting form interaction fix...');
  
  // Global error handler specifically for the S(...).catch error
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
      console.log('[FormInteractionFix] Caught the specific error during form interaction: ' + event.error.message);
      event.preventDefault();
      return false;
    }
  }, true);
  
  // Function to ensure all input fields have proper error handling
  function patchFormFields() {
    console.log('[FormInteractionFix] Patching form fields...');
    
    // Find all input fields
    const inputFields = document.querySelectorAll('input, textarea, select');
    
    inputFields.forEach(field => {
      if (field._formInteractionFixPatched) return;
      
      field._formInteractionFixPatched = true;
      
      // Add event listeners for common input events
      ['input', 'change', 'focus', 'blur', 'keydown', 'keyup'].forEach(eventType => {
        field.addEventListener(eventType, function(event) {
          try {
            // Wrap the event in a try-catch to prevent errors from bubbling up
            // This allows the event to proceed normally but catches any errors
            
            // If there's an error handler on the window, make sure it's called
            if (window._ensureCatchMethod) {
              // Ensure any promises returned have a catch method
              setTimeout(() => {
                // Look for any promises in the event path
                event.path && event.path.forEach(element => {
                  if (element && element._promise) {
                    window._ensureCatchMethod(element._promise);
                  }
                });
              }, 0);
            }
          } catch (error) {
            console.error('[FormInteractionFix] Error in form field event handler:', error);
          }
        }, true); // Use capture to ensure our handler runs first
      });
      
      console.log('[FormInteractionFix] Patched form field:', field);
    });
  }
  
  // Function to patch the Qr component specifically
  function patchQrComponent() {
    console.log('[FormInteractionFix] Looking for Qr component to patch...');
    
    // Method 1: Direct patching if Qr is in the global scope
    if (typeof window.Qr === 'function') {
      console.log('[FormInteractionFix] Found global Qr function, patching it');
      patchFunction('Qr', window, 'Qr');
    }
    
    // Method 2: Look for Qr in common React component locations
    const possibleContainers = ['components', 'Components', 'containers', 'Containers', 'pages', 'Pages'];
    possibleContainers.forEach(container => {
      if (window[container] && typeof window[container].Qr === 'function') {
        console.log(`[FormInteractionFix] Found Qr function in ${container}, patching it`);
        patchFunction('Qr', window[container], 'Qr');
      }
    });
    
    // Method 3: Add a global handler for S function results during form interactions
    window._handleFormInteractionResult = function(result) {
      if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
        console.log('[FormInteractionFix] Adding catch method to form interaction result');
        result.catch = function(onRejected) {
          return Promise.resolve(result).catch(onRejected);
        };
      }
      return result;
    };
  }
  
  // Helper function to patch a specific function
  function patchFunction(functionName, object, propertyName) {
    if (!object || typeof object[propertyName] !== 'function') return;
    
    // Check if already patched to avoid double patching
    if (object[propertyName]._formInteractionFixPatched) {
      console.log(`[FormInteractionFix] ${functionName} already patched, skipping`);
      return;
    }
    
    const originalFunction = object[propertyName];
    
    // Create a wrapped version of the function
    object[propertyName] = function(...args) {
      try {
        const result = originalFunction.apply(this, args);
        
        // Ensure the result has a catch method
        if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
          console.log(`[FormInteractionFix] Adding catch method to ${functionName} result`);
          result.catch = function(onRejected) {
            return Promise.resolve(result).catch(onRejected);
          };
        }
        
        return result;
      } catch (error) {
        console.error(`[FormInteractionFix] Error in ${functionName}:`, error);
        // Return a safe value that has a catch method
        return Promise.resolve(null);
      }
    };
    
    // Copy properties from original function
    Object.assign(object[propertyName], originalFunction);
    object[propertyName]._formInteractionFixPatched = true;
    
    console.log(`[FormInteractionFix] Successfully patched ${functionName}`);
  }
  
  // Function to observe DOM changes and patch new form fields
  function observeDOM() {
    console.log('[FormInteractionFix] Setting up DOM observer...');
    
    // Create a MutationObserver to watch for new form fields
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if any of the added nodes are form fields or contain form fields
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1) { // Element node
              // Check if the node itself is a form field
              if (node.tagName === 'INPUT' || node.tagName === 'TEXTAREA' || node.tagName === 'SELECT') {
                if (!node._formInteractionFixPatched) {
                  console.log('[FormInteractionFix] Found new form field, patching it');
                  patchFormFields();
                }
              }
              
              // Check if the node contains form fields
              const fields = node.querySelectorAll('input, textarea, select');
              if (fields.length > 0) {
                console.log('[FormInteractionFix] Found new container with form fields, patching them');
                patchFormFields();
              }
            }
          });
        }
      });
    });
    
    // Start observing the document
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
    
    console.log('[FormInteractionFix] DOM observer set up');
  }
  
  // Function to patch the attorney name field specifically
  function patchAttorneyNameField() {
    console.log('[FormInteractionFix] Looking for attorney name field...');
    
    // Common selectors for the attorney name field
    const possibleSelectors = [
      'input[name="name"]',
      'input[name="attorney_name"]',
      'input[name="attorneyName"]',
      'input[id="name"]',
      'input[id="attorney_name"]',
      'input[id="attorneyName"]',
      'input[placeholder*="name"]',
      'input[aria-label*="name"]'
    ];
    
    // Try each selector
    possibleSelectors.forEach(selector => {
      const fields = document.querySelectorAll(selector);
      if (fields.length > 0) {
        console.log(`[FormInteractionFix] Found attorney name field with selector: ${selector}`);
        fields.forEach(field => {
          if (!field._attorneyNameFieldPatched) {
            field._attorneyNameFieldPatched = true;
            
            // Add a special handler for this field
            ['input', 'change', 'focus', 'blur', 'keydown', 'keyup'].forEach(eventType => {
              field.addEventListener(eventType, function(event) {
                try {
                  console.log(`[FormInteractionFix] Handling ${eventType} event on attorney name field`);
                  
                  // Ensure any promises have a catch method
                  setTimeout(() => {
                    // Look for any promises in the event path
                    if (window._ensureCatchMethod) {
                      window._ensureCatchMethod(this);
                    }
                  }, 0);
                } catch (error) {
                  console.error('[FormInteractionFix] Error in attorney name field event handler:', error);
                }
              }, true); // Use capture to ensure our handler runs first
            });
            
            console.log('[FormInteractionFix] Patched attorney name field:', field);
          }
        });
      }
    });
  }
  
  // Initialize the fix
  function initialize() {
    // Patch form fields
    patchFormFields();
    
    // Patch the Qr component
    patchQrComponent();
    
    // Set up DOM observer
    observeDOM();
    
    // Patch the attorney name field specifically
    patchAttorneyNameField();
    
    // Set up a periodic check for the attorney name field
    // This is needed because the field might be added dynamically after the page loads
    setInterval(patchAttorneyNameField, 1000);
    
    console.log('[FormInteractionFix] Initialization complete');
  }
  
  // Wait for the DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }
  
  // Also run the fix when the window loads
  window.addEventListener('load', initialize);
  
  console.log('[FormInteractionFix] Form interaction fix setup complete');
})();
