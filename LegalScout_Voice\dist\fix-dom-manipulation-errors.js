/**
 * Fix DOM Manipulation Errors
 * 
 * This script fixes the "removeChild" errors caused by aggressive DOM manipulation
 * in the banner fix scripts. It provides safer alternatives that work with React.
 */

console.log('[FixDomManipulationErrors] Starting fix...');

// Function to safely remove a node
function safeRemoveNode(node) {
  try {
    if (node && node.parentNode && node.parentNode.contains(node)) {
      node.parentNode.removeChild(node);
      return true;
    }
  } catch (error) {
    console.warn('[FixDomManipulationErrors] Safe remove failed:', error);
  }
  return false;
}

// Function to safely replace a node
function safeReplaceNode(oldNode, newNode) {
  try {
    if (oldNode && oldNode.parentNode && oldNode.parentNode.contains(oldNode)) {
      oldNode.parentNode.replaceChild(newNode, oldNode);
      return true;
    }
  } catch (error) {
    console.warn('[FixDomManipulationErrors] Safe replace failed:', error);
  }
  return false;
}

// Override the problematic DOM manipulation methods
function overrideDOMManipulation() {
  // Override removeChild to be safer
  const originalRemoveChild = Node.prototype.removeChild;
  Node.prototype.removeChild = function(child) {
    try {
      // Check if the child is actually a child of this node
      if (!this.contains(child)) {
        console.warn('[FixDomManipulationErrors] Attempted to remove non-child node, ignoring');
        return child; // Return the child as if it was removed
      }
      return originalRemoveChild.call(this, child);
    } catch (error) {
      console.warn('[FixDomManipulationErrors] removeChild error caught:', error);
      return child; // Return the child as if it was removed
    }
  };

  // Override replaceChild to be safer
  const originalReplaceChild = Node.prototype.replaceChild;
  Node.prototype.replaceChild = function(newChild, oldChild) {
    try {
      // Check if the old child is actually a child of this node
      if (!this.contains(oldChild)) {
        console.warn('[FixDomManipulationErrors] Attempted to replace non-child node, appending instead');
        this.appendChild(newChild);
        return oldChild;
      }
      return originalReplaceChild.call(this, newChild, oldChild);
    } catch (error) {
      console.warn('[FixDomManipulationErrors] replaceChild error caught:', error);
      // Try to append the new child instead
      try {
        this.appendChild(newChild);
      } catch (appendError) {
        console.warn('[FixDomManipulationErrors] appendChild fallback failed:', appendError);
      }
      return oldChild;
    }
  };

  console.log('[FixDomManipulationErrors] DOM manipulation methods overridden');
}

// Function to disable aggressive DOM manipulation in banner fixes
function disableAggressiveDOMManipulation() {
  // Override the banner functionality fix to be less aggressive
  if (window.restoreBannerUploadFunctionality) {
    const originalRestore = window.restoreBannerUploadFunctionality;
    window.restoreBannerUploadFunctionality = function() {
      console.log('[FixDomManipulationErrors] Using safe banner restoration');
      // Don't clone and replace nodes, just add event listeners
      try {
        const bannerInputs = document.querySelectorAll('input[type="file"], input[id*="logo"], input[id*="banner"]');
        bannerInputs.forEach(input => {
          // Just add event listeners without replacing the element
          input.addEventListener('change', function(event) {
            console.log('[FixDomManipulationErrors] Banner file selected safely:', event.target.files[0]?.name);
          }, { once: false, passive: true });
        });
      } catch (error) {
        console.warn('[FixDomManipulationErrors] Safe banner restoration failed:', error);
      }
    };
  }

  // Override any functions that clone and replace nodes
  const originalCloneNode = Node.prototype.cloneNode;
  Node.prototype.cloneNode = function(deep) {
    const cloned = originalCloneNode.call(this, deep);
    
    // Mark cloned nodes to avoid aggressive replacement
    if (cloned.nodeType === 1) { // Element node
      cloned.setAttribute('data-cloned', 'true');
    }
    
    return cloned;
  };

  console.log('[FixDomManipulationErrors] Aggressive DOM manipulation disabled');
}

// Function to patch the banner fix scripts to be React-friendly
function patchBannerFixScripts() {
  // Disable the problematic parts of banner fix scripts
  setTimeout(() => {
    try {
      // Look for and disable aggressive DOM manipulation
      const scripts = document.querySelectorAll('script');
      scripts.forEach(script => {
        if (script.src && (script.src.includes('banner') || script.src.includes('fix'))) {
          console.log('[FixDomManipulationErrors] Found banner fix script:', script.src);
        }
      });

      // Override any global functions that might be doing aggressive DOM manipulation
      if (window.patchBannerRemoveHandlers) {
        const originalPatch = window.patchBannerRemoveHandlers;
        window.patchBannerRemoveHandlers = function() {
          console.log('[FixDomManipulationErrors] Using safe banner remove handler patching');
          // Don't clone and replace, just add event listeners
          try {
            const removeButtons = document.querySelectorAll('.remove-logo-button, button');
            removeButtons.forEach(button => {
              const buttonText = (button.textContent || button.innerText || '').toLowerCase();
              if (buttonText.includes('remove') && (buttonText.includes('banner') || buttonText.includes('logo'))) {
                // Add event listener without replacing the element
                button.addEventListener('click', function(event) {
                  console.log('[FixDomManipulationErrors] Banner remove button clicked safely');
                  // Mark banner as removed
                  localStorage.setItem('banner_removal_state', JSON.stringify({
                    isRemoved: true,
                    timestamp: Date.now()
                  }));
                }, { once: false, passive: false });
              }
            });
          } catch (error) {
            console.warn('[FixDomManipulationErrors] Safe banner remove patching failed:', error);
          }
        };
      }

    } catch (error) {
      console.error('[FixDomManipulationErrors] Error patching banner fix scripts:', error);
    }
  }, 1000);
}

// Function to handle React component errors gracefully
function handleReactComponentErrors() {
  // Add a global error handler for React errors
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message) {
      const message = event.error.message;
      
      if (message.includes('removeChild') || message.includes('replaceChild')) {
        console.warn('[FixDomManipulationErrors] DOM manipulation error caught and handled:', message);
        event.preventDefault();
        return false;
      }
    }
  }, true);

  // Add an unhandled promise rejection handler
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.message) {
      const message = event.reason.message;
      
      if (message.includes('removeChild') || message.includes('replaceChild')) {
        console.warn('[FixDomManipulationErrors] DOM manipulation promise rejection caught and handled:', message);
        event.preventDefault();
        return false;
      }
    }
  });

  console.log('[FixDomManipulationErrors] React component error handling set up');
}

// Function to create a safer banner removal system
function createSafeBannerRemovalSystem() {
  // Create a banner state manager that doesn't manipulate DOM
  window.safeBannerManager = {
    isRemoved: false,
    timestamp: null,
    
    markAsRemoved: function() {
      this.isRemoved = true;
      this.timestamp = Date.now();
      localStorage.setItem('banner_removal_state', JSON.stringify({
        isRemoved: true,
        timestamp: this.timestamp
      }));
      console.log('[FixDomManipulationErrors] Banner marked as removed safely');
      
      // Dispatch a custom event instead of manipulating DOM
      window.dispatchEvent(new CustomEvent('bannerRemoved', {
        detail: { timestamp: this.timestamp }
      }));

      // Also trigger upload interface visibility check
      setTimeout(() => {
        if (window.ensureUploadInterfaceVisible) {
          window.ensureUploadInterfaceVisible();
        }
      }, 100);
    },
    
    wasRecentlyRemoved: function() {
      try {
        const stored = localStorage.getItem('banner_removal_state');
        if (stored) {
          const state = JSON.parse(stored);
          return state.isRemoved && (Date.now() - state.timestamp) < 30000;
        }
      } catch (error) {
        console.warn('[FixDomManipulationErrors] Error checking removal state:', error);
      }
      return false;
    },
    
    clear: function() {
      this.isRemoved = false;
      this.timestamp = null;
      localStorage.removeItem('banner_removal_state');
      console.log('[FixDomManipulationErrors] Banner removal state cleared safely');
    }
  };

  // Listen for banner removal events
  window.addEventListener('bannerRemoved', function(event) {
    console.log('[FixDomManipulationErrors] Banner removal event received:', event.detail);
    
    // Update any React components that need to know about banner removal
    // without manipulating DOM directly
    setTimeout(() => {
      const logoImages = document.querySelectorAll('img[class*="logo"], img[alt*="Banner"]');
      logoImages.forEach(img => {
        img.style.display = 'none';
      });
    }, 100);
  });

  console.log('[FixDomManipulationErrors] Safe banner removal system created');
}

// Function to apply all fixes
function applyFixes() {
  try {
    overrideDOMManipulation();
    disableAggressiveDOMManipulation();
    patchBannerFixScripts();
    handleReactComponentErrors();
    createSafeBannerRemovalSystem();
    
    console.log('[FixDomManipulationErrors] All DOM manipulation fixes applied successfully');
  } catch (error) {
    console.error('[FixDomManipulationErrors] Error applying DOM manipulation fixes:', error);
  }
}

// Apply fixes immediately
applyFixes();

// Apply fixes when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFixes);
} else {
  setTimeout(applyFixes, 50);
}

console.log('[FixDomManipulationErrors] DOM manipulation error fix script loaded');
