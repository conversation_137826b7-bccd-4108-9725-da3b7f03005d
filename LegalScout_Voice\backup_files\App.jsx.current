import React, { useState, useEffect, Suspense, useRef, lazy, useCallback, useMemo } from 'react'
import { Routes, Route, useLocation } from 'react-router-dom'
import './App.css'
import CallOverlay from './components/CallOverlay'
import Button from './components/Button/index.jsx'
import { trackUserJourney } from './utils/debugConfig'
import { getAttorneyConfigAsync } from './config/attorneys'
import MapView from './components/MapView'
import AttorneyDossier from './components/AttorneyDossier'
import CallSummary from './components/CallSummary'
import { getCurrentSubdomain } from './utils/subdomainTester'
import Navbar from './components/Navbar.jsx'
import GlobeDossierView from './components/GlobeDossierView.jsx'
import TestSubdomains from './components/TestSubdomains.jsx'
import AnimatedBackground from './components/AnimatedBackground'
import ThemeToggle from './components/ThemeToggle.jsx'
import AboutPage from './pages/AboutPage'
import SimplifiedPreview from "./components/preview/SimplifiedPreview";
import TestPage from './TestPage'
import AdminPage from './pages/AdminPage'
import Header from './components/Header'
import DogPrints from './components/DogPrints'
import AttorneyInfo from './components/AttorneyInfo'
import TextShimmerWave from './components/TextShimmerWave'
import TestButton from './pages/TestButton'
import SendButton from './components/SendButton'
import SimpleDemoPage from './pages/SimpleDemoPage'
import { createLogger } from './utils/logger'
import YouTubeEmbed from './components/YouTubeEmbed'
import VapiApiTester from './components/VapiApiTester'
import { ErrorBoundary } from './utils/ErrorBoundary'
import SimpleVapiCall from './components/SimpleVapiCall'
import useVapiCall from './hooks/useVapiCall'
import { CALL_STATUS } from './constants/vapiConstants'
import VapiTest from './components/VapiTest'
import CallElements from './components/CallElements'
import { vapiService } from './services/vapiService'

// Initialize logger for App component
const logger = createLogger('App');

// Lazy load components
const PreviewPage = lazy(() => import('./pages/PreviewPage'));

// Default practice areas for initialization if needed
const defaultPracticeAreas = {
  'Personal Injury': {
    questions: "I want to know the circumstances of their injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?",
    practiceDescription: "**Our firm specializes in personal injury law**, and we have a proven track record of success in obtaining favorable settlements and verdicts for our clients.\n\n### Our Services:\n- Car accident claims\n- Slip and fall cases\n- Medical malpractice\n- Workplace injuries\n\nWe work on a contingency basis - *you don't pay unless we win*.",
    welcomeMessage: "Welcome to our personal injury consultation. I'm your virtual legal assistant and I'm here to help gather information about your case.",
    informationGathering: "I want to know the circumstances of your injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?"
  },
  'Family Law': {
    questions: "I need to understand the nature of their family law issue. Are they seeking a divorce, child custody, child support, alimony, or a modification of an existing order?",
    practiceDescription: "**Our firm is dedicated to helping families** navigate the complexities of family law. We handle all aspects of divorce, including property division, child custody and visitation.\n\n### Practice Areas:\n- Divorce proceedings\n- Child custody & support\n- Spousal support/alimony\n- Prenuptial agreements\n\n> We approach each case with compassion and understanding during difficult times.",
    welcomeMessage: "Welcome to our family law consultation. I'm your virtual legal assistant and I'm here to help with your family law matters.",
    informationGathering: "I need to understand the nature of your family law issue. Are you seeking a divorce, child custody, child support, alimony, or a modification of an existing order?"
  },
  'Criminal Defense': {
    questions: "I need to know the charges against the client, where they are in the criminal process, and if there are any deadlines or court dates already scheduled.",
    practiceDescription: "## Criminal Defense Experts\n\nOur **experienced criminal defense attorneys** provide aggressive representation for all criminal matters, from misdemeanors to serious felony charges.\n\n### Our Approach:\n1. Thorough case evaluation\n2. Strategic defense planning\n3. Aggressive courtroom advocacy\n4. Pursuit of best possible outcomes\n\n[Contact us](#) immediately if you've been charged with a crime.",
    welcomeMessage: "Thank you for considering our firm for your criminal defense needs. I'm here to gather some initial information about your case.",
    informationGathering: "Please tell me about the charges you're facing and where you are in the legal process. All information is confidential and protected by attorney-client privilege."
  }
};

// DogPrintsWrapper component to conditionally render based on route
const DogPrintsWrapper = ({ isDarkTheme }) => {
  const location = useLocation();
  // Only render DogPrints on the home page
  if (location.pathname === '/') {
    return <DogPrints isDarkTheme={isDarkTheme} />;
  }
  return null;
};

// YouTubeWrapper component to conditionally handle YouTube display based on route and hover state
const YouTubeWrapper = ({ isCallActive = false, isStartButtonHovered = false }) => {
  const location = useLocation();
  const [showYouTube, setShowYouTube] = useState(false);
  const [showMinimizedYouTube, setShowMinimizedYouTube] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [fromDownsized, setFromDownsized] = useState(false);
  const [videoWasViewed, setVideoWasViewed] = useState(false);
  
  useEffect(() => {
    // On about page, show minimized YouTube immediately
    if (location.pathname === '/about') {
      setShowMinimizedYouTube(true);
      setVideoWasViewed(true);
    } else {
      // For home page, reset to default state
      setShowMinimizedYouTube(false);
      setShowYouTube(false);
      setVideoWasViewed(false);
    }
  }, [location.pathname]);
  
  // Listen for dog tracks events and hover state (only on home page)
  useEffect(() => {
    // Only setup event listeners on home page
    if (location.pathname !== '/') return;
    
    // Create a custom event listener for when dog tracks catch up to cursor
    const handleCursorProximity = () => {
      if (videoWasViewed) {
        setShowMinimizedYouTube(true);
      } else {
        setShowYouTube(true);
      }
    };

    // Create a custom event listener for when video is viewed
    const handleVideoViewed = () => {
      setVideoWasViewed(true);
    };

    // Create a custom event listener to close YouTube embed
    const handleExternalClose = () => {
      setShowYouTube(false);
      setShowMinimizedYouTube(false);
      setIsFullscreen(false);
    };

    // Add event listeners
    window.addEventListener('dog-tracks-proximity', handleCursorProximity);
    window.addEventListener('youtube-video-viewed', handleVideoViewed);
    window.addEventListener('close-youtube-embed', handleExternalClose);

    // Check if we should hide due to hover
    if (isStartButtonHovered) {
      setShowYouTube(false);
      setShowMinimizedYouTube(false);
    }

    return () => {
      // Clean up event listeners
      window.removeEventListener('dog-tracks-proximity', handleCursorProximity);
      window.removeEventListener('youtube-video-viewed', handleVideoViewed);
      window.removeEventListener('close-youtube-embed', handleExternalClose);
    };
  }, [location.pathname, videoWasViewed, isCallActive, isStartButtonHovered]);
  
  // If a call is active OR the start button is hovered, don't display YouTube
  if (isCallActive || isStartButtonHovered) {
    return null;
  }
  
  // YouTube handlers
  const handleMaximizeVideo = () => {
    setFromDownsized(true);
    setShowMinimizedYouTube(false);
    setShowYouTube(true);
    setVideoWasViewed(true);
  };
  
  const handleFullscreenVideo = () => {
    setIsFullscreen(true);
    setVideoWasViewed(true);
  };
  
  const handleExitFullscreen = () => {
    setIsFullscreen(false);
  };
  
  const handleCloseVideo = () => {
    setIsFullscreen(false);
    setShowYouTube(false);
    setFromDownsized(false);
    setVideoWasViewed(true);
    
    // Show minimized after a small delay only if video was viewed
    setTimeout(() => {
      setShowMinimizedYouTube(true);
    }, 500);
  };
  
  return (
    <>
      {/* YouTube Embed - Full Size */}
      <div className={`youtube-embed-backdrop ${showYouTube ? 'visible' : ''} ${isFullscreen ? 'fullscreen' : ''}`} onClick={handleCloseVideo}></div>
      <div className={`youtube-embed-container ${showYouTube ? 'visible' : ''} ${fromDownsized ? 'from-downsized' : ''} ${isFullscreen ? 'fullscreen' : ''}`}>
        {showYouTube && (
          <>
            <div className="youtube-embed-close" onClick={handleCloseVideo}>×</div>
            <YouTubeEmbed 
              videoId="u0t9bZ6hcMI" 
              isFullscreen={isFullscreen}
              onExitFullscreen={handleExitFullscreen}
              onGoFullscreen={handleFullscreenVideo}
            />
          </>
        )}
      </div>
      
      {/* Downsized YouTube Overlay that appears after load */}
      {!showYouTube && showMinimizedYouTube && (
        <div className="youtube-preview-container">
          <YouTubeEmbed 
            videoId="u0t9bZ6hcMI" 
            initiallyMinimized={false}
            isDownsized={true}
            onMaximize={handleMaximizeVideo}
            onGoFullscreen={handleFullscreenVideo}
            style={{
              width: '300px',
              height: '169px'
            }}
          />
        </div>
      )}
    </>
  );
};

// Home component to contain the main app content
const Home = ({ isLoading, callActive, showAttorneyInfo, showCallSummary, attorneyProfile, startCall, endCall, callData, subdomain, connectionStatus: propConnectionStatus, setShowAttorneyInfo, setShowCallSummary, buttonText, welcomeMessage, isDarkTheme, onStartButtonHover, vapiInstanceRef }) => {
  // Reference to client dossier display area
  const dossierRef = useRef(null);
  const [vapiMessages, setVapiMessages] = useState([]);
  const messagesEndRef = useRef(null);
  const [vapiError, setVapiError] = useState(null);
  const [localConnectionStatus, setLocalConnectionStatus] = useState(propConnectionStatus);
  
  // Update local state if prop changes
  useEffect(() => {
    setLocalConnectionStatus(propConnectionStatus);
  }, [propConnectionStatus]);
  
  // Handle Vapi errors
  const handleVapiError = useCallback((error) => {
    logger.error('[App Home] Vapi hook error:', error);
    setVapiError(error);
    
    if (callActive) {
      if (typeof endCall === 'function') endCall({ reason: 'error', error });
    }
    
    showErrorNotification(error?.message || 'An unknown error occurred');
  }, [callActive, endCall]);
  
  const showErrorNotification = useCallback((message) => {
    createNotification(message, 'error');
  }, []);
  
  const createNotification = useCallback((message, type = 'info') => {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to DOM
    document.body.appendChild(notification);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }, []);
  
  // Handle Vapi status changes
  const handleStatusChange = useCallback((status) => {
    logger.info('[App] Call status changed to', status);
    
    // Only update if the status has actually changed and is defined
    if (status && status !== localConnectionStatus) {
      setLocalConnectionStatus(status);
    }
    
    // Handle specific status changes
    if (status === CALL_STATUS.CONNECTED) {
      logger.info('[App] Call successfully connected');
    } else if (status === CALL_STATUS.ERROR) {
      logger.error('[App] Call encountered an error');
      if (callActive) {
        logger.info('[App] Resetting call active state due to error');
        setTimeout(() => {
          // Assuming setCallActive is passed from App and stable
          // This might need adjustment if setCallActive is not stable
        }, 3000);
      }
    }
  }, [localConnectionStatus, callActive]);
  
  // Handle Vapi messages
  const handleMessageReceived = useCallback((message) => {
    if (message) {
      setVapiMessages(prev => [...prev, message]);
      console.log('[App Home] Message received:', message);
    }
  }, []);
  
  // Handle call start
  const handleCallStart = useCallback(() => {
    console.log('[App Home] Call started');
  }, []);
  
  const handleCallEnd = useCallback((data) => {
    if (typeof endCall === 'function') {
      endCall(data);
    }
  }, [endCall]);
  
  // Handle start button hover
  const handleStartButtonHover = useCallback((isHovered) => {
    if (typeof onStartButtonHover === 'function') {
      onStartButtonHover(isHovered);
    }
  }, [onStartButtonHover]);
  
  // Memoize customInstructions object
  const customInstructions = useMemo(() => {
    console.log('[Home] Welcome message:', welcomeMessage);
    const instructions = welcomeMessage ? { initialMessage: welcomeMessage } : undefined;
    console.log('[Home] Creating custom instructions:', instructions);
    return instructions;
  }, [welcomeMessage]);
  
  return (
    <>
      {isLoading ? (
        <div className="loading-indicator">Loading...</div>
      ) : (
        <>
          {!callActive && !showAttorneyInfo && !showCallSummary && (
            <div className="start-button-container">
              <Button 
                onClick={startCall} 
                label={buttonText || "Start Consultation"}
                disabled={false}
                mascot="/mascot.png"
                onHover={handleStartButtonHover}
              />
            </div>
          )}
          
          {callActive && (
            <SimpleVapiCall
              key={callActive}
              apiKey={import.meta.env.VITE_VAPI_PUBLIC_KEY && 
                import.meta.env.VITE_VAPI_PUBLIC_KEY.startsWith('vapi_') ? 
                import.meta.env.VITE_VAPI_PUBLIC_KEY.replace('vapi_', '') : 
                import.meta.env.VITE_VAPI_PUBLIC_KEY}
              assistantId="legal-consultation"
              onCallStarted={handleCallStart}
              onCallEnded={handleCallEnd}
              onMessageReceived={handleMessageReceived}
              onError={handleVapiError}
              isDarkTheme={isDarkTheme}
              customInstructions={customInstructions}
            />
          )}
          
          {showAttorneyInfo && callData && (
            <div className="attorney-info-container">
              <AttorneyInfo 
                attorney={callData.attorney} 
                onClose={() => setShowAttorneyInfo(false)} 
              />
            </div>
          )}
          
          {showCallSummary && callData && (
            <div className="call-summary-container">
              <CallSummary 
                data={callData.summary} 
                onClose={() => setShowCallSummary(false)} 
              />
            </div>
          )}
        </>
      )}
    </>
  );
};

function App() {
  const [callActive, setCallActive] = useState(false)
  const [showAttorneyInfo, setShowAttorneyInfo] = useState(false)
  const [showCallSummary, setShowCallSummary] = useState(false)
  const [callData, setCallData] = useState(null)
  const [callError, setCallError] = useState(null)
  const [subdomain, setSubdomain] = useState('default')
  const [attorneyId, setAttorneyId] = useState(null)
  const [attorneyProfile, setAttorneyProfile] = useState(null)
  const [isDevelopment, setIsDevelopment] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [availableSubdomains, setAvailableSubdomains] = useState(['default'])
  const [isDarkTheme, setIsDarkTheme] = useState(true)
  const [showSubdomains, setShowSubdomains] = useState(false)
  const [selectedPracticeArea, setSelectedPracticeArea] = useState('');
  const [showPreview, setShowPreview] = useState(false);
  const [activeConfigTab, setActiveConfigTab] = useState('firm');
  
  // Add missing state variables
  const [firmName, setFirmName] = useState('Smith & Associates, LLP');
  const [logoUrl, setLogoUrl] = useState('');
  const [state, setState] = useState('');
  const [primaryColor, setPrimaryColor] = useState('#2c3e50');
  const [secondaryColor, setSecondaryColor] = useState('#3498db');
  const [backgroundColor, setBackgroundColor] = useState('#f0f4f8');
  const [backgroundOpacity, setBackgroundOpacity] = useState(0.3);
  const [buttonText, setButtonText] = useState('Start Consultation');
  const [buttonOpacity, setButtonOpacity] = useState(1);
  const [buttonColor, setButtonColor] = useState('#2C3E50');
  const [previewHeight, setPreviewHeight] = useState(600);
  const [practiceDescription, setPracticeDescription] = useState('**Welcome to our legal practice**\n\nOur team of experienced attorneys is dedicated to providing you with exceptional legal representation. We combine expertise with a client-focused approach to help you navigate complex legal challenges.\n\n### How we can help:\n- Personalized legal solutions\n- Clear communication throughout your case\n- Decades of combined experience');
  const [welcomeMessage, setWelcomeMessage] = useState('Hello, I\'m an AI assistant from Smith & Associates. How can I help you today?');
  const [informationGathering, setInformationGathering] = useState('To better assist you, I\'ll need a few details about your situation.');
  const [attorneyName, setAttorneyName] = useState('John Smith');
  const [practiceAreaBackgroundOpacity, setPracticeAreaBackgroundOpacity] = useState(0.1);
  const [textBackgroundColor, setTextBackgroundColor] = useState('#634C38');
  
  // New state variables for attorney contact details
  const [attorneyAddress, setAttorneyAddress] = useState('123 Legal Street, Suite 100, New York, NY 10001');
  const [attorneyPhone, setAttorneyPhone] = useState('(*************');
  const [schedulingLink, setSchedulingLink] = useState('https://calendly.com/example');
  
  const [firmUrl, setFirmUrl] = useState('');
  const [isUrlLoading, setIsUrlLoading] = useState(false);
  
  const iframeRef = useRef(null);
  const vapiInstanceRef = useRef(null);
  
  const location = useLocation();
  const [isAdminMode, setIsAdminMode] = useState(false);
  
  // Add state for detailed call connection status if needed for UI indicators
  const [connectionStatus, setConnectionStatus] = useState('idle');
  
  // Practice areas configuration
  const practiceAreas = {
    'Personal Injury': {
      questions: "I want to know the circumstances of their injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?",
      practiceDescription: "**Our firm specializes in personal injury law**, and we have a proven track record of success in obtaining favorable settlements and verdicts for our clients.\n\n### Our Services:\n- Car accident claims\n- Slip and fall cases\n- Medical malpractice\n- Workplace injuries\n\nWe work on a contingency basis - *you don't pay unless we win*.",
      welcomeMessage: "Welcome to our personal injury consultation. I'm your virtual legal assistant and I'm here to help gather information about your case.",
      informationGathering: "I want to know the circumstances of your injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?"
    },
    'Family Law': {
      questions: "I need to understand the nature of their family law issue. Are they seeking a divorce, child custody, child support, alimony, or a modification of an existing order?",
      practiceDescription: "**Our firm is dedicated to helping families** navigate the complexities of family law. We handle all aspects of divorce, including property division, child custody and visitation.\n\n### Practice Areas:\n- Divorce proceedings\n- Child custody & support\n- Spousal support/alimony\n- Prenuptial agreements\n\n> We approach each case with compassion and understanding during difficult times.",
      welcomeMessage: "Welcome to our family law consultation. I'm your virtual legal assistant and I'm here to help with your family law matters.",
      informationGathering: "I need to understand the nature of your family law issue. Are you seeking a divorce, child custody, child support, alimony, or a modification of an existing order?"
    },
    'Criminal Defense': {
      questions: "I need to know the charges against the client, where they are in the criminal process, and if there are any deadlines or court dates already scheduled.",
      practiceDescription: "## Criminal Defense Experts\n\nOur **experienced criminal defense attorneys** provide aggressive representation for all criminal matters, from misdemeanors to serious felony charges.\n\n### Our Approach:\n1. Thorough case evaluation\n2. Strategic defense planning\n3. Aggressive courtroom advocacy\n4. Pursuit of best possible outcomes\n\n[Contact us](#) immediately if you've been charged with a crime.",
      welcomeMessage: "Thank you for considering our firm for your criminal defense needs. I'm here to gather some initial information about your case.",
      informationGathering: "Please tell me about the charges you're facing and where you are in the legal process. All information is confidential and protected by attorney-client privilege."
    }
  };
  
  // Handle practice area selection
  const handlePracticeAreaChange = (areaOrEvent) => {
    // Handle either receiving an event object or a direct value
    const area = areaOrEvent && areaOrEvent.target ? areaOrEvent.target.value : areaOrEvent;
    logger.info("[App] Setting practice area to:", area);
    
    setSelectedPracticeArea(area);
    
    if (area && practiceAreas[area]) {
      setWelcomeMessage(practiceAreas[area].welcomeMessage);
      setInformationGathering(practiceAreas[area].informationGathering);
      setPracticeDescription(practiceAreas[area].practiceDescription);
    }
  };
  
  // Handle file upload for logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoUrl(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle logo removal
  const handleRemoveLogo = () => {
    setLogoUrl('');
  };
  
  const goToPreview = () => {
    setShowPreview(true);
  };

  // Handle URL submission for auto-configuration
  const handleUrlSubmit = (e) => {
    e.preventDefault();
    
    if (!firmUrl) {
      alert('Please enter a URL');
      return;
    }
    
    setIsUrlLoading(true);
    
    // In a real implementation, this would call an API to scrape the website
    // For now, we'll simulate the process with a timeout
    setTimeout(() => {
      // Generate simulated data based on the URL
      const domain = firmUrl.replace(/^https?:\/\//, '').replace(/^www\./, '').split('/')[0];
      const generatedFirmName = domain
        .split(/[.-]/)
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ') + " Law";
      
      // Generate a practice description based on the domain
      let generatedDescription = "Specializing in various legal matters with a client-focused approach to help you navigate complex legal challenges.";
      
      // Set practice areas based on the domain keywords
      const domainLower = domain.toLowerCase();
      
      if (domainLower.includes('injury') || domainLower.includes('accident')) {
        setSelectedPracticeArea('Personal Injury');
        generatedDescription = "We handle all types of personal injury cases, including car accidents, slip and falls, and medical malpractice. Our experienced team will fight for the compensation you deserve.";
      } else if (domainLower.includes('family') || domainLower.includes('divorce')) {
        setSelectedPracticeArea('Family Law');
        generatedDescription = "Our compassionate family law attorneys provide guidance through divorce, custody matters, and other family legal issues with sensitivity and expertise.";
      } else if (domainLower.includes('criminal') || domainLower.includes('defense')) {
        setSelectedPracticeArea('Criminal Defense');
        generatedDescription = "Our criminal defense team provides aggressive representation for all criminal matters, from misdemeanors to serious felony charges.";
      }
      
      // Update state with generated information
      setFirmName(generatedFirmName);
      setPracticeDescription(generatedDescription);
      
      if (practiceAreas[selectedPracticeArea]) {
        setWelcomeMessage(practiceAreas[selectedPracticeArea].welcomeMessage);
        setInformationGathering(practiceAreas[selectedPracticeArea].informationGathering);
      }
      
      setIsUrlLoading(false);
    }, 1500);
  };

  // Helper functions for color handling
  const hexToRgb = (hex) => {
    // Remove # if present
    hex = hex.replace('#', '');
    
    // Parse hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    
    return `${r}, ${g}, ${b}`;
  };
  
  const getContrastColor = (hexColor) => {
    // Convert hex to RGB
    let hex = hexColor.replace('#', '');
    
    // Convert hex to RGB
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    
    // Calculate luminance - standard formula
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    
    // Return black for bright colors, white for dark colors
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  // Theme toggle function - enhanced for better debugging
  const toggleTheme = () => {
    logger.info('Theme toggle clicked, current state:', isDarkTheme);
    setIsDarkTheme(prev => !prev);
  };

  // Set theme on body element and document with enhanced logging
  useEffect(() => {
    // Apply theme to document
    logger.info(`Applying theme: ${isDarkTheme ? 'dark' : 'light'}`);
    
    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'dark' : 'light');
    
    // Also add/remove class for components that use class-based styling
    if (isDarkTheme) {
      document.body.classList.add('dark-theme');
      document.body.classList.remove('light-theme');
      logger.info('Added dark-theme class to body, removed light-theme');
    } else {
      document.body.classList.add('light-theme');
      document.body.classList.remove('dark-theme');
      logger.info('Added light-theme class to body, removed dark-theme');
    }
    
    // Apply theme to navbar as a fallback in case it's not using the Header component
    const navbarElement = document.querySelector('.main-nav');
    if (navbarElement) {
      navbarElement.classList.toggle('dark-theme', isDarkTheme);
      navbarElement.classList.toggle('light-theme', !isDarkTheme);
      logger.info(`Applied theme classes to navbar: ${isDarkTheme ? 'dark-theme' : 'light-theme'}`);
    }
    
    // Set CSS variables directly as another fallback
    const root = document.documentElement;
    root.style.setProperty('--nav-bg', isDarkTheme ? 
      'rgba(18, 18, 18, 0.95)' : 'rgba(99, 76, 56, 0.95)');
    
    logger.info(`Theme toggled to ${isDarkTheme ? 'dark' : 'light'}, CSS variables updated`);
  }, [isDarkTheme]);

  // Use useEffect to determine subdomain and initialize
  useEffect(() => {
    logger.info('App component mounted');
    
    // Initialize React DevTools global hook for LegalScout
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout = {};
    }
    
    // Determine if we're in development environment
    const isDev = import.meta.env.MODE === 'development' || 
                 window.location.hostname === 'localhost' || 
                 window.location.hostname === '127.0.0.1';
    setIsDevelopment(isDev);
    
    // Get subdomain using our utility
    const subdomainValue = getCurrentSubdomain();
    logger.info('Subdomain detected:', subdomainValue);
    setSubdomain(subdomainValue);
    
    // Get attorney profile based on subdomain
    const loadAttorneyProfile = async () => {
      setIsLoading(true);
      try {
        const profile = await getAttorneyConfigAsync(subdomainValue);
        logger.info('Attorney profile loaded:', profile);
        setAttorneyProfile(profile);
        // Set attorney ID from profile if available
        if (profile && profile.id) {
          setAttorneyId(profile.id);
        }
      } catch (error) {
        logger.error('Error loading attorney profile:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadAttorneyProfile();
    
    return () => {
      logger.info('App component unmounted');
    };
  }, []);

  // Setup subdomain testing UI in development
  useEffect(() => {
    if (!isDevelopment) return;
    
    // Fetch available subdomains
    const fetchSubdomains = async () => {
      try {
        const response = await fetch('/subdomain_config.json');
        if (!response.ok) {
          logger.warn('Failed to load subdomain config, using default only');
          setAvailableSubdomains(['default']);
          return;
        }
        
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
          logger.warn('Subdomain config response is not JSON, using default only');
          setAvailableSubdomains(['default']);
          return;
        }
        
        const data = await response.json();
        const subdomains = data ? Object.keys(data) : [];
        
        logger.info('Available subdomains for testing:', subdomains);
        setAvailableSubdomains(['default', ...subdomains]);
      } catch (error) {
        logger.warn('Error setting up subdomain testing, using default only:', error);
        setAvailableSubdomains(['default']);
      }
    };
    
    fetchSubdomains();
  }, [isDevelopment]);

  // *** ADDED useMemo for customAssistantConfig ***
  const customAssistantConfig = useMemo(() => {
    logger.info('[App] Recomputing customAssistantConfig');
    return welcomeMessage ? { initialMessage: welcomeMessage } : {}; // Return empty object if no message
  }, [welcomeMessage]);

  // Function to start the call
  const startCall = useCallback(() => {
    // Debug logs at the start of the call sequence
    logger.info('🚀 [App] Starting call sequence');
    logger.info('🔍 [App] Pre-call state:', { 
      callActive, 
      subdomain, 
      customConfig: attorneyProfile, // Assuming attorneyProfile holds the config
      vapiInstance: !!vapiInstanceRef.current,
      environmentVars: {
        present: [],
        missing: []
      },
    });

    // Environment variable initialization
    const envVars = {
      VITE_VAPI_PUBLIC_KEY: import.meta.env.VITE_VAPI_PUBLIC_KEY,
      VITE_VAPI_BASE_URL: import.meta.env.VITE_VAPI_BASE_URL,
      VITE_FALLBACK_MODE: import.meta.env.VITE_FALLBACK_MODE
    };

    const missingVars = Object.entries(envVars)
      .filter(([key, value]) => !value)
      .map(([key]) => key);

    if (missingVars.length > 0) {
      const message = `Missing environment variables: ${missingVars.join(', ')}. Cannot start call.`;
      logger.error('[App]', message);
      alert(message);
      return;
    }

    // Close any YouTube videos that might be playing
    const closeYouTubeEvent = new CustomEvent('close-youtube-embed');
    window.dispatchEvent(closeYouTubeEvent);
    
    setCallActive(true);
    setCallError(null); // Clear previous errors
    setShowCallSummary(false); // Hide previous summary
    logger.info('✅ [App] Call activation triggered, callActive set to true');
  }, [callActive, subdomain, attorneyProfile]);

  // Memoized callback for ending the call
  const endCall = useCallback((data) => {
    logger.info('[App] Ending call sequence', data);
    setCallActive(false);
    
    if (data && data.attorney) {
      setCallData(data);
      setShowAttorneyInfo(true);
      setShowCallSummary(false);
    } else if (data && data.summary) {
      setCallData(data);
      setShowAttorneyInfo(false);
      setShowCallSummary(true);
    } else {
      // Handle cases where call ends without summary/attorney info (e.g., error, user cancel)
      setCallData(null);
      setShowAttorneyInfo(false);
      setShowCallSummary(false);
      if (data && data.error) {
        setCallError(data.error);
        // Optionally show error notification here too
      }
    }
  }, []);

  // Debug the logo URL whenever it changes
  useEffect(() => {
    if (logoUrl) {
      logger.info('[App.jsx] Current logoUrl:', logoUrl);
    }
  }, [logoUrl]);

  // Update admin mode based on location
  useEffect(() => {
    const isAdminRoute = location.pathname === '/admin';
    setIsAdminMode(isAdminRoute);
    
    // Log current route for debugging
    logger.info(`Current route: ${location.pathname}`);
  }, [location]);

  const [isStartButtonHovered, setIsStartButtonHovered] = useState(false);
  const [showMap, setShowMap] = useState(false);

  const config = getAttorneyConfigAsync(subdomain);
  const effectiveProfile = { ...config, ...attorneyProfile };

  // Initialize vapiInstanceRef
  useEffect(() => {
    try {
      const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      if (!apiKey) {
        logger.error('No Vapi API key found in environment variables');
        return;
      }
      
      logger.info('Initializing VapiService with API key');
      const vapiInstance = vapiService.createVapiInstance(apiKey);
      
      if (vapiInstance) {
        vapiInstanceRef.current = vapiInstance;
        logger.info('Vapi instance created successfully and stored in ref:', vapiInstanceRef.current);
      } else {
        logger.error('Failed to create Vapi instance');
      }
    } catch (error) {
      logger.error('Error initializing VapiService:', error);
    }
  }, []);

  return (
    <ErrorBoundary>
      <div className="app-wrapper full-width" style={{
        backgroundColor: isDarkTheme ? 'rgba(0, 0, 0, 0.92)' : 'rgba(248, 249, 250, 1)',
        minHeight: '100vh',
        position: 'relative',
        overflow: 'hidden',
        zIndex: 0
      }}>
        <AnimatedBackground isDarkTheme={isDarkTheme} 
          style={{ 
            background: 'none',
            backgroundImage: 'none',
            zIndex: 1 // Keep background elements behind main content
          }} 
        />
        
        <div style={{ 
          pointerEvents: 'none',
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 0 // Keep background elements behind main content
        }}>
          <GlobeDossierView 
            isVisible={true} 
            caseData={{}}
            attorneys={[]}
            isDarkTheme={isDarkTheme}
          />
        </div>
        
        <DogPrintsWrapper isDarkTheme={isDarkTheme} />
        
        {/* Always show the Header component with ThemeToggle */}
        <Header isDarkTheme={isDarkTheme} onToggleTheme={toggleTheme} />
        
        {/* Ensure main content layer is above background but below potential modals */}
        <div className="main-content-layer" style={{ 
          position: 'relative', 
          zIndex: 2, 
          background: 'transparent', // Explicitly set background to transparent
          padding: '0', // Remove padding
          margin: '0' // Remove margin
        }}>
          <Routes>
            <Route path="/" element={
              <section className="hero-section" style={{ 
                background: 'transparent', // Ensure transparent background
                border: 'none',
                padding: '0', // Remove padding
                margin: '0', // Remove margin
                minHeight: 'calc(100vh - 60px)', // Adjust based on header height
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
                <div className="hero-content" style={{ background: 'transparent', width: '100%', height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
                  {/* Debug info remains conditional */}
                  {process.env.NODE_ENV === 'development' && (
                    <div className="debug-info" style={{ position: 'absolute', top: '70px', left: '10px', background: 'rgba(0,0,0,0.5)', color: 'white', padding: '5px', zIndex: 10 }}>
                      <p>Subdomain: {subdomain || 'default'}</p>
                      <p>Attorney ID: {attorneyId || 'N/A'}</p>
                    </div>
                  )}
                  {/* Main content area - takes full space of hero-content */}
                  <div className="content-area" style={{ 
                    background: 'transparent', 
                    width: '100%', 
                    flexGrow: 1, // Ensure this container grows
                    display: 'flex', // Use flex to center its child
                    justifyContent: 'center', 
                    alignItems: 'center' 
                  }}>
                    {/* Render Home component inside the restored structure */}
                    <Home 
                      isLoading={isLoading}
                      callActive={callActive} 
                      showAttorneyInfo={showAttorneyInfo} 
                      showCallSummary={showCallSummary}
                      attorneyProfile={attorneyProfile}
                      startCall={startCall}
                      endCall={endCall}
                      callData={callData}
                      subdomain={subdomain}
                      connectionStatus={connectionStatus}
                      setShowAttorneyInfo={setShowAttorneyInfo}
                      setShowCallSummary={setShowCallSummary}
                      buttonText={buttonText}
                      welcomeMessage={welcomeMessage}
                      isDarkTheme={isDarkTheme}
                      onStartButtonHover={setIsStartButtonHovered}
                      vapiInstanceRef={vapiInstanceRef}
                    />
                  </div>
                </div>
              </section>
            } />
            <Route path="/test" element={<VapiTest />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/contact" element={<div>Contact Page Coming Soon</div>} />
            <Route path="/admin" element={<AdminPage />} />
            <Route path="/demo" element={
              <Suspense fallback={
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <p>Loading demo interface...</p>
                </div>
              }>
                <SimpleDemoPage 
                  firmName={firmName}
                  logoUrl={logoUrl}
                  state={state}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  buttonColor={buttonColor}
                  setButtonColor={setButtonColor}
                  backgroundColor={backgroundColor}
                  backgroundOpacity={backgroundOpacity}
                  welcomeMessage={welcomeMessage}
                  informationGathering={informationGathering}
                  practiceDescription={practiceDescription}
                  previewHeight={previewHeight}
                  setPreviewHeight={setPreviewHeight}
                  attorneyName={attorneyName}
                  selectedPracticeArea={selectedPracticeArea}
                  handlePracticeAreaChange={handlePracticeAreaChange}
                  showPreview={showPreview}
                  setShowPreview={setShowPreview}
                  handleLogoUpload={handleLogoUpload}
                  handleRemoveLogo={handleRemoveLogo}
                  practiceAreas={practiceAreas}
                  activeConfigTab={activeConfigTab}
                  setActiveConfigTab={setActiveConfigTab}
                  buttonText={buttonText}
                  setButtonText={setButtonText}
                  buttonOpacity={buttonOpacity}
                  setButtonOpacity={setButtonOpacity}
                  practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                  setPracticeAreaBackgroundOpacity={setPracticeAreaBackgroundOpacity}
                  textBackgroundColor={textBackgroundColor}
                  setTextBackgroundColor={setTextBackgroundColor}
                  goToPreview={goToPreview}
                  setFirmName={setFirmName}
                  setAttorneyName={setAttorneyName}
                  setPracticeDescription={setPracticeDescription}
                  setState={setState}
                  setWelcomeMessage={setWelcomeMessage}
                  setInformationGathering={setInformationGathering}
                  setPrimaryColor={setPrimaryColor}
                  setSecondaryColor={setSecondaryColor}
                  setBackgroundColor={setBackgroundColor}
                  setBackgroundOpacity={setBackgroundOpacity}
                  iframeRef={iframeRef}
                  firmUrl={firmUrl}
                  setFirmUrl={setFirmUrl}
                  isLoading={isUrlLoading}
                  handleUrlSubmit={handleUrlSubmit}
                  isDarkTheme={isDarkTheme}
                  setLogoUrl={setLogoUrl}
                  attorneyAddress={attorneyAddress}
                  setAttorneyAddress={setAttorneyAddress}
                  attorneyPhone={attorneyPhone}
                  setAttorneyPhone={setAttorneyPhone}
                  schedulingLink={schedulingLink}
                  setSchedulingLink={setSchedulingLink}
                />
              </Suspense>
            } />
            <Route path="/demo/preview" element={
              <Suspense fallback={
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <p>Loading preview...</p>
                </div>
              }>
                <PreviewPage 
                  firmName={firmName} 
                  attorneyName={attorneyName}
                  darkMode={isDarkTheme}
                  onToggleDarkMode={() => setIsDarkTheme(!isDarkTheme)}
                />
              </Suspense>
            } />
            
            <Route path="/preview" element={
              <Suspense fallback={
                <div className="loading-container">
                  <div className="loading-spinner"></div>
                  <p>Loading preview...</p>
                </div>
              }>
                <SimplifiedPreview 
                  firmName={firmName}
                  primaryColor={primaryColor}
                  secondaryColor={secondaryColor}
                  practiceDescription={practiceDescription}
                  welcomeMessage={welcomeMessage}
                  informationGathering={informationGathering}
                  theme={isDarkTheme ? 'dark' : 'light'}
                  logoUrl={logoUrl || "/PRIMARY CLEAR.png"}
                  buttonText={buttonText || "Start Consultation"}
                  buttonOpacity={buttonOpacity}
                  practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                  textBackgroundColor={textBackgroundColor}
                  mascot="/PRIMARY CLEAR.png"
                  vapiInstructions="You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney."
                />
              </Suspense>
            } />
            <Route path="/simple-preview" element={
              <SimplifiedPreview 
                firmName={firmName}
                primaryColor={primaryColor}
                secondaryColor={secondaryColor}
                practiceDescription={practiceDescription}
                welcomeMessage={welcomeMessage}
                informationGathering={informationGathering}
                theme={isDarkTheme ? 'dark' : 'light'}
                logoUrl={logoUrl || "/PRIMARY CLEAR.png"}
                buttonText={buttonText || "Start Consultation"}
                buttonOpacity={buttonOpacity}
                practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}
                textBackgroundColor={textBackgroundColor}
                mascot="/PRIMARY CLEAR.png"
                vapiInstructions="You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney."
              />
            } />
            <Route path="/test" element={<TestPage />} />
            <Route path="/test-button" element={<TestButton />} />
            <Route path="/api-test" element={<VapiApiTester />} />
          </Routes>
        </div>
        
        {/* Attorney Info */}
        {showAttorneyInfo && (
          <AttorneyInfo
            attorneyProfile={attorneyProfile}
            isDarkTheme={isDarkTheme}
            onClose={() => setShowAttorneyInfo(false)}
          />
        )}
        
        {/* Call Summary */}
        {showCallSummary && callData && (
          <CallSummary
            callData={callData}
            isDarkTheme={isDarkTheme}
            onClose={() => setShowCallSummary(false)}
          />
        )}
        
        {/* Map View */}
        {showMap && (
          <MapView
            isDarkTheme={isDarkTheme}
            onClose={() => setShowMap(false)}
          />
        )}
      </div>
    </ErrorBoundary>
  );
}

// Export App directly instead of wrapped version
export default App; 