import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PreviewContainerProps {
  isOpen: boolean;
  previewUrl: string;
  onClose: () => void;
  customizations: {
    firmName: string;
    attorneyName: string;
    practiceAreas: string[];
    state: string;
    logoUrl?: string;
    backgroundColor: string;
    templateColors: {
      primary: string;
      secondary: string;
    };
  };
}

export const PreviewContainer = ({ 
  isOpen, 
  previewUrl, 
  onClose,
  customizations 
}: PreviewContainerProps) => {
  const [iframeHeight, setIframeHeight] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && containerRef.current) {
      // Calculate height based on viewport
      const viewportHeight = window.innerHeight;
      const containerTop = containerRef.current.getBoundingClientRect().top;
      const padding = 32; // 2rem
      setIframeHeight(viewportHeight - containerTop - padding);
    }
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          ref={containerRef}
          initial={{ height: 0, opacity: 0 }}
          animate={{ height: iframeHeight, opacity: 1 }}
          exit={{ height: 0, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="w-full relative overflow-hidden bg-gray-50 rounded-lg shadow-lg"
        >
          <div className="absolute top-0 right-0 p-2">
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              aria-label="Close preview"
            >
              <svg
                className="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          
          <iframe
            src={`${previewUrl}?${new URLSearchParams(customizations as any)}`}
            className="w-full h-full border-0"
            title="LegalScout Preview"
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}; 