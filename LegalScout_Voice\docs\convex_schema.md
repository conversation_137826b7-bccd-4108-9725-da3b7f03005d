# LegalScout Convex Schema Design

This document outlines the schema design for the Convex database that will be used in the LegalScout application.

## Tables

### attorneyProfiles

The main table for storing attorney profile information. This table will be used for white-labeled experiences and custom assistant ID mapping.

```typescript
{
  // Automatically generated by Convex
  _id: v.id("attorneyProfiles"),
  _creationTime: v.number(),

  // Basic Information
  name: v.string(),
  email: v.string(),
  phone: v.string(),
  website: v.optional(v.string()),
  
  // Firm Information
  firmName: v.string(),
  firmLogo: v.optional(v.id("_storage")), // ID of uploaded logo in Convex Storage
  profileImage: v.optional(v.id("_storage")), // ID of uploaded profile image
  
  // Address Information
  address: v.string(),
  city: v.string(),
  state: v.string(),
  zipCode: v.string(),
  country: v.string(),
  latitude: v.number(),
  longitude: v.number(),
  
  // Practice Areas - array of specialties
  practiceAreas: v.array(v.string()),
  
  // White-labeling
  subdomain: v.string(), // Used for subdomain-based lookup
  customColors: v.optional(v.object({
    primary: v.string(),
    secondary: v.string(),
    accent: v.string()
  })),
  
  // Integration Settings
  assistantId: v.optional(v.string()), // Custom Vapi Assistant ID
  promptInstructions: v.optional(v.string()), // Custom prompt instructions for the assistant
  
  // Status and Metadata
  isActive: v.boolean(),
  verificationStatus: v.string(), // "pending", "verified", "rejected"
  subscriptionTier: v.string(), // "basic", "premium", "enterprise"
  
  // Timestamps for updates
  lastUpdated: v.number()
}
```

### webhookLogs

Table for storing logs of incoming webhook data from attorney profile integrations.

```typescript
{
  _id: v.id("webhookLogs"),
  _creationTime: v.number(),
  
  // Webhook data
  source: v.string(), // Source of the webhook (e.g., "attorney_frontend")
  eventType: v.string(), // Type of event (e.g., "profile_update", "subscription_change")
  payload: v.any(), // Raw payload from the webhook
  
  // Processing metadata
  processed: v.boolean(), // Whether the webhook has been processed
  processingErrors: v.optional(v.array(v.string())), // Any errors that occurred during processing
  
  // Reference to affected attorney profile
  attorneyProfileId: v.optional(v.id("attorneyProfiles")),
  
  // Timestamps
  processedAt: v.optional(v.number())
}
```

### dossierUpdates

Table for storing dossier updates from Vapi's live_dossier tool.

```typescript
{
  _id: v.id("dossierUpdates"),
  _creationTime: v.number(),
  
  // Session information
  sessionId: v.string(), // Unique identifier for the call session
  userId: v.optional(v.string()), // User ID if authenticated
  
  // Dossier data
  clientBackground: v.optional(v.string()),
  status: v.optional(v.string()),
  jurisdiction: v.optional(v.object({
    address: v.optional(v.string()),
    lat: v.optional(v.number()),
    lng: v.optional(v.number())
  })),
  statementOfFacts: v.optional(v.string()),
  legalIssues: v.optional(v.string()),
  objectives: v.optional(v.string()),
  propertyDetails: v.optional(v.object({
    name: v.optional(v.string()),
    description: v.optional(v.string())
  })),
  
  // Integration reference
  attorneyProfileId: v.optional(v.id("attorneyProfiles")), // If this dossier is associated with a specific attorney
  
  // Timestamps
  lastUpdated: v.number()
}
```

## API Design

### Queries

- `getAttorneyBySubdomain(subdomain: string)`: Retrieve attorney profile based on subdomain
- `getRecentDossierUpdates(sessionId: string)`: Get recent dossier updates for a session
- `searchAttorneysByLocation(latitude: number, longitude: number, radius: number)`: Find attorneys near a location
- `searchAttorneysByPracticeArea(practiceArea: string)`: Find attorneys by practice area

### Mutations

- `updateAttorneyProfile(attorneyId: Id<"attorneyProfiles">, data: Partial<AttorneyProfile>)`: Update attorney profile
- `processWebhook(payload: any)`: Process incoming webhook data
- `saveDossierUpdate(sessionId: string, data: Partial<DossierUpdate>)`: Save dossier update from Vapi
- `markAttorneyActive(attorneyId: Id<"attorneyProfiles">, isActive: boolean)`: Update attorney active status

### HTTP Actions

- `POST /api/webhook`: Endpoint for receiving webhook data from attorney frontend
- `POST /api/dossier-update`: Endpoint for receiving dossier updates from Vapi

## Integration with Vapi live_dossier

The `dossierUpdates` table is designed to closely match the structure of the Vapi live_dossier tool. When the tool fires an update, the data will be:

1. Received in the VapiCall component
2. Processed to extract relevant fields
3. Saved to the Convex database via the `saveDossierUpdate` mutation
4. Real-time updates will automatically flow to the UI via Convex's subscription mechanism

This allows the MapDossierView component to listen for updates from the database and maintain a consistent state across clients. 