/**
 * Minimal Vapi Webhook Handler
 * 
 * A super simple webhook handler that should work without any issues
 */

export default async function handler(req, res) {
  try {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS, GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Vapi-Signature');

    // Handle OPTIONS request for CORS
    if (req.method === 'OPTIONS') {
      return res.status(200).json({ message: 'CORS preflight successful' });
    }

    // Handle GET request for testing
    if (req.method === 'GET') {
      return res.status(200).json({ 
        message: 'Minimal Vapi webhook endpoint is working',
        timestamp: new Date().toISOString(),
        method: 'GET',
        status: 'healthy'
      });
    }

    // Handle POST request (actual webhook)
    if (req.method === 'POST') {
      const callData = req.body;
      
      console.log('📞 Received webhook for call:', callData?.id, 'status:', callData?.status);
      
      // For now, just log and return success
      // TODO: Add Supabase integration once basic webhook is working
      
      return res.status(200).json({ 
        success: true,
        message: 'Webhook received successfully',
        timestamp: new Date().toISOString(),
        callId: callData?.id,
        assistantId: callData?.assistant_id,
        status: callData?.status
      });
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });

  } catch (error) {
    console.error('Error in minimal webhook handler:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
