/**
 * Emergency API Key Fix
 * 
 * This script fixes the critical "Invalid Key" error by ensuring the correct
 * Vapi API keys are used for different operations.
 */

(function() {
  'use strict';

  console.log('🚨 [EmergencyApiKeyFix] Starting emergency API key fix...');

  // Define the correct API keys
  const VAPI_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
  const VAPI_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';

  // 1. Set global environment variables
  function setGlobalEnvironmentVariables() {
    console.log('🔑 [EmergencyApiKeyFix] Setting global environment variables...');
    
    // Set window globals
    window.VITE_VAPI_PUBLIC_KEY = VAPI_PUBLIC_KEY;
    window.VITE_VAPI_SECRET_KEY = VAPI_SECRET_KEY;
    window.VAPI_TOKEN = VAPI_SECRET_KEY;
    
    // Set import.meta.env if available (safely check for import.meta)
    try {
      if (typeof window !== 'undefined' && window.import && window.import.meta && window.import.meta.env) {
        window.import.meta.env.VITE_VAPI_PUBLIC_KEY = VAPI_PUBLIC_KEY;
        window.import.meta.env.VITE_VAPI_SECRET_KEY = VAPI_SECRET_KEY;
        window.import.meta.env.VAPI_TOKEN = VAPI_SECRET_KEY;
      }
    } catch (e) {
      // Ignore import.meta errors in non-module contexts
      console.log('[EmergencyApiKeyFix] Skipping import.meta.env (not in module context)');
    }
    
    console.log('✅ [EmergencyApiKeyFix] Global environment variables set');
  }

  // 2. Create API key helper function
  function createApiKeyHelper() {
    console.log('🔧 [EmergencyApiKeyFix] Creating API key helper function...');
    
    window.getVapiApiKey = function(operationType) {
      operationType = operationType || 'client';
      
      if (operationType === 'server' || operationType === 'assistant' || operationType === 'mcp' || operationType === 'create') {
        console.log('[EmergencyApiKeyFix] Using SECRET key for:', operationType);
        return VAPI_SECRET_KEY;
      } else {
        console.log('[EmergencyApiKeyFix] Using PUBLIC key for:', operationType);
        return VAPI_PUBLIC_KEY;
      }
    };
    
    console.log('✅ [EmergencyApiKeyFix] API key helper function created');
  }

  // 3. Override fetch to use correct API keys
  function overrideFetch() {
    console.log('🌐 [EmergencyApiKeyFix] Overriding fetch for correct API key usage...');
    
    const originalFetch = window.fetch;
    
    window.fetch = function(url, options) {
      options = options || {};
      
      // Handle Vapi API requests
      if (typeof url === 'string' && (url.includes('api.vapi.ai') || url.includes('dashboard.vapi.ai'))) {
        console.log('[EmergencyApiKeyFix] Intercepting Vapi API request:', url);
        
        // Determine which API key to use based on the endpoint and method
        let apiKey;
        const method = (options.method || 'GET').toUpperCase();
        
        if (url.includes('/assistant') && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
          // Creating or updating assistants - use SECRET key
          apiKey = VAPI_SECRET_KEY;
          console.log('[EmergencyApiKeyFix] Using SECRET key for assistant creation/update');
        } else if (url.includes('/assistant') && method === 'GET') {
          // Getting assistants - use SECRET key
          apiKey = VAPI_SECRET_KEY;
          console.log('[EmergencyApiKeyFix] Using SECRET key for assistant retrieval');
        } else if (url.includes('/call')) {
          // Call operations - use SECRET key
          apiKey = VAPI_SECRET_KEY;
          console.log('[EmergencyApiKeyFix] Using SECRET key for call operations');
        } else {
          // Default to SECRET key for server operations
          apiKey = VAPI_SECRET_KEY;
          console.log('[EmergencyApiKeyFix] Using SECRET key as default for server operations');
        }
        
        // Set the correct headers
        const headers = {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + apiKey,
          ...options.headers
        };
        
        const enhancedOptions = {
          ...options,
          headers,
          mode: 'cors',
          credentials: 'omit'
        };
        
        console.log('[EmergencyApiKeyFix] Using API key:', apiKey.substring(0, 8) + '...');
        
        return originalFetch(url, enhancedOptions);
      }
      
      // Handle Supabase requests - preserve existing headers
      if (typeof url === 'string' && url.includes('supabase.co')) {
        const headers = {
          'apikey': window.VITE_SUPABASE_KEY || window.VITE_SUPABASE_ANON_KEY,
          'Authorization': 'Bearer ' + (window.VITE_SUPABASE_KEY || window.VITE_SUPABASE_ANON_KEY),
          ...options.headers
        };
        
        return originalFetch(url, { ...options, headers });
      }
      
      // Default behavior for other requests
      return originalFetch(url, options);
    };
    
    console.log('✅ [EmergencyApiKeyFix] Fetch override applied');
  }

  // 4. Fix existing service instances
  function fixExistingServices() {
    console.log('🔧 [EmergencyApiKeyFix] Fixing existing service instances...');
    
    // Fix VapiMcpService if it exists
    if (window.vapiMcpService) {
      console.log('[EmergencyApiKeyFix] Updating VapiMcpService API key...');
      window.vapiMcpService.apiKey = VAPI_SECRET_KEY;
      window.vapiMcpService.directApiKey = VAPI_SECRET_KEY;
    }
    
    // Fix any global Vapi instances
    if (window.vapi) {
      console.log('[EmergencyApiKeyFix] Updating global Vapi instance...');
      // Note: We can't easily change the API key of an existing Vapi instance
      // but we can ensure new instances use the correct key
    }
    
    console.log('✅ [EmergencyApiKeyFix] Existing services updated');
  }

  // 5. Initialize all fixes
  function initializeEmergencyFix() {
    try {
      setGlobalEnvironmentVariables();
      createApiKeyHelper();
      overrideFetch();
      fixExistingServices();
      
      // Set completion flag
      window.EMERGENCY_API_KEY_FIX_COMPLETE = true;
      
      console.log('🎉 [EmergencyApiKeyFix] Emergency API key fix complete!');
      
      // Dispatch event if possible
      if (typeof CustomEvent !== 'undefined') {
        window.dispatchEvent(new CustomEvent('emergencyApiKeyFixComplete', {
          detail: {
            timestamp: new Date().toISOString(),
            publicKey: VAPI_PUBLIC_KEY.substring(0, 8) + '...',
            secretKey: VAPI_SECRET_KEY.substring(0, 8) + '...'
          }
        }));
      }
      
    } catch (error) {
      console.error('❌ [EmergencyApiKeyFix] Error applying emergency fix:', error);
    }
  }

  // Execute immediately
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeEmergencyFix);
  } else {
    initializeEmergencyFix();
  }

})();
