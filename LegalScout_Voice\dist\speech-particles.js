// Speech Particles Visualization
// Adapted for Vapi integration with user microphone input

(function() {
  // Wait for DOM to be ready
  document.addEventListener('DOMContentLoaded', setup);

  // Also try to set up immediately in case the DOM is already loaded
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    console.log('Speech particles: DOM already loaded, setting up immediately');
    setTimeout(setup, 100); // Small delay to ensure the canvas is in the DOM
  }

  // Global variables
  let canvas, ctx;
  let particles = [];
  let time = 0;
  let currentAmplitude = 0.5;
  let currentFrequency = 200; // Default middle frequency
  let targetAmplitude = 0.5;
  let targetFrequency = currentFrequency;
  let isSilent = false;
  let currentSpeaker = 'user'; // 'user' or 'assistant'
  let speakerTimer = 0; // Frames remaining for current speaker

  // For external control
  window.externalAmplitude = null;
  window.externalFrequency = null;
  window.externalSpeaker = null;

  // Color palettes
  const colorPalettes = {
    user: {
      name: "User Colors",
      quiet: { h: 140, s: 70, l: 35 }, // Green-based colors for user
      loud:  { h: 120, s: 90, l: 60 }  // Brighter green for louder user sounds
    },
    assistant: {
      name: "Assistant Colors",
      quiet: { h: 200, s: 70, l: 40 }, // Light blue for assistant
      loud:  { h: 190, s: 90, l: 65 }  // Brighter light blue for louder assistant sounds
    },
    ambient: {
      name: "Ambient Colors",
      quiet: { h: 210, s: 30, l: 30 }, // Subtle blue for quiet ambient state
      loud:  { h: 200, s: 40, l: 40 }  // Slightly brighter blue for ambient movement
    }
  };

  // Settings
  const settings = {
    // Visual settings
    maxParticles: 2000, // More particles for denser mist
    particleSize: 1.2, // Smaller particles for mist effect
    particleSizeMinRatio: 0.7,
    particleSizeMaxRatio: 1.3, // Less variation for more consistent mist
    maxLife: 40, // Shorter life for faster decay
    fadeInDuration: 0.1, // Quick fade in
    fadeOutStart: 0.7, // Start fading out earlier
    opacityBase: 0.9, // Higher base opacity
    opacityMax: 1.0, // Full opacity for maximum visibility
    trailOpacity: 0, // No trailing transparency

    // Physics settings
    physicsMode: 'gentleWave', // Wave motion
    gravity: 0,
    vortexStrength: 0,
    vortexRange: 0,
    vortexTwist: 0,
    vortexDirection: 1,

    // Emitter settings
    emitterMode: 'waveLine', // Wave line emitter
    emitterSize: 0.2,
    emitterX: 0.5,
    emitterY: 0.5,
    shellPulseRate: 0.5,

    // Wave settings
    waveAmplitude: 60, // Height of the wave
    waveFrequency: 0.03, // Frequency of the wave

    // Audio simulation settings
    activePalette: 'ambient', // Default to ambient when no one is speaking
    colorAmplitudeIntensity: 1.0, // Maximum color intensity for more dramatic effect
    minAmplitudeDuringSilence: 0.01, // Even more subtle ambient effect
    maxAmplitudeDuringSpeech: 1.0, // Full amplitude for speech
    utteranceLength: 2, // Shorter utterance length for more frequent changes
    minFrequencyHz: 80,
    maxFrequencyHz: 600,
    amplitudeChangeSpeed: 0.2, // Extremely fast amplitude changes for immediate response
    frequencyChangeSpeed: 0.15, // Very fast frequency changes
    silenceChance: 0.02, // Slightly higher chance of silence for more dynamic changes
    speakingChance: 0.08, // Higher chance of speaking for more activity

    // Internal state
    shellLastPulseTime: 0,
    galaxyAngleOffset: 0
  };

  // Utility functions
  function mapRange(value, inMin, inMax, outMin, outMax) {
    return ((value - inMin) * (outMax - outMin)) / (inMax - inMin) + outMin;
  }

  function interpolate(a, b, t) {
    return a + (b - a) * t;
  }

  function getHSLColor(h, s, l, a = 1) {
    return `hsla(${h}, ${s}%, ${l}%, ${a})`;
  }

  // Particle class
  class Particle {
    constructor(initialFrequency, birthAmplitude, speaker) {
      const baseParticleSize = settings.particleSize + (Math.random() - 0.5) * (settings.particleSize * 0.2);
      let ampFactorSize = mapRange(birthAmplitude, settings.minAmplitudeDuringSilence, 1.0, 0, 1);
      const sizeRatio = interpolate(settings.particleSizeMinRatio, settings.particleSizeMaxRatio, ampFactorSize);

      this.size = Math.max(0.3, baseParticleSize * sizeRatio);
      this.opacityFactor = 0.8;
      this.life = settings.maxLife * (0.75 + Math.random() * 0.5);
      this.maxLife = this.life;

      // Select the appropriate color palette based on speaker
      let palette;
      if (speaker === 'user') {
        palette = colorPalettes.user;
      } else if (speaker === 'assistant') {
        palette = colorPalettes.assistant;
      } else {
        palette = colorPalettes.ambient; // Default to ambient
      }

      // Store the speaker for reference
      this.speaker = speaker;

      // Calculate color based on frequency and amplitude
      let freqFactor = mapRange(initialFrequency, settings.minFrequencyHz, settings.maxFrequencyHz, 0, 1);
      let normAmpColor = mapRange(birthAmplitude, settings.minAmplitudeDuringSilence, 1.0, 0, 1);
      const colorShift = (normAmpColor - 0.5) * settings.colorAmplitudeIntensity;
      let effectiveFreqFactor = Math.max(0, Math.min(1, freqFactor + colorShift));

      // No color inversion - use colors as configured from dashboard
      // User particles will use primary color, assistant particles will use secondary color

      // Interpolate between quiet and loud colors
      this.h = interpolate(palette.quiet.h, palette.loud.h, effectiveFreqFactor);
      this.s = interpolate(palette.quiet.s, palette.loud.s, effectiveFreqFactor);
      this.l = interpolate(palette.quiet.l, palette.loud.l, effectiveFreqFactor);

      // Position and velocity based on emitter mode
      if (settings.emitterMode === 'waveLine') {
        // Wave line emitter - particles spawn along a horizontal wave
        this.x = Math.random() * canvas.width;

        // More dynamic wave height based on amplitude
        const waveHeight = settings.waveAmplitude * (0.3 + birthAmplitude * 0.8); // More amplitude influence

        // More dynamic wave position with sharper peaks
        const wavePhase = time * 3 + this.x * settings.waveFrequency * 1.5;
        const waveValue = Math.sin(wavePhase) * Math.pow(Math.abs(Math.sin(wavePhase * 0.5)), 0.5); // Sharper peaks
        this.y = canvas.height / 2 + waveValue * waveHeight;

        // Much more dynamic initial velocities based on amplitude
        const velocityFactor = 1.0 + birthAmplitude * 3.0; // Amplify velocity based on sound level

        // More erratic horizontal movement
        this.vx = (Math.random() - 0.5) * 2.5 * velocityFactor;

        // More dynamic vertical movement with stronger upward bias when loud
        this.vy = (Math.random() - 0.5) * 2.0 * velocityFactor - (0.3 + birthAmplitude * 0.7);
      } else if (settings.emitterMode === 'expandingShell') {
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.random() * 0.1;
        this.x = canvas.width * settings.emitterX + Math.cos(angle) * distance * canvas.width;
        this.y = canvas.height * settings.emitterY + Math.sin(angle) * distance * canvas.height;
        this.vx = Math.cos(angle) * (0.5 + Math.random() * 1.5) * (1 + birthAmplitude * 2);
        this.vy = Math.sin(angle) * (0.5 + Math.random() * 1.5) * (1 + birthAmplitude * 2);
      } else if (settings.emitterMode === 'distributedVibrate') {
        const emitterRadius = canvas.width * settings.emitterSize;
        const angle = Math.random() * Math.PI * 2;
        const distance = Math.pow(Math.random(), 0.5) * emitterRadius;
        this.x = canvas.width * settings.emitterX + Math.cos(angle) * distance;
        this.y = canvas.height * settings.emitterY + Math.sin(angle) * distance;
        this.vx = (Math.random() - 0.5) * 2 * (0.5 + birthAmplitude * 2);
        this.vy = (Math.random() - 0.5) * 2 * (0.5 + birthAmplitude * 2);
      }

      // Store birth time for wave calculations
      this.birthTime = time;
    }

    update() {
      // Update position based on physics mode
      if (settings.physicsMode === 'gentleWave') {
        // Add more sudden, spiky wave motion
        this.vx += Math.sin(this.y * 0.05 + time * 1.2) * 0.08; // More aggressive horizontal movement

        // Add some random jitter for spiky effect
        this.vx += (Math.random() - 0.5) * 0.1;
        this.vy += (Math.random() - 0.5) * 0.1;

        // Faster, more erratic upward drift
        this.vy -= 0.02 + Math.random() * 0.02;
      } else if (settings.physicsMode === 'vortex') {
        // Calculate distance from center
        const dx = this.x - canvas.width * settings.emitterX;
        const dy = this.y - canvas.height * settings.emitterY;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const maxDistance = Math.max(canvas.width, canvas.height) * settings.vortexRange;

        if (distance < maxDistance) {
          // Calculate angle to center
          let angle = Math.atan2(dy, dx);

          // Add vortex effect (perpendicular force)
          const vortexFactor = 1 - (distance / maxDistance);
          const vortexForce = settings.vortexStrength * vortexFactor;

          // Adjust angle based on vortex direction and twist
          angle += settings.vortexDirection * (Math.PI / 2) * settings.vortexTwist;

          // Apply vortex force
          this.vx += Math.cos(angle) * vortexForce;
          this.vy += Math.sin(angle) * vortexForce;
        }

        // Apply gravity towards center
        const gravityFactor = settings.gravity;
        this.vx -= (dx / canvas.width) * gravityFactor;
        this.vy -= (dy / canvas.height) * gravityFactor;
      }

      // Update position - even faster, more responsive movement
      this.x += this.vx * 1.8; // Increased from 1.2 for more sudden movement
      this.y += this.vy * 1.8;

      // Apply less damping for more erratic movement
      this.vx *= 0.95; // Reduced from 0.98 to maintain momentum longer
      this.vy *= 0.95;

      // Update life
      this.life--;
    }

    draw(ctx) {
      // Calculate opacity based on life with more sudden fade-in/fade-out
      let opacity = this.opacityFactor * settings.opacityBase;

      // More sudden fade in
      if (this.life > this.maxLife * (1 - settings.fadeInDuration)) {
        const fadeInProgress = (this.maxLife - this.life) / (this.maxLife * settings.fadeInDuration);
        // Use non-linear scaling for more sudden appearance
        opacity *= Math.pow(fadeInProgress, 0.7);
      }

      // More sudden fade out
      if (this.life < this.maxLife * settings.fadeOutStart) {
        const fadeOutProgress = this.life / (this.maxLife * settings.fadeOutStart);
        // Use non-linear scaling for more sudden disappearance
        opacity *= Math.pow(fadeOutProgress, 1.3);
      }

      // Randomly vary the particle shape for more spiky appearance
      const shapeRandom = Math.random();

      if (shapeRandom < 0.7) {
        // Draw standard circular particle (70% of particles)
        ctx.fillStyle = getHSLColor(this.h, this.s, this.l, opacity);
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();
      } else if (shapeRandom < 0.9) {
        // Draw elongated particle (20% of particles)
        // This creates a more dynamic, spiky effect
        ctx.fillStyle = getHSLColor(this.h, this.s + 5, this.l + 5, opacity);
        ctx.beginPath();

        // Use velocity to determine elongation direction
        const angle = Math.atan2(this.vy, this.vx);
        const stretchFactor = 2.0; // How elongated the particle is

        // Draw elongated shape
        ctx.ellipse(
          this.x,
          this.y,
          this.size * stretchFactor,
          this.size / stretchFactor,
          angle,
          0,
          Math.PI * 2
        );
        ctx.fill();
      } else {
        // Draw small bright spike (10% of particles)
        // This creates occasional bright highlights for a spiky effect
        ctx.fillStyle = getHSLColor(this.h, this.s - 10, this.l + 15, opacity * 1.2);
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size * 0.7, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  }

  // Setup function
  function setup() {
    console.log('Speech particles: Setting up visualization');
    canvas = document.getElementById('mistCanvas');

    if (!canvas) {
      console.warn('Speech particles: Canvas element not found, will retry in 500ms');
      setTimeout(setup, 500); // Retry after a delay
      return;
    }

    console.log('Speech particles: Canvas found, initializing visualization');
    ctx = canvas.getContext('2d');

    // Set canvas size to match container
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Start animation
    animate();

    console.log('Speech particles: Visualization started');
  }

  // Resize canvas to match container
  function resizeCanvas() {
    if (!canvas) return;

    const container = canvas.parentElement;
    canvas.width = container.clientWidth || window.innerWidth;
    canvas.height = container.clientHeight || window.innerHeight;

    // Update emitter position
    settings.emitterX = 0.5;
    settings.emitterY = 0.5;
  }

  // Animation loop
  function animate() {
    time += 0.15; // Even faster time increment for more dynamic wave motion

    // Clear canvas completely - no trailing transparency
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set blending mode for particles
    ctx.globalCompositeOperation = 'lighter';

    // Update audio parameters
    if (window.externalAmplitude !== null) {
      // Log occasionally to avoid console spam
      if (Math.random() < 0.01) {
        console.log('Speech particles: Animation using external audio data',
          'externalAmplitude:', window.externalAmplitude,
          'externalFrequency:', window.externalFrequency,
          'externalSpeaker:', window.externalSpeaker);
      }

      // External control is active - this is when user or assistant is speaking
      currentAmplitude = window.externalAmplitude;
      currentFrequency = window.externalFrequency || currentFrequency;
      currentSpeaker = window.externalSpeaker || 'ambient';

      // Make the visualization more dramatic and responsive when someone is speaking
      if (currentAmplitude > 0.05) { // Lower threshold for more sensitivity
        // Calculate a more dynamic wave amplitude with sharper response
        if (settings.emitterMode === 'waveLine') {
          // More dramatic wave height changes with non-linear scaling
          const amplitudeEffect = Math.pow(currentAmplitude, 0.8) * 100; // Non-linear scaling for more dramatic effect
          settings.waveAmplitude = 30 + amplitudeEffect; // More dynamic range

          // Add some jitter to wave frequency for spikier appearance
          settings.waveFrequency = 0.03 + (Math.random() * 0.01) + (currentAmplitude * 0.01);
        }

        // More dynamic particle size changes
        const sizeFactor = Math.pow(currentAmplitude, 0.7); // Non-linear scaling
        settings.particleSize = 1.0 + sizeFactor * 1.2; // More dynamic size range

        // Spawn particles in bursts for more sudden visual changes
        const baseParticles = Math.round(settings.maxParticles / 200);
        const amplitudeParticles = Math.round(Math.pow(currentAmplitude, 1.2) * (settings.maxParticles / 80));
        const burstFactor = 1.0 + (Math.random() * 0.5); // Random bursts
        particlesToSpawn = Math.round((baseParticles + amplitudeParticles) * burstFactor);

        // Log when significant speech is detected
        if (currentAmplitude > 0.2 && Math.floor(time) % 30 === 0) {
          console.log(`Speech particles: ${currentSpeaker} speaking at amplitude ${currentAmplitude.toFixed(2)}`);
        }
      } else {
        // Reset to normal size when quiet, but with slight variation for subtle movement
        settings.particleSize = 1.0 + (Math.random() * 0.2);
        if (settings.emitterMode === 'waveLine') {
          settings.waveAmplitude = 30 + (Math.random() * 5); // Slight variation in quiet state
          settings.waveFrequency = 0.03 + (Math.random() * 0.005); // Slight variation in frequency
        }
      }
    } else {
      // Internal simulation for ambient state - no one is speaking
      currentSpeaker = 'ambient';

      // Subtle ambient movement - just enough to show the visualization is alive
      if (Math.random() < 0.03) {
        // Very subtle variations for ambient mode
        targetAmplitude = settings.minAmplitudeDuringSilence +
          (Math.random() * 0.05);
      }

      // Randomly change target frequency for ambient movement
      if (Math.random() < 0.05) {
        // Use a narrower frequency range for ambient mode
        const ambientMinFreq = settings.minFrequencyHz + 50;
        const ambientMaxFreq = settings.minFrequencyHz + 150;
        targetFrequency = ambientMinFreq +
          Math.random() * (ambientMaxFreq - ambientMinFreq);
      }

      // Ensure we're using the ambient color palette
      settings.activePalette = 'ambient';

      // Smoothly interpolate current values towards targets
      currentAmplitude += (targetAmplitude - currentAmplitude) * settings.amplitudeChangeSpeed;
      currentFrequency += (targetFrequency - currentFrequency) * settings.frequencyChangeSpeed;
    }

    // Spawn new particles
    // Only calculate particlesToSpawn if it hasn't been set already
    if (typeof particlesToSpawn === 'undefined') {
      particlesToSpawn = 0;

      if (settings.emitterMode === 'waveLine') {
        // Wave line emitter spawns particles along the wave - create dense mist
        particlesToSpawn = Math.round(currentAmplitude * (settings.maxParticles / 150) + 5);
      } else {
        particlesToSpawn = Math.round(currentAmplitude * (settings.maxParticles / 150));
      }
    }

    // Create more particles for a denser mist
    for (let i = 0; i < particlesToSpawn; i++) {
      if (particles.length < settings.maxParticles) {
        particles.push(new Particle(currentFrequency, currentAmplitude, currentSpeaker));
      }
    }

    // Update and draw particles
    for (let i = particles.length - 1; i >= 0; i--) {
      const p = particles[i];
      p.update();
      p.draw(ctx);

      // Remove dead or out-of-bounds particles
      if (p.life <= 0 ||
          p.x < -p.size * 5 || p.x > canvas.width + p.size * 5 ||
          p.y < -p.size * 5 || p.y > canvas.height + p.size * 5) {
        particles.splice(i, 1);
      }
    }

    requestAnimationFrame(animate);
  }

  // External control function
  window.updateAudioSource = function(amplitude, frequency, speaker) {
    console.log('Speech particles: updateAudioSource called with',
      'amplitude:', amplitude,
      'frequency:', frequency,
      'speaker:', speaker);

    window.externalAmplitude = amplitude;
    window.externalFrequency = frequency;
    window.externalSpeaker = speaker;

    // Log the current state after update
    console.log('Speech particles: Global state updated to',
      'externalAmplitude:', window.externalAmplitude,
      'externalFrequency:', window.externalFrequency,
      'externalSpeaker:', window.externalSpeaker);
  };

  // Function to convert hex color to HSL
  function hexToHsl(hex) {
    // Remove the hash if present
    hex = hex.replace('#', '');

    // Parse the hex values
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
      }
      h /= 6;
    }

    return {
      h: Math.round(h * 360),
      s: Math.round(s * 100),
      l: Math.round(l * 100)
    };
  }

  // Function to set custom colors for speech particles
  window.setSpeechParticleColors = function(colors) {
    console.log('🎨 Speech particles: Setting custom colors', colors);

    if (colors.userColor) {
      console.log('🎨 Converting user color:', colors.userColor);
      const userHsl = hexToHsl(colors.userColor);
      console.log('🎨 User HSL:', userHsl);
      colorPalettes.user = {
        name: "User Colors (Custom)",
        quiet: { h: userHsl.h, s: Math.max(50, userHsl.s - 20), l: Math.max(25, userHsl.l - 15) },
        loud:  { h: userHsl.h, s: Math.min(100, userHsl.s + 10), l: Math.min(80, userHsl.l + 10) }
      };
      console.log('🎨 Speech particles: Updated user colors to', colorPalettes.user);
    }

    if (colors.assistantColor) {
      console.log('🎨 Converting assistant color:', colors.assistantColor);
      const assistantHsl = hexToHsl(colors.assistantColor);
      console.log('🎨 Assistant HSL:', assistantHsl);
      colorPalettes.assistant = {
        name: "Assistant Colors (Custom)",
        quiet: { h: assistantHsl.h, s: Math.max(50, assistantHsl.s - 20), l: Math.max(25, assistantHsl.l - 15) },
        loud:  { h: assistantHsl.h, s: Math.min(100, assistantHsl.s + 10), l: Math.min(80, assistantHsl.l + 10) }
      };
      console.log('🎨 Speech particles: Updated assistant colors to', colorPalettes.assistant);
    }

    console.log('🎨 Final color palettes:', colorPalettes);
  };
})();
