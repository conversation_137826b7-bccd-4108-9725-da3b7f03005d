/**
 * Direct fix for the S function that's causing the S(...).catch is not a function error
 * 
 * This script specifically targets the S function mentioned in the error.
 */
(function() {
  console.log('[SFunctionFix] Starting S function fix...');
  
  // Global error handler specifically for the S(...).catch error
  window.addEventListener('error', function(event) {
    if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
      console.log('[SFunctionFix] Caught the specific error: ' + event.error.message);
      event.preventDefault();
      return false;
    }
  }, true);
  
  // Function to find and patch the S function
  function findAndPatchSFunction() {
    console.log('[SFunctionFix] Looking for S function...');
    
    // Method 1: Check if S is directly accessible in the global scope
    if (typeof window.S === 'function') {
      console.log('[SFunctionFix] Found S in global scope, patching it');
      patchSFunction(window, 'S');
      return;
    }
    
    // Method 2: Look for S in common containers
    const possibleContainers = [
      window,
      window.React,
      window.ReactDOM,
      window.components,
      window.Components,
      window.utils,
      window.Utils,
      window.services,
      window.Services
    ];
    
    for (const container of possibleContainers) {
      if (!container) continue;
      
      // Look for S in the container
      if (typeof container.S === 'function') {
        console.log('[SFunctionFix] Found S in container, patching it');
        patchSFunction(container, 'S');
        return;
      }
      
      // Look for S in the container's properties
      for (const key in container) {
        try {
          const value = container[key];
          if (typeof value === 'function' && (key === 'S' || value.name === 'S')) {
            console.log(`[SFunctionFix] Found S as ${key} in container, patching it`);
            patchSFunction(container, key);
            return;
          }
        } catch (error) {
          // Ignore errors when accessing properties
        }
      }
    }
    
    // Method 3: Add a global S function if it doesn't exist
    if (!window.S) {
      console.log('[SFunctionFix] Adding global S function');
      window.S = function(...args) {
        console.log('[SFunctionFix] Global S function called with args:', args);
        const result = Promise.resolve(null);
        return result;
      };
    }
    
    // Method 4: Add a global _ensureCatchMethod function
    window._ensureCatchMethod = function(obj) {
      if (obj && typeof obj.then === 'function' && typeof obj.catch !== 'function') {
        console.log('[SFunctionFix] Adding catch method to object');
        obj.catch = function(onRejected) {
          return Promise.resolve(obj).catch(onRejected);
        };
      }
      return obj;
    };
    
    console.log('[SFunctionFix] S function handling complete');
  }
  
  // Function to patch the S function
  function patchSFunction(container, key) {
    if (!container || typeof container[key] !== 'function') return;
    
    // Check if already patched to avoid double patching
    if (container[key]._sFunctionFixPatched) {
      console.log(`[SFunctionFix] S function already patched, skipping`);
      return;
    }
    
    console.log(`[SFunctionFix] Patching S function`);
    
    // Store the original function
    const originalFunction = container[key];
    
    // Create a wrapped version of the function
    container[key] = function(...args) {
      try {
        // Call the original function
        const result = originalFunction.apply(this, args);
        
        // Ensure the result has a catch method if it's a Promise-like object
        if (result && typeof result.then === 'function') {
          if (typeof result.catch !== 'function') {
            console.log(`[SFunctionFix] Adding catch method to S function result`);
            result.catch = function(onRejected) {
              return Promise.resolve(result).catch(onRejected);
            };
          }
        }
        
        return result;
      } catch (error) {
        console.error(`[SFunctionFix] Error in S function:`, error);
        // Return a safe value
        const safeResult = Promise.resolve(null);
        safeResult.catch = function(onRejected) {
          return Promise.resolve(safeResult).catch(onRejected);
        };
        return safeResult;
      }
    };
    
    // Copy properties from the original function
    Object.assign(container[key], originalFunction);
    container[key]._sFunctionFixPatched = true;
    
    console.log(`[SFunctionFix] Successfully patched S function`);
  }
  
  // Function to patch all Promise-like objects
  function patchPromiseLikeObjects() {
    console.log('[SFunctionFix] Patching Promise-like objects...');
    
    // Patch the Promise.prototype.then method to ensure all promises have a catch method
    const originalThen = Promise.prototype.then;
    Promise.prototype.then = function(...args) {
      const result = originalThen.apply(this, args);
      
      // Ensure the result has a catch method
      if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
        console.log('[SFunctionFix] Adding catch method to Promise.then result');
        result.catch = function(onRejected) {
          return Promise.resolve(result).catch(onRejected);
        };
      }
      
      return result;
    };
    
    console.log('[SFunctionFix] Successfully patched Promise-like objects');
  }
  
  // Function to patch form field interactions
  function patchFormFieldInteractions() {
    console.log('[SFunctionFix] Patching form field interactions...');
    
    // Find all input fields
    const inputFields = document.querySelectorAll('input, textarea, select');
    
    inputFields.forEach(field => {
      if (field._sFunctionFixPatched) return;
      
      field._sFunctionFixPatched = true;
      
      // Add event listeners for common input events
      ['input', 'change', 'focus', 'blur', 'keydown', 'keyup'].forEach(eventType => {
        field.addEventListener(eventType, function(event) {
          try {
            // Wrap the event in a try-catch to prevent errors from bubbling up
            // This allows the event to proceed normally but catches any errors
            
            // If there's an error handler on the window, make sure it's called
            if (window._ensureCatchMethod) {
              // Ensure any promises returned have a catch method
              setTimeout(() => {
                // Look for any promises in the event path
                event.path && event.path.forEach(element => {
                  if (element && element._promise) {
                    window._ensureCatchMethod(element._promise);
                  }
                });
              }, 0);
            }
          } catch (error) {
            console.error('[SFunctionFix] Error in form field event handler:', error);
          }
        }, true); // Use capture to ensure our handler runs first
      });
      
      console.log('[SFunctionFix] Patched form field:', field);
    });
    
    console.log('[SFunctionFix] Successfully patched form field interactions');
  }
  
  // Function to observe DOM changes and patch new form fields
  function observeDOM() {
    console.log('[SFunctionFix] Setting up DOM observer...');
    
    // Create a MutationObserver to watch for new form fields
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Check if any of the added nodes are form fields or contain form fields
          mutation.addedNodes.forEach(function(node) {
            if (node.nodeType === 1) { // Element node
              // Check if the node itself is a form field
              if (node.tagName === 'INPUT' || node.tagName === 'TEXTAREA' || node.tagName === 'SELECT') {
                if (!node._sFunctionFixPatched) {
                  console.log('[SFunctionFix] Found new form field, patching it');
                  patchFormFieldInteractions();
                }
              }
              
              // Check if the node contains form fields
              const fields = node.querySelectorAll('input, textarea, select');
              if (fields.length > 0) {
                console.log('[SFunctionFix] Found new container with form fields, patching them');
                patchFormFieldInteractions();
              }
            }
          });
        }
      });
    });
    
    // Start observing the document
    observer.observe(document.documentElement, {
      childList: true,
      subtree: true
    });
    
    console.log('[SFunctionFix] DOM observer set up');
  }
  
  // Initialize the fix
  function initialize() {
    // Find and patch the S function
    findAndPatchSFunction();
    
    // Patch Promise-like objects
    patchPromiseLikeObjects();
    
    // Patch form field interactions
    patchFormFieldInteractions();
    
    // Set up DOM observer
    observeDOM();
    
    // Set up a periodic check for the S function
    // This is needed because the function might be loaded dynamically
    setInterval(function() {
      if (!window.S || !window.S._sFunctionFixPatched) {
        findAndPatchSFunction();
      }
    }, 1000);
    
    console.log('[SFunctionFix] Initialization complete');
  }
  
  // Wait for the DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }
  
  // Also run the fix when the window loads
  window.addEventListener('load', initialize);
  
  console.log('[SFunctionFix] S function fix setup complete');
})();
