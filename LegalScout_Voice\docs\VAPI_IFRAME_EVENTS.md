# Vapi Iframe Events Documentation

## Overview

This document describes how Vapi's SDK communicates events through iframe messages, which is critical for properly implementing the volume level indicator and transcript rendering in the voice call interface.

## Key Insight

The Vapi SDK embeds an iframe for handling calls and communicates with the parent window through `window.postMessage()`. These messages have a specific format that differs from the direct SDK events.

## Message Format

Iframe messages from Vapi have the following structure:

```javascript
{
  what: 'iframe-call-message',
  callClientId: '17477145600300.6862929156165595',
  from: 'embedded',
  action: 'remote-participants-audio-level',
  participantsAudioLevel: {
    // Audio level data
  }
}
```

## Important Event Types

### Audio Level Events

Audio level events are sent as iframe messages with the action `remote-participants-audio-level`:

```javascript
{
  action: 'remote-participants-audio-level',
  participantsAudioLevel: {
    // Object with participant IDs as keys and audio levels as values
  },
  what: 'iframe-call-message',
  callClientId: '17477145600300.6862929156165595',
  from: 'embedded'
}
```

To process these events:

1. Extract the `participantsAudioLevel` object
2. Convert the values to numbers
3. Find the maximum level
4. Scale the level to be more visible (the raw levels are often very small, 0.01-0.1)

```javascript
const audioLevels = Object.values(event.data.participantsAudioLevel);
if (audioLevels.length > 0) {
  const numericLevels = audioLevels
    .map(level => typeof level === 'number' ? level : parseFloat(level))
    .filter(level => !isNaN(level));
  
  if (numericLevels.length > 0) {
    const level = Math.max(...numericLevels);
    // Scale the level to be more visible
    const scaledLevel = Math.min(level * 5, 1);
    // Use scaledLevel for the volume indicator
  }
}
```

### Transcript Events

Transcript events can come in various formats:

1. Direct SDK events with type `transcript` or `transcription`
2. Iframe messages with action `transcript`
3. Iframe messages containing transcript data in other fields

To handle all possible formats:

```javascript
// Check for transcript data in iframe messages
if (event.data.what === 'iframe-call-message' && 
    (event.data.action === 'transcript' || 
     event.data.transcript || 
     (event.data.message && event.data.message.content))) {
  
  // Extract transcript text
  let transcriptText = '';
  let isFinal = false;
  
  if (event.data.transcript) {
    transcriptText = event.data.transcript;
    isFinal = event.data.is_final || false;
  } else if (event.data.message && event.data.message.content) {
    transcriptText = event.data.message.content;
    isFinal = event.data.message.is_final || false;
  }
  
  // Process the transcript
  if (transcriptText) {
    // Update UI with transcript
  }
}
```

## Implementation Strategy

To properly handle Vapi events, use a dual approach:

1. **Direct SDK Event Listeners**:
   ```javascript
   vapi.on('volume-level', handleVolumeLevel);
   vapi.on('transcript', handleTranscript);
   ```

2. **Window Message Listeners**:
   ```javascript
   window.addEventListener('message', (event) => {
     if (event.data && event.data.what === 'iframe-call-message') {
       // Process iframe messages
     }
   });
   ```

This ensures that events are captured regardless of how they're emitted by the SDK.

## Debugging Tips

1. Add console logging for all window messages:
   ```javascript
   window.addEventListener('message', (event) => {
     console.log('Window message received:', event.data);
     // Process the message
   });
   ```

2. Look for messages with `what: 'iframe-call-message'` in the console

3. Examine the `action` field to determine the type of event

4. For audio levels, inspect the `participantsAudioLevel` object

5. For transcripts, check various fields that might contain the text

## Best Practices

1. **Handle Multiple Event Formats**: Always check for multiple event formats to ensure compatibility with different SDK versions

2. **Scale Audio Levels**: Raw audio levels are often very small, so scale them up for better visualization

3. **Direct DOM Manipulation**: As a fallback, directly manipulate the DOM to ensure UI updates even if React state updates aren't working

4. **Comprehensive Logging**: Log all events and their data for debugging

5. **Proper Cleanup**: Always remove event listeners when components unmount
