# Preview Component VAPI Integration Guide

## Overview

The preview component needs proper integration with the VAPI system to initiate real calls instead of showing a simulated chat interface. This document outlines the integration requirements and implementation steps.

## Current State

Currently, the `SimplifiedPreview.jsx` component:

1. Renders a start consultation button
2. When clicked, shows a simulated chat interface
3. Attempts to communicate with the parent window if in an iframe
4. Does not properly initiate a real VAPI call

## Required Integration

### 1. VAPI Call Initialization

The start consultation button should:

- Initialize a VAPI call using the proper assistant ID
- Connect to the voice system
- Show call status indicators
- Display the real-time interface for the call

### 2. Parent Window Communication

For iframe scenarios, the component needs to:

- Send the correct message structure to the parent window
- Include all necessary parameters for call initialization
- Handle response from the parent window
- Show appropriate UI based on the parent's response

### 3. Implementation Steps

Replace the current `handleStartConsultation` function with proper VAPI integration:

```jsx
const handleStartConsultation = () => {
  console.log('Start consultation clicked');
  
  // For iframe scenarios, communicate with parent
  if (window !== window.parent) {
    try {
      window.parent.postMessage({
        type: 'REQUEST_START_CONSULTATION',
        firmName,
        practiceDescription,
        assistantId: DEFAULT_ASSISTANT_ID,
        primaryColor,
        secondaryColor,
        mascot: logoUrl || mascot,
        vapiInstructions,
        theme: isDark ? 'dark' : 'light'
      }, '*');
      console.log('Sent message to parent window');
      
      // After sending message, we don't need to do anything else
      // The parent will handle the call initiation
      return;
    } catch (e) {
      console.error('Error sending message to parent:', e);
    }
  }
  
  // For standalone scenarios (not in iframe), initialize direct call
  initializeVapiCall();
};

// Function to initialize VAPI call directly
const initializeVapiCall = () => {
  // Import the VAPI service
  import('../../services/vapiService').then(({ initializeCall }) => {
    initializeCall({
      assistantId: DEFAULT_ASSISTANT_ID,
      customInstructions: vapiInstructions,
      firmName,
      primaryColor,
      secondaryColor,
      onCallStarted: () => {
        // Show call interface
        setShowStartButton(false);
        setChatActive(true);
      },
      onMessageReceived: (text) => {
        // Add assistant message to the chat
        setMessages(prev => [...prev, { sender: 'bot', text }]);
      }
    });
  }).catch(error => {
    console.error('Failed to initialize VAPI call:', error);
    
    // Fallback to simulated chat if VAPI fails
    setShowStartButton(false);
    setChatActive(true);
    setMessages([
      { sender: 'bot', text: welcomeMessage },
      { sender: 'bot', text: informationGathering },
      { sender: 'bot', text: "This is a demo chat interface. The voice call failed to initialize." }
    ]);
  });
};
```

### 4. Integration with App.jsx

App.jsx needs to:

1. Pass the correct VAPI-related props to the SimplifiedPreview component
2. Handle messages from the preview component if it's embedded in an iframe
3. Initialize the VAPI call when requested by the preview

### 5. Testing Steps

1. Test standalone preview (direct URL access)
2. Test embedded preview (inside iframe)
3. Verify call initialization in both scenarios
4. Check error handling for failed calls
5. Verify all customization options are correctly passed to the VAPI system

## VAPI Message Structure

The message structure for parent-child communication should include:

```json
{
  "type": "REQUEST_START_CONSULTATION",
  "firmName": "Example Law Firm",
  "practiceDescription": "We specialize in family law...",
  "assistantId": "vapi_assistant_id",
  "primaryColor": "#4B74AA",
  "secondaryColor": "#2C3E50",
  "mascot": "https://example.com/logo.png",
  "vapiInstructions": "You are a legal assistant...",
  "theme": "dark"
}
```

The parent window should listen for this message and initialize the call accordingly.

## UI Considerations

When integrating with VAPI:

1. Show appropriate loading state during call initialization
2. Provide visual feedback for microphone status
3. Include option to end the call
4. Show error messages for failed calls
5. Consider adding a text input fallback for voice communications

## Next Steps

1. Implement the VAPI integration
2. Test in various scenarios
3. Add proper error handling
4. Update documentation with implementation details 