<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bug Reporter Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        .error-button {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .log-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .warning-button {
            background: #ffc107;
            color: black;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .bug-report-demo {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 48px;
            height: 48px;
            background: #ef4444;
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
            opacity: 0.7;
        }
        .bug-report-demo:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .status {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🐛 Bug Reporter Test Page</h1>
    
    <div class="status">
        <strong>Status:</strong> This is a test page to demonstrate the bug reporter functionality.
        Look for the small 🐛 button in the bottom-right corner!
    </div>

    <div class="test-section">
        <h2>Test Console Logs</h2>
        <p>Click these buttons to generate different types of console logs that will be captured by the bug reporter:</p>
        
        <button class="log-button" onclick="console.log('Test info log:', new Date())">
            Generate Info Log
        </button>
        
        <button class="warning-button" onclick="console.warn('Test warning:', 'Something might be wrong')">
            Generate Warning
        </button>
        
        <button class="error-button" onclick="console.error('Test error:', 'Something went wrong!')">
            Generate Error Log
        </button>
    </div>

    <div class="test-section">
        <h2>Test JavaScript Errors</h2>
        <p>These buttons will trigger actual JavaScript errors that will be captured:</p>
        
        <button class="error-button" onclick="triggerError()">
            Trigger TypeError
        </button>
        
        <button class="error-button" onclick="triggerPromiseRejection()">
            Trigger Promise Rejection
        </button>
        
        <button class="error-button" onclick="triggerReferenceError()">
            Trigger Reference Error
        </button>
    </div>

    <div class="test-section">
        <h2>How to Test</h2>
        <ol>
            <li><strong>Generate some logs:</strong> Click the buttons above to create console logs and errors</li>
            <li><strong>Open bug reporter:</strong> Click the 🐛 button in the bottom-right corner</li>
            <li><strong>Fill out the form:</strong> 
                <ul>
                    <li>Select a report type (Bug, Feature, etc.)</li>
                    <li>Write a description of the issue</li>
                    <li>Optionally provide your email</li>
                    <li>Keep "Include logs" checked to see the captured logs</li>
                    <li>Keep "Include screenshot" checked to capture the page</li>
                </ul>
            </li>
            <li><strong>Submit:</strong> Click "Submit Report" to send to Slack</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <ul>
            <li>✅ Bug button should be barely visible in bottom-right corner</li>
            <li>✅ Clicking it opens a modal with the bug report form</li>
            <li>✅ Form should include all the logs generated above</li>
            <li>✅ Screenshot should capture this page</li>
            <li>✅ Report should be sent to your configured Slack channel</li>
        </ul>
    </div>

    <!-- Demo Bug Report Button (since this is a static HTML file) -->
    <button class="bug-report-demo" onclick="alert('In the real app, this would open the bug reporter modal!\n\nTo test the real bug reporter, run your React app and look for the 🐛 button.')" title="Bug Reporter Demo">
        🐛
    </button>

    <script>
        // Test functions to generate errors
        function triggerError() {
            try {
                // This will cause a TypeError
                let obj = null;
                obj.someProperty.anotherProperty = 'test';
            } catch (error) {
                // Re-throw to trigger global error handler
                throw error;
            }
        }

        function triggerPromiseRejection() {
            // Create an unhandled promise rejection
            Promise.reject(new Error('Test promise rejection error'));
        }

        function triggerReferenceError() {
            // Reference a variable that doesn't exist
            console.log(nonExistentVariable);
        }

        // Add some initial logs
        console.log('Bug Reporter Test Page loaded at:', new Date());
        console.info('This page is for testing the bug reporter functionality');
        
        // Simulate some application logs
        setTimeout(() => {
            console.log('Simulated app initialization complete');
        }, 1000);

        setTimeout(() => {
            console.warn('Simulated warning: API response took longer than expected');
        }, 2000);

        // Add global error handlers to show they're working
        window.addEventListener('error', (event) => {
            console.log('Global error handler caught:', event.error.message);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.log('Global promise rejection handler caught:', event.reason.message);
        });
    </script>
</body>
</html>
