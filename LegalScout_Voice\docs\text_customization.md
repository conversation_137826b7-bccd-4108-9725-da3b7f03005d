# Text Customization in LegalScout

This document describes the text customization functionality in the LegalScout platform, focusing on the text background transparency feature.

## Text Background Transparency

### Overview

The text background transparency feature allows users to control the opacity of the background behind practice area text. This creates a semi-transparent panel that improves text readability regardless of the background color.

### Benefits

- **Improved Readability**: Creates contrast between text and any background color
- **Visual Consistency**: Maintains readability across different background colors and patterns
- **Simplified Controls**: Offers an intuitive slider for easy adjustment
- **Adaptive Design**: Text color automatically adjusts (light or dark) based on the background color

### Implementation Details

#### User Interface

- A slider control labeled "Text Background Transparency" is provided in the customization panel
- The slider ranges from 0 (completely transparent) to 1 (completely opaque)
- A percentage label (0-100%) indicates the current transparency level

#### Technical Implementation

The feature is implemented across several components:

1. **SimpleDemoPage.jsx**:
   - Contains the slider control UI
   - Manages the state value for `practiceAreaBackgroundOpacity`
   - Updates the customizations object passed to the preview component

2. **PreviewPage.jsx**:
   - Handles the URL parameter for background opacity
   - Sets a default value of 0.1 (10% opacity)

3. **PreviewInterface.tsx**:
   - Applies the opacity value to create a semi-transparent background:
   - `backgroundColor: rgba(${hexToRgb(backgroundColorState)}, ${practiceAreaBackgroundOpacityState})`
   - Text colors automatically adjust using the `isDark` state variable
   - All text elements (h1, h2, h3, h4, strong) use contrasting colors based on background

### Usage Guidelines

For optimal readability:
- Use opacity values between 0.1 and 0.3 for subtle backgrounds
- Higher values (0.4-0.7) provide stronger contrast for important information
- Setting to 0 removes the background completely, useful for clean interfaces
- Setting to 1 creates a solid background that completely blocks the underlying content

### Testing

When testing this feature, verify:
1. The slider adjusts the background opacity correctly
2. Text remains readable across the full range of opacity values
3. Text color adapts appropriately to light and dark background settings
4. The percentage label accurately reflects the slider value

### Potential Enhancements

Future improvements could include:
- Text size/weight customization
- Automated accessibility checks for optimal contrast ratios
- Additional background effects (blur, gradient, etc.)
- Presets for commonly used opacity values 