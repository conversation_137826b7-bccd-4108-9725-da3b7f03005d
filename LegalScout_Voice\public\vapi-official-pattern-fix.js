/**
 * Vapi Official Pattern Fix
 * 
 * This script implements the official Vapi Web SDK patterns from docs.vapi.ai/sdk/web
 * to fix the URL object error and ensure proper call functionality.
 */

(function() {
  'use strict';

  console.log('[VapiOfficialPatternFix] Starting Vapi Web SDK pattern fixes...');

  // Prevent multiple executions
  if (window.__VAPI_OFFICIAL_PATTERN_FIX_APPLIED) {
    console.log('[VapiOfficialPatternFix] Already applied, skipping...');
    return;
  }

  // 1. Fix Vapi Instance Creation
  const fixVapiInstanceCreation = () => {
    console.log('[VapiOfficialPatternFix] Fixing Vapi instance creation...');
    
    // Override the global Vapi constructor to ensure correct usage
    if (window.Vapi) {
      const OriginalVapi = window.Vapi;
      
      window.Vapi = function(apiKeyOrJwt, options) {
        console.log('[VapiOfficialPatternFix] Creating Vapi instance with official pattern');
        
        // Official pattern: new Vapi("your-public-key-or-jwt")
        // No configuration object should be passed
        if (typeof apiKeyOrJwt === 'string' && !options) {
          console.log('[VapiOfficialPatternFix] ✅ Correct usage: new Vapi(apiKey)');
          return new OriginalVapi(apiKeyOrJwt);
        } else if (typeof apiKeyOrJwt === 'string' && options) {
          console.warn('[VapiOfficialPatternFix] ⚠️ Ignoring options parameter, using official pattern');
          return new OriginalVapi(apiKeyOrJwt);
        } else {
          console.error('[VapiOfficialPatternFix] ❌ Invalid Vapi constructor usage');
          throw new Error('Vapi constructor expects a string API key or JWT');
        }
      };
      
      // Copy all static methods and properties
      Object.setPrototypeOf(window.Vapi, OriginalVapi);
      Object.assign(window.Vapi, OriginalVapi);
    }
  };

  // 2. Fix Call Start Method
  const fixCallStartMethod = () => {
    console.log('[VapiOfficialPatternFix] Fixing call start method...');
    
    // Intercept vapi.start() calls to ensure correct usage
    const originalFetch = window.fetch;
    
    window.fetch = function(url, options) {
      // Check for malformed URLs that indicate the old pattern
      if (typeof url === 'object' || (typeof url === 'string' && url.includes('[object Object]'))) {
        console.error('[VapiOfficialPatternFix] ❌ Detected malformed URL in fetch:', url);
        console.error('[VapiOfficialPatternFix] This indicates incorrect Vapi SDK usage');
        
        // Try to fix the URL if possible
        if (typeof url === 'string' && url.includes('[object Object]')) {
          const fixedUrl = url.replace('[object Object]', 'https://api.vapi.ai');
          console.log('[VapiOfficialPatternFix] 🔧 Attempting to fix URL:', fixedUrl);
          return originalFetch.call(this, fixedUrl, options);
        }
        
        // If we can't fix it, throw an error
        throw new Error('Invalid URL in fetch request. Please use official Vapi Web SDK pattern: new Vapi(apiKey)');
      }
      
      return originalFetch.call(this, url, options);
    };
  };

  // 3. Add Vapi Usage Validator
  const addVapiUsageValidator = () => {
    console.log('[VapiOfficialPatternFix] Adding Vapi usage validator...');
    
    // Monitor for incorrect Vapi usage patterns
    const originalConsoleError = console.error;
    
    console.error = function(...args) {
      const message = args.join(' ');
      
      // Detect URL object errors
      if (message.includes("property 'url': url should be a string")) {
        console.log('[VapiOfficialPatternFix] 🚨 Detected URL object error - this indicates incorrect Vapi SDK usage');
        console.log('[VapiOfficialPatternFix] 📖 Please use the official pattern: new Vapi("your-api-key")');
        console.log('[VapiOfficialPatternFix] 📖 Then call: vapi.start("assistant-id")');
        console.log('[VapiOfficialPatternFix] 📖 See: https://docs.vapi.ai/sdk/web');
      }
      
      return originalConsoleError.apply(this, args);
    };
  };

  // 4. Provide Official Pattern Helper
  const addOfficialPatternHelper = () => {
    console.log('[VapiOfficialPatternFix] Adding official pattern helper...');
    
    // Add a helper function to the window for easy debugging
    window.createVapiOfficially = function(apiKey) {
      console.log('[VapiOfficialPatternFix] Creating Vapi instance using official pattern');
      
      if (!apiKey || typeof apiKey !== 'string') {
        throw new Error('API key must be a string');
      }
      
      if (!window.Vapi) {
        throw new Error('Vapi SDK not loaded. Please ensure the Vapi Web SDK is loaded first.');
      }
      
      // Official pattern from docs.vapi.ai/sdk/web
      const vapi = new window.Vapi(apiKey);
      
      console.log('[VapiOfficialPatternFix] ✅ Vapi instance created successfully');
      console.log('[VapiOfficialPatternFix] Available methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(vapi)));
      
      return vapi;
    };
    
    // Add a helper for starting calls
    window.startVapiCallOfficially = async function(vapi, assistantIdOrConfig) {
      console.log('[VapiOfficialPatternFix] Starting call using official pattern');
      
      if (!vapi || typeof vapi.start !== 'function') {
        throw new Error('Invalid Vapi instance provided');
      }
      
      if (!assistantIdOrConfig) {
        throw new Error('Assistant ID or configuration is required');
      }
      
      try {
        // Official pattern from docs.vapi.ai/sdk/web
        const call = await vapi.start(assistantIdOrConfig);
        console.log('[VapiOfficialPatternFix] ✅ Call started successfully:', call);
        return call;
      } catch (error) {
        console.error('[VapiOfficialPatternFix] ❌ Call start failed:', error);
        throw error;
      }
    };
  };

  // 5. Add Event Listener Helpers
  const addEventListenerHelpers = () => {
    console.log('[VapiOfficialPatternFix] Adding event listener helpers...');
    
    // Helper for setting up official event listeners
    window.setupVapiEventsOfficially = function(vapi, callbacks = {}) {
      console.log('[VapiOfficialPatternFix] Setting up event listeners using official pattern');
      
      if (!vapi || typeof vapi.on !== 'function') {
        throw new Error('Invalid Vapi instance provided');
      }
      
      // Official events from docs.vapi.ai/sdk/web
      const officialEvents = [
        'speech-start',
        'speech-end', 
        'call-start',
        'call-end',
        'volume-level',
        'message',
        'error'
      ];
      
      officialEvents.forEach(eventName => {
        if (callbacks[eventName] && typeof callbacks[eventName] === 'function') {
          vapi.on(eventName, callbacks[eventName]);
          console.log(`[VapiOfficialPatternFix] ✅ Set up ${eventName} listener`);
        }
      });
      
      console.log('[VapiOfficialPatternFix] ✅ All event listeners set up');
    };
  };

  // 6. Add Cache Busting
  const addCacheBusting = () => {
    console.log('[VapiOfficialPatternFix] Adding cache busting for Vapi SDK...');
    
    // Force reload of Vapi SDK if needed using official npm CDNs
    // Note: cdn.vapi.ai does not exist (confirmed DNS_PROBE_FINISHED_NXDOMAIN)
    if (!window.Vapi) {
      const cdnSources = [
        `https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js?t=${Date.now()}`,
        `https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js?t=${Date.now()}`
      ];

      let currentIndex = 0;

      const tryLoadFromCDN = () => {
        if (currentIndex >= cdnSources.length) {
          console.error('[VapiOfficialPatternFix] ❌ Failed to load Vapi SDK from all CDN sources');
          return;
        }

        const script = document.createElement('script');
        script.src = cdnSources[currentIndex];

        script.onload = () => {
          console.log(`[VapiOfficialPatternFix] ✅ Vapi SDK loaded from ${cdnSources[currentIndex]}`);
          fixVapiInstanceCreation();
        };

        script.onerror = () => {
          console.warn(`[VapiOfficialPatternFix] ⚠️ Failed to load from ${cdnSources[currentIndex]}, trying next...`);
          currentIndex++;
          tryLoadFromCDN();
        };

        document.head.appendChild(script);
      };

      tryLoadFromCDN();
    }
  };

  // Apply all fixes
  try {
    fixVapiInstanceCreation();
    fixCallStartMethod();
    addVapiUsageValidator();
    addOfficialPatternHelper();
    addEventListenerHelpers();
    addCacheBusting();
    
    // Mark as applied
    window.__VAPI_OFFICIAL_PATTERN_FIX_APPLIED = true;
    
    console.log('[VapiOfficialPatternFix] ✅ All official pattern fixes applied successfully');
    console.log('[VapiOfficialPatternFix] 📖 Use window.createVapiOfficially(apiKey) for correct usage');
    console.log('[VapiOfficialPatternFix] 📖 See https://docs.vapi.ai/sdk/web for documentation');
  } catch (error) {
    console.error('[VapiOfficialPatternFix] ❌ Error applying fixes:', error);
  }

})();
