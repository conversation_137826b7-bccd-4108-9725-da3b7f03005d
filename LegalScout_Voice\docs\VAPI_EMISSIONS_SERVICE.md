# Vapi Emissions Service

This document provides an overview of the Vapi Emissions Service, which captures and processes emissions from Vapi calls using the Model Context Protocol (MCP) integration.

## Overview

The Vapi Emissions Service provides a more direct and privacy-focused alternative to using external webhooks for capturing call data. It uses the Vapi MCP Server to monitor calls in real-time and store the data in Supabase.

## Features

- **Real-time Call Monitoring**: Monitor calls in real-time to capture transcripts, messages, and tool executions
- **Dossier Data Extraction**: Extract dossier data from tool executions
- **Supabase Integration**: Store call data in Supabase for later analysis
- **Privacy-focused**: Keep all data within your own infrastructure

## Implementation

The implementation consists of the following components:

1. **VapiEmissionsService**: A service that captures and processes emissions from Vapi calls
2. **useVapiEmissions**: A React hook that provides a simple interface for using the emissions service
3. **call_records Table**: A Supabase table for storing call data

### VapiEmissionsService

The `VapiEmissionsService` is responsible for:

- Connecting to the Vapi MCP Server
- Monitoring calls for emissions
- Processing and storing call data

```javascript
// Initialize the emissions service
await vapiEmissionsService.initialize(apiKey);

// Start monitoring a call
await vapiEmissionsService.monitorCall(callId, {
  onTranscript: (transcript) => { /* Handle transcript */ },
  onMessage: (message) => { /* Handle message */ },
  onToolExecution: (toolExecution) => { /* Handle tool execution */ },
  onCallEnd: (call) => { /* Handle call end */ }
});

// Stop monitoring a call
vapiEmissionsService.stopMonitoring(callId);

// Extract dossier data from a call
const dossierData = vapiEmissionsService.extractDossierData(call);
```

### useVapiEmissions Hook

The `useVapiEmissions` hook provides a React interface for the emissions service:

```javascript
const {
  initialized,
  monitoring,
  error,
  transcripts,
  messages,
  toolExecutions,
  callData,
  dossierData,
  startMonitoring,
  stopMonitoring
} = useVapiEmissions({
  apiKey,
  callId
});
```

### call_records Table

The `call_records` table in Supabase stores the following data:

- Call metadata (ID, assistant ID, status, duration, etc.)
- Transcripts
- Messages
- Tool executions
- Custom metadata

## Advantages over Webhooks

1. **Privacy**: All data stays within your own infrastructure
2. **Reliability**: No dependency on external services
3. **Cost**: No need to pay for external webhook services
4. **Flexibility**: Full control over data processing and storage
5. **Real-time**: Monitor calls in real-time

## Migration from Make.com Webhooks

To migrate from Make.com webhooks to the Vapi Emissions Service:

1. Update the `VapiCall` component to use the `useVapiEmissions` hook
2. Replace webhook calls with direct calls to the emissions service
3. Update any code that depends on webhook responses to use the emissions service data

## Future Improvements

1. **Analytics**: Add analytics capabilities to the emissions service
2. **Notifications**: Add notification capabilities for important events
3. **Integration with CRM**: Integrate with CRM systems for lead management
4. **Custom Processing**: Add custom processing for specific tool executions
5. **Batch Processing**: Add batch processing for historical calls
