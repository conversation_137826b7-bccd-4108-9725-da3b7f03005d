/**
 * Test Attorney Fix
 * 
 * This script tests the attorney profile persistence fix.
 */

(function() {
  console.log('[TestAttorneyFix] Starting test...');
  
  // Wait for the simplified attorney manager to be available
  function waitForAttorneyManager() {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (window.simplifiedAttorneyManager) {
          clearInterval(checkInterval);
          resolve(window.simplifiedAttorneyManager);
        }
      }, 100);
      
      // Timeout after 5 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        console.error('[TestAttorneyFix] Timed out waiting for simplifiedAttorneyManager');
        resolve(null);
      }, 5000);
    });
  }
  
  // Wait for the attorney sync helper to be available
  function waitForSyncHelper() {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (window.attorneySyncHelper) {
          clearInterval(checkInterval);
          resolve(window.attorneySyncHelper);
        }
      }, 100);
      
      // Timeout after 5 seconds
      setTimeout(() => {
        clearInterval(checkInterval);
        console.error('[TestAttorneyFix] Timed out waiting for attorneySyncHelper');
        resolve(null);
      }, 5000);
    });
  }
  
  // Run the test
  async function runTest() {
    try {
      console.log('[TestAttorneyFix] Waiting for attorney manager...');
      const attorneyManager = await waitForAttorneyManager();
      
      if (!attorneyManager) {
        console.error('[TestAttorneyFix] Attorney manager not available');
        return;
      }
      
      console.log('[TestAttorneyFix] Waiting for sync helper...');
      const syncHelper = await waitForSyncHelper();
      
      if (!syncHelper) {
        console.error('[TestAttorneyFix] Sync helper not available');
        return;
      }
      
      console.log('[TestAttorneyFix] Getting current attorney...');
      const attorney = attorneyManager.getCurrentAttorney();
      
      console.log('[TestAttorneyFix] Current attorney:', attorney);
      
      // Test saving to localStorage
      console.log('[TestAttorneyFix] Testing saveToLocalStorage...');
      const savedAttorney = attorneyManager.saveToLocalStorage(attorney);
      
      console.log('[TestAttorneyFix] Saved attorney:', savedAttorney);
      
      // Test loading from localStorage
      console.log('[TestAttorneyFix] Testing loadFromLocalStorage...');
      const loadedAttorney = attorneyManager.loadFromLocalStorage();
      
      console.log('[TestAttorneyFix] Loaded attorney:', loadedAttorney);
      
      // Test updating attorney
      console.log('[TestAttorneyFix] Testing updateAttorney...');
      const updatedAttorney = attorneyManager.updateAttorney({
        name: 'Test Attorney ' + Date.now()
      });
      
      console.log('[TestAttorneyFix] Updated attorney:', updatedAttorney);
      
      // Test ensuring persistence
      console.log('[TestAttorneyFix] Testing ensureAttorneyPersistence...');
      const persistenceResult = await syncHelper.ensureAttorneyPersistence({
        attorney: updatedAttorney,
        syncWithVapi: false
      });
      
      console.log('[TestAttorneyFix] Persistence result:', persistenceResult);
      
      console.log('[TestAttorneyFix] Test completed successfully!');
    } catch (error) {
      console.error('[TestAttorneyFix] Test failed:', error);
    }
  }
  
  // Run the test when the document is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runTest);
  } else {
    // Wait a bit to ensure other scripts have loaded
    setTimeout(runTest, 1000);
  }
})();
