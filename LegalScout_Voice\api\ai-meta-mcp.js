/**
 * AI Meta MCP API Endpoint
 * 
 * This file provides a simple API endpoint for the AI Meta MCP server.
 * It's used by the Web Evaluator component to call the web evaluation tools.
 */

import { VM } from "vm2";
import 'isomorphic-fetch';
import { J<PERSON><PERSON> } from "jsdom";

// Simple implementation of web evaluation tools
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { name, arguments: args } = req.body;

    // Handle different tools
    switch (name) {
      case 'web_evaluate':
        return handleWebEvaluate(req, res, args);
      
      case 'web_extract_data':
        return handleWebExtractData(req, res, args);
      
      case 'generate_web_eval_script':
        return handleGenerateScript(req, res, args);
      
      default:
        return res.status(404).json({
          content: [{ 
            type: "text", 
            text: JSON.stringify({
              success: false,
              error: `Tool ${name} not found`
            }, null, 2)
          }]
        });
    }
  } catch (error) {
    console.error('Error handling request:', error);
    return res.status(500).json({
      content: [{ 
        type: "text", 
        text: JSON.stringify({
          success: false,
          error: error.message
        }, null, 2)
      }]
    });
  }
}

// Handle web evaluation
async function handleWebEvaluate(req, res, args) {
  const { url, evaluationScript, options = {} } = args;

  try {
    // Fetch the web page
    const response = await fetchWebPage(url, options);
    
    // Create a virtual DOM from the HTML
    const { dom, window } = createVirtualDOM(response.html);
    
    // Execute the evaluation script in the context of the virtual DOM
    const result = executeEvaluationScript(evaluationScript, window, response);
    
    // Clean up
    window.close();
    
    return res.status(200).json({
      content: [{ 
        type: "text", 
        text: JSON.stringify({
          success: true,
          url: url,
          result: result,
          status: response.status,
          headers: response.headers,
        }, null, 2)
      }]
    });
  } catch (error) {
    console.error("Error evaluating web page:", error);
    return res.status(500).json({
      content: [{ 
        type: "text", 
        text: JSON.stringify({
          success: false,
          url: url,
          error: error.message,
        }, null, 2)
      }]
    });
  }
}

// Handle data extraction
async function handleWebExtractData(req, res, args) {
  const { url, dataSchema, options = {} } = args;

  try {
    // Fetch the web page
    const response = await fetchWebPage(url, options);
    
    // Create a virtual DOM from the HTML
    const { dom, window } = createVirtualDOM(response.html);
    
    // Extract data according to the schema
    const extractedData = extractStructuredData(window.document, dataSchema);
    
    // Clean up
    window.close();
    
    return res.status(200).json({
      content: [{ 
        type: "text", 
        text: JSON.stringify({
          success: true,
          url: url,
          data: extractedData,
          status: response.status,
        }, null, 2)
      }]
    });
  } catch (error) {
    console.error("Error extracting data from web page:", error);
    return res.status(500).json({
      content: [{ 
        type: "text", 
        text: JSON.stringify({
          success: false,
          url: url,
          error: error.message,
        }, null, 2)
      }]
    });
  }
}

// Handle script generation
async function handleGenerateScript(req, res, args) {
  const { description, dataRequirements, exampleUrl } = args;

  try {
    // If an example URL is provided, fetch it to help with script generation
    let html = "";
    if (exampleUrl) {
      const response = await fetchWebPage(exampleUrl);
      html = response.html;
    }
    
    // Generate a script template based on the requirements
    const scriptTemplate = generateEvaluationScriptTemplate(description, dataRequirements, html);
    
    return res.status(200).json({
      content: [{ 
        type: "text", 
        text: JSON.stringify({
          success: true,
          scriptTemplate: scriptTemplate,
          usage: "Use this script with the web_evaluate tool",
        }, null, 2)
      }]
    });
  } catch (error) {
    console.error("Error generating evaluation script:", error);
    return res.status(500).json({
      content: [{ 
        type: "text", 
        text: JSON.stringify({
          success: false,
          error: error.message,
        }, null, 2)
      }]
    });
  }
}

// Helper functions (copied from web-eval-tools.js)

async function fetchWebPage(url, options = {}) {
  const { userAgent, headers = {}, timeout = 10000 } = options;
  
  const fetchOptions = {
    headers: {
      'User-Agent': userAgent || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
      ...headers,
    },
    timeout: timeout,
  };
  
  const response = await fetch(url, fetchOptions);
  const html = await response.text();
  
  return {
    html,
    status: response.status,
    headers: Object.fromEntries(response.headers.entries()),
    url: response.url,
  };
}

function createVirtualDOM(html) {
  const dom = new JSDOM(html, {
    url: "https://example.org/",
    referrer: "https://example.com/",
    contentType: "text/html",
    includeNodeLocations: true,
    storageQuota: 10000000,
    runScripts: "outside-only",
  });
  
  return { dom, window: dom.window };
}

function executeEvaluationScript(script, window, response) {
  const vm = new VM({
    timeout: 5000,
    sandbox: {
      window,
      document: window.document,
      response,
      console: console,
    },
  });
  
  // Wrap the script in a function and execute it
  const wrappedScript = `
    (function() {
      try {
        ${script}
      } catch (error) {
        return { error: error.message };
      }
    })();
  `;
  
  return vm.run(wrappedScript);
}

function extractStructuredData(document, schema) {
  const result = {};
  
  // Extract individual fields
  if (schema.fields && schema.fields.length > 0) {
    for (const field of schema.fields) {
      try {
        const element = document.querySelector(field.selector);
        if (!element) {
          result[field.name] = null;
          continue;
        }
        
        let value;
        if (field.attribute) {
          value = element.getAttribute(field.attribute);
        } else {
          value = element.textContent.trim();
        }
        
        // Apply transformation if specified
        if (field.transform) {
          const transformFn = new Function('value', `return ${field.transform}`);
          value = transformFn(value);
        }
        
        result[field.name] = value;
      } catch (error) {
        console.error(`Error extracting field ${field.name}:`, error);
        result[field.name] = null;
      }
    }
  }
  
  // Extract collections
  if (schema.collections && schema.collections.length > 0) {
    for (const collection of schema.collections) {
      try {
        const items = document.querySelectorAll(collection.itemSelector);
        result[collection.name] = Array.from(items).map(item => {
          const itemData = {};
          
          for (const field of collection.fields) {
            try {
              const element = item.querySelector(field.selector);
              if (!element) {
                itemData[field.name] = null;
                continue;
              }
              
              let value;
              if (field.attribute) {
                value = element.getAttribute(field.attribute);
              } else {
                value = element.textContent.trim();
              }
              
              // Apply transformation if specified
              if (field.transform) {
                const transformFn = new Function('value', `return ${field.transform}`);
                value = transformFn(value);
              }
              
              itemData[field.name] = value;
            } catch (error) {
              console.error(`Error extracting collection field ${field.name}:`, error);
              itemData[field.name] = null;
            }
          }
          
          return itemData;
        });
      } catch (error) {
        console.error(`Error extracting collection ${collection.name}:`, error);
        result[collection.name] = [];
      }
    }
  }
  
  return result;
}

function generateEvaluationScriptTemplate(description, dataRequirements, html = "") {
  // This is a simple template generator
  // In a real implementation, you might use AI to generate a more sophisticated script
  
  return `
/**
 * Web Evaluation Script
 * Description: ${description}
 * Data Requirements: ${dataRequirements}
 */

// Access the document object to interact with the page
const doc = document;

// Define the data extraction function
function extractData() {
  // Initialize the result object
  const result = {
    title: doc.title,
    url: response.url,
    timestamp: new Date().toISOString(),
    data: {}
  };
  
  try {
    // TODO: Add your custom extraction logic here
    // Example:
    // result.data.heading = doc.querySelector('h1').textContent.trim();
    // result.data.paragraphs = Array.from(doc.querySelectorAll('p')).map(p => p.textContent.trim());
    
    // Add success flag
    result.success = true;
  } catch (error) {
    result.success = false;
    result.error = error.message;
  }
  
  return result;
}

// Execute the extraction and return the result
return extractData();
  `.trim();
}
