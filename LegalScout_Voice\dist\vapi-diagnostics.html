<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi API Key Diagnostics</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #007acc;
            padding-bottom: 10px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007acc;
            background-color: #f8f9fa;
        }
        .success { border-left-color: #28a745; background-color: #d4edda; }
        .error { border-left-color: #dc3545; background-color: #f8d7da; }
        .warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .info { border-left-color: #17a2b8; background-color: #d1ecf1; }
        
        .key-display {
            font-family: 'Courier New', monospace;
            background: #f1f1f1;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #005a9e;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        
        .loading {
            color: #666;
            font-style: italic;
        }
        
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vapi API Key Diagnostics</h1>
        <p>This tool helps diagnose Vapi API key issues in your current environment.</p>
        
        <div class="section info">
            <h3>📊 Environment Information</h3>
            <div id="environment-info">Loading...</div>
        </div>
        
        <div class="grid">
            <div class="section">
                <h3>🔑 API Key Configuration</h3>
                <div id="api-keys">Loading...</div>
            </div>
            
            <div class="section">
                <h3>🌍 Environment Variables</h3>
                <div id="env-vars">Loading...</div>
            </div>
        </div>
        
        <div class="section">
            <h3>🧪 API Tests</h3>
            <button onclick="window.runApiTests()" id="test-btn">Run API Tests</button>
            <button onclick="window.runFullDiagnostics()" id="full-test-btn">Run Full Diagnostics</button>
            <div id="test-results"></div>
        </div>
        
        <div class="section">
            <h3>📋 Diagnostic Report</h3>
            <div id="diagnostic-report"></div>
        </div>
    </div>

    <script>
        // Expected keys
        const EXPECTED_PUBLIC_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';
        const EXPECTED_SECRET_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
        
        // Global diagnostic data
        let diagnosticData = {};
        
        /**
         * Get environment variable from multiple sources
         */
        function getEnvVar(name) {
            // Try window object (runtime injection)
            if (window[name]) {
                return window[name];
            }
            
            // Try import.meta.env (if available)
            if (typeof import !== 'undefined' && import.meta && import.meta.env && import.meta.env[name]) {
                return import.meta.env[name];
            }
            
            return null;
        }
        
        /**
         * Initialize diagnostics on page load
         */
        function initDiagnostics() {
            loadEnvironmentInfo();
            loadApiKeyInfo();
            loadEnvironmentVariables();
        }
        
        /**
         * Load environment information
         */
        function loadEnvironmentInfo() {
            const info = {
                hostname: window.location.hostname,
                protocol: window.location.protocol,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                isLocalhost: window.location.hostname === 'localhost',
                isProduction: window.location.hostname !== 'localhost'
            };
            
            diagnosticData.environment = info;
            
            document.getElementById('environment-info').innerHTML = `
                <strong>Hostname:</strong> ${info.hostname}<br>
                <strong>Protocol:</strong> ${info.protocol}<br>
                <strong>Environment:</strong> ${info.isProduction ? 'Production' : 'Development'}<br>
                <strong>Timestamp:</strong> ${info.timestamp}
            `;
        }
        
        /**
         * Load API key information
         */
        function loadApiKeyInfo() {
            // Try to get keys from vapiConfig if available
            let publicKey = EXPECTED_PUBLIC_KEY;
            let secretKey = EXPECTED_SECRET_KEY;

            try {
                // Try to access the global vapiConfig if it exists
                if (window.getVapiApiKey) {
                    publicKey = window.getVapiApiKey('client') || EXPECTED_PUBLIC_KEY;
                    secretKey = window.getVapiApiKey('server') || EXPECTED_SECRET_KEY;
                } else {
                    // Fallback to environment variables
                    publicKey = getEnvVar('VITE_VAPI_PUBLIC_KEY') || EXPECTED_PUBLIC_KEY;
                    secretKey = getEnvVar('VITE_VAPI_SECRET_KEY') ||
                               getEnvVar('VAPI_SECRET_KEY') ||
                               getEnvVar('VAPI_TOKEN') ||
                               EXPECTED_SECRET_KEY;
                }
            } catch (e) {
                console.warn('Could not load from vapiConfig, using fallbacks:', e.message);
                publicKey = getEnvVar('VITE_VAPI_PUBLIC_KEY') || EXPECTED_PUBLIC_KEY;
                secretKey = getEnvVar('VITE_VAPI_SECRET_KEY') ||
                           getEnvVar('VAPI_SECRET_KEY') ||
                           getEnvVar('VAPI_TOKEN') ||
                           EXPECTED_SECRET_KEY;
            }
            
            const publicKeyValid = publicKey === EXPECTED_PUBLIC_KEY;
            const secretKeyValid = secretKey === EXPECTED_SECRET_KEY;
            
            diagnosticData.apiKeys = {
                publicKey,
                secretKey,
                publicKeyValid,
                secretKeyValid
            };
            
            document.getElementById('api-keys').innerHTML = `
                <div class="${publicKeyValid ? 'success' : 'error'} test-result">
                    <strong>Public Key:</strong> <span class="key-display">${publicKey ? publicKey.substring(0, 8) + '...' : 'NOT FOUND'}</span><br>
                    <strong>Valid:</strong> ${publicKeyValid ? '✅ Yes' : '❌ No'}
                </div>
                <div class="${secretKeyValid ? 'success' : 'error'} test-result">
                    <strong>Secret Key:</strong> <span class="key-display">${secretKey ? secretKey.substring(0, 8) + '...' : 'NOT FOUND'}</span><br>
                    <strong>Valid:</strong> ${secretKeyValid ? '✅ Yes' : '❌ No'}
                </div>
            `;
        }
        
        /**
         * Load environment variables
         */
        function loadEnvironmentVariables() {
            const envVars = [
                'VITE_VAPI_PUBLIC_KEY',
                'VITE_VAPI_SECRET_KEY',
                'VAPI_PUBLIC_KEY',
                'VAPI_SECRET_KEY',
                'VAPI_TOKEN',
                'VAPI_PRIVATE_KEY'
            ];
            
            const foundVars = {};
            let html = '';
            
            envVars.forEach(varName => {
                const value = getEnvVar(varName);
                foundVars[varName] = value;
                const status = value ? '✅ Set' : '❌ Not Set';
                const preview = value ? value.substring(0, 8) + '...' : 'N/A';
                
                html += `
                    <div class="${value ? 'success' : 'error'} test-result">
                        <strong>${varName}:</strong> ${status}<br>
                        ${value ? `<strong>Preview:</strong> <span class="key-display">${preview}</span>` : ''}
                    </div>
                `;
            });
            
            diagnosticData.environmentVariables = foundVars;
            document.getElementById('env-vars').innerHTML = html;
        }
        
        /**
         * Test API key against Vapi API
         */
        async function testApiKey(key, keyType, endpoint = 'phone-number') {
            if (!key) {
                return {
                    success: false,
                    error: 'Key not available',
                    keyType,
                    endpoint
                };
            }
            
            try {
                const response = await fetch(`https://api.vapi.ai/${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${key}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                const responseText = await response.text();
                let responseData = null;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    responseData = responseText;
                }
                
                return {
                    success: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    data: responseData,
                    keyType,
                    endpoint
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    keyType,
                    endpoint
                };
            }
        }
        
        /**
         * Run API connectivity tests
         */
        window.runApiTests = async function() {
            const testBtn = document.getElementById('test-btn');
            const resultsDiv = document.getElementById('test-results');
            
            testBtn.disabled = true;
            testBtn.textContent = 'Running Tests...';
            resultsDiv.innerHTML = '<div class="loading">Running API tests...</div>';
            
            const { publicKey, secretKey } = diagnosticData.apiKeys;
            const tests = [];
            
            // Test 1: Public key for phone numbers (should fail)
            resultsDiv.innerHTML += '<div class="loading">Testing public key for phone numbers...</div>';
            const publicPhoneTest = await testApiKey(publicKey, 'PUBLIC', 'phone-number');
            tests.push(publicPhoneTest);
            
            // Test 2: Secret key for phone numbers (should succeed)
            resultsDiv.innerHTML += '<div class="loading">Testing secret key for phone numbers...</div>';
            const secretPhoneTest = await testApiKey(secretKey, 'SECRET', 'phone-number');
            tests.push(secretPhoneTest);
            
            // Test 3: Public key for assistants
            resultsDiv.innerHTML += '<div class="loading">Testing public key for assistants...</div>';
            const publicAssistantTest = await testApiKey(publicKey, 'PUBLIC', 'assistant');
            tests.push(publicAssistantTest);
            
            // Test 4: Secret key for assistants
            resultsDiv.innerHTML += '<div class="loading">Testing secret key for assistants...</div>';
            const secretAssistantTest = await testApiKey(secretKey, 'SECRET', 'assistant');
            tests.push(secretAssistantTest);
            
            // Display results
            let html = '<h4>Test Results:</h4>';
            
            tests.forEach((test, index) => {
                const testNumber = index + 1;
                const cssClass = test.success ? 'success' : 'error';
                const icon = test.success ? '✅' : '❌';
                
                html += `
                    <div class="${cssClass} test-result">
                        <strong>Test ${testNumber}: ${test.keyType} key for ${test.endpoint}</strong> ${icon}<br>
                        ${test.success ? 
                            `Status: ${test.status} ${test.statusText}` : 
                            `Error: ${test.error || test.status + ' ' + test.statusText}`
                        }<br>
                        ${test.data && Array.isArray(test.data) ? `Found ${test.data.length} items` : ''}
                        ${test.status === 401 ? '<br><strong>⚠️ This indicates wrong API key or key type mismatch</strong>' : ''}
                    </div>
                `;
            });
            
            diagnosticData.apiTests = tests;
            resultsDiv.innerHTML = html;
            
            testBtn.disabled = false;
            testBtn.textContent = 'Run API Tests';
        };

        /**
         * Run full diagnostics and generate report
         */
        window.runFullDiagnostics = async function() {
            const fullTestBtn = document.getElementById('full-test-btn');
            const reportDiv = document.getElementById('diagnostic-report');
            
            fullTestBtn.disabled = true;
            fullTestBtn.textContent = 'Running Full Diagnostics...';
            
            // Run API tests if not already done
            if (!diagnosticData.apiTests) {
                await runApiTests();
            }
            
            // Generate comprehensive report
            const report = generateDiagnosticReport();
            
            reportDiv.innerHTML = `
                <h4>📋 Complete Diagnostic Report</h4>
                <pre>${JSON.stringify(report, null, 2)}</pre>
                
                <h4>💡 Key Findings</h4>
                ${generateRecommendations(report)}
            `;

            fullTestBtn.disabled = false;
            fullTestBtn.textContent = 'Run Full Diagnostics';
        };
        
        /**
         * Generate diagnostic report
         */
        function generateDiagnosticReport() {
            return {
                timestamp: new Date().toISOString(),
                environment: diagnosticData.environment,
                apiKeys: {
                    publicKeyValid: diagnosticData.apiKeys.publicKeyValid,
                    secretKeyValid: diagnosticData.apiKeys.secretKeyValid,
                    publicKeyPreview: diagnosticData.apiKeys.publicKey ? diagnosticData.apiKeys.publicKey.substring(0, 8) + '...' : null,
                    secretKeyPreview: diagnosticData.apiKeys.secretKey ? diagnosticData.apiKeys.secretKey.substring(0, 8) + '...' : null
                },
                environmentVariables: Object.keys(diagnosticData.environmentVariables).reduce((acc, key) => {
                    acc[key] = !!diagnosticData.environmentVariables[key];
                    return acc;
                }, {}),
                apiTests: diagnosticData.apiTests || [],
                summary: {
                    totalTests: diagnosticData.apiTests ? diagnosticData.apiTests.length : 0,
                    passedTests: diagnosticData.apiTests ? diagnosticData.apiTests.filter(t => t.success).length : 0,
                    failedTests: diagnosticData.apiTests ? diagnosticData.apiTests.filter(t => !t.success).length : 0
                }
            };
        }
        
        /**
         * Generate recommendations based on findings
         */
        function generateRecommendations(report) {
            const recommendations = [];
            
            // Check for main issue: secret key not working for phone numbers
            const secretPhoneTest = report.apiTests.find(t => t.keyType === 'SECRET' && t.endpoint === 'phone-number');
            if (secretPhoneTest && !secretPhoneTest.success) {
                recommendations.push({
                    type: 'error',
                    message: '🎯 MAIN ISSUE: Secret key failed for phone numbers',
                    details: `Status: ${secretPhoneTest.status || 'Network Error'}`,
                    action: 'This is likely the cause of your 401 error in production'
                });
            }
            
            // Check key validity
            if (!report.apiKeys.publicKeyValid) {
                recommendations.push({
                    type: 'error',
                    message: 'Public API key is incorrect',
                    action: 'Update VITE_VAPI_PUBLIC_KEY environment variable'
                });
            }
            
            if (!report.apiKeys.secretKeyValid) {
                recommendations.push({
                    type: 'error',
                    message: 'Secret API key is incorrect',
                    action: 'Update VITE_VAPI_SECRET_KEY environment variable'
                });
            }
            
            // Environment-specific recommendations
            if (report.environment.isProduction) {
                recommendations.push({
                    type: 'info',
                    message: 'Running in production environment',
                    action: 'Ensure environment variables are set in your deployment platform (Vercel, Netlify, etc.)'
                });
            }
            
            let html = '';
            recommendations.forEach((rec, index) => {
                const icon = rec.type === 'error' ? '❌' : rec.type === 'warning' ? '⚠️' : 'ℹ️';
                html += `
                    <div class="${rec.type} test-result">
                        ${icon} <strong>${rec.message}</strong><br>
                        ${rec.details ? `Details: ${rec.details}<br>` : ''}
                        Action: ${rec.action}
                    </div>
                `;
            });
            
            return html || '<div class="success test-result">✅ No issues detected!</div>';
        }
        
        // Initialize when page loads
        window.addEventListener('load', initDiagnostics);
    </script>
</body>
</html>
