<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Launch LegalScout</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            text-align: center;
        }
        h1 {
            color: #333;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
        }
        .button:hover {
            background-color: #45a049;
        }
        .info {
            margin: 20px;
            padding: 15px;
            background-color: #f8f8f8;
            border-radius: 5px;
            text-align: left;
        }
    </style>
</head>
<body>
    <h1>Launch LegalScout</h1>
    
    <p>Choose which version of the application to launch:</p>
    
    <a href="/" class="button">Original Version</a>
    <a href="/index-fixed.html" class="button">Fixed Version</a>
    
    <div class="info">
        <h2>What's Fixed in the Fixed Version?</h2>
        <ul>
            <li><strong>Three.js Loading:</strong> Fixed the Three.js loading order to prevent "Cannot read properties of undefined (reading 'Group')" error</li>
            <li><strong>Supabase Connection:</strong> Added a fake Supabase client to prevent waiting for Supabase connection</li>
            <li><strong>Attorney Data:</strong> Ensured a valid attorney is always available in localStorage</li>
            <li><strong>Vapi Integration:</strong> Simplified Vapi integration with proper fallbacks when MCP is not available</li>
        </ul>
        
        <h2>Known Issues</h2>
        <ul>
            <li>React context timeouts - These are expected since we're not loading the full application</li>
            <li>API errors - These are expected since we're not connecting to the real API</li>
        </ul>
    </div>
</body>
</html>
