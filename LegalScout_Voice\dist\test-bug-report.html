<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Bug Report Proxy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Bug Report Proxy Test</h1>
    <p>This page tests the Vite proxy configuration for the bug report endpoint.</p>

    <div class="test-section">
        <h3>Test 1: Simple Bug Report</h3>
        <p>Send a basic bug report through the proxy to Slack.</p>
        <button onclick="testSimpleBugReport()" id="test1-btn">Send Test Bug Report</button>
        <div id="test1-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Detailed Bug Report</h3>
        <p>Send a detailed bug report with all fields populated.</p>
        <button onclick="testDetailedBugReport()" id="test2-btn">Send Detailed Bug Report</button>
        <div id="test2-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Error Handling</h3>
        <p>Test error handling with invalid data.</p>
        <button onclick="testErrorHandling()" id="test3-btn">Test Error Handling</button>
        <div id="test3-result"></div>
    </div>

    <script>
        function showResult(elementId, type, message, details = null) {
            const element = document.getElementById(elementId);
            element.innerHTML = `
                <div class="result ${type}">
                    <strong>${type.toUpperCase()}:</strong> ${message}
                    ${details ? `<pre>${JSON.stringify(details, null, 2)}</pre>` : ''}
                </div>
            `;
        }

        function disableButton(buttonId, disabled = true) {
            const button = document.getElementById(buttonId);
            button.disabled = disabled;
            if (disabled) {
                button.textContent = 'Testing...';
            } else {
                button.textContent = button.textContent.replace('Testing...', button.textContent);
            }
        }

        async function testSimpleBugReport() {
            disableButton('test1-btn');
            showResult('test1-result', 'info', 'Sending test bug report...');

            try {
                const testData = {
                    text: "🐛 **Bug Report from LegalScout (DEV)**\n\n**Type:** bug\n**Description:** Test bug report from proxy test page\n**Email:** <EMAIL>\n**URL:** http://localhost:5173/test-bug-report.html\n**Time:** " + new Date().toLocaleString() + "\n**Browser:** " + navigator.userAgent
                };

                const response = await fetch('/api/bug-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                });

                if (response.ok) {
                    showResult('test1-result', 'success', 'Bug report sent successfully!', {
                        status: response.status,
                        statusText: response.statusText
                    });
                } else {
                    showResult('test1-result', 'error', `Request failed with status ${response.status}`, {
                        status: response.status,
                        statusText: response.statusText
                    });
                }
            } catch (error) {
                showResult('test1-result', 'error', 'Failed to send bug report', {
                    error: error.message,
                    stack: error.stack
                });
            } finally {
                disableButton('test1-btn', false);
                document.getElementById('test1-btn').textContent = 'Send Test Bug Report';
            }
        }

        async function testDetailedBugReport() {
            disableButton('test2-btn');
            showResult('test2-result', 'info', 'Sending detailed bug report...');

            try {
                const testData = {
                    text: "🐛 **Bug Report from LegalScout (DEV)**\n\n**Type:** feature\n**Description:** This is a detailed test report with multiple lines of description.\n\nIt includes:\n- Multiple bullet points\n- Detailed information\n- Various formatting\n\n**Email:** <EMAIL>\n**URL:** http://localhost:5173/test-bug-report.html\n**Time:** " + new Date().toLocaleString() + "\n**Browser:** " + navigator.userAgent
                };

                const response = await fetch('/api/bug-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData),
                });

                if (response.ok) {
                    showResult('test2-result', 'success', 'Detailed bug report sent successfully!', {
                        status: response.status,
                        statusText: response.statusText
                    });
                } else {
                    showResult('test2-result', 'error', `Request failed with status ${response.status}`, {
                        status: response.status,
                        statusText: response.statusText
                    });
                }
            } catch (error) {
                showResult('test2-result', 'error', 'Failed to send detailed bug report', {
                    error: error.message,
                    stack: error.stack
                });
            } finally {
                disableButton('test2-btn', false);
                document.getElementById('test2-btn').textContent = 'Send Detailed Bug Report';
            }
        }

        async function testErrorHandling() {
            disableButton('test3-btn');
            showResult('test3-result', 'info', 'Testing error handling...');

            try {
                // Send invalid data to test error handling
                const response = await fetch('/api/bug-report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ invalid: 'data' }),
                });

                if (response.ok) {
                    showResult('test3-result', 'info', 'Request succeeded (unexpected)', {
                        status: response.status,
                        statusText: response.statusText
                    });
                } else {
                    showResult('test3-result', 'success', 'Error handling working correctly', {
                        status: response.status,
                        statusText: response.statusText,
                        note: 'This is expected behavior for invalid data'
                    });
                }
            } catch (error) {
                showResult('test3-result', 'success', 'Error handling working correctly', {
                    error: error.message,
                    note: 'This is expected behavior for invalid data'
                });
            } finally {
                disableButton('test3-btn', false);
                document.getElementById('test3-btn').textContent = 'Test Error Handling';
            }
        }

        // Show initial status
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Bug Report Proxy Test Page Loaded');
            console.log('Environment:', window.location.hostname === 'localhost' ? 'Development' : 'Production');
        });
    </script>
</body>
</html>
