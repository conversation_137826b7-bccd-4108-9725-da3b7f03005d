/**
 * FIX INVALID JWT CLAIM
 * 
 * Fixes the "invalid claim: missing sub claim" error by clearing corrupted
 * authentication state and forcing a fresh login.
 */

(function() {
  console.log('[FixInvalidJWTClaim] 🔧 Fixing invalid JWT claim error...');

  // Function to clear all authentication-related storage
  function clearAuthenticationState() {
    console.log('[FixInvalidJWTClaim] 🧹 Clearing authentication state...');
    
    try {
      // Clear localStorage items related to Supabase auth
      const authKeys = [
        'supabase.auth.token',
        'sb-utopqxsvudgrtiwenlzl-auth-token',
        'sb-auth-token',
        'attorney',
        'attorneyData',
        'user',
        'session'
      ];
      
      authKeys.forEach(key => {
        if (localStorage.getItem(key)) {
          localStorage.removeItem(key);
          console.log(`[FixInvalidJWTClaim] ✅ Cleared localStorage: ${key}`);
        }
      });
      
      // Clear sessionStorage items
      authKeys.forEach(key => {
        if (sessionStorage.getItem(key)) {
          sessionStorage.removeItem(key);
          console.log(`[FixInvalidJWTClaim] ✅ Cleared sessionStorage: ${key}`);
        }
      });
      
      // Clear any Supabase auth cookies
      document.cookie.split(";").forEach(function(c) { 
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
      });
      
      console.log('[FixInvalidJWTClaim] ✅ Authentication state cleared');
      
    } catch (error) {
      console.error('[FixInvalidJWTClaim] ❌ Error clearing auth state:', error);
    }
  }

  // Function to check if we have an invalid JWT
  function hasInvalidJWT() {
    try {
      // Check localStorage for Supabase auth token
      const authToken = localStorage.getItem('supabase.auth.token') || 
                       localStorage.getItem('sb-utopqxsvudgrtiwenlzl-auth-token');
      
      if (!authToken) {
        console.log('[FixInvalidJWTClaim] No auth token found');
        return false;
      }
      
      const authData = JSON.parse(authToken);
      const accessToken = authData?.access_token || authData?.currentSession?.access_token;
      
      if (!accessToken) {
        console.log('[FixInvalidJWTClaim] No access token in auth data');
        return true; // Invalid if no access token
      }
      
      // Decode JWT to check for 'sub' claim
      const payload = JSON.parse(atob(accessToken.split('.')[1]));
      
      if (!payload.sub) {
        console.log('[FixInvalidJWTClaim] ❌ Missing sub claim in JWT');
        return true; // Invalid - missing sub claim
      }
      
      // Check if token is expired
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp && payload.exp < now) {
        console.log('[FixInvalidJWTClaim] ❌ JWT token expired');
        return true; // Invalid - expired
      }
      
      console.log('[FixInvalidJWTClaim] ✅ JWT token appears valid');
      return false;
      
    } catch (error) {
      console.error('[FixInvalidJWTClaim] ❌ Error checking JWT:', error);
      return true; // Treat as invalid if we can't parse it
    }
  }

  // Function to force fresh authentication
  function forceReauthentication() {
    console.log('[FixInvalidJWTClaim] 🔄 Forcing fresh authentication...');
    
    // Clear authentication state
    clearAuthenticationState();
    
    // Wait a moment for storage to clear
    setTimeout(() => {
      // Redirect to login page or refresh to trigger auth flow
      if (window.location.pathname.includes('/dashboard')) {
        console.log('[FixInvalidJWTClaim] 🔄 Redirecting to login...');
        window.location.href = '/';
      } else {
        console.log('[FixInvalidJWTClaim] 🔄 Refreshing page...');
        window.location.reload();
      }
    }, 500);
  }

  // Main fix function
  function applyFix() {
    console.log('[FixInvalidJWTClaim] 🔍 Checking for invalid JWT claims...');
    
    // Check if we have an invalid JWT
    if (hasInvalidJWT()) {
      console.log('[FixInvalidJWTClaim] 🚨 Invalid JWT detected, applying fix...');
      forceReauthentication();
      return;
    }
    
    // Also listen for Supabase auth errors
    if (window.supabase) {
      window.supabase.auth.onAuthStateChange((event, session) => {
        if (event === 'TOKEN_REFRESHED' && !session) {
          console.log('[FixInvalidJWTClaim] 🚨 Token refresh failed, clearing auth state...');
          clearAuthenticationState();
        }
      });
    }
    
    console.log('[FixInvalidJWTClaim] ✅ JWT claim fix applied');
  }

  // Apply fix immediately
  applyFix();
  
  // Also apply fix when Supabase is loaded
  if (window.supabase) {
    applyFix();
  } else {
    // Wait for Supabase to load
    const checkInterval = setInterval(() => {
      if (window.supabase) {
        clearInterval(checkInterval);
        applyFix();
      }
    }, 100);
    
    // Clear interval after 10 seconds
    setTimeout(() => clearInterval(checkInterval), 10000);
  }
  
  // Expose function globally for manual use
  window.fixInvalidJWTClaim = forceReauthentication;
  
  console.log('[FixInvalidJWTClaim] 🎯 Invalid JWT claim fix loaded');
  
})();
