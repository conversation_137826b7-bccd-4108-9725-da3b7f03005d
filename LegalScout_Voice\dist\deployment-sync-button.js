/**
 * Deployment Sync Button
 * 
 * A deployment-safe version of the sync button that works without MCP.
 */

(function() {
  console.log('[DeploymentSyncButton] Initializing...');
  
  // Create the sync button
  function createSyncButton() {
    // Check if the button already exists
    if (document.getElementById('deployment-sync-button')) {
      console.log('[DeploymentSyncButton] Button already exists');
      return;
    }
    
    // Create the button container
    const buttonContainer = document.createElement('div');
    buttonContainer.style.position = 'fixed';
    buttonContainer.style.bottom = '20px';
    buttonContainer.style.right = '20px';
    buttonContainer.style.zIndex = '9999';
    
    // Create the button
    const button = document.createElement('button');
    button.id = 'deployment-sync-button';
    button.innerHTML = '<i class="fas fa-sync"></i> Sync Profile';
    button.style.padding = '10px 15px';
    button.style.backgroundColor = '#4a90e2';
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '5px';
    button.style.cursor = 'pointer';
    button.style.fontWeight = 'bold';
    button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    button.style.display = 'flex';
    button.style.alignItems = 'center';
    button.style.gap = '8px';
    
    // Add hover effect
    button.onmouseover = function() {
      this.style.backgroundColor = '#3a80d2';
    };
    button.onmouseout = function() {
      this.style.backgroundColor = '#4a90e2';
    };
    
    // Add click event
    button.onclick = async function() {
      try {
        // Disable the button and show loading state
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
        
        // Get the attorney profile manager
        const attorneyProfileManager = window.attorneyProfileManager;
        if (!attorneyProfileManager) {
          throw new Error('Attorney profile manager not available');
        }
        
        // Get the current attorney
        const attorney = attorneyProfileManager.getCurrentAttorney ? 
                        attorneyProfileManager.getCurrentAttorney() : 
                        attorneyProfileManager.getAttorney();
        
        if (!attorney) {
          throw new Error('No attorney profile loaded');
        }
        
        // Refresh from Supabase
        let refreshedAttorney = attorney;
        try {
          if (attorney.id) {
            refreshedAttorney = await attorneyProfileManager.loadAttorneyById(attorney.id);
            if (refreshedAttorney) {
              // Update local state
              attorneyProfileManager.currentAttorney = refreshedAttorney;
              attorneyProfileManager.saveToLocalStorage(refreshedAttorney);
              attorneyProfileManager.notifyListeners();
            }
          }
        } catch (refreshError) {
          console.warn('[DeploymentSyncButton] Error refreshing from Supabase:', refreshError);
        }
        
        // Try to sync with Vapi if available
        let vapiResult = { success: false, message: 'Vapi service not available' };
        try {
          if (typeof attorneyProfileManager.checkVapiSynchronization === 'function') {
            await attorneyProfileManager.checkVapiSynchronization(refreshedAttorney);
            vapiResult = { success: true, message: 'Vapi synchronization checked' };
          }
        } catch (vapiError) {
          console.warn('[DeploymentSyncButton] Error syncing with Vapi:', vapiError);
          vapiResult = { success: false, message: 'Vapi sync error: ' + vapiError.message };
        }
        
        // Show success state
        button.innerHTML = '<i class="fas fa-check"></i> Synced!';
        button.style.backgroundColor = '#4CAF50';
        
        // Show a notification
        showNotification('Profile synced with Supabase' + 
                         (vapiResult.success ? ' and Vapi' : '') + 
                         '!', true);
        
        // Reset button after 3 seconds
        setTimeout(() => {
          button.innerHTML = '<i class="fas fa-sync"></i> Sync Profile';
          button.style.backgroundColor = '#4a90e2';
          button.disabled = false;
        }, 3000);
      } catch (error) {
        console.error('[DeploymentSyncButton] Error syncing profile:', error);
        
        // Show error state
        button.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Error';
        button.style.backgroundColor = '#f44336';
        
        // Show a notification
        showNotification('Error syncing profile: ' + error.message, false);
        
        // Reset button after 3 seconds
        setTimeout(() => {
          button.innerHTML = '<i class="fas fa-sync"></i> Sync Profile';
          button.style.backgroundColor = '#4a90e2';
          button.disabled = false;
        }, 3000);
      }
    };
    
    // Add the button to the container
    buttonContainer.appendChild(button);
    
    // Add the container to the body
    document.body.appendChild(buttonContainer);
    
    console.log('[DeploymentSyncButton] Button created');
  }
  
  // Create a notification
  function showNotification(message, isSuccess) {
    // Create the notification container
    const notificationContainer = document.createElement('div');
    notificationContainer.style.position = 'fixed';
    notificationContainer.style.top = '20px';
    notificationContainer.style.right = '20px';
    notificationContainer.style.zIndex = '10000';
    notificationContainer.style.padding = '15px 20px';
    notificationContainer.style.backgroundColor = isSuccess ? '#4CAF50' : '#f44336';
    notificationContainer.style.color = 'white';
    notificationContainer.style.borderRadius = '5px';
    notificationContainer.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
    notificationContainer.style.opacity = '0';
    notificationContainer.style.transition = 'opacity 0.3s ease-in-out';
    
    // Add the message
    notificationContainer.textContent = message;
    
    // Add the notification to the body
    document.body.appendChild(notificationContainer);
    
    // Show the notification
    setTimeout(() => {
      notificationContainer.style.opacity = '1';
    }, 100);
    
    // Remove the notification after 5 seconds
    setTimeout(() => {
      notificationContainer.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notificationContainer);
      }, 300);
    }, 5000);
  }
  
  // Check if we're on a dashboard page
  function checkForDashboard() {
    // Check if we're on a dashboard page
    const isDashboard = window.location.pathname.includes('/dashboard') || 
                        window.location.pathname.includes('/profile') ||
                        document.querySelector('.dashboard-container') !== null;
    
    if (isDashboard) {
      console.log('[DeploymentSyncButton] Dashboard detected, creating button');
      createSyncButton();
    } else {
      console.log('[DeploymentSyncButton] Not on dashboard, skipping button creation');
    }
  }
  
  // Initialize when the document is ready
  function initialize() {
    console.log('[DeploymentSyncButton] Initializing...');
    
    // Check for dashboard immediately
    checkForDashboard();
    
    // Also check when the URL changes
    const pushState = history.pushState;
    history.pushState = function() {
      pushState.apply(history, arguments);
      checkForDashboard();
    };
    
    // And check when the popstate event is fired
    window.addEventListener('popstate', checkForDashboard);
    
    console.log('[DeploymentSyncButton] Initialized');
  }
  
  // Initialize when the document is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    // Wait a bit to ensure other scripts have loaded
    setTimeout(initialize, 1000);
  }
})();
