/**
 * Fix Banner Functionality
 * 
 * This script ensures that banner upload/remove functionality works properly
 * by excluding banner-related elements from the prevention scripts.
 */

console.log('[FixBannerFunctionality] Starting fix...');

// Function to check if an element is banner-related
function isBannerRelated(element) {
  if (!element) return false;
  
  const text = (element.textContent || element.innerText || '').toLowerCase();
  const className = (element.className || '').toLowerCase();
  const id = (element.id || '').toLowerCase();
  
  // Check for banner-related keywords
  const bannerKeywords = [
    'banner', 'logo', 'upload', 'remove', 'file', 'image',
    'logo-upload', 'remove-logo', 'file-input', 'logo-preview'
  ];
  
  return bannerKeywords.some(keyword => 
    text.includes(keyword) || className.includes(keyword) || id.includes(keyword)
  );
}

// Function to restore banner upload functionality
function restoreBannerUploadFunctionality() {
  try {
    // Find and restore banner upload inputs
    const bannerInputs = document.querySelectorAll('input[type="file"], input[id*="logo"], input[id*="banner"]');
    
    bannerInputs.forEach(input => {
      if (isBannerRelated(input)) {
        console.log('[FixBannerFunctionality] Restoring banner input functionality:', input.id || input.className);
        
        // Add event listener without replacing the element (safer for React)
        input.addEventListener('change', function(event) {
          console.log('[FixBannerFunctionality] Banner file selected:', event.target.files[0]?.name);
          // Let the original handler process this
        }, { once: false, passive: true });
      }
    });
    
    // Find and restore banner remove buttons
    const removeButtons = document.querySelectorAll('button');
    
    removeButtons.forEach(button => {
      const buttonText = (button.textContent || button.innerText || '').toLowerCase();
      
      if (buttonText.includes('remove') && (buttonText.includes('banner') || buttonText.includes('logo'))) {
        console.log('[FixBannerFunctionality] Restoring banner remove button:', buttonText);

        // Add event listener without replacing the element (safer for React)
        button.addEventListener('click', function(event) {
          console.log('[FixBannerFunctionality] Banner remove button clicked');
          // Mark banner as removed using safe method
          if (window.safeBannerManager) {
            window.safeBannerManager.markAsRemoved();
          }
          // Let the original handler process this
        }, { once: false, passive: false });
      }
    });
    
    console.log('[FixBannerFunctionality] Banner functionality restored');
    
  } catch (error) {
    console.error('[FixBannerFunctionality] Error restoring banner functionality:', error);
  }
}

// Function to patch the dashboard assistant creation fix to exclude banner elements
function patchDashboardAssistantCreationFix() {
  // Override the mutation observer in the dashboard assistant creation fix
  if (window.MutationObserver) {
    const originalObserve = MutationObserver.prototype.observe;
    
    MutationObserver.prototype.observe = function(target, options) {
      // If this is the dashboard assistant creation observer, modify its behavior
      if (this._isDashboardAssistantCreationObserver) {
        console.log('[FixBannerFunctionality] Patching dashboard assistant creation observer to exclude banner elements');
        
        const originalCallback = this._originalCallback;
        this._originalCallback = function(mutations) {
          // Filter out mutations related to banner elements
          const filteredMutations = mutations.filter(mutation => {
            if (mutation.type === 'childList') {
              const hasNonBannerNodes = Array.from(mutation.addedNodes).some(node => {
                if (node.nodeType === 1) { // Element node
                  return !isBannerRelated(node);
                }
                return true;
              });
              return hasNonBannerNodes;
            }
            return true;
          });
          
          if (filteredMutations.length > 0) {
            originalCallback.call(this, filteredMutations);
          }
        };
      }
      
      return originalObserve.call(this, target, options);
    };
  }
}

// Function to restore Supabase storage functionality for banners
function restoreSupabaseStorageForBanners() {
  try {
    if (window.supabase && window.supabase.storage) {
      console.log('[FixBannerFunctionality] Ensuring Supabase storage works for banner uploads');
      
      // Check if storage has been patched and restore original functionality for banner uploads
      const storage = window.supabase.storage;
      
      // Store reference to any patched methods
      if (storage._originalFrom) {
        console.log('[FixBannerFunctionality] Supabase storage was patched, creating banner-specific override');
        
        // Create a banner-specific storage method
        storage.fromForBanner = function(bucketName) {
          console.log('[FixBannerFunctionality] Using original Supabase storage for banner upload to:', bucketName);
          return storage._originalFrom.call(this, bucketName);
        };
      }
    }
  } catch (error) {
    console.error('[FixBannerFunctionality] Error restoring Supabase storage for banners:', error);
  }
}

// Function to exclude banner elements from form interaction fixes
function excludeBannerFromFormFixes() {
  try {
    // Override the form field patching to exclude banner-related fields
    if (window._patchFormField) {
      const originalPatchFormField = window._patchFormField;
      window._patchFormField = function(field) {
        if (isBannerRelated(field)) {
          console.log('[FixBannerFunctionality] Excluding banner field from form fixes:', field.id || field.className);
          return; // Don't patch banner-related fields
        }
        return originalPatchFormField.call(this, field);
      };
    }
    
    // Override event listener additions to exclude banner elements
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      // If this is a banner-related element, allow normal event handling
      if (isBannerRelated(this)) {
        console.log('[FixBannerFunctionality] Allowing normal event handling for banner element:', type);
        return originalAddEventListener.call(this, type, listener, options);
      }
      
      // For non-banner elements, use the original method
      return originalAddEventListener.call(this, type, listener, options);
    };
    
  } catch (error) {
    console.error('[FixBannerFunctionality] Error excluding banner from form fixes:', error);
  }
}

// Function to create a banner-specific event handler
function createBannerEventHandler() {
  // Wait for banner elements to be available
  setTimeout(() => {
    try {
      // Handle banner upload
      const logoUploadInput = document.getElementById('logo-upload');
      if (logoUploadInput) {
        console.log('[FixBannerFunctionality] Setting up banner upload handler');
        
        logoUploadInput.addEventListener('change', function(event) {
          const file = event.target.files[0];
          if (file) {
            console.log('[FixBannerFunctionality] Banner file selected:', file.name);
            
            // Create a FileReader to handle the upload
            const reader = new FileReader();
            reader.onloadend = function() {
              // Find the logo URL setter in the React component
              if (window.React && window.React.useState) {
                console.log('[FixBannerFunctionality] Banner file read successfully');
                // The React component should handle this automatically
              }
            };
            reader.readAsDataURL(file);
          }
        });
      }
      
      // Handle banner removal
      const removeButtons = document.querySelectorAll('.remove-logo-button, button[onclick*="remove"], button[onclick*="logo"]');
      removeButtons.forEach(button => {
        if (isBannerRelated(button)) {
          console.log('[FixBannerFunctionality] Setting up banner remove handler');
          
          button.addEventListener('click', function(event) {
            console.log('[FixBannerFunctionality] Banner remove button clicked');
            // The React component should handle this automatically
          });
        }
      });
      
    } catch (error) {
      console.error('[FixBannerFunctionality] Error creating banner event handler:', error);
    }
  }, 1000);
}

// Function to apply all fixes
function applyFixes() {
  try {
    restoreBannerUploadFunctionality();
    patchDashboardAssistantCreationFix();
    restoreSupabaseStorageForBanners();
    excludeBannerFromFormFixes();
    createBannerEventHandler();
    
    console.log('[FixBannerFunctionality] All banner fixes applied successfully');
    
  } catch (error) {
    console.error('[FixBannerFunctionality] Error applying banner fixes:', error);
  }
}

// Apply fixes immediately
applyFixes();

// Apply fixes when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFixes);
} else {
  setTimeout(applyFixes, 100);
}

// DISABLED: Apply fixes periodically to prevent excessive polling
// setInterval(applyFixes, 5000);
console.log('[FixBannerFunctionality] Periodic fixes disabled to prevent performance issues');

console.log('[FixBannerFunctionality] Fix script loaded');
