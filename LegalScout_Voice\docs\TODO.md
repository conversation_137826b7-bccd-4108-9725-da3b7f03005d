# TODO List

## High Priority
- [ ] Fix End Call Button
  - Ensure visibility in top right corner during call
  - Adjust z-index and styling to maintain visibility

- [ ] Improve Message Display
  - [ ] Optimize message chunking for smoother display
  - [ ] Remove unnecessary backgrounds/borders from typed text
  - [ ] Implement smooth auto-scroll to bottom
  - [ ] Add hover effect for conversation history
  - [ ] Adjust typing animation timing

- [ ] Fix React Ref Warning
  - [ ] Update MapDossierView to use React.forwardRef
  - [ ] Properly handle ref passing in CallController

## Medium Priority
- [ ] Set up Vercel Deployment
  - [ ] Configure Vercel project
  - [ ] Set up legalscout.net/Preview route
  - [ ] Implement basic authentication
    - [ ] Create simple password system
    - [ ] Add login page
    - [ ] Set up authentication middleware

- [ ] Message Handling Optimization
  - [ ] Consolidate model-output chunks
  - [ ] Reduce duplicate message logging
  - [ ] Improve timing of assistant responses
  - [ ] Clean up message state management

## Low Priority
- [ ] UI Polish
  - [ ] Refine conversation container styling
  - [ ] Improve transition animations
  - [ ] Add loading states
  - [ ] Enhance visual feedback

- [ ] Code Cleanup
  - [ ] Refactor message handling logic
  - [ ] Clean up unused CSS
  - [ ] Optimize component rendering
  - [ ] Add error boundaries

## Completed
- [x] Basic conversation UI implementation
- [x] Vapi integration
- [x] Message type handling
- [x] Typewriter effect
- [x] Map view integration 