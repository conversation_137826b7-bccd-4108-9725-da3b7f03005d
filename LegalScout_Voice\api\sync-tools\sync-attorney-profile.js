/**
 * API Route: /api/sync-tools/sync-attorney-profile
 *
 * This endpoint handles synchronizing attorney profile data between Supabase and Vapi.
 */

import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_KEY;

let supabase = null;
if (supabaseUrl && supabaseServiceKey) {
  // Use clean, simple Supabase client configuration
  supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Server-side implementation
const syncAttorneyProfile = async ({ attorneyId, forceUpdate }) => {
  console.log('Server-side syncAttorneyProfile called with:', { attorneyId, forceUpdate });

  if (!supabase) {
    return {
      success: false,
      error: 'Supabase not configured'
    };
  }

  try {
    // Fetch attorney data from Supabase
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();

    if (error) {
      throw error;
    }

    // Here you would sync with Vapi
    // For now, just return success
    return {
      success: true,
      message: 'Attorney profile synced successfully',
      attorneyId,
      attorney
    };
  } catch (error) {
    console.error('Error syncing attorney profile:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { attorneyId, forceUpdate } = req.body;

    // Validate required parameters
    if (!attorneyId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: attorneyId is required'
      });
    }

    // Call the syncAttorneyProfile function
    const result = await syncAttorneyProfile({ attorneyId, forceUpdate });

    // Return the result
    return res.status(200).json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error syncing attorney profile:', error);

    // Return a proper error response
    return res.status(500).json({
      success: false,
      error: error.message || 'An unknown error occurred'
    });
  }
}
