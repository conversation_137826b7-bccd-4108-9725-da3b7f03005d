// Direct replacement for MotionConfigContext.mjs
// This version doesn't use React.createContext at all

// Create a standalone context object
const MotionConfigContext = {
  Provider: function(props) { 
    return typeof props.children !== 'undefined' ? props.children : null; 
  },
  Consumer: function(props) { 
    return props.children && typeof props.children === 'function' 
      ? props.children({}) 
      : null; 
  },
  displayName: 'MotionConfigContext',
  _currentValue: {},
  _currentValue2: {},
  _threadCount: 0,
  _defaultValue: {}
};

// Make it available globally
if (typeof window !== 'undefined') {
  window.MotionConfigContext = MotionConfigContext;
  
  // Also make it available as a module
  if (!window.__framer_motion_MotionConfigContext_mjs__) {
    window.__framer_motion_MotionConfigContext_mjs__ = {
      MotionConfigContext: MotionConfigContext,
      default: MotionConfigContext
    };
  }
}

// Export both as named export and default
export { MotionConfigContext };
export default MotionConfigContext;
