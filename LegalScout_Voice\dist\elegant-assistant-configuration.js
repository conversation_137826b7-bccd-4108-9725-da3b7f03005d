/**
 * Elegant Assistant Configuration
 * 
 * Creates a complete Vapi assistant configuration with all necessary fields
 * for LegalScout, following the official Vapi API documentation patterns.
 * 
 * Based on:
 * - https://docs.vapi.ai/api-reference/assistants/create
 * - https://docs.vapi.ai/api-reference/assistants/update
 * - https://docs.vapi.ai/quickstart/web
 */

(async function elegantAssistantConfiguration() {
  console.log('🎯 [ElegantAssistantConfig] Starting comprehensive assistant configuration...');
  
  const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
  
  // Complete assistant configuration following Vapi API documentation
  const assistantConfig = {
    // Basic Configuration
    name: "LegalScout Assistant",
    firstMessage: "Hello! I'm <PERSON>, your AI legal assistant. How can I help you today?",
    firstMessageMode: "assistant-speaks-first",
    
    // Model Configuration (following Vapi API structure)
    model: {
      provider: "openai",
      model: "gpt-4o",
      temperature: 0.7,
      messages: [
        {
          role: "system",
          content: `You are Scout, the AI legal assistant for LegalScout. You help potential clients understand their legal needs and connect them with qualified attorneys. You guide users through initial legal consultations and collect relevant information.

[Identity]
You are Scout, an AI legal assistant representing LegalScout. You are akin to a legal K9, navigating the complex legal terrain in the USA, and occasionally making light-hearted dog puns when appropriate.

[Primary Role]
Guide users through understanding their legal matters and gathering necessary details for creating a precise legal brief.

[Style Guidelines]
- Maintain a professional yet friendly tone
- Inject dog-themed humor when suitable without detracting from the seriousness
- Spell slowly when pronouncing letters for clarity

[Data Collection Process]
1. Begin by requesting the user's email address and confirm its accuracy by spelling it back letter by letter
2. Introduce yourself and your role, incorporating the user's name into a dog pun if inferred from their email
3. Use iterative questioning to understand the user's legal matter
4. Collect: client background, jurisdiction, statement of facts, legal issues, client objectives, practice area, name, phone number, street address, city, state, and zip code
5. Verify all information, particularly the user's state and email address

[Tool Usage]
You must initiate the tool "Live_Dossier" once the user provides their email and with every reply following that.

[Completion]
Conclude with: "Thank you! I have all the details I need to create your case file. LegalScout is now working to find an attorney interested in your case. You'll receive an update soon."

Note: Always ensure to gather all necessary contact information before proceeding to inform the user about finding an attorney.`
        }
      ],
      toolIds: ["4a0d63cf-0b84-4eec-bddf-9c5869439d7e"] // Live Dossier tool
    },
    
    // Voice Configuration (using "echo" as default)
    voice: {
      provider: "openai",
      voiceId: "echo",
      speed: 1.0
    },
    
    // Transcriber Configuration
    transcriber: {
      provider: "deepgram",
      model: "nova-3",
      language: "en"
    },
    
    // Analysis Plan (following Vapi API structure)
    analysisPlan: {
      summaryPlan: {
        enabled: true,
        messages: [
          {
            role: "system",
            content: "Summarize the key points of this legal consultation, including the client's legal issue, urgency level, jurisdiction, and next steps for attorney assignment."
          }
        ]
      },
      
      structuredDataPlan: {
        enabled: true,
        messages: [
          {
            role: "system", 
            content: "Extract the following structured data from this legal consultation for CRM and case management:"
          }
        ],
        schema: {
          type: "object",
          properties: {
            // Client Information
            client: {
              type: "object",
              properties: {
                full_name: { type: "string", description: "Client's full legal name" },
                email: { type: "string", description: "Client's email address" },
                phone: { type: "string", description: "Client's phone number" },
                address: {
                  type: "object",
                  properties: {
                    street: { type: "string" },
                    city: { type: "string" },
                    state: { type: "string" },
                    zip: { type: "string" }
                  }
                }
              }
            },
            
            // Case Information
            case: {
              type: "object",
              properties: {
                legal_issue: { type: "string", description: "Primary legal issue or case type" },
                practice_area: { 
                  type: "string", 
                  enum: ["Personal Injury", "Family Law", "Criminal Defense", "Business Law", "Real Estate", "Estate Planning", "Immigration", "Employment Law", "Other"],
                  description: "Primary practice area"
                },
                urgency: { 
                  type: "string", 
                  enum: ["Low", "Medium", "High", "Critical"],
                  description: "Case urgency level"
                },
                jurisdiction: { type: "string", description: "Legal jurisdiction (state/county)" },
                case_summary: { type: "string", description: "Brief summary of the case" }
              }
            },
            
            // Consultation Metadata
            consultation: {
              type: "object",
              properties: {
                completeness: {
                  type: "string",
                  enum: ["Complete", "Mostly Complete", "Partial", "Incomplete"],
                  description: "Information completeness assessment"
                },
                follow_up_required: { type: "boolean", description: "Does this case need follow-up" }
              }
            }
          },
          required: ["client", "case"]
        }
      },
      
      successEvaluationPlan: {
        enabled: true,
        rubric: "NumericScale",
        messages: [
          {
            role: "system",
            content: "Evaluate this consultation's success (1-10) based on: information gathering completeness, client engagement, lead quality, and process efficiency. Provide an overall score with brief explanation."
          }
        ]
      }
    },
    
    // Call Management Settings
    silenceTimeoutSeconds: 30,
    maxDurationSeconds: 900, // 15 minutes
    backgroundSound: "office",
    endCallMessage: "Thank you for consulting with LegalScout. You'll receive an update about your case soon. Have a great day!",
    
    // Recording and Artifacts
    artifactPlan: {
      recordingEnabled: true,
      transcriptPlan: {
        enabled: true,
        assistantName: "Scout",
        userName: "Client"
      }
    }
  };
  
  console.log('🎯 [ElegantAssistantConfig] Configuration prepared:', {
    voice: assistantConfig.voice,
    toolsCount: assistantConfig.model?.toolIds?.length || 0,
    hasAnalysis: !!assistantConfig.analysisPlan,
    hasInstructions: !!assistantConfig.model?.messages?.[0]?.content
  });
  
  try {
    // Use the existing vapiMcpService pattern
    if (window.vapiMcpService) {
      console.log('📡 [ElegantAssistantConfig] Using vapiMcpService...');
      await window.vapiMcpService.ensureConnection();
      const result = await window.vapiMcpService.updateAssistant(assistantId, assistantConfig);
      console.log('✅ [ElegantAssistantConfig] Assistant updated via MCP:', result);
      return result;
    }
    
    // Fallback to direct API (following existing patterns)
    console.log('📡 [ElegantAssistantConfig] Using direct API fallback...');
    const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
    
    const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assistantConfig)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    const result = await response.json();
    console.log('✅ [ElegantAssistantConfig] Assistant updated via API:', result);
    
    // Elegant verification
    const verification = {
      voice: result.voice?.voiceId === 'echo' ? '✅ Echo' : `❌ ${result.voice?.voiceId || 'Missing'}`,
      firstMessage: result.firstMessage ? '✅ Present' : '❌ Missing',
      instructions: result.model?.messages?.[0]?.content ? '✅ Present' : '❌ Missing',
      liveDossier: result.model?.toolIds?.includes('4a0d63cf-0b84-4eec-bddf-9c5869439d7e') ? '✅ Configured' : '❌ Missing',
      analysis: result.analysisPlan ? '✅ Configured' : '❌ Missing'
    };
    
    console.log('🔍 [ElegantAssistantConfig] Verification:', verification);
    
    return result;
    
  } catch (error) {
    console.error('❌ [ElegantAssistantConfig] Error updating assistant:', error);
    throw error;
  }
})();
