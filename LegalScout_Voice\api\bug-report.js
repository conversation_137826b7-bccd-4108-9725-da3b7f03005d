import { IncomingForm } from 'formidable';
import fs from 'fs';

// Disable body parsing for multipart/form-data
export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Parse the multipart form data
    const form = new IncomingForm();
    const [fields, files] = await form.parse(req);
    
    const reportData = JSON.parse(fields.reportData[0]);
    const screenshotFile = files.screenshot?.[0];

    // Validate required fields
    if (!reportData.feedback?.trim()) {
      return res.status(400).json({ error: 'Feedback is required' });
    }

    // Send to Slack
    const slackResponse = await sendToSlack(reportData, screenshotFile);
    
    res.status(200).json({ 
      success: true, 
      message: 'Bug report submitted successfully',
      slackResponse 
    });

  } catch (error) {
    console.error('Bug report submission error:', error);
    res.status(500).json({ 
      error: 'Failed to submit bug report',
      details: error.message 
    });
  }
}

async function sendToSlack(reportData, screenshotFile) {
  const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
  
  if (!slackWebhookUrl) {
    console.warn('SLACK_WEBHOOK_URL not configured, logging bug report instead');
    console.log('Bug Report:', JSON.stringify(reportData, null, 2));
    return { logged: true };
  }

  // Format the message for Slack
  const message = formatSlackMessage(reportData);
  
  try {
    // Send text message first
    const textResponse = await fetch(slackWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(message),
    });

    if (!textResponse.ok) {
      throw new Error(`Slack webhook failed: ${textResponse.status}`);
    }

    // If there's a screenshot, upload it separately
    let imageResponse = null;
    if (screenshotFile && process.env.SLACK_BOT_TOKEN) {
      imageResponse = await uploadScreenshotToSlack(screenshotFile);
    }

    return {
      textSent: true,
      imageSent: !!imageResponse,
      imageResponse
    };

  } catch (error) {
    console.error('Slack sending error:', error);
    throw error;
  }
}

function formatSlackMessage(reportData) {
  const categoryEmojis = {
    bug: '🐛',
    feature: '💡',
    feedback: '💬',
    ui: '🎨',
    performance: '⚡'
  };

  const emoji = categoryEmojis[reportData.category] || '📝';
  const title = `${emoji} ${reportData.category.toUpperCase()}: New Report`;

  const blocks = [
    {
      type: 'header',
      text: {
        type: 'plain_text',
        text: title
      }
    },
    {
      type: 'section',
      fields: [
        {
          type: 'mrkdwn',
          text: `*Type:* ${reportData.category}`
        },
        {
          type: 'mrkdwn',
          text: `*Time:* ${new Date(reportData.timestamp).toLocaleString()}`
        },
        {
          type: 'mrkdwn',
          text: `*URL:* ${reportData.url}`
        },
        {
          type: 'mrkdwn',
          text: `*Email:* ${reportData.email || 'Not provided'}`
        }
      ]
    },
    {
      type: 'section',
      text: {
        type: 'mrkdwn',
        text: `*Description:*\n${reportData.feedback}`
      }
    }
  ];

  // Add technical details if available
  if (reportData.logs && reportData.logs.length > 0) {
    const recentErrors = reportData.logs
      .filter(log => log.type === 'error')
      .slice(-3)
      .map(log => `• ${log.message}`)
      .join('\n');

    if (recentErrors) {
      blocks.push({
        type: 'section',
        text: {
          type: 'mrkdwn',
          text: `*Recent Errors:*\n\`\`\`${recentErrors}\`\`\``
        }
      });
    }
  }

  // Add browser info
  blocks.push({
    type: 'context',
    elements: [
      {
        type: 'mrkdwn',
        text: `Browser: ${reportData.userAgent?.split(' ').slice(-2).join(' ') || 'Unknown'} | Viewport: ${reportData.viewport?.width}x${reportData.viewport?.height}`
      }
    ]
  });

  return {
    text: title, // Fallback text
    blocks: blocks
  };
}

async function uploadScreenshotToSlack(screenshotFile) {
  const slackBotToken = process.env.SLACK_BOT_TOKEN;
  const slackChannelId = process.env.SLACK_CHANNEL_ID;

  if (!slackBotToken || !slackChannelId) {
    console.warn('Slack bot token or channel ID not configured for file upload');
    return null;
  }

  try {
    const formData = new FormData();
    
    // Read the file
    const fileBuffer = fs.readFileSync(screenshotFile.filepath);
    const blob = new Blob([fileBuffer], { type: 'image/png' });
    
    formData.append('file', blob, 'screenshot.png');
    formData.append('channels', slackChannelId);
    formData.append('title', 'Bug Report Screenshot');
    formData.append('initial_comment', 'Screenshot from bug report');

    const response = await fetch('https://slack.com/api/files.upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${slackBotToken}`,
      },
      body: formData,
    });

    const result = await response.json();
    
    if (!result.ok) {
      throw new Error(`Slack file upload failed: ${result.error}`);
    }

    return result;

  } catch (error) {
    console.error('Screenshot upload error:', error);
    return null;
  }
}
