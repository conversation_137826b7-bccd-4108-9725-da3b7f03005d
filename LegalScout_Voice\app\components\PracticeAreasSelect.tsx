import React from 'react';
import { MultiSelect } from '@mantine/core';

const PRACTICE_AREAS = [
  'Administrative Law',
  'Bankruptcy',
  'Business Law',
  'Civil Rights',
  'Constitutional Law',
  'Consumer Law',
  'Contract Law',
  'Corporate Law',
  'Criminal Law',
  'Employment Law',
  'Environmental Law',
  'Family Law',
  'Health Law',
  'Immigration Law',
  'Insurance Law',
  'Intellectual Property',
  'International Law',
  'Labor Law',
  'Personal Injury',
  'Real Estate Law',
  'Securities Law',
  'Tax Law',
  'Trust and Estate Law',
].map(area => ({ value: area.toLowerCase().replace(/\s+/g, '-'), label: area }));

interface PracticeAreasSelectProps {
  value: string[];
  onChange: (value: string[]) => void;
  className?: string;
}

export const PracticeAreasSelect = ({
  value,
  onChange,
  className,
}: PracticeAreasSelectProps) => {
  return (
    <MultiSelect
      data={PRACTICE_AREAS}
      value={value}
      onChange={onChange}
      placeholder="Select practice areas"
      searchable
      clearable
      className={className}
      classNames={{
        input: 'p-2 border rounded',
      }}
    />
  );
}; 