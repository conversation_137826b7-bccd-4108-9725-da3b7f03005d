<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test - Dashboard Fixes</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Dashboard API Fix Test</h1>
    
    <div class="test-container">
        <h2>API Endpoint Tests</h2>
        <button onclick="testManageAuthState()">Test Manage Auth State</button>
        <button onclick="testVapiSDK()">Test Vapi SDK Loading</button>
        <button onclick="testEnvironmentVars()">Test Environment Variables</button>
        <button onclick="runAllTests()">Run All Tests</button>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>Console Logs</h2>
        <div id="console-logs"></div>
    </div>

    <script>
        const resultsDiv = document.getElementById('test-results');
        const logsDiv = document.getElementById('console-logs');

        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }

        function addLog(message) {
            const div = document.createElement('div');
            div.innerHTML = `<pre>${new Date().toISOString()}: ${message}</pre>`;
            logsDiv.appendChild(div);
            console.log(message);
        }

        async function testManageAuthState() {
            addResult('🧪 Testing Manage Auth State API...', 'info');
            
            try {
                const response = await fetch('/api/sync-tools/manage-auth-state', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        authData: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            },
                            session: { access_token: 'test-token' }
                        },
                        action: 'refresh'
                    })
                });

                addLog(`Response status: ${response.status}`);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                addLog(`Response data: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    addResult('✅ Manage Auth State API working correctly', 'success');
                } else {
                    addResult(`⚠️ API returned success=false: ${data.error}`, 'error');
                }
            } catch (error) {
                addLog(`Error: ${error.message}`);
                addResult(`❌ Manage Auth State API failed: ${error.message}`, 'error');
            }
        }

        async function testVapiSDK() {
            addResult('🧪 Testing Vapi SDK Loading...', 'info');
            
            try {
                // Check if Vapi is loaded
                if (window.Vapi) {
                    addResult('✅ Vapi SDK is loaded and available', 'success');
                    addLog(`Vapi object: ${typeof window.Vapi}`);
                } else {
                    addResult('⚠️ Vapi SDK not loaded yet, checking CDN sources...', 'info');
                    
                    // Try to load manually
                    const script = document.createElement('script');
                    script.src = 'https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js';
                    
                    script.onload = () => {
                        if (window.Vapi) {
                            addResult('✅ Vapi SDK loaded successfully from CDN', 'success');
                        } else {
                            addResult('❌ Vapi SDK script loaded but Vapi not available', 'error');
                        }
                    };
                    
                    script.onerror = () => {
                        addResult('❌ Failed to load Vapi SDK from CDN', 'error');
                    };
                    
                    document.head.appendChild(script);
                }
            } catch (error) {
                addLog(`Error: ${error.message}`);
                addResult(`❌ Vapi SDK test failed: ${error.message}`, 'error');
            }
        }

        function testEnvironmentVars() {
            addResult('🧪 Testing Environment Variables...', 'info');
            
            const requiredVars = [
                'VITE_SUPABASE_URL',
                'VITE_SUPABASE_KEY', 
                'VITE_VAPI_PUBLIC_KEY'
            ];
            
            let allPresent = true;
            
            requiredVars.forEach(varName => {
                const value = window[varName] || import.meta?.env?.[varName];
                if (value) {
                    addResult(`✅ ${varName}: Present`, 'success');
                    addLog(`${varName}: ${value.substring(0, 20)}...`);
                } else {
                    addResult(`❌ ${varName}: Missing`, 'error');
                    allPresent = false;
                }
            });
            
            if (allPresent) {
                addResult('✅ All required environment variables are present', 'success');
            } else {
                addResult('❌ Some environment variables are missing', 'error');
            }
        }

        async function runAllTests() {
            resultsDiv.innerHTML = '';
            logsDiv.innerHTML = '';
            
            addResult('🚀 Running all dashboard fix tests...', 'info');
            
            await testManageAuthState();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testVapiSDK();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testEnvironmentVars();
            
            addResult('🏁 All tests completed', 'info');
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            addResult('🔧 Dashboard Fix Test Page Loaded', 'info');
            addLog('Test page initialized');
        });
    </script>
</body>
</html>
