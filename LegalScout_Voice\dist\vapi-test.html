<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vapi Connection Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .card {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    .results {
      margin-top: 20px;
      white-space: pre-wrap;
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      border-left: 4px solid #4CAF50;
      overflow-x: auto;
    }
    .success {
      color: #4CAF50;
    }
    .error {
      color: #f44336;
    }
    .warning {
      color: #ff9800;
    }
    .info {
      color: #2196F3;
    }
  </style>
</head>
<body>
  <h1>Vapi Connection Test</h1>
  
  <div class="card">
    <h2>API Key Configuration</h2>
    <div class="form-group">
      <label for="apiKey">Vapi API Key:</label>
      <input type="text" id="apiKey" placeholder="Enter your Vapi API key">
    </div>
    <button id="loadFromEnv">Load from Environment</button>
    <button id="saveKey">Save Key</button>
  </div>
  
  <div class="card">
    <h2>Direct API Connection Test</h2>
    <p>Tests the connection to the Vapi API directly.</p>
    <button id="testDirectApi">Test Direct API</button>
    <div id="directApiResults" class="results" style="display: none;"></div>
  </div>
  
  <div class="card">
    <h2>MCP Server Connection Test</h2>
    <p>Tests the connection to the Vapi MCP server.</p>
    <div class="form-group">
      <label for="mcpEndpoint">MCP Server Endpoint:</label>
      <input type="text" id="mcpEndpoint" value="/vapi-mcp-server/sse" placeholder="Enter MCP server endpoint">
    </div>
    <button id="testMcpServer">Test MCP Server</button>
    <div id="mcpServerResults" class="results" style="display: none;"></div>
  </div>
  
  <div class="card">
    <h2>Environment Check</h2>
    <p>Checks the environment variables and configuration.</p>
    <button id="checkEnvironment">Check Environment</button>
    <div id="environmentResults" class="results" style="display: none;"></div>
  </div>
  
  <script>
    // Load API key from localStorage if available
    document.addEventListener('DOMContentLoaded', () => {
      const savedApiKey = localStorage.getItem('vapi_api_key');
      if (savedApiKey) {
        document.getElementById('apiKey').value = savedApiKey;
      }
    });
    
    // Save API key to localStorage
    document.getElementById('saveKey').addEventListener('click', () => {
      const apiKey = document.getElementById('apiKey').value.trim();
      if (apiKey) {
        localStorage.setItem('vapi_api_key', apiKey);
        alert('API key saved to localStorage');
      } else {
        alert('Please enter an API key');
      }
    });
    
    // Load API key from environment variables
    document.getElementById('loadFromEnv').addEventListener('click', async () => {
      try {
        // Try to get the API key from window.env or import.meta.env
        let apiKey = '';
        
        if (window.env && window.env.VITE_VAPI_PUBLIC_KEY) {
          apiKey = window.env.VITE_VAPI_PUBLIC_KEY;
        } else if (window.VITE_VAPI_PUBLIC_KEY) {
          apiKey = window.VITE_VAPI_PUBLIC_KEY;
        } else {
          // Try to fetch from a server endpoint that returns environment variables
          try {
            const response = await fetch('/api/env');
            if (response.ok) {
              const data = await response.json();
              apiKey = data.VITE_VAPI_PUBLIC_KEY || data.VAPI_TOKEN || '';
            }
          } catch (error) {
            console.warn('Error fetching environment variables:', error);
          }
        }
        
        if (apiKey) {
          document.getElementById('apiKey').value = apiKey;
          alert('API key loaded from environment variables');
        } else {
          alert('No API key found in environment variables');
        }
      } catch (error) {
        console.error('Error loading API key from environment variables:', error);
        alert('Error loading API key from environment variables');
      }
    });
    
    // Test direct API connection
    document.getElementById('testDirectApi').addEventListener('click', async () => {
      const apiKey = document.getElementById('apiKey').value.trim();
      const resultsElement = document.getElementById('directApiResults');
      
      if (!apiKey) {
        resultsElement.innerHTML = '<span class="error">❌ No API key provided</span>';
        resultsElement.style.display = 'block';
        return;
      }
      
      resultsElement.innerHTML = '<span class="info">🔍 Testing direct API connection...</span>';
      resultsElement.style.display = 'block';
      
      try {
        // Try to list assistants
        const response = await fetch('https://api.vapi.ai/assistants', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          resultsElement.innerHTML = `<span class="success">✅ Direct API connection successful</span>\n📊 Found ${data.length} assistants\n\n${JSON.stringify(data, null, 2)}`;
        } else {
          resultsElement.innerHTML = `<span class="error">❌ Direct API connection failed: ${response.status} ${response.statusText}</span>`;
          
          // Try to get more details from the response
          try {
            const errorData = await response.json();
            resultsElement.innerHTML += `\n\nError details: ${JSON.stringify(errorData, null, 2)}`;
          } catch (e) {
            // Ignore if we can't parse the response
          }
        }
      } catch (error) {
        resultsElement.innerHTML = `<span class="error">❌ Direct API connection error: ${error.message}</span>`;
      }
    });
    
    // Test MCP server connection
    document.getElementById('testMcpServer').addEventListener('click', async () => {
      const apiKey = document.getElementById('apiKey').value.trim();
      const mcpEndpoint = document.getElementById('mcpEndpoint').value.trim();
      const resultsElement = document.getElementById('mcpServerResults');
      
      if (!apiKey) {
        resultsElement.innerHTML = '<span class="error">❌ No API key provided</span>';
        resultsElement.style.display = 'block';
        return;
      }
      
      if (!mcpEndpoint) {
        resultsElement.innerHTML = '<span class="error">❌ No MCP server endpoint provided</span>';
        resultsElement.style.display = 'block';
        return;
      }
      
      resultsElement.innerHTML = `<span class="info">🔍 Testing MCP server connection to ${mcpEndpoint}...</span>`;
      resultsElement.style.display = 'block';
      
      try {
        // Make a simple fetch request to the MCP server
        const response = await fetch(mcpEndpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          resultsElement.innerHTML = `<span class="success">✅ MCP server connection successful</span>`;
          
          // Try to get more details from the response
          try {
            const data = await response.text();
            resultsElement.innerHTML += `\n\nResponse: ${data}`;
          } catch (e) {
            // Ignore if we can't parse the response
          }
        } else {
          resultsElement.innerHTML = `<span class="error">❌ MCP server connection failed: ${response.status} ${response.statusText}</span>`;
          
          // Try to get more details from the response
          try {
            const errorData = await response.text();
            resultsElement.innerHTML += `\n\nError details: ${errorData}`;
          } catch (e) {
            // Ignore if we can't parse the response
          }
        }
      } catch (error) {
        resultsElement.innerHTML = `<span class="error">❌ MCP server connection error: ${error.message}</span>`;
      }
    });
    
    // Check environment
    document.getElementById('checkEnvironment').addEventListener('click', async () => {
      const resultsElement = document.getElementById('environmentResults');
      
      resultsElement.innerHTML = '<span class="info">🔍 Checking environment...</span>';
      resultsElement.style.display = 'block';
      
      try {
        // Collect environment information
        const environment = {
          apiKey: {
            fromInput: document.getElementById('apiKey').value ? 'Set (hidden)' : 'Not set',
            fromLocalStorage: localStorage.getItem('vapi_api_key') ? 'Set (hidden)' : 'Not set'
          },
          window: {
            origin: window.location.origin,
            pathname: window.location.pathname,
            href: window.location.href
          },
          navigator: {
            userAgent: navigator.userAgent
          }
        };
        
        // Try to get environment variables from window.env or import.meta.env
        if (window.env) {
          environment.env = {
            VITE_VAPI_PUBLIC_KEY: window.env.VITE_VAPI_PUBLIC_KEY ? 'Set (hidden)' : 'Not set',
            VAPI_TOKEN: window.env.VAPI_TOKEN ? 'Set (hidden)' : 'Not set'
          };
        }
        
        if (window.VITE_VAPI_PUBLIC_KEY) {
          environment.windowVars = {
            VITE_VAPI_PUBLIC_KEY: 'Set (hidden)'
          };
        }
        
        // Try to fetch from a server endpoint that returns environment variables
        try {
          const response = await fetch('/api/env');
          if (response.ok) {
            const data = await response.json();
            environment.serverEnv = {
              VITE_VAPI_PUBLIC_KEY: data.VITE_VAPI_PUBLIC_KEY ? 'Set (hidden)' : 'Not set',
              VAPI_TOKEN: data.VAPI_TOKEN ? 'Set (hidden)' : 'Not set'
            };
          }
        } catch (error) {
          environment.serverEnvError = error.message;
        }
        
        resultsElement.innerHTML = `<span class="success">✅ Environment check completed</span>\n\n${JSON.stringify(environment, null, 2)}`;
      } catch (error) {
        resultsElement.innerHTML = `<span class="error">❌ Environment check error: ${error.message}</span>`;
      }
    });
  </script>
</body>
</html>
