# Subdomain System (Updated)

## Overview

The LegalScout subdomain system allows each attorney to have a custom branded experience at their own subdomain (e.g., `attorneyname.legalscout.net`). This system dynamically loads attorney-specific configuration, branding, and AI assistant settings based on the subdomain.

## System Architecture

### Critical Architecture Understanding

#### App.jsx Component Structure
The App.jsx file contains **two separate components**:

1. **Home Component (lines 1-317)**: Contains the iframe logic for attorney subdomains
2. **App Component (lines 318+)**: Main component that handles routing, state management, and subdomain detection

**Flow**: App component detects subdomain → loads attorney profile → routes to Home component → Home component receives attorney data as props → renders iframe if conditions are met.

#### Common Issues and Solutions

##### Property Name Mismatch Issue
**Problem**: Supabase returns snake_case properties (`firm_name`) but conditions check for camelCase (`firmName`).

**Solution**: Check for both property formats:
```javascript
if (attorneyProfile.firmName || attorneyProfile.firm_name) {
  // Condition met
}
```

**Symptoms**: Subdomain loads main app instead of preview iframe, no iframe-related logs appear.

##### PostMessage Timing Issues
**Problem**: Parent window sends postMessage before iframe is ready to receive it.

**Solution**: Use PREVIEW_READY handshake:
```javascript
// Iframe sends ready message
window.parent.postMessage({ type: 'PREVIEW_READY' }, '*');

// Parent waits for ready message before sending config
if (event.data && event.data.type === 'PREVIEW_READY') {
  iframe.contentWindow.postMessage({ type: 'UPDATE_PREVIEW_CONFIG', config }, '*');
}
```

### System Components

The subdomain system consists of several components:

1. **Subdomain Detection** - Identifies the current subdomain
2. **Attorney Configuration Loading** - Loads attorney data from Supabase
3. **Dynamic Rendering** - Renders the UI based on attorney configuration
4. **Vapi Integration** - Connects to the attorney's custom AI assistant

## Subdomain Detection

### Implementation

The system detects the current subdomain using the `getCurrentSubdomain` function:

```javascript
// src/utils/subdomainTester.js
export const getCurrentSubdomain = () => {
  // Check if we have a test subdomain in localStorage
  const testSubdomain = localStorage.getItem('testSubdomain');
  if (testSubdomain) {
    console.log('Using test subdomain from localStorage:', testSubdomain);
    return testSubdomain;
  }

  // Extract subdomain from hostname
  const hostname = window.location.hostname;

  // Skip extraction for localhost or IP addresses
  if (hostname === 'localhost' || hostname === '127.0.0.1' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return null;
  }

  // Extract subdomain from hostname
  const parts = hostname.split('.');

  // Check if we have a subdomain (e.g., attorney.legalscout.net)
  if (parts.length > 2 && parts[0] !== 'www') {
    return parts[0];
  }

  return null;
};
```

### Testing Utility

For local development, the system includes a testing utility:

```javascript
// Set a test subdomain (persists in localStorage)
export const setTestSubdomain = (subdomain) => {
  localStorage.setItem('testSubdomain', subdomain);
  console.log('Test subdomain set to:', subdomain);
  return subdomain;
};

// Clear the test subdomain
export const clearTestSubdomain = () => {
  localStorage.removeItem('testSubdomain');
  console.log('Test subdomain cleared');
};
```

## Attorney Configuration Loading

### Database Structure

Attorney configurations are stored in the Supabase `attorneys` table:

```sql
CREATE TABLE IF NOT EXISTS public.attorneys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Basic Information
  subdomain TEXT UNIQUE NOT NULL,
  firm_name TEXT NOT NULL,
  name TEXT,
  email TEXT,
  phone TEXT,

  -- Media URLs
  logo_url TEXT,
  profile_image TEXT,

  -- Vapi Configuration
  vapi_instructions TEXT,
  vapi_context TEXT,
  vapi_assistant_id TEXT,

  -- Other Configuration
  practice_areas TEXT[] DEFAULT '{}',
  interaction_deposit_url TEXT,

  -- Configuration
  is_active BOOLEAN DEFAULT true
);
```

### Loading Process

The attorney configuration is loaded using the `getAttorneyConfigAsync` function:

```javascript
// src/config/attorneys.js
export const getAttorneyConfigAsync = async (subdomain) => {
  // Normalize subdomain
  const normalizedSubdomain = subdomain?.toLowerCase().replace(/[^a-z0-9-]/g, '') || 'default';

  try {
    console.log('Fetching attorney config for subdomain:', normalizedSubdomain);

    // Query Supabase for the attorney record
    const { data, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('subdomain', normalizedSubdomain)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching attorney config:', error);
      return getDefaultAttorneyConfig();
    }

    if (!data) {
      console.log('No attorney found for subdomain:', normalizedSubdomain);
      return getDefaultAttorneyConfig();
    }

    console.log('Found attorney config:', data);

    // Transform database record to attorney config
    return {
      subdomain: data.subdomain,
      firmName: data.firm_name,
      name: data.name,
      logo: data.logo_url || '/PRIMARY CLEAR.png',
      profileImage: data.profile_image,
      vapiInstructions: data.vapi_instructions,
      vapiContext: data.vapi_context,
      vapiAssistantId: data.vapi_assistant_id,
      practiceAreas: data.practice_areas || [],
      interactionDepositUrl: data.interaction_deposit_url,
      isActive: data.is_active
    };
  } catch (error) {
    console.error('Unexpected error fetching attorney config:', error);
    return getDefaultAttorneyConfig();
  }
};
```

## Dynamic Rendering

### App Component

The main `App` component checks for a subdomain and renders accordingly:

```javascript
// src/App.jsx
const App = () => {
  const [attorneyProfile, setAttorneyProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const subdomain = getCurrentSubdomain();
  const isAttorneySubdomain = !!subdomain;

  // Load attorney profile based on subdomain
  useEffect(() => {
    const loadAttorneyProfile = async () => {
      setIsLoading(true);
      try {
        const profile = await getAttorneyConfigAsync(subdomain);
        setAttorneyProfile(profile);
      } catch (error) {
        console.error('Error loading attorney profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAttorneyProfile();
  }, [subdomain]);

  // Render attorney-specific UI if on a subdomain
  if (isAttorneySubdomain && attorneyProfile) {
    return <AttorneySubdomainView profile={attorneyProfile} />;
  }

  // Otherwise render the main app
  return <MainAppView />;
};
```

### Attorney Subdomain View

The attorney subdomain view renders a customized experience:

```javascript
// src/components/AttorneySubdomainView.jsx
const AttorneySubdomainView = ({ profile }) => {
  const [callActive, setCallActive] = useState(false);

  const startCall = () => {
    setCallActive(true);
  };

  const endCall = () => {
    setCallActive(false);
  };

  return (
    <div className="attorney-subdomain-view">
      <header>
        <img src={profile.logo} alt={`${profile.firmName} Logo`} />
        <h1>{profile.firmName}</h1>
      </header>

      {!callActive ? (
        <div className="start-call-container">
          <h2>Welcome to {profile.firmName}</h2>
          <p>Click below to start your consultation</p>
          <Button
            onClick={startCall}
            label="Start Consultation"
            mascot={profile.profileImage || profile.logo}
          />
        </div>
      ) : (
        <VapiCall
          onEndCall={endCall}
          subdomain={profile.subdomain}
          assistantId={profile.vapiAssistantId}
          customInstructions={{
            firmName: profile.firmName,
            attorneyName: profile.name,
            practiceAreas: profile.practiceAreas
          }}
        />
      )}
    </div>
  );
};
```

## Vapi Integration

### Assistant Selection

The system selects the appropriate Vapi assistant based on the subdomain:

```javascript
// src/components/VapiCall.jsx
const VapiCall = ({
  onEndCall,
  subdomain = 'default',
  customInstructions = null,
  assistantId = null,
  isDemo = false
}) => {
  // Use the attorney's assistant ID if available
  const effectiveAssistantId = assistantId || DEFAULT_ASSISTANT_ID;

  // Use our custom hook to manage call state and functionality
  const {
    status,
    startCall,
    stopCall,
    messageHistory
  } = useVapiCall({
    subdomain,
    onEndCall,
    customInstructions,
    assistantId: effectiveAssistantId
  });

  // Rest of component...
};
```

### Custom Instructions

The system passes attorney-specific instructions to the assistant:

```javascript
// src/hooks/useVapiCall.js
const callParams = useMemo(() => {
  // Base parameters
  const params = {
    assistantId,
    assistantOverrides: {
      firstMessage: customInstructions?.welcomeMessage || 'Hello, how can I help you today?',
      firstMessageMode: 'assistant-speaks-first'
    }
  };

  // Add attorney-specific context if available
  if (customInstructions) {
    params.assistantOverrides.model = {
      provider: 'openai',
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: `You are a legal assistant for ${customInstructions.firmName}.
                   The attorney specializes in ${customInstructions.practiceAreas.join(', ')}.
                   ${customInstructions.vapiInstructions || ''}`
        }
      ]
    };
  }

  return params;
}, [assistantId, customInstructions]);
```

## DNS Configuration

### Vercel Configuration

The subdomain system uses Vercel's domain configuration:

1. Main domain (`legalscout.net`) is configured in Vercel
2. Wildcard subdomain (`*.legalscout.net`) is configured to point to the same Vercel project
3. Vercel handles routing based on the hostname

### Custom Domain Setup

For attorneys who want to use their own domain:

1. Attorney configures a CNAME record pointing to `cname.vercel-dns.com`
2. Domain is added to the Vercel project
3. Attorney record is updated with the custom domain

## Testing and Development

### Subdomain Test Page

The application includes a dedicated test page for subdomains:

```javascript
// src/pages/SubdomainTestPage.jsx
const SubdomainTestPage = () => {
  const [testSubdomain, setTestSubdomain] = useState(
    localStorage.getItem('testSubdomain') || ''
  );
  const [availableSubdomains, setAvailableSubdomains] = useState([]);

  // Load available subdomains from Supabase
  useEffect(() => {
    const loadSubdomains = async () => {
      const { data, error } = await supabase
        .from('attorneys')
        .select('subdomain, firm_name')
        .eq('is_active', true);

      if (!error && data) {
        setAvailableSubdomains(data);
      }
    };

    loadSubdomains();
  }, []);

  const handleSetSubdomain = () => {
    if (testSubdomain) {
      setTestSubdomain(testSubdomain);
      localStorage.setItem('testSubdomain', testSubdomain);
    }
  };

  const handleClearSubdomain = () => {
    setTestSubdomain('');
    localStorage.removeItem('testSubdomain');
  };

  return (
    <div className="subdomain-test-page">
      <h1>Subdomain Testing</h1>

      <div className="subdomain-form">
        <input
          type="text"
          value={testSubdomain}
          onChange={(e) => setTestSubdomain(e.target.value)}
          placeholder="Enter test subdomain"
        />
        <button onClick={handleSetSubdomain}>Set Test Subdomain</button>
        <button onClick={handleClearSubdomain}>Clear Test Subdomain</button>
      </div>

      <div className="available-subdomains">
        <h2>Available Subdomains</h2>
        <ul>
          {availableSubdomains.map((item) => (
            <li key={item.subdomain}>
              <button onClick={() => setTestSubdomain(item.subdomain)}>
                {item.subdomain} - {item.firm_name}
              </button>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};
```

### Local Testing

For local development, the system provides several testing utilities:

1. **Test Subdomain Setting**: Set a test subdomain in localStorage
2. **Subdomain Test Page**: UI for testing different subdomains
3. **Mock Data**: Fallback data for development without Supabase

## Security Considerations

### Subdomain Validation

The system validates subdomains to prevent security issues:

```javascript
// Normalize subdomain for consistency and security
const normalizedSubdomain = subdomain?.toLowerCase().replace(/[^a-z0-9-]/g, '') || 'default';
```

### Access Control

The system implements access control for attorney data:

1. **Row-Level Security**: Supabase RLS policies restrict access to attorney records
2. **Authentication**: Attorney authentication required for dashboard access
3. **Validation**: Server-side validation of subdomain ownership

## Performance Optimization

### Caching

The system implements caching to improve performance:

```javascript
// Cache attorney configurations
const attorneyConfigCache = new Map();

export const getAttorneyConfigAsync = async (subdomain) => {
  const normalizedSubdomain = subdomain?.toLowerCase().replace(/[^a-z0-9-]/g, '') || 'default';

  // Check cache first
  if (attorneyConfigCache.has(normalizedSubdomain)) {
    return attorneyConfigCache.get(normalizedSubdomain);
  }

  // Fetch from Supabase
  // ...

  // Cache the result
  attorneyConfigCache.set(normalizedSubdomain, config);
  return config;
};
```

### Lazy Loading

The system uses lazy loading to improve initial load time:

```javascript
// Lazy load components
const AttorneySubdomainView = lazy(() => import('./components/AttorneySubdomainView'));
```

## Troubleshooting

### Debugging Subdomain Issues

#### Step 1: Check Condition Values
Add debugging to see which condition is failing:

```javascript
console.log('🔍 [App.jsx] Condition check:', {
  isAttorneySubdomain,
  hasAttorneyProfile: !!attorneyProfile,
  isLoading,
  firmName: attorneyProfile?.firmName,
  firm_name: attorneyProfile?.firm_name,
  subdomain,
  subdomainNotDefault: subdomain !== 'default'
});
```

#### Step 2: Verify PostMessage Flow
Look for these logs in sequence:

```
🎯 [App.jsx] Subdomain iframe loaded, waiting for PREVIEW_READY message...
🎯 [SimplePreviewPage] Sent PREVIEW_READY message to parent
🎯 [App.jsx] Received PREVIEW_READY from subdomain iframe, sending config...
🎯 [SimplePreviewPage] Received message from parent: [config object]
🎯 [SimplePreviewPage] Config updated successfully
```

#### Step 3: Check Attorney Data Loading
Verify attorney profile is loaded correctly:

```javascript
console.log('✅ [App.jsx] Attorney profile loaded:', {
  hasProfile: !!profile,
  firmName: profile?.firmName,
  firm_name: profile?.firm_name,
  id: profile?.id,
  subdomain: profile?.subdomain
});
```

#### Common Error Patterns

1. **No iframe logs**: Condition not met, check property names
2. **Iframe loads but shows default content**: PostMessage not working
3. **Multiple attorney manager scripts loading**: Not using iframe, falling through to main app

## Subdomain Management

### Creation Process

The process for creating a new attorney subdomain:

1. Attorney signs up through the platform
2. System generates a unique subdomain based on firm name
3. Attorney record is created in Supabase with the subdomain
4. Vapi assistant is created for the attorney
5. Subdomain becomes immediately available

### Subdomain Generation

The system generates subdomains based on the firm name:

```javascript
const generateSubdomain = (firmName) => {
  // Convert firm name to lowercase and remove special characters
  let subdomain = firmName.toLowerCase()
    .replace(/[^a-z0-9]/g, '')
    .replace(/\s+/g, '');

  // Add random suffix to ensure uniqueness
  const randomSuffix = Math.floor(Math.random() * 1000);
  subdomain = `${subdomain}${randomSuffix}`;

  return subdomain;
};
```

### Subdomain Customization

Attorneys can customize their subdomain through the dashboard:

1. Navigate to Profile tab
2. Enter desired subdomain
3. System checks availability
4. If available, updates the attorney record

## Future Enhancements

Planned enhancements for the subdomain system:

1. **Custom Domain Support**
   - Allow attorneys to use their own domains
   - Automatic SSL certificate provisioning
   - Domain verification process

2. **Subdomain Analytics**
   - Track visits to attorney subdomains
   - Analyze conversion rates
   - Provide performance metrics

3. **Enhanced Customization**
   - Custom page layouts
   - Additional branding options
   - Custom CSS support

4. **Multi-language Support**
   - Localized subdomain content
   - Language-specific assistants
   - Regional customization

5. **Advanced Routing**
   - Practice area specific paths
   - Custom landing pages
   - A/B testing support
