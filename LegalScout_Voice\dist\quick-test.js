// Quick Website Import Test - Single execution
(async function() {
    const TEST_URL = 'https://www.generalcounsel.online';
    
    console.log('🧪 Quick website import test starting...');
    console.log(`📍 Testing URL: ${TEST_URL}`);
    
    try {
        // Test Jina AI
        console.log('1️⃣ Testing Jina AI...');
        const response = await fetch(`https://r.jina.ai/${TEST_URL}`, {
            headers: {
                'Accept': 'application/json',
                'X-With-Generated-Alt': 'true'
            }
        });
        
        if (response.ok) {
            const content = await response.text();
            console.log(`✅ Jina AI SUCCESS! Content length: ${content.length}`);
            console.log(`📄 Sample: ${content.substring(0, 200)}...`);
            
            // Show success modal
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                background: #d4edda; color: #155724; padding: 20px; border-radius: 8px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 10000; max-width: 500px;
            `;
            modal.innerHTML = `
                <h3>✅ Website Import Test SUCCESS!</h3>
                <p><strong>URL:</strong> ${TEST_URL}</p>
                <p><strong>Content Length:</strong> ${content.length} characters</p>
                <p><strong>Status:</strong> Jina AI working, CSP fixed!</p>
                <button onclick="this.parentElement.remove()" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">Close</button>
            `;
            document.body.appendChild(modal);
            
            return { success: true, contentLength: content.length };
        } else {
            throw new Error(`Jina API error: ${response.status} ${response.statusText}`);
        }
    } catch (error) {
        console.error('❌ Test failed:', error);
        
        // Show error modal
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
            background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 10000; max-width: 500px;
        `;
        modal.innerHTML = `
            <h3>❌ Website Import Test FAILED</h3>
            <p><strong>Error:</strong> ${error.message}</p>
            <p><strong>Likely Issue:</strong> CSP still blocking or network error</p>
            <button onclick="this.parentElement.remove()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">Close</button>
        `;
        document.body.appendChild(modal);
        
        return { success: false, error: error.message };
    }
})();
