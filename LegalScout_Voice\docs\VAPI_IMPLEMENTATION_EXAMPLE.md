# Vapi Implementation Example

This document provides a concrete implementation example for handling Vapi's iframe messages to update volume level indicators and transcript rendering.

## Window Message Handler

```jsx
import React, { useState, useEffect, useRef } from 'react';

function VapiCallComponent() {
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [messages, setMessages] = useState([]);
  const [volumeLevel, setVolumeLevel] = useState(0);
  
  // Add window message listener for Vapi iframe messages
  useEffect(() => {
    const handleWindowMessage = (event) => {
      if (!event.data) return;
      
      // Only process Vapi iframe messages
      if (event.data.what === 'iframe-call-message') {
        console.log('Vapi iframe message received:', event.data);
        
        // Handle audio level events
        if (event.data.action === 'remote-participants-audio-level' && 
            event.data.participantsAudioLevel) {
          
          // Extract audio levels
          const audioLevels = Object.values(event.data.participantsAudioLevel);
          
          if (audioLevels.length > 0) {
            // Convert to numbers and filter out NaN values
            const numericLevels = audioLevels
              .map(level => typeof level === 'number' ? level : parseFloat(level))
              .filter(level => !isNaN(level));
            
            if (numericLevels.length > 0) {
              // Find the maximum level
              const level = Math.max(...numericLevels);
              
              // Scale the level to be more visible
              const scaledLevel = Math.min(level * 5, 1);
              
              // Update the volume level
              setVolumeLevel(scaledLevel);
              
              // Also update the DOM directly
              updateVolumeBars(scaledLevel);
            }
          }
        }
        
        // Handle transcript events
        if (event.data.action === 'transcript' || 
            event.data.transcript || 
            (event.data.message && event.data.message.content)) {
          
          // Extract transcript text and final status
          let transcriptText = '';
          let isFinal = false;
          
          if (event.data.transcript) {
            transcriptText = event.data.transcript;
            isFinal = event.data.is_final || false;
          } else if (event.data.message && event.data.message.content) {
            transcriptText = event.data.message.content;
            isFinal = event.data.message.is_final || false;
          } else if (event.data.content) {
            transcriptText = event.data.content;
            isFinal = event.data.is_final || false;
          }
          
          if (transcriptText && transcriptText.trim()) {
            console.log(`Transcript: "${transcriptText}", isFinal: ${isFinal}`);
            
            // Always update the current transcript
            setCurrentTranscript(transcriptText);
            
            // If this is a final transcript, add it to messages
            if (isFinal) {
              addTranscriptToMessages(transcriptText);
              
              // Clear current transcript
              setCurrentTranscript('');
            }
          }
        }
      }
    };
    
    // Add window event listener
    window.addEventListener('message', handleWindowMessage);
    
    // Cleanup function
    return () => {
      window.removeEventListener('message', handleWindowMessage);
    };
  }, []);
  
  // Function to update volume bars directly in the DOM
  const updateVolumeBars = (level) => {
    try {
      const volumeBars = document.querySelectorAll('.volume-bar');
      if (volumeBars && volumeBars.length > 0) {
        const activeBarCount = Math.floor(level * 10);
        
        volumeBars.forEach((bar, index) => {
          if (index < activeBarCount) {
            bar.classList.add('active');
          } else {
            bar.classList.remove('active');
          }
        });
      }
    } catch (error) {
      console.warn('Error updating volume bars:', error);
    }
  };
  
  // Function to add transcript to messages
  const addTranscriptToMessages = (text) => {
    setMessages(prev => {
      // Check for duplicates
      const isDuplicate = prev.some(msg => 
        msg.type === 'user' && 
        msg.text === text
      );
      
      if (isDuplicate) {
        console.log('Skipping duplicate transcript');
        return prev;
      }
      
      return [...prev, {
        type: 'user',
        text,
        timestamp: new Date().toISOString(),
        isTranscript: true
      }];
    });
    
    // Also add directly to DOM as a fallback
    try {
      const conversationArea = document.querySelector('.conversation-area');
      if (conversationArea) {
        // Create message element
        const messageElement = document.createElement('div');
        messageElement.className = 'message user';
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        const textElement = document.createElement('p');
        textElement.textContent = text;
        
        messageContent.appendChild(textElement);
        messageElement.appendChild(messageContent);
        
        // Add to conversation area
        conversationArea.appendChild(messageElement);
        
        // Scroll to bottom
        conversationArea.scrollTop = conversationArea.scrollHeight;
      }
    } catch (error) {
      console.warn('Error adding transcript to DOM:', error);
    }
  };
  
  // Render volume level indicator
  const renderVolumeIndicator = () => {
    return (
      <div className="volume-indicator">
        {[...Array(10)].map((_, index) => (
          <div 
            key={index} 
            className={`volume-bar ${index < Math.floor(volumeLevel * 10) ? 'active' : ''}`} 
          />
        ))}
      </div>
    );
  };
  
  // Render current transcript
  const renderCurrentTranscript = () => {
    if (!currentTranscript) return null;
    
    return (
      <div className="message user transcribing">
        <div className="message-content">
          <p>{currentTranscript}</p>
          <div className="typing-indicator">...</div>
        </div>
      </div>
    );
  };
  
  // Render conversation messages
  const renderMessages = () => {
    return messages.map((message, index) => (
      <div key={index} className={`message ${message.type}`}>
        <div className="message-content">
          <p>{message.text}</p>
        </div>
      </div>
    ));
  };
  
  return (
    <div className="vapi-call-container">
      <div className="conversation-area">
        {renderMessages()}
        {renderCurrentTranscript()}
        <div ref={messagesEndRef} />
      </div>
      
      <div className="call-controls">
        {renderVolumeIndicator()}
        {/* Other call controls */}
      </div>
    </div>
  );
}

export default VapiCallComponent;
```

## Key Points

1. **Window Message Listener**:
   - Listens for all window messages
   - Filters for messages with `what: 'iframe-call-message'`
   - Processes both audio level and transcript events

2. **Audio Level Processing**:
   - Extracts values from `participantsAudioLevel` object
   - Converts to numbers and filters out NaN values
   - Scales the levels to be more visible
   - Updates both React state and DOM directly

3. **Transcript Processing**:
   - Extracts transcript text from various possible fields
   - Determines if the transcript is final
   - Updates current transcript for interim transcripts
   - Adds final transcripts to the conversation

4. **Direct DOM Manipulation**:
   - Updates volume bars directly in the DOM
   - Adds transcript messages directly to the conversation area
   - Ensures UI updates even if React state updates fail

5. **Cleanup**:
   - Removes the window event listener when the component unmounts

## CSS Example

```css
/* Volume indicator styles */
.volume-indicator {
  display: flex;
  align-items: flex-end;
  height: 20px;
  margin: 10px 0;
}

.volume-bar {
  width: 4px;
  height: 5px;
  margin-right: 2px;
  background-color: #ccc;
  transition: height 0.1s ease, background-color 0.1s ease;
}

.volume-bar.active {
  background-color: #4CAF50;
}

.volume-bar:nth-child(1).active { height: 5px; }
.volume-bar:nth-child(2).active { height: 7px; }
.volume-bar:nth-child(3).active { height: 9px; }
.volume-bar:nth-child(4).active { height: 11px; }
.volume-bar:nth-child(5).active { height: 13px; }
.volume-bar:nth-child(6).active { height: 15px; }
.volume-bar:nth-child(7).active { height: 17px; }
.volume-bar:nth-child(8).active { height: 19px; }
.volume-bar:nth-child(9).active { height: 21px; }
.volume-bar:nth-child(10).active { height: 23px; }

/* Transcript styles */
.transcribing .typing-indicator {
  display: inline-block;
  margin-left: 5px;
  animation: typing 1s infinite;
}

@keyframes typing {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}
```

This implementation example demonstrates how to handle Vapi's iframe messages to update volume level indicators and transcript rendering in a React component. The key is to listen for window messages with the format `{what: 'iframe-call-message', ...}` and process the audio level and transcript data accordingly.
