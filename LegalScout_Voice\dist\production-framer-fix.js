/**
 * Production-specific Framer Motion Fix
 * 
 * This script is specifically designed to fix Framer Motion issues in production environments.
 */

(function() {
  console.log('[ProductionFix] Setting up production-specific Framer Motion fix');

  // STEP 1: Immediately define React and React.createContext globally
  if (typeof window !== 'undefined') {
    // Create a global React object if it doesn't exist
    if (!window.React) {
      window.React = {};
    }

    // Define createContext if it doesn't exist
    if (!window.React.createContext) {
      window.React.createContext = function(defaultValue) {
        console.log('[ProductionFix] Using production mock createContext');
        return {
          Provider: function(props) { return props.children || null; },
          Consumer: function(props) { return props.children ? props.children({}) : null; },
          displayName: 'MockContext',
          _currentValue: defaultValue,
          _currentValue2: defaultValue,
          _threadCount: 0,
          _defaultValue: defaultValue
        };
      };
    }
  }

  // STEP 2: Define a global LayoutGroupContext
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'LayoutGroupContext'
  };

  // STEP 3: Override import.meta to prevent errors
  if (typeof window.import !== 'undefined' && typeof window.import.meta === 'undefined') {
    window.import.meta = { url: window.location.href };
  }

  // STEP 4: Define a global __vite__resolveUrl function if it doesn't exist
  if (typeof window.__vite__resolveUrl === 'undefined') {
    window.__vite__resolveUrl = function(url) { return url; };
  }

  // STEP 5: Create a MutationObserver to watch for script tags loading LayoutGroupContext.mjs
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.tagName === 'SCRIPT' && 
                node.src && 
                node.src.includes('LayoutGroupContext')) {
              console.log('[ProductionFix] Blocking script:', node.src);
              node.src = '';
              node.textContent = `
                // Mock implementation
                const LayoutGroupContext = window.LayoutGroupContext;
                export { LayoutGroupContext };
                export default LayoutGroupContext;
              `;
            }
          });
        }
      });
    });

    // Start observing the document
    observer.observe(document, { childList: true, subtree: true });
  }

  // STEP 6: Set up a periodic check for React.createContext
  const checkAndRestoreReact = function() {
    if (window.React && !window.React.createContext) {
      console.log('[ProductionFix] Restoring React.createContext');
      window.React.createContext = function(defaultValue) {
        console.log('[ProductionFix] Using production mock createContext');
        return {
          Provider: function(props) { return props.children || null; },
          Consumer: function(props) { return props.children ? props.children({}) : null; },
          displayName: 'MockContext',
          _currentValue: defaultValue,
          _currentValue2: defaultValue,
          _threadCount: 0,
          _defaultValue: defaultValue
        };
      };
    }
  };

  // Set up an interval to periodically check React.createContext
  setInterval(checkAndRestoreReact, 100);

  // Also set up event listeners for various events that might trigger React loading
  window.addEventListener('DOMContentLoaded', checkAndRestoreReact);
  window.addEventListener('load', checkAndRestoreReact);

  console.log('[ProductionFix] Production-specific Framer Motion fix setup complete');
})();
