# LegalScout Backend Structure

## Database Schema (Supabase)

### Tables

#### attorneys
```sql
CREATE TABLE IF NOT EXISTS public.attorneys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Basic Information
  subdomain TEXT UNIQUE NOT NULL,
  firm_name TEXT NOT NULL,
  name TEXT,
  email TEXT,
  phone TEXT,
  
  -- Media URLs
  logo_url TEXT,
  profile_image TEXT,
  
  -- Vapi Configuration
  vapi_instructions TEXT,
  vapi_context TEXT,
  
  -- Other Configuration
  practice_areas TEXT[] DEFAULT '{}',
  interaction_deposit_url TEXT,
  
  -- Configuration
  is_active BOOLEAN DEFAULT true
);
```

#### briefs
```sql
CREATE TABLE IF NOT EXISTS public.briefs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relationships
  attorney_id UUID REFERENCES public.attorneys(id),
  
  -- Client Information
  client_name TEXT,
  email TEXT,
  phone TEXT,
  
  -- Case Information
  practice_area TEXT,
  location TEXT,
  issue TEXT,
  urgency TEXT,
  noteworthy TEXT,
  
  -- Extended Data
  location_data JSONB,
  metadata JSONB,
  
  -- Status tracking
  status TEXT DEFAULT 'new',
  follow_up_date TIMESTAMP WITH TIME ZONE
);
```

### Indexes
```sql
CREATE INDEX IF NOT EXISTS attorneys_subdomain_idx ON public.attorneys (subdomain);
CREATE INDEX IF NOT EXISTS briefs_attorney_id_idx ON public.briefs (attorney_id);
```

### Triggers
```sql
-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to update attorneys updated_at
CREATE TRIGGER set_attorneys_updated_at
BEFORE UPDATE ON public.attorneys
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Add trigger to update briefs updated_at
CREATE TRIGGER set_briefs_updated_at
BEFORE UPDATE ON public.briefs
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();
```

## API Structure

### Authentication Endpoints
```typescript
// Auth Routes
POST /auth/signup
POST /auth/login
POST /auth/logout
POST /auth/refresh-token
```

### Attorney Endpoints
```typescript
// Attorney Routes
GET /attorneys
GET /attorneys/:id
GET /attorneys/search
GET /attorneys/nearby
POST /attorneys
PUT /attorneys/:id
DELETE /attorneys/:id
```

### Consultation Endpoints
```typescript
// Consultation Routes
GET /consultations
GET /consultations/:id
POST /consultations
PUT /consultations/:id
DELETE /consultations/:id
```

### Review Endpoints
```typescript
// Review Routes
GET /reviews/attorney/:attorneyId
POST /reviews
PUT /reviews/:id
DELETE /reviews/:id
```

## Security Implementation

### Row Level Security (RLS)
```sql
-- Attorneys table policies
ALTER TABLE attorneys ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public attorneys are viewable by everyone"
  ON attorneys FOR SELECT
  USING (true);

CREATE POLICY "Attorneys can update their own data"
  ON attorneys FOR UPDATE
  USING (auth.uid() = id);

-- Consultations table policies
ALTER TABLE consultations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own consultations"
  ON consultations FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own consultations"
  ON consultations FOR INSERT
  WITH CHECK (auth.uid() = user_id);
```

## Data Access Patterns

### Stored Procedures
```sql
-- Find nearby attorneys
CREATE OR REPLACE FUNCTION find_nearby_attorneys(
  search_location GEOGRAPHY,
  max_distance_meters INTEGER,
  specialties TEXT[]
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  distance FLOAT,
  specialty TEXT[]
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    a.id,
    a.name,
    ST_Distance(a.location, search_location) as distance,
    a.specialty
  FROM attorneys a
  WHERE
    ST_DWithin(a.location, search_location, max_distance_meters)
    AND specialty && specialties
  ORDER BY distance;
END;
$$ LANGUAGE plpgsql;
```

## Error Handling

### Error Codes
```typescript
const ERROR_CODES = {
  AUTHENTICATION: {
    INVALID_CREDENTIALS: 'AUTH001',
    TOKEN_EXPIRED: 'AUTH002',
    UNAUTHORIZED: 'AUTH003'
  },
  CONSULTATION: {
    SCHEDULING_CONFLICT: 'CONS001',
    INVALID_TIME: 'CONS002',
    NOT_FOUND: 'CONS003'
  },
  ATTORNEY: {
    NOT_FOUND: 'ATT001',
    INVALID_SPECIALTY: 'ATT002',
    UNAVAILABLE: 'ATT003'
  }
};
```

## Caching Strategy

### Redis Cache Configuration
```typescript
interface CacheConfig {
  attorneys: {
    ttl: 3600, // 1 hour
    keys: ['location', 'specialty']
  },
  consultations: {
    ttl: 300, // 5 minutes
    keys: ['user_id', 'attorney_id']
  },
  reviews: {
    ttl: 1800, // 30 minutes
    keys: ['attorney_id']
  }
}
```

## Background Jobs

### Job Definitions
```typescript
interface JobDefinitions {
  reminderJob: {
    schedule: '*/15 * * * *', // Every 15 minutes
    handler: async () => {
      // Send consultation reminders
    }
  },
  cleanupJob: {
    schedule: '0 0 * * *', // Daily at midnight
    handler: async () => {
      // Clean up expired sessions
    }
  }
}
```

## Monitoring and Logging

### Log Structure
```typescript
interface LogEntry {
  timestamp: string;
  level: 'INFO' | 'WARN' | 'ERROR';
  service: string;
  action: string;
  userId?: string;
  metadata: {
    request?: {
      method: string;
      path: string;
      params: object;
    };
    response?: {
      status: number;
      body: object;
    };
    error?: {
      code: string;
      message: string;
      stack?: string;
    };
  };
}
```

## API Response Format

### Standard Response Structure
```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata: {
    timestamp: string;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
    };
  };
}
``` 