/**
 * Direct Website Import Test
 * Tests the website import functionality directly in the browser
 */

// Prevent multiple loading issues
if (window.DIRECT_TEST_LOADED) {
    console.log('Direct test already loaded, skipping...');
} else {
    window.DIRECT_TEST_LOADED = true;

// Test configuration
const TEST_URL = 'https://www.generalcounsel.online';

// Direct test function that bypasses the API server
async function directWebsiteImportTest() {
    console.log('🧪 Starting direct website import test...');
    console.log(`📍 Testing URL: ${TEST_URL}`);
    
    try {
        // Step 1: Test Jina AI directly
        console.log('\n1️⃣ Testing Jina AI Reader...');
        const jinaResponse = await fetch(`https://r.jina.ai/${TEST_URL}`, {
            headers: {
                'Accept': 'application/json',
                'X-With-Generated-Alt': 'true'
            }
        });
        
        if (!jinaResponse.ok) {
            throw new Error(`Jina API error: ${jinaResponse.status} ${jinaResponse.statusText}`);
        }
        
        const jinaContent = await jinaResponse.text();
        console.log(`✅ Jina AI success! Content length: ${jinaContent.length}`);
        console.log(`📄 Sample content: ${jinaContent.substring(0, 300)}...`);
        
        // Step 2: Test OpenAI extraction (mock for now)
        console.log('\n2️⃣ Testing data extraction...');
        
        // Mock extraction since we can't call OpenAI directly from browser
        const mockExtractedData = {
            firmName: 'General Counsel Online',
            attorneyName: 'Legal Team',
            phone: '(*************',
            email: '<EMAIL>',
            practiceAreas: ['Corporate Law', 'Legal Consulting'],
            primaryColor: '#1e40af',
            secondaryColor: '#3b82f6',
            welcomeMessage: 'Welcome to General Counsel Online! How can we help you today?',
            buttonText: 'Start Consultation',
            vapiInstructions: 'You are an AI assistant for General Counsel Online. Help potential clients with their legal questions.',
            extractionConfidence: 0.75,
            websiteUrl: TEST_URL,
            importedAt: new Date().toISOString()
        };
        
        console.log('✅ Data extraction (mocked):', mockExtractedData);
        
        // Step 3: Test the preview modal
        console.log('\n3️⃣ Testing preview functionality...');
        
        // Create a preview modal to test the UI
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 500px;
            max-height: 80vh;
            overflow-y: auto;
        `;
        
        modal.innerHTML = `
            <h3>🎉 Website Import Test Results</h3>
            <p><strong>Firm:</strong> ${mockExtractedData.firmName}</p>
            <p><strong>Phone:</strong> ${mockExtractedData.phone}</p>
            <p><strong>Email:</strong> ${mockExtractedData.email}</p>
            <p><strong>Practice Areas:</strong> ${mockExtractedData.practiceAreas.join(', ')}</p>
            <p><strong>Confidence:</strong> ${Math.round(mockExtractedData.extractionConfidence * 100)}%</p>
            <button onclick="this.parentElement.remove()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">Close</button>
        `;
        
        document.body.appendChild(modal);
        
        console.log('✅ Preview modal created successfully!');
        console.log('\n🎉 Direct website import test completed successfully!');
        
        return {
            success: true,
            jinaContent: jinaContent.substring(0, 1000),
            extractedData: mockExtractedData,
            message: 'Direct test completed - check the modal on screen!'
        };
        
    } catch (error) {
        console.error('❌ Direct test failed:', error);
        
        // Show error modal
        const errorModal = document.createElement('div');
        errorModal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 500px;
        `;
        
        errorModal.innerHTML = `
            <h3>❌ Test Failed</h3>
            <p><strong>Error:</strong> ${error.message}</p>
            <button onclick="this.parentElement.remove()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-top: 10px;">Close</button>
        `;
        
        document.body.appendChild(errorModal);
        
        return {
            success: false,
            error: error.message
        };
    }
}

// Test the WebsiteImporter component integration
async function testWebsiteImporterComponent() {
    console.log('🧪 Testing WebsiteImporter component integration...');
    
    try {
        // Check if the component exists
        const importerElement = document.querySelector('.website-importer');
        if (!importerElement) {
            throw new Error('WebsiteImporter component not found on page');
        }
        
        console.log('✅ WebsiteImporter component found');
        
        // Check if the URL input exists
        const urlInput = importerElement.querySelector('input[type="url"]');
        if (!urlInput) {
            throw new Error('URL input not found in WebsiteImporter');
        }
        
        console.log('✅ URL input found');
        
        // Check if the import button exists
        const importButton = importerElement.querySelector('button');
        if (!importButton) {
            throw new Error('Import button not found in WebsiteImporter');
        }
        
        console.log('✅ Import button found');
        console.log('🎉 WebsiteImporter component integration test passed!');
        
        return { success: true, message: 'Component integration test passed' };
        
    } catch (error) {
        console.error('❌ Component integration test failed:', error);
        return { success: false, error: error.message };
    }
}

// Make functions available globally
window.directWebsiteImportTest = directWebsiteImportTest;
window.testWebsiteImporterComponent = testWebsiteImporterComponent;

console.log('🧪 Direct test functions loaded!');
console.log('📋 Available functions:');
console.log('  - directWebsiteImportTest() - Test the full import flow');
console.log('  - testWebsiteImporterComponent() - Test component integration');
console.log('');
console.log('💡 Try: directWebsiteImportTest()');

} // End of DIRECT_TEST_LOADED check
