<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout Subdomain Debugger</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background-color: #0056b3; }
        .danger { background-color: #dc3545; }
        .danger:hover { background-color: #c82333; }
        .success-btn { background-color: #28a745; }
        .success-btn:hover { background-color: #218838; }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
        .highlight {
            background-color: yellow;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 LegalScout Subdomain Debugger</h1>
        <p>This tool helps diagnose and fix subdomain detection issues in development.</p>

        <div id="status"></div>

        <h3>🔍 Current Status:</h3>
        <pre id="current-status">Loading...</pre>

        <h3>⚡ Quick Actions:</h3>
        <button onclick="clearTestSubdomain()" class="danger">Clear Test Subdomain</button>
        <button onclick="setToDefault()" class="success-btn">Reset to Default</button>
        <button onclick="reloadApp()">🔄 Reload App</button>
        <button onclick="checkStatus()">🔍 Refresh Status</button>

        <h3>🧪 Test Subdomains:</h3>
        <p><small>Click to simulate different attorney subdomains:</small></p>
        <div id="subdomain-buttons"></div>

        <h3>📋 Debug Information:</h3>
        <pre id="debug-info">Checking...</pre>

        <h3>💡 Instructions:</h3>
        <div id="instructions"></div>
    </div>

    <script>
        const LOCAL_STORAGE_KEY = 'legalscout_test_subdomain';

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function checkStatus() {
            const testSubdomain = localStorage.getItem(LOCAL_STORAGE_KEY);
            const hostname = window.location.hostname;
            const currentUrl = window.location.href;
            const port = window.location.port;

            // Check for ALL possible localStorage keys that might affect subdomain
            const allLocalStorageKeys = Object.keys(localStorage);
            const subdomainRelatedKeys = allLocalStorageKeys.filter(key =>
                key.includes('legal') ||
                key.includes('subdomain') ||
                key.includes('attorney') ||
                key.includes('test')
            );

            const statusInfo = {
                hostname: hostname,
                port: port,
                currentUrl: currentUrl,
                testSubdomain: testSubdomain || 'None set',
                isLocalhost: hostname === 'localhost',
                shouldBeDefault: hostname === 'localhost' && !testSubdomain,
                localStorage_keys: subdomainRelatedKeys,
                all_localStorage: allLocalStorageKeys.length > 10 ?
                    `${allLocalStorageKeys.length} total keys` :
                    allLocalStorageKeys
            };

            document.getElementById('current-status').textContent = JSON.stringify(statusInfo, null, 2);

            // Determine the issue and provide instructions
            if (testSubdomain && hostname === 'localhost') {
                updateStatus(`⚠️ ISSUE FOUND: Test subdomain "${testSubdomain}" is active!`, 'error');
                document.getElementById('debug-info').innerHTML =
                    `<span class="highlight">PROBLEM IDENTIFIED:</span>\n` +
                    `Test subdomain "${testSubdomain}" is overriding localhost behavior.\n` +
                    `This is why you're seeing the attorney subdomain page instead of the home page.\n\n` +
                    `<span class="highlight">SOLUTION:</span>\n` +
                    `1. Click "Clear Test Subdomain" button above\n` +
                    `2. Click "Reload App" to go back to localhost:5173\n` +
                    `3. You should now see the normal home page with Home, Agent, and About navigation`;

                document.getElementById('instructions').innerHTML = `
                    <div class="status error">
                        <strong>🚨 Action Required:</strong><br>
                        1. Click the <strong>"Clear Test Subdomain"</strong> button above<br>
                        2. Click <strong>"Reload App"</strong> to return to the main app<br>
                        3. You should now see the proper home page
                    </div>
                `;
            } else if (hostname === 'localhost' && !testSubdomain) {
                updateStatus('✅ Status: Normal - No test subdomain interference', 'success');
                document.getElementById('debug-info').textContent =
                    `Status: Normal\n` +
                    `No test subdomain is set.\n` +
                    `Localhost should show the home page with Home, Agent, and About navigation.\n\n` +
                    `If you're still seeing issues, the problem might be elsewhere in the code.`;

                document.getElementById('instructions').innerHTML = `
                    <div class="status success">
                        <strong>✅ All Clear:</strong><br>
                        No subdomain issues detected. Click "Reload App" to return to the main application.
                    </div>
                `;
            } else {
                updateStatus(`ℹ️ Running on: ${hostname}${port ? ':' + port : ''}`, 'info');
                document.getElementById('debug-info').textContent =
                    `Running on: ${hostname}\n` +
                    `This may be a real subdomain or production domain.`;

                document.getElementById('instructions').innerHTML = `
                    <div class="status info">
                        <strong>ℹ️ Non-localhost Environment:</strong><br>
                        You're not on localhost, so subdomain behavior may be different.
                    </div>
                `;
            }
        }

        function clearTestSubdomain() {
            const wasSet = localStorage.getItem(LOCAL_STORAGE_KEY);

            // Clear ALL subdomain-related localStorage keys
            const allKeys = Object.keys(localStorage);
            const clearedKeys = [];

            allKeys.forEach(key => {
                if (key.includes('legal') ||
                    key.includes('subdomain') ||
                    key.includes('attorney') ||
                    key.includes('test')) {
                    localStorage.removeItem(key);
                    clearedKeys.push(key);
                }
            });

            if (clearedKeys.length > 0) {
                updateStatus(`✅ Cleared ${clearedKeys.length} localStorage keys: ${clearedKeys.join(', ')}`, 'success');
            } else {
                updateStatus('ℹ️ No subdomain-related localStorage keys found', 'info');
            }

            setTimeout(checkStatus, 100);
        }

        function setToDefault() {
            localStorage.removeItem(LOCAL_STORAGE_KEY);
            updateStatus('✅ Reset to default (test subdomain cleared)', 'success');
            setTimeout(checkStatus, 100);
        }

        function setTestSubdomain(subdomain) {
            if (subdomain === 'default') {
                clearTestSubdomain();
            } else {
                localStorage.setItem(LOCAL_STORAGE_KEY, subdomain);
                updateStatus(`🧪 Test subdomain set to: ${subdomain}`, 'warning');
                setTimeout(checkStatus, 100);
            }
        }

        function reloadApp() {
            updateStatus('🔄 Redirecting to main app...', 'info');
            setTimeout(() => {
                window.location.href = 'http://localhost:5173/';
            }, 1000);
        }

        // Load available subdomains
        function loadSubdomains() {
            const mockSubdomains = ['default', 'smith-law', 'jones-legal', 'test-attorney', 'demo-firm'];
            const container = document.getElementById('subdomain-buttons');

            mockSubdomains.forEach(subdomain => {
                const btn = document.createElement('button');
                btn.textContent = subdomain;
                btn.onclick = () => setTestSubdomain(subdomain);
                if (subdomain === 'default') {
                    btn.className = 'success-btn';
                    btn.textContent = '🏠 ' + subdomain;
                } else {
                    btn.textContent = '🏢 ' + subdomain;
                }
                container.appendChild(btn);
            });
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkStatus();
            loadSubdomains();
        });

        // Auto-refresh status every 5 seconds
        setInterval(checkStatus, 5000);
    </script>
</body>
</html>
