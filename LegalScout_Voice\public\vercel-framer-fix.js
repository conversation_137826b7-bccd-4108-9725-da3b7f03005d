/**
 * Vercel-specific Framer Motion Fix
 * 
 * This script is specifically designed to fix Framer Motion issues in Vercel deployments.
 * It must be the first script loaded in the HTML.
 */

(function() {
  console.log('[VercelFix] Setting up Vercel-specific Framer Motion fix');

  // STEP 1: Define React and React.createContext globally before anything else loads
  window.React = window.React || {};
  
  // Store the original createContext if it exists
  const originalCreateContext = window.React.createContext;
  
  // Define our own createContext that will always work
  window.React.createContext = function(defaultValue) {
    console.log('[VercelFix] Using mock createContext');
    return {
      Provider: function(props) { return props.children || null; },
      Consumer: function(props) { return props.children ? props.children({}) : null; },
      displayName: 'MockContext',
      _currentValue: defaultValue,
      _currentValue2: defaultValue,
      _threadCount: 0,
      _defaultValue: defaultValue
    };
  };

  // STEP 2: Define a global LayoutGroupContext
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; },
    displayName: 'LayoutGroupContext'
  };

  // STEP 3: Intercept the actual LayoutGroupContext.mjs module
  // Create a mock module system
  window.__vite_ssr_exports__ = {};
  window.__vite_ssr_import_meta__ = { url: '' };
  
  // Define the problematic module
  window.__framer_motion_LayoutGroupContext_mjs__ = {
    LayoutGroupContext: window.LayoutGroupContext,
    default: window.LayoutGroupContext
  };

  // STEP 4: Intercept script loading
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.call(document, tagName);
    
    if (tagName.toLowerCase() === 'script') {
      const originalSetAttribute = element.setAttribute;
      element.setAttribute = function(name, value) {
        if (name === 'src' && value && value.includes('LayoutGroupContext')) {
          console.log('[VercelFix] Intercepting LayoutGroupContext script:', value);
          
          // Replace the src with a data URL containing our mock implementation
          const mockScript = `
            console.log('[VercelFix] Using mock LayoutGroupContext module');
            const LayoutGroupContext = window.LayoutGroupContext;
            export { LayoutGroupContext };
            export default LayoutGroupContext;
          `;
          
          const dataUrl = 'data:text/javascript;base64,' + btoa(mockScript);
          return originalSetAttribute.call(this, name, dataUrl);
        }
        
        return originalSetAttribute.apply(this, arguments);
      };
    }
    
    return element;
  };

  // STEP 5: Set up a MutationObserver to watch for dynamically added scripts
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(function(node) {
            if (node.tagName === 'SCRIPT' && 
                node.src && 
                node.src.includes('LayoutGroupContext')) {
              console.log('[VercelFix] Blocking dynamically added script:', node.src);
              node.src = '';
              node.textContent = `
                console.log('[VercelFix] Using mock LayoutGroupContext module');
                const LayoutGroupContext = window.LayoutGroupContext;
                export { LayoutGroupContext };
                export default LayoutGroupContext;
              `;
            }
          });
        }
      });
    });

    // Start observing the document
    observer.observe(document, { childList: true, subtree: true });
  }

  // STEP 6: Set up a periodic check for React.createContext
  const checkAndRestoreReact = function() {
    if (window.React && !window.React.createContext) {
      console.log('[VercelFix] Restoring React.createContext');
      window.React.createContext = originalCreateContext || function(defaultValue) {
        console.log('[VercelFix] Using mock createContext (restored)');
        return {
          Provider: function(props) { return props.children || null; },
          Consumer: function(props) { return props.children ? props.children({}) : null; },
          displayName: 'MockContext',
          _currentValue: defaultValue,
          _currentValue2: defaultValue,
          _threadCount: 0,
          _defaultValue: defaultValue
        };
      };
    }
  };

  // Set up an interval to periodically check React.createContext
  const intervalId = setInterval(checkAndRestoreReact, 50);

  // Clean up the interval after 10 seconds
  setTimeout(() => {
    clearInterval(intervalId);
    console.log('[VercelFix] Stopped monitoring React.createContext');
  }, 10000);

  // STEP 7: Define a global __vite__import function to handle dynamic imports
  if (typeof window.__vite__import === 'undefined') {
    window.__vite__import = function(moduleId) {
      if (moduleId.includes('LayoutGroupContext')) {
        console.log('[VercelFix] Intercepted import for LayoutGroupContext:', moduleId);
        return Promise.resolve(window.__framer_motion_LayoutGroupContext_mjs__);
      }
      
      // For other imports, try to use the real import function
      if (typeof window.import === 'function') {
        return window.import(moduleId);
      }
      
      return Promise.reject(new Error(`[VercelFix] Cannot import module: ${moduleId}`));
    };
  }

  console.log('[VercelFix] Vercel-specific Framer Motion fix setup complete');
})();
