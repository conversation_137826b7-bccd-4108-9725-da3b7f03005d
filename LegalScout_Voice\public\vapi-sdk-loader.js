/**
 * Robust Vapi Web SDK Loader
 * 
 * This script provides a reliable way to load the Vapi Web SDK with multiple fallbacks
 * and proper error handling. Based on official Vapi documentation.
 */

(function() {
  'use strict';

  // Prevent multiple loads
  if (window.__VAPI_SDK_LOADING || window.Vapi) {
    console.log('[VapiSDKLoader] Vapi SDK already loaded or loading');
    return;
  }

  window.__VAPI_SDK_LOADING = true;

  // Configuration
  const config = {
    cdnSources: [
      'https://cdn.vapi.ai/web-sdk@2.2.2/dist/web-sdk.js',
      'https://unpkg.com/@vapi-ai/web@latest/dist/index.umd.js',
      'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.umd.js'
    ],
    timeout: 10000,
    retryDelay: 1000
  };

  // Logging utility
  function log(message, level = 'info') {
    const prefix = '[VapiSDKLoader]';
    console[level](`${prefix} ${message}`);
  }

  // Load script with timeout and error handling
  function loadScript(src, timeout = config.timeout) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = src;
      script.async = true;
      script.crossOrigin = 'anonymous';

      const timeoutId = setTimeout(() => {
        script.remove();
        reject(new Error(`Timeout loading ${src}`));
      }, timeout);

      script.onload = () => {
        clearTimeout(timeoutId);
        log(`Successfully loaded from ${src}`);
        resolve();
      };

      script.onerror = () => {
        clearTimeout(timeoutId);
        script.remove();
        reject(new Error(`Failed to load ${src}`));
      };

      document.head.appendChild(script);
    });
  }

  // Try to load from the built bundle (since we installed the package)
  async function tryInstalledPackage() {
    try {
      log('Attempting to load from installed package bundle');

      // Try to load from the built Vite bundle that includes @vapi-ai/web
      // This should work since the package is now installed and bundled
      if (window.Vapi) {
        log('✅ Vapi already available in window (bundled)');
        return true;
      }

      // Check if it's available as a global after Vite bundling
      if (typeof window.__VAPI_BUNDLED__ !== 'undefined') {
        window.Vapi = window.__VAPI_BUNDLED__;
        log('✅ Successfully loaded from bundled package');
        return true;
      }

      log('Installed package not yet available, will try CDN fallback');
      return false;
    } catch (error) {
      log(`Installed package loading failed: ${error.message}`, 'warn');
      return false;
    }
  }

  // Validate Vapi is properly loaded
  function validateVapi() {
    if (typeof window.Vapi === 'function') {
      try {
        // Test instantiation with dummy key
        const testInstance = new window.Vapi('test-key');
        if (testInstance && typeof testInstance.start === 'function') {
          log('Vapi SDK validation successful');
          return true;
        }
      } catch (error) {
        log(`Vapi validation failed: ${error.message}`, 'warn');
      }
    }
    return false;
  }

  // Main loading function
  async function loadVapiSDK() {
    log('Starting Vapi SDK loading process');

    // Try installed package first (most reliable)
    log('Trying installed package bundle');
    const installedPackageSuccess = await tryInstalledPackage();

    if (installedPackageSuccess && validateVapi()) {
      log('✅ Vapi SDK loaded successfully from installed package');
      return true;
    }

    // Fallback to CDN sources if installed package fails
    log('Installed package not available, trying CDN sources as fallback');

    // Try each CDN source
    for (let i = 0; i < config.cdnSources.length; i++) {
      const src = config.cdnSources[i];

      try {
        log(`Trying CDN source ${i + 1}/${config.cdnSources.length}: ${src}`);
        await loadScript(src);

        // Wait a bit for the script to initialize
        await new Promise(resolve => setTimeout(resolve, 500));

        if (validateVapi()) {
          log('✅ Vapi SDK loaded successfully from CDN');
          return true;
        } else {
          log('CDN script loaded but Vapi not available', 'warn');
        }
      } catch (error) {
        log(`CDN source failed: ${error.message}`, 'warn');

        // Add delay before trying next source
        if (i < config.cdnSources.length - 1) {
          await new Promise(resolve => setTimeout(resolve, config.retryDelay));
        }
      }
    }

    // Final failure
    log('❌ Failed to load Vapi SDK from all sources', 'error');
    return false;
  }

  // Create helper function for easy Vapi instantiation
  function createVapiHelper() {
    window.createVapiInstance = function(apiKey, options = {}) {
      if (!window.Vapi) {
        throw new Error('Vapi SDK not loaded. Call loadVapiSDK() first.');
      }

      if (!apiKey || typeof apiKey !== 'string') {
        throw new Error('API key must be a non-empty string');
      }

      try {
        const vapi = new window.Vapi(apiKey);
        log(`Vapi instance created with key: ${apiKey.substring(0, 8)}...`);
        return vapi;
      } catch (error) {
        log(`Failed to create Vapi instance: ${error.message}`, 'error');
        throw error;
      }
    };
  }

  // Expose loading function globally
  window.loadVapiSDK = loadVapiSDK;

  // Auto-load and setup
  loadVapiSDK()
    .then(success => {
      window.__VAPI_SDK_LOADING = false;
      window.__VAPI_SDK_LOADED = success;
      
      if (success) {
        createVapiHelper();
        
        // Dispatch custom event for components waiting for Vapi
        const event = new CustomEvent('vapiSDKLoaded', { 
          detail: { success: true, vapi: window.Vapi } 
        });
        window.dispatchEvent(event);
        
        log('🎉 Vapi SDK setup complete');
      } else {
        // Dispatch failure event
        const event = new CustomEvent('vapiSDKLoaded', { 
          detail: { success: false, error: 'Failed to load from all sources' } 
        });
        window.dispatchEvent(event);
        
        log('💥 Vapi SDK setup failed');
      }
    })
    .catch(error => {
      window.__VAPI_SDK_LOADING = false;
      window.__VAPI_SDK_LOADED = false;
      log(`Unexpected error during SDK loading: ${error.message}`, 'error');
      
      // Dispatch failure event
      const event = new CustomEvent('vapiSDKLoaded', { 
        detail: { success: false, error: error.message } 
      });
      window.dispatchEvent(event);
    });

})();
