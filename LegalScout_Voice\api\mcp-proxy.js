/**
 * MCP Proxy API Endpoint
 * 
 * This endpoint acts as a proxy for MCP requests to avoid CORS issues.
 * It forwards requests to the appropriate MCP servers and returns the responses.
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/http.js';

// Cache for MCP clients to avoid reconnecting on every request
const mcpClients = new Map();

/**
 * Get or create an MCP client for the given configuration
 */
async function getMcpClient(config) {
  const { server, apiKey, transport = 'sse' } = config;
  const clientKey = `${server}-${transport}-${apiKey?.substring(0, 8)}`;
  
  if (mcpClients.has(clientKey)) {
    return mcpClients.get(clientKey);
  }
  
  try {
    const client = new Client({
      name: 'legalscout-mcp-proxy',
      version: '1.0.0',
    });
    
    let transportInstance;
    
    if (transport === 'sse') {
      transportInstance = new SSEClientTransport({
        url: `https://mcp.${server}.ai/sse`,
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });
    } else if (transport === 'http') {
      transportInstance = new StreamableHTTPClientTransport({
        url: `https://mcp.${server}.ai/mcp`,
        requestInit: {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      });
    } else {
      throw new Error(`Unsupported transport type: ${transport}`);
    }
    
    await client.connect(transportInstance);
    mcpClients.set(clientKey, client);
    
    return client;
  } catch (error) {
    console.error(`[MCP Proxy] Failed to connect to ${server}:`, error);
    throw error;
  }
}

/**
 * Handle MCP tool calls
 */
async function handleToolCall(client, toolName, args = {}) {
  try {
    const result = await client.callTool({
      name: toolName,
      arguments: args
    });
    
    return {
      success: true,
      data: result.content
    };
  } catch (error) {
    console.error(`[MCP Proxy] Tool call failed for ${toolName}:`, error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Main handler function
 */
export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.setHeader('Access-Control-Max-Age', '86400');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const { method } = req;
    const { server, tool, transport, ...args } = req.method === 'GET' ? req.query : req.body;
    
    // Validate required parameters
    if (!server) {
      return res.status(400).json({
        success: false,
        error: 'Server parameter is required (e.g., "vapi")'
      });
    }
    
    if (!tool) {
      return res.status(400).json({
        success: false,
        error: 'Tool parameter is required'
      });
    }
    
    // Get API key from environment or request
    let apiKey;
    if (server === 'vapi') {
      apiKey = process.env.VAPI_TOKEN || process.env.VITE_VAPI_SECRET_KEY;
    }
    
    if (!apiKey) {
      return res.status(400).json({
        success: false,
        error: `API key not configured for server: ${server}`
      });
    }
    
    // Get MCP client
    const client = await getMcpClient({
      server,
      apiKey,
      transport: transport || 'sse'
    });
    
    // Handle the tool call
    const result = await handleToolCall(client, tool, args);
    
    return res.status(200).json(result);
    
  } catch (error) {
    console.error('[MCP Proxy] Request failed:', error);
    
    return res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
}
