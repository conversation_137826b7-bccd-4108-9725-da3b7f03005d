# Button Customization in LegalScout

This document describes the button customization options available in the LegalScout platform, including color and opacity controls.

## Features

### Color Selection

The platform allows customization of the consultation button's background color:
- Users can select any color using the standard color picker
- The selected color is immediately reflected in the preview interface
- Colors are applied using RGBA values for proper opacity support

### Opacity Control

A new opacity slider allows users to adjust the transparency of the button:
- The opacity ranges from 0 (completely transparent) to 1 (fully opaque)
- The current opacity percentage is displayed next to the slider (e.g., "Opacity: 80%")
- Changes to opacity are immediately reflected in the preview interface

### Text Customization

Users can also customize the button text:
- The "Button Text" input field allows changing the call-to-action text
- Default value is "Start Consultation"
- Text automatically resizes to fit within the button

## Implementation Details

### State Management

Button customization properties are stored in React state:
- `secondaryColor` controls the background color
- `buttonOpacity` controls the transparency level
- `buttonText` controls the displayed text

### RGBA Implementation

The button uses RGBA color values to support opacity:
- The `hexToRgb` function converts hex color codes to RGB values
- Button background is set using: `rgba(${hexToRgb(secondaryColorState)}, ${Number(buttonOpacityState || 1)})`
- This ensures proper color with variable transparency

### Data Flow

Changes to button properties flow through the application:
1. User adjusts controls in SimpleDemoPage
2. State changes in App.jsx
3. Updates are passed to SimpleDemoPage
4. Changes are communicated to the PreviewInterface via postMessage
5. Interface updates to reflect new settings

## Technical Considerations

### Opacity vs. Alpha Channel

The implementation separates color and opacity to provide more control:
- Color picker handles the RGB values
- Separate slider controls opacity
- This separation makes the interface more intuitive for non-technical users

### Z-Index and Layering

To ensure proper rendering with transparency:
- Button uses relative positioning
- Content elements have z-index values to ensure proper stacking
- Text and images remain fully opaque when button background is transparent

## Testing

When testing button customization, verify:
1. Color changes apply correctly to the button
2. Opacity slider updates the button transparency as expected
3. Text changes appear correctly
4. Button remains functional at all opacity levels
5. Image/logo visibility is maintained at different opacity settings

## Future Enhancements

Potential improvements for button customization include:
- Gradient background options
- Border customization (width, color, style)
- Button shape options (round, square, pill)
- Hover and active state customization
- Animation effects for button interactions

## Related Documentation

For additional interface customization options, including practice area font color, see:
- [Interface Customization](./interface_customization.md) - Details on practice area text styling and additional interface options 