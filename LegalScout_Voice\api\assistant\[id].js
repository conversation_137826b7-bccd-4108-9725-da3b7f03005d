/**
 * Assistant Proxy API
 * 
 * This file creates a proxy specifically for assistant requests to avoid CORS issues.
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Get the assistant ID from the URL
  const assistantId = req.query.id;
  
  if (!assistantId) {
    res.status(400).json({ error: 'Assistant ID is required' });
    return;
  }

  // Get the API key from the request headers or environment variables
  const apiKey = req.headers.authorization?.replace('Bearer ', '') || 
                process.env.VAPI_TOKEN || 
                process.env.VITE_VAPI_SECRET_KEY || 
                process.env.VITE_VAPI_PUBLIC_KEY;

  if (!apiKey) {
    res.status(401).json({ error: 'No API key provided' });
    return;
  }

  // Construct the Vapi API URL
  const vapiUrl = `https://api.vapi.ai/assistant/${assistantId}`;

  try {
    // Forward the request to the Vapi API
    const response = await fetch(vapiUrl, {
      method: req.method,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    res.status(response.status).json(data);
  } catch (error) {
    console.error('Error proxying request to Vapi API:', error);
    res.status(500).json({ error: 'Error proxying request to Vapi API' });
  }
}
