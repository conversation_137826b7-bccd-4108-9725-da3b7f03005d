# Map Visualization System

## Overview
LegalScout provides two types of attorney location visualizations:
1. A 2D interactive map using Leaflet
2. A 3D globe visualization using Three.js

These visualizations help users understand attorney locations and make informed decisions about legal representation.

## Components

### 1. GlobeDossierView
```typescript
interface GlobeDossierProps {
  isVisible: boolean;
  caseData: CaseData;
  attorneys: Attorney[];
  onSelectAttorney: (attorney: Attorney) => void;
}
```

#### Features
- Interactive 3D globe visualization
- Attorney location markers
- Case location highlighting
- Smooth camera transitions
- Responsive design
- Performance optimization

#### Technical Implementation
```jsx
const GlobeDossierView = ({ isVisible, caseData, attorneys, onSelectAttorney }) => {
  // Three.js setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
  const renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true,
    logarithmicDepthBuffer: true
  });
}
```

### 2. MapDossierView
```typescript
interface MapDossierProps {
  caseLocation: LatLng;
  attorneys: Attorney[];
  onSelectAttorney: (attorney: Attorney) => void;
  zoom?: number;
}
```

#### Features
- 2D map visualization
- Custom attorney markers
- Distance calculations
- Clustering for multiple attorneys
- Interactive tooltips
- Responsive zoom levels

## Data Structures

### Attorney Location
```typescript
interface AttorneyLocation {
  id: string;
  latitude: number;
  longitude: number;
  name: string;
  practiceAreas: string[];
  distance?: number;
}
```

### Case Location
```typescript
interface CaseLocation {
  latitude: number;
  longitude: number;
  address: string;
  city: string;
  state: string;
  country: string;
}
```

## Visualization Features

### Globe View
1. **Globe Creation**
   ```javascript
   const globeGeometry = new THREE.SphereGeometry(5, 32, 32);
   const globeMaterial = new THREE.MeshPhongMaterial({
     map: earthTexture,
     bumpMap: bumpMap,
     bumpScale: 0.05
   });
   ```

2. **Location Markers**
   ```javascript
   const createMarker = (lat, lng) => {
     const marker = new THREE.Mesh(
       new THREE.SphereGeometry(0.1, 16, 16),
       new THREE.MeshBasicMaterial({ color: 0xff0000 })
     );
     positionOnGlobe(marker, lat, lng);
     return marker;
   };
   ```

3. **Camera Controls**
   ```javascript
   const controls = new OrbitControls(camera, renderer.domElement);
   controls.enableDamping = true;
   controls.dampingFactor = 0.05;
   controls.rotateSpeed = 0.5;
   ```

### Map View
1. **Map Initialization**
   ```javascript
   const map = L.map('map-container').setView([lat, lng], zoom);
   L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png').addTo(map);
   ```

2. **Attorney Markers**
   ```javascript
   const addAttorneyMarker = (attorney) => {
     const marker = L.marker([attorney.lat, attorney.lng], {
       icon: createCustomIcon(attorney)
     }).addTo(map);
     marker.bindPopup(createAttorneyPopup(attorney));
   };
   ```

3. **Distance Circles**
   ```javascript
   const addDistanceCircle = (center, radius) => {
     L.circle(center, {
       radius: radius * 1000,
       fillColor: '#007bff',
       fillOpacity: 0.1,
       color: '#007bff',
       weight: 1
     }).addTo(map);
   };
   ```

## Performance Optimization

### Globe Performance
1. **Level of Detail**
   ```javascript
   const LOD = {
     NEAR: { segments: 32, markers: true },
     MID: { segments: 24, markers: true },
     FAR: { segments: 16, markers: false }
   };
   ```

2. **Marker Clustering**
   ```javascript
   const clusterMarkers = (markers, threshold) => {
     // Implementation for clustering nearby markers
   };
   ```

### Map Performance
1. **Marker Clustering**
   ```javascript
   const markerCluster = L.markerClusterGroup({
     maxClusterRadius: 50,
     spiderfyOnMaxZoom: true
   });
   ```

2. **Viewport Optimization**
   ```javascript
   const optimizeMarkersForViewport = (bounds) => {
     // Show only markers in current viewport
   };
   ```

## User Interaction

### Controls
1. **Globe Controls**
   - Rotate: Left mouse drag
   - Zoom: Mouse wheel
   - Pan: Right mouse drag
   - Reset: Double click

2. **Map Controls**
   - Pan: Left mouse drag
   - Zoom: Mouse wheel or buttons
   - Select: Click on marker
   - Reset: Home button

### Events
```typescript
interface MapEvents {
  onMarkerClick: (attorney: Attorney) => void;
  onViewportChange: (bounds: LatLngBounds) => void;
  onZoomChange: (zoom: number) => void;
}
```

## Integration Example

```jsx
function AttorneyLocator({ caseData }) {
  const [view, setView] = useState('globe');
  const [selectedAttorney, setSelectedAttorney] = useState(null);

  return (
    <div className="attorney-locator">
      <ViewToggle current={view} onChange={setView} />
      
      {view === 'globe' ? (
        <GlobeDossierView
          isVisible={true}
          caseData={caseData}
          attorneys={attorneys}
          onSelectAttorney={setSelectedAttorney}
        />
      ) : (
        <MapDossierView
          caseLocation={caseData.location}
          attorneys={attorneys}
          onSelectAttorney={setSelectedAttorney}
          zoom={12}
        />
      )}
      
      {selectedAttorney && (
        <AttorneyDetail attorney={selectedAttorney} />
      )}
    </div>
  );
}
``` 