# Vapi MCP Server on Vercel

This document provides an overview of the Vapi Model Context Protocol (MCP) Server integration on Vercel in LegalScout Voice.

## Overview

The Vapi MCP Server on Vercel is an implementation of the Model Context Protocol that exposes Vapi's APIs as callable tools. This allows programmatic control of Vapi voice agents, calls, and other resources through a standardized protocol.

By hosting the MCP server on Vercel, we gain several benefits:
- Support for both SSE and Streamable HTTP transport
- Improved performance through Vercel's Fluid Compute
- Simplified deployment and management
- Better reliability and scalability

## Features

- **Assistant Management**: Create, retrieve, and list Vapi assistants
- **Call Management**: Create outbound calls, schedule future calls, and retrieve call details
- **Phone Number Management**: List and retrieve phone numbers
- **Tool Discovery**: Dynamically discover available tools and capabilities

## Implementation

The Vapi MCP Server is implemented as a Vercel serverless function in the `api/vapi-mcp-server` directory using the official Vercel MCP adapter. It consists of:

- `index.js`: The main handler for the MCP server using the Vercel MCP adapter
- `[path].js`: A catch-all route for handling all MCP server requests

The Vercel MCP adapter (`@vercel/mcp-adapter`) simplifies the implementation by handling the routing, SSE connections, and other MCP-specific functionality automatically.

## Configuration

### Environment Variables

The Vapi MCP Server requires the following environment variables:

- `VAPI_TOKEN`: Your Vapi API key

### Vercel Configuration

The `vercel.json` file includes the necessary configuration for the Vapi MCP Server:

```json
{
  "functions": {
    "api/vapi-mcp-server/*.js": {
      "memory": 1024,
      "maxDuration": 60
    }
  },
  "routes": [
    {
      "src": "/api/vapi-mcp-server/(.*)",
      "dest": "/api/vapi-mcp-server/[path].js?path=$1"
    },
    {
      "src": "/api/vapi-mcp-server",
      "dest": "/api/vapi-mcp-server/index.js"
    },
    {
      "src": "/vapi-mcp-server/(.*)",
      "dest": "/api/vapi-mcp-server/[path].js?path=$1"
    },
    {
      "src": "/vapi-mcp-server",
      "dest": "/api/vapi-mcp-server/index.js"
    }
  ]
}
```

The implementation uses the official Vercel MCP adapter:

```javascript
import { createMcpHandler } from '@vercel/mcp-adapter';
import { createServer } from '@vapi-ai/mcp-server';

// Create a Vapi MCP server instance
const createVapiMcpServer = () => {
  // Create and return the Vapi MCP server
  return createServer({
    vapiToken: process.env.VAPI_TOKEN
  });
};

// Create the MCP handler using the Vercel MCP adapter
export default createMcpHandler({
  createServer: createVapiMcpServer,
  config: {
    debug: process.env.NODE_ENV === 'development',
    ssePath: '/sse',
    cors: {
      origin: '*',
      methods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }
  }
});
```

## Usage

### Client-Side Integration

To use the Vapi MCP Server from the client side, you can use the `vapiMcpService` service:

```javascript
import { vapiMcpService } from '../services/vapiMcpService';

// Connect to the Vapi MCP Server
const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
const connected = await vapiMcpService.connect(apiKey);

if (connected) {
  console.log('Connected to Vapi MCP Server');

  // List assistants
  const assistants = await vapiMcpService.listAssistants();
  console.log('Assistants:', assistants);

  // Create a call
  const call = await vapiMcpService.createCall(assistantId, phoneNumber, options);
  console.log('Call created:', call);
}
```

### Testing

You can test the Vapi MCP Server using the `scripts/test-vapi-mcp-server.js` script:

```bash
node scripts/test-vapi-mcp-server.js
```

This script will connect to the Vapi MCP Server, list available tools, and list assistants.

## Endpoints

The Vapi MCP Server is available at the following endpoints:

- `/vapi-mcp-server`: The main endpoint for the MCP server
- `/vapi-mcp-server/sse`: The SSE endpoint for the MCP server

## Troubleshooting

If you encounter issues with the Vapi MCP Server, check the following:

1. Make sure the `VAPI_TOKEN` environment variable is set correctly
2. Check the Vercel logs for any errors
3. Verify that the Vapi API key has the necessary permissions
4. Try connecting to the Vapi-hosted MCP server at `https://mcp.vapi.ai/sse` to see if the issue is with your deployment or with Vapi

## References

- [Vapi MCP Server Documentation](https://docs.vapi.ai/mcp-server)
- [Model Context Protocol Specification](https://modelcontextprotocol.ai)
- [Vercel Serverless Functions](https://vercel.com/docs/functions)
