/**
 * Interaction Fix
 * 
 * This script fixes issues with input fields and dropdowns
 * that prevent users from clicking or interacting with them.
 */

(function() {
  // Function to fix specific interactive elements
  function fixInteractions() {
    // Fix the website URL input
    const firmUrlInput = document.getElementById('firmUrl');
    if (firmUrlInput) {
      // Make sure it's clickable
      firmUrlInput.style.pointerEvents = 'auto';
      firmUrlInput.style.position = 'relative';
      firmUrlInput.style.zIndex = '1000';
      
      // Add a direct click handler
      firmUrlInput.onclick = function(e) {
        this.focus();
        e.stopPropagation();
      };
      
      // Add a mousedown handler to prevent event bubbling
      firmUrlInput.onmousedown = function(e) {
        e.stopPropagation();
      };
      
      // Also fix its parent containers
      let parent = firmUrlInput.parentElement;
      while (parent && !parent.classList.contains('start-option')) {
        parent.style.pointerEvents = 'auto';
        parent.style.position = 'relative';
        parent.style.zIndex = '999';
        parent = parent.parentElement;
      }
      
      // If we found the start-option container, fix it too
      if (parent) {
        parent.style.pointerEvents = 'auto';
        parent.style.position = 'relative';
        parent.style.zIndex = '999';
      }
    }
    
    // Fix dropdown elements
    const dropdowns = document.querySelectorAll('select, .dropdown, .dropdown-toggle');
    dropdowns.forEach(dropdown => {
      // Make sure it's clickable
      dropdown.style.pointerEvents = 'auto';
      dropdown.style.position = 'relative';
      dropdown.style.zIndex = '1000';
      
      // Add direct click handler
      dropdown.onclick = function(e) {
        e.stopPropagation();
      };
      
      // Add mousedown handler
      dropdown.onmousedown = function(e) {
        e.stopPropagation();
      };
      
      // Fix parent containers
      let parent = dropdown.parentElement;
      for (let i = 0; i < 3 && parent; i++) { // Limit to 3 levels up
        parent.style.pointerEvents = 'auto';
        parent.style.position = 'relative';
        parent.style.zIndex = '999';
        parent = parent.parentElement;
      }
    });
    
    // Fix dropdown menus
    const dropdownMenus = document.querySelectorAll('.dropdown-menu, .select-dropdown, .options-list');
    dropdownMenus.forEach(menu => {
      menu.style.position = 'absolute';
      menu.style.zIndex = '1001';
      menu.style.pointerEvents = 'auto';
    });
    
    // Disable pointer events on potential overlays
    const overlays = document.querySelectorAll('.overlay, .modal-overlay, .backdrop, .background-layer, .animated-background');
    overlays.forEach(overlay => {
      overlay.style.pointerEvents = 'none';
    });
  }
  
  // Run the fix immediately
  fixInteractions();
  
  // Run the fix when the DOM is fully loaded
  document.addEventListener('DOMContentLoaded', fixInteractions);
  
  // Run the fix periodically to catch dynamically added elements
  setInterval(fixInteractions, 1000);
  
  // Add a global click handler to help with debugging
  document.addEventListener('click', function(e) {
    console.log('Click at coordinates:', e.clientX, e.clientY);
    console.log('Element clicked:', e.target);
    
    // Check if we're clicking near the firmUrl input
    const firmUrlInput = document.getElementById('firmUrl');
    if (firmUrlInput) {
      const rect = firmUrlInput.getBoundingClientRect();
      const isNearInput = (
        e.clientX >= rect.left - 10 &&
        e.clientX <= rect.right + 10 &&
        e.clientY >= rect.top - 10 &&
        e.clientY <= rect.bottom + 10
      );
      
      if (isNearInput) {
        console.log('Click near firmUrl input, forcing focus');
        setTimeout(() => firmUrlInput.focus(), 0);
      }
    }
  });
})();
