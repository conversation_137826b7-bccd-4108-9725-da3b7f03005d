/**
 * Attorney Creation Fix
 *
 * This script ensures that an attorney record exists for the authenticated user.
 * If no attorney record is found, it creates one using the user's ID.
 */

(function() {
  console.log('[AttorneyCreationFix] Initializing attorney creation fix');

  // Function to get user data from Supabase auth
  const getUserFromAuth = () => {
    try {
      const authData = localStorage.getItem('supabase.auth.token');
      if (authData) {
        const parsedData = JSON.parse(authData);
        return parsedData?.currentSession?.user;
      }
    } catch (error) {
      console.error('[AttorneyCreationFix] Error getting user from auth:', error);
    }
    return null;
  };

  // Function to get attorney data from localStorage
  const getStoredAttorney = () => {
    try {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        return JSON.parse(storedAttorney);
      }
    } catch (error) {
      console.error('[AttorneyCreationFix] Error parsing stored attorney:', error);
    }
    return null;
  };

  // Function to store attorney data in localStorage
  const storeAttorney = (attorney) => {
    if (attorney) {
      try {
        localStorage.setItem('attorney', JSON.stringify(attorney));
        console.log('[AttorneyCreationFix] Stored attorney in localStorage:', attorney);
      } catch (error) {
        console.error('[AttorneyCreationFix] Error storing attorney:', error);
      }
    }
  };

  // Function to create an attorney record in Supabase
  const createAttorneyInSupabase = async (user) => {
    if (!user || !user.id) {
      console.error('[AttorneyCreationFix] Cannot create attorney: No user ID');
      return null;
    }

    try {
      console.log('[AttorneyCreationFix] Creating attorney record for user:', user.id);

      // Create attorney data
      const attorneyData = {
        id: user.id,
        user_id: user.id,
        name: user.user_metadata?.name || user.email?.split('@')[0] || 'New Attorney',
        email: user.email || '',
        firm_name: `${user.user_metadata?.name || user.email?.split('@')[0] || 'New'}'s Law Firm`,
        welcome_message: "Hello! I'm Scout, your legal assistant. How can I help you today?",
        information_gathering: "Tell me about your situation, and I'll help find the right solution for you.",
        vapi_instructions: "You are a legal assistant. Help the user with their legal questions and concerns.",
        primary_color: '#4B74AA',
        secondary_color: '#2C3E50',
        background_color: '#1a1a1a',
        voice_provider: 'playht',
        voice_id: 'ranger'
      };

      // Get Supabase URL and key from meta tags
      const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.content;
      const supabaseKey = document.querySelector('meta[name="supabase-anon-key"]')?.content;

      if (!supabaseUrl || !supabaseKey) {
        console.error('[AttorneyCreationFix] Supabase URL or key not found in meta tags');
        return null;
      }

      // Make direct API call to create attorney
      const response = await fetch(`${supabaseUrl}/rest/v1/attorneys`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`
        },
        body: JSON.stringify(attorneyData)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[AttorneyCreationFix] Error creating attorney:', errorText);
        return null;
      }

      console.log('[AttorneyCreationFix] Attorney created successfully');
      return attorneyData;
    } catch (error) {
      console.error('[AttorneyCreationFix] Error creating attorney:', error);
      return null;
    }
  };

  // Function to check if attorney exists and create if needed
  const ensureAttorneyExists = async () => {
    const user = getUserFromAuth();
    if (!user || !user.id) {
      console.log('[AttorneyCreationFix] No authenticated user found');
      return;
    }

    console.log('[AttorneyCreationFix] Checking for attorney record for user:', user.id);

    // Get Supabase URL and key from meta tags
    const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.content;
    const supabaseKey = document.querySelector('meta[name="supabase-anon-key"]')?.content;

    if (!supabaseUrl || !supabaseKey) {
      console.error('[AttorneyCreationFix] Supabase URL or key not found in meta tags');
      return;
    }

    try {
      // First check if attorney exists with ID matching user ID
      let response = await fetch(`${supabaseUrl}/rest/v1/attorneys?select=*&id=eq.${user.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`
        }
      });

      if (!response.ok) {
        console.error('[AttorneyCreationFix] Error checking for attorney by ID:', await response.text());
        return;
      }

      let attorneys = await response.json();

      if (attorneys && attorneys.length > 0) {
        console.log('[AttorneyCreationFix] Attorney record found with matching ID:', attorneys[0]);

        // Store in localStorage
        storeAttorney(attorneys[0]);
        return attorneys[0];
      }

      // If not found by ID, check by user_id
      console.log('[AttorneyCreationFix] No attorney with matching ID found, checking by user_id');

      response = await fetch(`${supabaseUrl}/rest/v1/attorneys?select=*&user_id=eq.${user.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`
        }
      });

      if (!response.ok) {
        console.error('[AttorneyCreationFix] Error checking for attorney by user_id:', await response.text());
        return;
      }

      attorneys = await response.json();

      if (attorneys && attorneys.length > 0) {
        console.log('[AttorneyCreationFix] Attorney records found by user_id:', attorneys.length);

        // Check if any of the attorneys have an ID matching the user ID
        const matchingAttorney = attorneys.find(attorney => attorney.id === user.id);

        if (matchingAttorney) {
          console.log('[AttorneyCreationFix] Found attorney with ID matching user ID:', matchingAttorney);

          // Store in localStorage
          storeAttorney(matchingAttorney);
          return matchingAttorney;
        }

        // If no matching attorney, use the most recently updated one
        const mostRecentAttorney = attorneys.sort((a, b) => {
          const dateA = new Date(a.updated_at || a.created_at);
          const dateB = new Date(b.updated_at || b.created_at);
          return dateB - dateA;
        })[0];

        console.log('[AttorneyCreationFix] Using most recent attorney record:', mostRecentAttorney);

        // Check if we need to create a record with matching ID
        if (mostRecentAttorney.id !== user.id) {
          console.log('[AttorneyCreationFix] Attorney ID does not match user ID, creating new record with matching ID');

          // Create a new attorney record with the user's ID
          const attorneyData = {
            ...mostRecentAttorney,
            id: user.id
          };

          // Remove any fields that might cause issues
          delete attorneyData.created_at;
          delete attorneyData.updated_at;

          // Create the new record
          const createResponse = await fetch(`${supabaseUrl}/rest/v1/attorneys`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': supabaseKey,
              'Authorization': `Bearer ${supabaseKey}`
            },
            body: JSON.stringify(attorneyData)
          });

          if (!createResponse.ok) {
            console.error('[AttorneyCreationFix] Error creating attorney with matching ID:', await createResponse.text());

            // Still use the existing attorney record
            storeAttorney(mostRecentAttorney);
            return mostRecentAttorney;
          }

          const newAttorney = await createResponse.json();
          console.log('[AttorneyCreationFix] Created new attorney record with matching ID:', newAttorney);

          // Store in localStorage
          storeAttorney(newAttorney);
          return newAttorney;
        }

        // Store in localStorage
        storeAttorney(mostRecentAttorney);
        return mostRecentAttorney;
      }

      console.log('[AttorneyCreationFix] No attorney record found, creating one');

      // Create attorney record
      const newAttorney = await createAttorneyInSupabase(user);

      if (newAttorney) {
        // Store in localStorage
        storeAttorney(newAttorney);
        return newAttorney;
      }
    } catch (error) {
      console.error('[AttorneyCreationFix] Error ensuring attorney exists:', error);
    }

    // If we get here, we couldn't create an attorney in Supabase
    // Create a local fallback
    const storedAttorney = getStoredAttorney();
    if (!storedAttorney || !storedAttorney.id) {
      const fallbackAttorney = {
        id: user.id,
        user_id: user.id,
        name: user.user_metadata?.name || user.email?.split('@')[0] || 'New Attorney',
        email: user.email || '',
        firm_name: `${user.user_metadata?.name || user.email?.split('@')[0] || 'New'}'s Law Firm`,
        welcome_message: "Hello! I'm Scout, your legal assistant. How can I help you today?",
        information_gathering: "Tell me about your situation, and I'll help find the right solution for you.",
        vapi_instructions: "You are a legal assistant. Help the user with their legal questions and concerns.",
        primary_color: '#4B74AA',
        secondary_color: '#2C3E50',
        background_color: '#1a1a1a',
        voice_provider: 'playht',
        voice_id: 'ranger',
        fallback: true
      };

      storeAttorney(fallbackAttorney);
      console.log('[AttorneyCreationFix] Created fallback attorney in localStorage');
    }
  };

  // Patch the fetch function to intercept attorney queries
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    // Check if this is a request to get an attorney
    if (typeof url === 'string' && url.includes('/rest/v1/attorneys')) {
      // Check if this is a query for an attorney by ID
      const idMatch = url.match(/id=eq\.([0-9a-f-]+)/i);

      // If this is a GET request for an attorney by ID
      if (idMatch && (!options.method || options.method === 'GET')) {
        const requestedId = idMatch[1];
        const user = getUserFromAuth();

        // If the requested ID matches the user ID, make sure it exists
        if (user && user.id === requestedId) {
          console.log(`[AttorneyCreationFix] Intercepted request for attorney with ID matching user ID: ${requestedId}`);

          // Call the original fetch
          return originalFetch(url, options)
            .then(async response => {
              // If the response is ok and not empty, return it
              if (response.ok) {
                const clonedResponse = response.clone();
                try {
                  const data = await clonedResponse.json();

                  // If we got data, return the original response
                  if (data && (Array.isArray(data) ? data.length > 0 : data !== null)) {
                    return response;
                  }
                } catch (error) {
                  // If we can't parse the response, assume it's empty
                  console.error('[AttorneyCreationFix] Error parsing response:', error);
                }
              }

              // If we get here, the attorney wasn't found or the response was empty
              console.log('[AttorneyCreationFix] Attorney with matching ID not found, checking by user_id');

              // Check if there's an attorney with matching user_id
              const supabaseUrl = document.querySelector('meta[name="supabase-url"]')?.content;
              const supabaseKey = document.querySelector('meta[name="supabase-anon-key"]')?.content;

              if (!supabaseUrl || !supabaseKey) {
                console.error('[AttorneyCreationFix] Supabase URL or key not found in meta tags');
                return response;
              }

              try {
                const userIdResponse = await originalFetch(`${supabaseUrl}/rest/v1/attorneys?select=*&user_id=eq.${user.id}`, {
                  headers: {
                    'Content-Type': 'application/json',
                    'apikey': supabaseKey,
                    'Authorization': `Bearer ${supabaseKey}`
                  }
                });

                if (!userIdResponse.ok) {
                  console.error('[AttorneyCreationFix] Error checking for attorney by user_id');
                  return response;
                }

                const attorneys = await userIdResponse.json();

                if (attorneys && attorneys.length > 0) {
                  console.log('[AttorneyCreationFix] Found attorney by user_id, creating a new response');

                  // Create a new response with the attorney data
                  const newResponse = new Response(JSON.stringify([attorneys[0]]), {
                    status: 200,
                    headers: {
                      'Content-Type': 'application/json'
                    }
                  });

                  return newResponse;
                }
              } catch (error) {
                console.error('[AttorneyCreationFix] Error checking for attorney by user_id:', error);
              }

              // If we get here, no attorney was found, trigger creation
              console.log('[AttorneyCreationFix] No attorney found, triggering creation');

              // Create attorney in background
              ensureAttorneyExists().then(attorney => {
                if (attorney) {
                  console.log('[AttorneyCreationFix] Attorney created, refresh the page to see changes');

                  // Add a notification to the page
                  const notification = document.createElement('div');
                  notification.style.position = 'fixed';
                  notification.style.bottom = '20px';
                  notification.style.right = '20px';
                  notification.style.backgroundColor = '#4B74AA';
                  notification.style.color = 'white';
                  notification.style.padding = '15px';
                  notification.style.borderRadius = '5px';
                  notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
                  notification.style.zIndex = '9999';
                  notification.innerHTML = 'Attorney profile created. <a href="#" style="color:white;text-decoration:underline;">Refresh</a> to see changes.';

                  // Add click handler to refresh
                  notification.querySelector('a').addEventListener('click', (e) => {
                    e.preventDefault();
                    window.location.reload();
                  });

                  document.body.appendChild(notification);

                  // Remove after 10 seconds
                  setTimeout(() => {
                    if (notification.parentNode) {
                      notification.parentNode.removeChild(notification);
                    }
                  }, 10000);
                }
              });

              return response;
            });
        }
      }

      // For update operations, make sure we're using the correct ID
      if (options.method === 'PUT' || options.method === 'PATCH') {
        const user = getUserFromAuth();
        if (user && user.id) {
          // Check if the URL contains an ID
          const idMatch = url.match(/id=eq\.([0-9a-f-]+)/i);

          if (idMatch) {
            const requestedId = idMatch[1];

            // If the requested ID doesn't match the user ID, modify the URL
            if (requestedId !== user.id) {
              console.log(`[AttorneyCreationFix] Intercepted update for attorney with ID ${requestedId}, checking if user ID ${user.id} should be used instead`);

              // Check if there's an attorney with matching user ID
              const storedAttorney = getStoredAttorney();

              if (storedAttorney && storedAttorney.id === user.id) {
                console.log(`[AttorneyCreationFix] Using user ID ${user.id} for update instead of ${requestedId}`);

                // Modify the URL to use the user ID
                url = url.replace(`id=eq.${requestedId}`, `id=eq.${user.id}`);
              }
            }
          }
        }
      }
    }

    // For all other requests, call the original fetch
    return originalFetch(url, options);
  };

  // Run the check when the page loads
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', ensureAttorneyExists);
  } else {
    // Small delay to ensure auth is loaded
    setTimeout(ensureAttorneyExists, 1000);
  }

  // Also run when auth state changes
  window.addEventListener('supabase.auth.statechange', ensureAttorneyExists);

  // Add a global function to manually trigger attorney creation
  window.ensureAttorneyExists = ensureAttorneyExists;

  console.log('[AttorneyCreationFix] Attorney creation fix initialized');
})();
