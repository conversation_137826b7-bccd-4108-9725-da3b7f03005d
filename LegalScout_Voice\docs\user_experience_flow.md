# LegalScout User Experience Flow

## Overview

LegalScout is a voice-guided legal consultation platform that provides users with quick access to appropriate legal assistance. The platform uses an AI assistant (<PERSON>) to gather information and assess the urgency of the user's legal situation, then connects them with relevant attorneys when necessary.

This document outlines the complete user journey through the LegalScout platform, detailing both the voice interaction flow and UI components. It serves as a reference for understanding the core functionality and purpose of the application.

## Core User Flows

### 1. Initial Landing & Consultation Initiation

**User Interface:**
- Dark-themed interface with legal professionalism
- Prominent LegalScout logo in the header
- Navigation links for Features, About, Contact
- Hero section with statement about needing legal help
- Blue "Start Consultation" button to initiate the process

**User Actions:**
- User arrives at landing page
- User clicks "Start Consultation" button to begin

### 2. Voice Assistant Interaction (Scout)

**User Interface:**
- Call interface appears (replacing landing page)
- Call status indicator shows connection status
- Minimal controls focused on core functionality:
  - Call/End Call buttons
  - Map visibility toggle (when available)

**Voice Interaction Flow:**
- Scout introduces itself and explains the purpose
- Scout asks initial questions about the user's legal situation
- User responds via voice to provide details
- Scout continues gathering essential information:
  - Type of legal issue
  - Basic facts of the situation
  - Location information (for attorney matching)

### 3. Hidden Urgency Assessment

**Internal Processing (Not Visible to User):**
- Based on user's responses, Scout internally assesses urgency
- System determines whether immediate attorney connection is needed
- No explicit urgency questions are presented to the user

**Path Determination:**
- For non-urgent cases: Scout provides general guidance and ends call
- For urgent cases: System proceeds to attorney locator functionality

### 4. Direct Attorney Request Option

**User Control:**
- At ANY point during the interaction, user can:
  - Verbally indicate they want to speak with an attorney
  - This immediately initiates attorney search/matching
  - This overrides the normal assessment flow

**User Statement Examples:**
- "I'd like to speak with an attorney now"
- "Connect me with a lawyer"
- "I need legal representation"

### 5. Map Dossier Visualization (For Urgent Cases)

**User Interface:**
- Map view appears showing user's location 
- Dossier panel displays organized case information
- Attorney markers appear on map showing nearby legal professionals
- Attorney listing with:
  - Names and practice areas
  - Ratings/reviews
  - Contact information
- Map visibility can be toggled

**Functionality:**
- User can explore attorney options on the map
- View attorney details in the listing
- Select attorney to initiate connection

### 6. Attorney Connection

**User Interface:**
- Call status updates to show connection attempt
- Confirmation when attorney is connected
- Call controls adapt for multi-party call

**Process:**
- System initiates outbound call to selected attorney
- Bridges user and attorney for live consultation
- Scout can remain on call to assist with information

### 7. Post-Call Summary

**User Interface:**
- After call ends, a beautifully formatted call summary appears
- Summary includes key information collected during the consultation:
  - Legal issue identified
  - Relevant facts discussed
  - Location information
  - Any specific advice provided by Scout
  - Next steps based on urgency assessment

**Path-Specific Information:**
- For non-urgent cases:
  - Clear message to check email for additional resources
  - Potential for follow-up scheduling options
  - Helpful resources based on legal issue type
- For urgent cases that connected with attorneys:
  - Attorney contact information for future reference
  - Case reference number (if applicable)

**Visual Presentation:**
- Clean card-based layout with clear section headings
- Consistent styling with the main application theme
- Option to email or download summary for record-keeping

## UI Components & Controls

### Essential Controls (Current Implementation)
- **Call/End Call buttons**: Primary call control
- **Map toggle**: Show/hide map visualization
- **Map dossier controls**: Interact with map component

### Future UI Enhancements (Planned)
- Response buttons that mirror voice options
- Enhanced attorney filtering
- Call recording/transcript options
- Document sharing capabilities

## Technical Integration Points

### Voice Processing
- Vapi.ai handles voice recognition and processing
- Converts speech to actions via tool calls
- Updates UI based on conversation context

### Map & Location Services
- Uses React-Leaflet for map visualization
- Attorney markers placed based on search results
- User location determined through conversation

### Attorney Database
- Attorney results retrieved via API integration
- Practice areas matched to user's legal issue
- Contact facilitated through outbound call service

## Design Principles

1. **Voice-First Interaction**: Primary interaction is through natural conversation
2. **Progressive Disclosure**: Information and options revealed as needed
3. **Essential Controls**: UI limited to necessary functionality
4. **Seamless Transitions**: Smooth flow between conversation stages
5. **Urgent Case Prioritization**: Quick path to attorney connection for urgent matters

## Future Enhancements

- Convex push notifications for status updates
- Enhanced attorney filtering options
- Document upload/sharing capabilities
- Follow-up scheduling
- Case tracking dashboard

## Technical Flow with Make.com Integration

The LegalScout platform utilizes Make.com webhooks to enhance the user experience through automated workflows and data processing. This section outlines the technical flow of data through the application, highlighting the integration points.

### Data Flow Architecture

1. **User Voice Input → Vapi.ai → Tool Calls → Make.com → Application Updates**

   User voice interactions are processed by Vapi.ai, which triggers tool calls with structured data. This data is sent to Make.com webhooks for processing, then results are reflected in the application UI.

### Key Webhook Integration Points

#### 1. Live Dossier Updates (LIVE_DOSSIER Tool)
- **Tool ID**: `4a0d63cf-0b84-4eec-bddf-9c5869439d7e`
- **Webhook URL**: `https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1`
- **Technical Flow**:
  1. User provides case information verbally to Scout
  2. Vapi processes speech and identifies key information
  3. Vapi triggers the live_dossier tool with structured data
  4. Data is sent to Make.com webhook in emoji-prefixed format:
     ```json
     {
       "🚨Urgency": "High/Medium/Low",
       "Case Dossier": {
         "📁 Client Background": "Client information",
         "🔄 STATUS": "Case status",
         "📍 Jurisdiction": "Location information",
         "📝 Statement of Facts": "Case facts",
         "⚖️ Legal Issues": "Legal issues",
         "🎯 Objectives": "Client goals"
       }
     }
     ```
  5. Make.com processes the data (enrichment, validation, transformation)
  6. Application receives updated information for the UI
  7. Map visualization and dossier panels are updated with new information

#### 2. Attorney Matching (CHECK_MATCHING_ATTORNEYS Tool)
- **Tool ID**: `313b71f6-f4ea-44d4-a304-f5050e890693`
- **Webhook URL**: `https://hook.us1.make.com/4empo1t13htixhbkz7bs9ettvxs8g0j5`
- **Technical Flow**:
  1. Scout identifies the case as urgent or user explicitly requests an attorney
  2. System triggers the CHECK_MATCHING_ATTORNEYS tool
  3. Tool data sent to Make.com webhook with practice area and jurisdiction
  4. Make.com performs attorney matching algorithms
  5. Results are returned to application and displayed on map
  6. Attorney list is populated with matched professionals
  7. Map markers are placed at attorney locations

#### 3. Local Attorney Search (LOCAL_ATTORNEY_SEARCH Tool)
- **Tool ID**: `42375b0b-9c0b-481b-b445-d219b41f1988`
- **Webhook URL**: `https://hook.us1.make.com/g6k2w1raan6ovyodlvfoevdtuuw09hb6`
- **Technical Flow**:
  1. User requires location-specific attorney search
  2. System triggers LOCAL_ATTORNEY_SEARCH tool
  3. Location and practice area information sent to Make.com webhook
  4. Make.com performs geo-based filtering and searching
  5. Results returned to application UI
  6. Map and attorney listings updated with location-specific results

#### 4. User Information Collection (GET_USER_INFO Tool)
- **Tool ID**: `40e60896-518c-470a-a713-7abc2cd0c924`
- **Webhook URL**: `https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1`
- **Technical Flow**:
  1. Near end of consultation, system collects comprehensive user information
  2. GET_USER_INFO tool is triggered with collected data
  3. Information sent to Make.com webhook for processing
  4. Make.com creates CRM entries, lead records, or triggers notifications
  5. Application displays appropriate post-call UI with next steps
  6. Follow-up communications are triggered based on user information

### Make.com Scenario Functions

Behind each webhook, Make.com scenarios perform various functions:

1. **Data Enrichment & Validation**
   - Verifying addresses and normalizing location data
   - Enhancing practice area categorization
   - Validating contact information

2. **CRM Integration**
   - Creating new lead records in CRM systems
   - Updating existing client records
   - Triggering follow-up tasks for sales team

3. **Notification Systems**
   - Alerting available attorneys about new consultation requests
   - Sending SMS/email notifications to staff
   - Triggering push notifications via mobile apps

4. **Analytics & Reporting**
   - Recording consultation metrics
   - Tracking conversion rates
   - Generating automated reports

### Technical Benefits

This integration architecture provides several key benefits:

1. **Real-time Processing**: Information is processed as it's collected during the call
2. **Scalability**: Processing is offloaded to Make.com's infrastructure
3. **Flexibility**: Make.com scenarios can be modified without code changes
4. **Integration Capabilities**: Easy connection to hundreds of external services
5. **Error Handling**: Robust retry mechanisms for failed requests
6. **Logging**: Comprehensive logging of all data flows for debugging

### Response Button Mechanism

When the conversation reaches appropriate decision points, the Make.com integration enables:

1. Voice assistant suggests possible user responses
2. Application displays these as clickable buttons in the UI
3. User can either speak their response or click a button
4. Selected response is fed back into the Vapi conversation
5. Conversation continues with the chosen path

This hybrid interaction model combines the natural feel of voice conversation with the convenience of button selection for key decision points.

---

This reference document provides a comprehensive overview of the LegalScout user experience journey. It serves as a guide for understanding the platform's purpose and functionality during development. 