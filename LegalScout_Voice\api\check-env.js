/**
 * Environment Variables Check
 * Shows what environment variables are available in production
 */

export default function handler(req, res) {
  const envVars = {
    SUPABASE_URL: process.env.SUPABASE_URL ? 'Set' : 'Missing',
    VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL ? 'Set' : 'Missing',
    SUPABASE_ANON_KEY: process.env.SUPABASE_ANON_KEY ? 'Set' : 'Missing',
    VITE_SUPABASE_KEY: process.env.VITE_SUPABASE_KEY ? 'Set' : 'Missing',
    SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing',
    VAPI_TOKEN: process.env.VAPI_TOKEN ? 'Set' : 'Missing',
    VAPI_SECRET_KEY: process.env.VAPI_SECRET_KEY ? 'Set' : 'Missing',
    VAPI_WEBHOOK_SECRET: process.env.VAPI_WEBHOOK_SECRET ? 'Set' : 'Missing',
    NODE_ENV: process.env.NODE_ENV || 'Not set'
  };

  res.status(200).json({
    message: 'Environment check',
    timestamp: new Date().toISOString(),
    environment: envVars
  });
}
