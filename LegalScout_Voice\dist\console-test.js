/**
 * Console Test for Assistant Propagation
 * Copy and paste this into your browser console while on the dashboard
 */

// Assistant Propagation Console Test
window.testAssistantPropagation = async function() {
    console.log('🧪 Assistant Propagation Console Test');
    console.log('====================================');
    
    const results = [];
    
    function logResult(test, success, message) {
        const result = { test, success, message };
        results.push(result);
        const emoji = success ? '✅' : '❌';
        console.log(`${emoji} ${test}: ${message}`);
        return result;
    }
    
    // Test 1: Check if Vapi services are available
    console.log('\n🔍 Test 1: Service Availability');
    
    const services = [];
    if (typeof window.vapiAssistantService !== 'undefined') services.push('vapiAssistantService');
    if (typeof window.vapiMcpService !== 'undefined') services.push('vapiMcpService');
    if (typeof window.useAssistantStore !== 'undefined') services.push('useAssistantStore');
    
    logResult('Service Availability', services.length > 0, `Found: ${services.join(', ')}`);
    
    if (services.length === 0) {
        console.log('❌ No services available. Make sure you\'re on the dashboard page.');
        return results;
    }
    
    // Test 2: Load assistants
    console.log('\n🔍 Test 2: Load Assistants');
    
    try {
        let assistants = [];
        
        if (window.vapiAssistantService && window.vapiAssistantService.getAllAssistants) {
            assistants = await window.vapiAssistantService.getAllAssistants();
            logResult('Load Assistants', assistants.length > 0, `Loaded ${assistants.length} assistants`);
            
            // Log first few assistants
            assistants.slice(0, 3).forEach((assistant, index) => {
                console.log(`   ${index + 1}. ${assistant.name} (${assistant.id.substring(0, 8)}...)`);
            });
        } else {
            logResult('Load Assistants', false, 'vapiAssistantService.getAllAssistants not available');
        }
        
        // Test 3: Get specific assistant
        if (assistants.length > 0) {
            console.log('\n🔍 Test 3: Get Specific Assistant');
            
            const firstAssistant = assistants[0];
            
            if (window.vapiMcpService && window.vapiMcpService.getAssistant) {
                try {
                    const assistant = await window.vapiMcpService.getAssistant(firstAssistant.id);
                    logResult('Get Assistant', true, `Retrieved ${assistant.name}`);
                    console.log(`   Model: ${assistant.model?.provider} ${assistant.model?.model}`);
                    console.log(`   Voice: ${assistant.voice?.provider} ${assistant.voice?.voiceId}`);
                } catch (error) {
                    logResult('Get Assistant', false, `Error: ${error.message}`);
                }
            } else {
                logResult('Get Assistant', false, 'vapiMcpService.getAssistant not available');
            }
        }
        
        // Test 4: Check dropdown element
        console.log('\n🔍 Test 4: UI Elements');
        
        const dropdown = document.querySelector('select[data-testid="assistant-dropdown"], select#assistant-select, .assistant-dropdown select');
        if (dropdown) {
            const optionCount = dropdown.options.length;
            logResult('Dropdown Element', optionCount > 1, `Found dropdown with ${optionCount} options`);
            
            // Log dropdown options
            Array.from(dropdown.options).slice(0, 5).forEach((option, index) => {
                if (option.value) {
                    console.log(`   ${index}. ${option.textContent} (${option.value.substring(0, 8)}...)`);
                }
            });
        } else {
            logResult('Dropdown Element', false, 'Assistant dropdown not found in DOM');
        }
        
        // Test 5: Store state (if available)
        console.log('\n🔍 Test 5: Store State');
        
        if (window.useAssistantStore) {
            try {
                // This might need adjustment based on your store implementation
                const storeState = window.useAssistantStore.getState ? window.useAssistantStore.getState() : null;
                if (storeState) {
                    logResult('Store State', true, `Current assistant: ${storeState.currentAssistantId || 'None'}`);
                } else {
                    logResult('Store State', false, 'Could not access store state');
                }
            } catch (error) {
                logResult('Store State', false, `Store error: ${error.message}`);
            }
        } else {
            logResult('Store State', false, 'useAssistantStore not available');
        }
        
    } catch (error) {
        logResult('Test Execution', false, `Test failed: ${error.message}`);
    }
    
    // Summary
    console.log('\n📊 Test Summary');
    console.log('===============');
    
    const passCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    console.log(`🎯 ${passCount}/${totalCount} tests passed`);
    
    if (passCount === totalCount) {
        console.log('🎉 All tests passed! Assistant propagation is working correctly.');
    } else {
        console.log('⚠️  Some tests failed. Check the issues above.');
        
        // Provide specific guidance
        const failedTests = results.filter(r => !r.success);
        console.log('\n🔧 Troubleshooting:');
        failedTests.forEach(test => {
            console.log(`   • ${test.test}: ${test.message}`);
        });
    }
    
    return results;
};

// Test assistant switching
window.testAssistantSwitch = async function(fromId, toId) {
    console.log(`🔄 Testing assistant switch: ${fromId?.substring(0, 8)}... → ${toId?.substring(0, 8)}...`);
    
    try {
        if (window.vapiAssistantService && window.vapiAssistantService.switchAssistant) {
            const result = await window.vapiAssistantService.switchAssistant(toId);
            if (result.success) {
                console.log(`✅ Switch successful: ${result.assistant.name}`);
                return true;
            } else {
                console.log(`❌ Switch failed: ${result.error}`);
                return false;
            }
        } else {
            console.log('❌ switchAssistant method not available');
            return false;
        }
    } catch (error) {
        console.log(`❌ Switch error: ${error.message}`);
        return false;
    }
};

// Auto-run test when script loads
console.log('🧪 Assistant Propagation Console Test loaded!');
console.log('📝 Run: testAssistantPropagation()');
console.log('🔄 Run: testAssistantSwitch(fromId, toId)');

// Auto-run if we're on the dashboard
if (window.location.pathname.includes('dashboard')) {
    console.log('🚀 Auto-running test on dashboard...');
    setTimeout(() => {
        window.testAssistantPropagation();
    }, 2000);
}
