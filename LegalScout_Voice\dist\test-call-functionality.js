/**
 * ULTRA-THINKING Call Functionality Test
 * 
 * This script tests and validates call functionality to identify
 * why calls are terminating and provides diagnostic information.
 */

(function() {
  'use strict';

  console.log('[CallFunctionalityTest] 🧪 Starting call functionality test...');

  class CallFunctionalityTester {
    constructor() {
      this.testResults = [];
      this.callAttempts = 0;
      this.successfulCalls = 0;
      this.failedCalls = 0;
    }

    /**
     * Run comprehensive call tests
     */
    async runTests() {
      console.log('[CallFunctionalityTest] 🚀 Running comprehensive call tests...');

      const tests = [
        { name: 'Vapi SDK Availability', fn: this.testVapiSDK },
        { name: 'Assistant Configuration', fn: this.testAssistantConfig },
        { name: 'API Connectivity', fn: this.testApiConnectivity },
        { name: 'Iframe Communication', fn: this.testIframeCommunication },
        { name: 'Call Initialization', fn: this.testCallInitialization },
        { name: 'Call Stability', fn: this.testCallStability }
      ];

      for (const test of tests) {
        await this.runTest(test.name, test.fn.bind(this));
      }

      this.printResults();
    }

    /**
     * Run individual test
     */
    async runTest(name, testFn) {
      console.log(`[CallFunctionalityTest] 🔬 Testing: ${name}`);
      const startTime = Date.now();

      try {
        const result = await testFn();
        const duration = Date.now() - startTime;
        
        this.testResults.push({
          name,
          status: 'PASS',
          duration,
          result
        });
        
        console.log(`[CallFunctionalityTest] ✅ ${name}: PASS (${duration}ms)`);
        
      } catch (error) {
        const duration = Date.now() - startTime;
        
        this.testResults.push({
          name,
          status: 'FAIL',
          duration,
          error: error.message
        });
        
        console.log(`[CallFunctionalityTest] ❌ ${name}: FAIL (${duration}ms) - ${error.message}`);
      }
    }

    /**
     * Test Vapi SDK availability and configuration
     */
    async testVapiSDK() {
      // Check if Vapi SDK is loaded
      if (!window.vapi) {
        throw new Error('Vapi SDK not loaded');
      }

      // Check if Vapi is properly configured
      const publicKey = window.vapi.publicKey || 
                       document.querySelector('meta[name="vapi-public-key"]')?.content ||
                       '310f0d43-27c2-47a5-a76d-e55171d024f7';

      if (!publicKey) {
        throw new Error('Vapi public key not configured');
      }

      return {
        sdkLoaded: true,
        publicKey: publicKey.substring(0, 8) + '...',
        methods: Object.keys(window.vapi).length
      };
    }

    /**
     * Test assistant configuration
     */
    async testAssistantConfig() {
      const assistantId = this.getCurrentAssistantId();
      
      if (!assistantId) {
        throw new Error('No assistant ID configured');
      }

      // Validate assistant exists in Vapi
      try {
        const response = await fetch('/api/optimized-sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'validate_assistant',
            data: { assistantId }
          })
        });

        if (!response.ok) {
          throw new Error(`Assistant validation failed: ${response.status}`);
        }

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(`Assistant validation error: ${result.error}`);
        }

        return {
          assistantId: assistantId.substring(0, 8) + '...',
          valid: result.data.valid,
          validated: true
        };

      } catch (error) {
        // Fallback: direct Vapi API check
        const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
        const directResponse = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });

        return {
          assistantId: assistantId.substring(0, 8) + '...',
          valid: directResponse.ok,
          validated: true,
          method: 'direct'
        };
      }
    }

    /**
     * Test API connectivity
     */
    async testApiConnectivity() {
      const tests = [
        { name: 'Optimized Sync', url: '/api/optimized-sync' },
        { name: 'Supabase', url: 'https://utopqxsvudgrtiwenlzl.supabase.co/rest/v1/' },
        { name: 'Vapi API', url: 'https://api.vapi.ai/assistant' }
      ];

      const results = {};

      for (const test of tests) {
        try {
          const startTime = Date.now();
          
          let response;
          if (test.name === 'Optimized Sync') {
            response = await fetch(test.url, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                action: 'get_sync_status',
                data: {}
              })
            });
          } else if (test.name === 'Supabase') {
            response = await fetch(test.url + 'attorneys?limit=1', {
              headers: {
                'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU'
              }
            });
          } else {
            response = await fetch(test.url, {
              headers: {
                'Authorization': 'Bearer 6734febc-fc65-4669-93b0-929b31ff6564'
              }
            });
          }
          
          const duration = Date.now() - startTime;
          
          results[test.name] = {
            status: response.ok ? 'OK' : 'FAIL',
            statusCode: response.status,
            duration
          };
          
        } catch (error) {
          results[test.name] = {
            status: 'ERROR',
            error: error.message
          };
        }
      }

      return results;
    }

    /**
     * Test iframe communication
     */
    async testIframeCommunication() {
      const iframes = document.querySelectorAll('iframe');
      
      if (iframes.length === 0) {
        throw new Error('No iframes found for communication test');
      }

      const results = {
        iframeCount: iframes.length,
        accessible: 0,
        responsive: 0
      };

      for (const iframe of iframes) {
        try {
          // Check if iframe is accessible
          if (iframe.contentWindow) {
            results.accessible++;
            
            // Test message sending
            iframe.contentWindow.postMessage({
              type: 'test-message',
              timestamp: Date.now()
            }, '*');
            
            results.responsive++;
          }
        } catch (error) {
          // Iframe not accessible (cross-origin)
        }
      }

      return results;
    }

    /**
     * Test call initialization
     */
    async testCallInitialization() {
      if (!window.vapi) {
        throw new Error('Vapi SDK not available for call test');
      }

      const assistantId = this.getCurrentAssistantId();
      
      if (!assistantId) {
        throw new Error('No assistant ID for call test');
      }

      // Test call configuration
      const callConfig = {
        assistantId: assistantId,
        // Add minimal config for testing
      };

      try {
        // Don't actually start a call, just validate the configuration
        if (typeof window.vapi.start === 'function') {
          return {
            configValid: true,
            assistantId: assistantId.substring(0, 8) + '...',
            vapiReady: true
          };
        } else {
          throw new Error('Vapi start method not available');
        }
      } catch (error) {
        throw new Error(`Call initialization failed: ${error.message}`);
      }
    }

    /**
     * Test call stability (mock test)
     */
    async testCallStability() {
      // This is a mock test since we don't want to actually make calls
      const stabilityFactors = {
        apiConnectivity: this.testResults.find(r => r.name === 'API Connectivity')?.status === 'PASS',
        assistantValid: this.testResults.find(r => r.name === 'Assistant Configuration')?.status === 'PASS',
        vapiSDK: this.testResults.find(r => r.name === 'Vapi SDK Availability')?.status === 'PASS',
        iframeCommunication: this.testResults.find(r => r.name === 'Iframe Communication')?.status === 'PASS'
      };

      const stableFactors = Object.values(stabilityFactors).filter(Boolean).length;
      const totalFactors = Object.keys(stabilityFactors).length;
      const stabilityScore = (stableFactors / totalFactors) * 100;

      return {
        stabilityScore: Math.round(stabilityScore),
        factors: stabilityFactors,
        recommendation: stabilityScore >= 75 ? 'Calls should be stable' : 'Calls may be unstable'
      };
    }

    /**
     * Get current assistant ID
     */
    getCurrentAssistantId() {
      // Try multiple sources
      const sources = [
        () => window.attorney?.vapi_assistant_id,
        () => JSON.parse(localStorage.getItem('attorney') || '{}').vapi_assistant_id,
        () => document.querySelector('[data-assistant-id]')?.dataset.assistantId,
        () => 'f9b97d13-f9c4-40af-a660-62ba5925ff2a' // Your known assistant ID
      ];

      for (const source of sources) {
        try {
          const id = source();
          if (id && id !== 'undefined') {
            return id;
          }
        } catch (error) {
          // Continue to next source
        }
      }

      return null;
    }

    /**
     * Print test results
     */
    printResults() {
      console.log('\n' + '='.repeat(60));
      console.log('📊 CALL FUNCTIONALITY TEST RESULTS');
      console.log('='.repeat(60));

      const passed = this.testResults.filter(r => r.status === 'PASS').length;
      const failed = this.testResults.filter(r => r.status === 'FAIL').length;
      const totalTime = this.testResults.reduce((sum, r) => sum + r.duration, 0);

      console.log(`✅ Passed: ${passed}`);
      console.log(`❌ Failed: ${failed}`);
      console.log(`⏱️  Total Time: ${totalTime}ms`);
      console.log(`📈 Success Rate: ${Math.round((passed / this.testResults.length) * 100)}%`);

      if (failed > 0) {
        console.log('\n❌ Failed Tests:');
        this.testResults
          .filter(r => r.status === 'FAIL')
          .forEach(r => {
            console.log(`   - ${r.name}: ${r.error}`);
          });
      }

      // Recommendations
      console.log('\n💡 Recommendations:');
      
      const stabilityTest = this.testResults.find(r => r.name === 'Call Stability');
      if (stabilityTest && stabilityTest.result) {
        console.log(`   📊 Call Stability Score: ${stabilityTest.result.stabilityScore}%`);
        console.log(`   💬 ${stabilityTest.result.recommendation}`);
      }

      if (failed === 0) {
        console.log('   🎉 All tests passed! Calls should work properly.');
      } else {
        console.log('   🔧 Fix the failed tests to improve call stability.');
      }

      console.log('\n🔧 To run manual call test: CallFunctionalityTester.testManualCall()');
    }

    /**
     * Manual call test (for debugging)
     */
    testManualCall() {
      console.log('[CallFunctionalityTest] 🎯 Starting manual call test...');
      
      if (!window.vapi) {
        console.error('❌ Vapi SDK not available');
        return;
      }

      const assistantId = this.getCurrentAssistantId();
      
      if (!assistantId) {
        console.error('❌ No assistant ID available');
        return;
      }

      console.log(`📞 Attempting call with assistant: ${assistantId.substring(0, 8)}...`);
      
      // Add call event listeners
      window.vapi.on('call-start', () => {
        console.log('✅ Call started successfully');
        this.successfulCalls++;
      });

      window.vapi.on('call-end', () => {
        console.log('📞 Call ended');
      });

      window.vapi.on('error', (error) => {
        console.error('❌ Call error:', error);
        this.failedCalls++;
      });

      // Start the call
      try {
        window.vapi.start(assistantId);
        this.callAttempts++;
        console.log('🚀 Call initiated');
      } catch (error) {
        console.error('❌ Failed to start call:', error);
        this.failedCalls++;
      }
    }
  }

  // Create global instance
  window.CallFunctionalityTester = new CallFunctionalityTester();

  // Auto-run tests when script loads
  setTimeout(() => {
    window.CallFunctionalityTester.runTests();
  }, 2000);

  console.log('[CallFunctionalityTest] 📋 Call functionality test script loaded');

})();
