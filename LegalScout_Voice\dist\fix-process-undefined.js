/**
 * Fix Process Undefined Error
 * 
 * This script provides a minimal process object for browser environments
 * to prevent "process is not defined" errors.
 */

(function() {
  // Only add process if it doesn't exist
  if (typeof window !== 'undefined' && typeof window.process === 'undefined') {
    console.log('[ProcessFix] Adding process polyfill for browser environment');
    
    window.process = {
      env: {
        NODE_ENV: 'development'
      },
      browser: true,
      version: '',
      versions: {
        node: ''
      }
    };
    
    console.log('[ProcessFix] Process polyfill added successfully');
  }
})();
