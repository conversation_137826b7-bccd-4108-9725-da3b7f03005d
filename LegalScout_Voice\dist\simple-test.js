/**
 * Simple Website Import Test
 * Run this in browser console for quick testing
 */

// Test configuration
const TEST_URL = 'https://www.generalcounsel.online';

// Simple test function
async function quickTest() {
    console.log('🚀 Starting quick website import test...');
    console.log(`📍 Testing URL: ${TEST_URL}`);
    
    try {
        // Test 1: Basic API connectivity
        console.log('\n1️⃣ Testing API connectivity...');
        const optionsResponse = await fetch('/api/website-import', {
            method: 'OPTIONS'
        });
        console.log(`✅ OPTIONS request: ${optionsResponse.status}`);
        
        // Test 2: Actual import request
        console.log('\n2️⃣ Testing website import...');
        const importResponse = await fetch('/api/website-import', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ url: TEST_URL })
        });
        
        console.log(`📊 Import response status: ${importResponse.status}`);
        
        const responseText = await importResponse.text();
        console.log(`📄 Response length: ${responseText.length} characters`);
        
        if (importResponse.ok) {
            try {
                const result = JSON.parse(responseText);
                console.log('✅ Import SUCCESS!');
                console.log('📋 Extracted data:', {
                    firmName: result.data?.firmName,
                    attorneyName: result.data?.attorneyName,
                    phone: result.data?.phone,
                    email: result.data?.email,
                    practiceAreas: result.data?.practiceAreas,
                    confidence: result.data?.extractionConfidence
                });
                return result;
            } catch (parseError) {
                console.log('❌ JSON parse error:', parseError.message);
                console.log('📄 Raw response:', responseText.substring(0, 500));
            }
        } else {
            console.log('❌ Import FAILED');
            console.log('📄 Error response:', responseText);
        }
        
    } catch (error) {
        console.log('❌ Test failed:', error.message);
    }
}

// Test debug API
async function debugTest() {
    console.log('🔧 Starting debug API test...');
    
    try {
        const response = await fetch('/api/debug-website-import', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ url: TEST_URL })
        });
        
        const result = await response.text();
        console.log('🔧 Debug response:', result);
        
        if (response.ok) {
            const parsed = JSON.parse(result);
            if (parsed.debug) {
                console.log('📊 Debug info:', parsed.debug);
            }
        }
    } catch (error) {
        console.log('❌ Debug test failed:', error.message);
    }
}

// Test Jina AI directly
async function jinaTest() {
    console.log('📖 Testing Jina AI directly...');
    
    try {
        const response = await fetch(`https://r.jina.ai/${TEST_URL}`, {
            headers: {
                'Accept': 'application/json',
                'X-With-Generated-Alt': 'true'
            }
        });
        
        if (response.ok) {
            const content = await response.text();
            console.log('✅ Jina AI success!');
            console.log(`📄 Content length: ${content.length}`);
            console.log(`📄 Sample: ${content.substring(0, 300)}...`);
            return content;
        } else {
            console.log('❌ Jina AI failed:', response.status, response.statusText);
        }
    } catch (error) {
        console.log('❌ Jina AI error:', error.message);
    }
}

// Run all tests
async function runAllQuickTests() {
    console.log('🧪 Running all quick tests...');
    
    await jinaTest();
    await quickTest();
    await debugTest();
    
    console.log('✅ All tests completed!');
}

// Make functions available globally
window.quickTest = quickTest;
window.debugTest = debugTest;
window.jinaTest = jinaTest;
window.runAllQuickTests = runAllQuickTests;

console.log('🧪 Quick test functions loaded!');
console.log('📋 Available functions:');
console.log('  - quickTest() - Test the main import API');
console.log('  - debugTest() - Test the debug API');
console.log('  - jinaTest() - Test Jina AI directly');
console.log('  - runAllQuickTests() - Run all tests');
console.log('');
console.log('💡 Try: runAllQuickTests()');
