/**
 * Auto-Configure Direct Fix
 *
 * This script provides a direct way to trigger the Auto-Configure button
 * by exposing a global function that can be called from anywhere.
 */

(function() {
  console.log('[AutoConfigureDirectFix] Starting direct fix...');

  // Create a global function to trigger the Auto-Configure button
  window.triggerAutoConfigureButtonGlobal = function() {
    console.log('[AutoConfigureDirectFix] Global trigger function called');

    // Try multiple approaches to trigger the Auto-Configure button

    // 1. Try the React component's exposed function
    if (window.triggerAutoConfigureButton) {
      console.log('[AutoConfigureDirectFix] Using component triggerAutoConfigureButton function');
      try {
        window.triggerAutoConfigureButton();
        return true;
      } catch (error) {
        console.error('[AutoConfigureDirectFix] Error calling triggerAutoConfigureButton:', error);
      }
    }

    // 2. Try to find and click the Auto-Configure button directly
    const autoConfigButton = document.querySelector('button.auto-configure, button.begin-config.modern-button.auto-configure');
    if (autoConfigButton) {
      console.log('[AutoConfigureDirectFix] Found Auto-Configure button, clicking it');
      try {
        autoConfigButton.click();
        return true;
      } catch (error) {
        console.error('[AutoConfigureDirectFix] Error clicking button:', error);
      }
    }

    // 3. Try to dispatch a custom event
    console.log('[AutoConfigureDirectFix] Dispatching autoConfigureClicked event');
    try {
      // Get the URL from the input field if available
      const urlInput = document.getElementById('firmUrl') ||
                      document.querySelector('input[type="url"]') ||
                      document.querySelector('input[placeholder*="firm"]') ||
                      document.querySelector('input[placeholder*="website"]');

      const url = urlInput ? urlInput.value : window.location.href;

      const event = new CustomEvent('autoConfigureClicked', {
        detail: { url: url }
      });
      document.dispatchEvent(event);

      // Also store in localStorage as a backup
      localStorage.setItem('pendingUrlAutoConfig', url);
      localStorage.setItem('urlAutoConfigTimestamp', Date.now());

      return true;
    } catch (error) {
      console.error('[AutoConfigureDirectFix] Error dispatching event:', error);
    }

    console.log('[AutoConfigureDirectFix] All approaches failed');
    return false;
  };

  // Create a button in the corner of the screen as a last resort - only if needed
  function createEmergencyButton() {
    // Only create the emergency button if we're on a page that needs it
    // and the regular Auto-Configure button isn't found

    // Check if we're on a page that needs auto-configuration
    const isConfigPage = window.location.pathname === '/' ||
                        window.location.pathname.includes('/demo') ||
                        window.location.pathname.includes('/subdomain-test');

    if (!isConfigPage) {
      console.log('[AutoConfigureDirectFix] Not on a configuration page, skipping emergency button');
      return;
    }

    // Check if the button already exists
    if (document.getElementById('emergency-auto-configure-button')) {
      return;
    }

    // Check if the regular Auto-Configure button exists
    const regularButton = document.querySelector('button.auto-configure, button.begin-config.modern-button.auto-configure');
    if (regularButton) {
      console.log('[AutoConfigureDirectFix] Regular Auto-Configure button found, skipping emergency button');
      return;
    }

    // Check if we're in URL input mode
    const urlInput = document.getElementById('firmUrl') ||
                    document.querySelector('input[type="url"]') ||
                    document.querySelector('input[placeholder*="firm"]') ||
                    document.querySelector('input[placeholder*="website"]');

    if (!urlInput) {
      console.log('[AutoConfigureDirectFix] No URL input found, skipping emergency button');
      return;
    }

    // Create the button
    const button = document.createElement('button');
    button.id = 'emergency-auto-configure-button';
    button.textContent = 'Auto-Configure';
    button.style.position = 'fixed';
    button.style.bottom = '10px';
    button.style.right = '10px';
    button.style.zIndex = '9999';
    button.style.backgroundColor = '#4B74AA';
    button.style.color = 'white';
    button.style.padding = '10px 20px';
    button.style.borderRadius = '4px';
    button.style.border = 'none';
    button.style.fontWeight = 'bold';
    button.style.cursor = 'pointer';
    button.style.boxShadow = '0 2px 5px rgba(0,0,0,0.3)';

    // Add click handler
    button.addEventListener('click', function() {
      window.triggerAutoConfigureButtonGlobal();

      // Remove the button after it's clicked
      setTimeout(() => {
        if (button.parentNode) {
          button.parentNode.removeChild(button);
        }
      }, 500);
    });

    // Add to the document
    document.body.appendChild(button);
    console.log('[AutoConfigureDirectFix] Created emergency button');

    // Auto-remove the button after 30 seconds
    setTimeout(() => {
      if (button.parentNode) {
        button.parentNode.removeChild(button);
        console.log('[AutoConfigureDirectFix] Auto-removed emergency button after timeout');
      }
    }, 30000);
  }

  // Wait for the DOM to be fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      // Wait a bit to ensure React has mounted
      setTimeout(createEmergencyButton, 3000);
    });
  } else {
    // DOM already loaded, wait a bit to ensure React has mounted
    setTimeout(createEmergencyButton, 3000);
  }

  console.log('[AutoConfigureDirectFix] Direct fix initialized');
})();
