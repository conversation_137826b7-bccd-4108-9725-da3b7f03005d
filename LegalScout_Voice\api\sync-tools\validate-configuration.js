/**
 * API Route: /api/sync-tools/validate-configuration
 *
 * This endpoint handles validating attorney configuration data.
 */

import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_KEY;

let supabase = null;
if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Server-side implementation
const validateConfiguration = async ({ attorneyId, configData }) => {
  console.log('Server-side validateConfiguration called with:', { attorneyId, configData: !!configData });

  if (!supabase) {
    return {
      success: false,
      error: 'Supabase not configured'
    };
  }

  try {
    const errors = [];

    // Basic validation
    if (!configData.firm_name) {
      errors.push('Firm name is required');
    }

    if (!configData.email) {
      errors.push('Email is required');
    }

    // Validate attorney exists
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('id')
      .eq('id', attorneyId)
      .single();

    if (error) {
      errors.push('Attorney not found');
    }

    return {
      success: true,
      message: 'Configuration validation completed',
      attorneyId,
      isValid: errors.length === 0,
      errors
    };
  } catch (error) {
    console.error('Error validating configuration:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    const { attorneyId, configData } = req.body;

    // Validate required parameters
    if (!attorneyId || !configData) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameters: attorneyId and configData are required'
      });
    }

    // Call the validateConfiguration function
    const result = await validateConfiguration({ attorneyId, configData });

    // Return the result
    return res.status(200).json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error validating configuration:', error);

    // Return a proper error response
    return res.status(500).json({
      success: false,
      error: error.message || 'An unknown error occurred'
    });
  }
}
