/**
 * Vapi MCP Server Catch-All Route
 *
 * This serverless function provides a catch-all route for the Vapi MCP Server.
 * It forwards all requests to the main Vapi MCP Server handler.
 *
 * The Vercel MCP adapter automatically handles routing for both the main endpoint
 * and the catch-all route.
 *
 * This version includes explicit handling of OPTIONS requests for CORS preflight.
 */

import handler from './index.js';

// Handle OPTIONS requests for CORS preflight
const handleOptions = (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Return 200 for OPTIONS requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return true;
  }

  return false;
};

// Wrap the handler to handle OPTIONS requests
export default (req, res) => {
  // Handle OPTIONS requests
  if (handleOptions(req, res)) {
    return;
  }

  // Otherwise, pass to the MCP handler
  return handler(req, res);
};
