/**
 * Fix Banner Removal Persistence
 * 
 * This script fixes the issue where banner removal doesn't persist because
 * the AgentTab component keeps restoring fallback logo values.
 */

console.log('[FixBannerRemovalPersistence] Starting fix...');

// Track banner removal state
let bannerRemovalState = {
  isRemoved: false,
  timestamp: null
};

// Function to mark banner as removed
function markBannerAsRemoved() {
  bannerRemovalState.isRemoved = true;
  bannerRemovalState.timestamp = Date.now();
  
  // Store in localStorage for persistence
  localStorage.setItem('banner_removal_state', JSON.stringify(bannerRemovalState));
  
  console.log('[FixBannerRemovalPersistence] Banner marked as removed');
}

// Function to check if banner was recently removed
function wasBannerRecentlyRemoved() {
  try {
    const stored = localStorage.getItem('banner_removal_state');
    if (stored) {
      const state = JSON.parse(stored);
      // Consider it recently removed if it was within the last 30 seconds
      const isRecent = state.timestamp && (Date.now() - state.timestamp) < 30000;
      return state.isRemoved && isRecent;
    }
  } catch (error) {
    console.warn('[FixBannerRemovalPersistence] Error checking removal state:', error);
  }
  return false;
}

// Function to clear banner removal state
function clearBannerRemovalState() {
  bannerRemovalState.isRemoved = false;
  bannerRemovalState.timestamp = null;
  localStorage.removeItem('banner_removal_state');
  console.log('[FixBannerRemovalPersistence] Banner removal state cleared');
}

// Function to patch the AgentTab component's useEffect
function patchAgentTabUseEffect() {
  // Wait for React to load
  setTimeout(() => {
    try {
      // Look for the AgentTab component in the React fiber tree
      const findReactComponent = (element, componentName) => {
        if (!element || !element._reactInternalFiber && !element._reactInternals) {
          return null;
        }
        
        const fiber = element._reactInternalFiber || element._reactInternals;
        let currentFiber = fiber;
        
        while (currentFiber) {
          if (currentFiber.type && currentFiber.type.name === componentName) {
            return currentFiber;
          }
          currentFiber = currentFiber.child || currentFiber.sibling || currentFiber.return;
        }
        
        return null;
      };
      
      // Find all elements that might contain the AgentTab component
      const allElements = document.querySelectorAll('*');
      
      for (let element of allElements) {
        const agentTabFiber = findReactComponent(element, 'AgentTab');
        if (agentTabFiber && agentTabFiber.stateNode) {
          console.log('[FixBannerRemovalPersistence] Found AgentTab component, patching...');
          
          // Patch the component's state setter
          const component = agentTabFiber.stateNode;
          
          if (component.setFormData) {
            const originalSetFormData = component.setFormData;
            component.setFormData = function(updater) {
              // Check if this is trying to restore a logo after removal
              if (typeof updater === 'function') {
                const wrappedUpdater = (prev) => {
                  const result = updater(prev);
                  
                  // If banner was recently removed, don't restore logo
                  if (wasBannerRecentlyRemoved() && result.logoUrl) {
                    console.log('[FixBannerRemovalPersistence] Preventing logo restoration after removal');
                    return {
                      ...result,
                      logoUrl: ''
                    };
                  }
                  
                  return result;
                };
                return originalSetFormData.call(this, wrappedUpdater);
              } else {
                // Direct state update
                if (wasBannerRecentlyRemoved() && updater.logoUrl) {
                  console.log('[FixBannerRemovalPersistence] Preventing logo restoration after removal (direct update)');
                  updater = {
                    ...updater,
                    logoUrl: ''
                  };
                }
                return originalSetFormData.call(this, updater);
              }
            };
            
            console.log('[FixBannerRemovalPersistence] AgentTab setFormData patched');
          }
          
          break;
        }
      }
    } catch (error) {
      console.error('[FixBannerRemovalPersistence] Error patching AgentTab:', error);
    }
  }, 2000);
}

// Function to patch banner remove handlers
function patchBannerRemoveHandlers() {
  // Patch the Dashboard handleRemoveLogo function
  setTimeout(() => {
    try {
      // Look for remove logo buttons
      const removeButtons = document.querySelectorAll('.remove-logo-button, button');
      
      removeButtons.forEach(button => {
        const buttonText = (button.textContent || button.innerText || '').toLowerCase();
        
        if (buttonText.includes('remove') && (buttonText.includes('banner') || buttonText.includes('logo'))) {
          console.log('[FixBannerRemovalPersistence] Patching remove button:', buttonText);
          
          // Add our own event listener that runs first
          button.addEventListener('click', function(event) {
            console.log('[FixBannerRemovalPersistence] Banner remove button clicked, marking as removed');
            markBannerAsRemoved();
            
            // Also clear any stored logo URLs
            setTimeout(() => {
              // Clear from localStorage
              const keys = Object.keys(localStorage);
              keys.forEach(key => {
                if (key.includes('logo') || key.includes('banner')) {
                  try {
                    const value = localStorage.getItem(key);
                    if (value && (value.includes('data:image') || value.includes('blob:') || value.includes('http'))) {
                      localStorage.removeItem(key);
                      console.log('[FixBannerRemovalPersistence] Cleared stored logo:', key);
                    }
                  } catch (e) {
                    // Ignore errors
                  }
                }
              });
            }, 100);
          }, true); // Use capture phase to run before other handlers
        }
      });
    } catch (error) {
      console.error('[FixBannerRemovalPersistence] Error patching remove handlers:', error);
    }
  }, 1000);
}

// Function to patch attorney data loading
function patchAttorneyDataLoading() {
  // Patch the standalone attorney manager if available
  if (window.standaloneAttorneyManager) {
    const manager = window.standaloneAttorneyManager;
    
    // Override the validateAttorneyData method
    const originalValidateAttorneyData = manager.validateAttorneyData;
    manager.validateAttorneyData = function(attorneyData) {
      const validated = originalValidateAttorneyData.call(this, attorneyData);
      
      // If banner was recently removed, clear logo fields
      if (wasBannerRecentlyRemoved() && validated) {
        console.log('[FixBannerRemovalPersistence] Clearing logo fields from attorney data after removal');
        validated.logo_url = '';
        validated.profile_image = '';
        validated.button_image = '';
      }
      
      return validated;
    };
    
    console.log('[FixBannerRemovalPersistence] Patched standalone attorney manager');
  }
}

// Function to monitor for logo restoration attempts
function monitorLogoRestoration() {
  // Set up a mutation observer to watch for logo elements being added back
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver((mutations) => {
      if (wasBannerRecentlyRemoved()) {
        mutations.forEach((mutation) => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1) { // Element node
                // Check if this is a logo image being restored
                if (node.tagName === 'IMG' && (node.className.includes('logo') || node.alt.includes('Banner'))) {
                  console.log('[FixBannerRemovalPersistence] Detected logo restoration attempt, hiding...');
                  node.style.display = 'none';
                  // Don't remove from DOM, just hide it to avoid React conflicts
                }
                
                // Check for logo images within the added node
                const logoImages = node.querySelectorAll && node.querySelectorAll('img[class*="logo"], img[alt*="Banner"]');
                if (logoImages) {
                  logoImages.forEach(img => {
                    console.log('[FixBannerRemovalPersistence] Detected nested logo restoration attempt, hiding...');
                    img.style.display = 'none';
                    // Don't remove from DOM, just hide it to avoid React conflicts
                  });
                }
              }
            });
          }
        });
      }
    });
    
    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    console.log('[FixBannerRemovalPersistence] Logo restoration monitor set up');
  }
}

// Function to clear removal state when a new logo is uploaded
function setupLogoUploadDetection() {
  setTimeout(() => {
    const logoInputs = document.querySelectorAll('input[type="file"], input[id*="logo"]');
    
    logoInputs.forEach(input => {
      input.addEventListener('change', function(event) {
        if (event.target.files && event.target.files.length > 0) {
          console.log('[FixBannerRemovalPersistence] New logo uploaded, clearing removal state');
          clearBannerRemovalState();
        }
      });
    });
  }, 1000);
}

// Function to apply all fixes
function applyFixes() {
  try {
    patchAgentTabUseEffect();
    patchBannerRemoveHandlers();
    patchAttorneyDataLoading();
    monitorLogoRestoration();
    setupLogoUploadDetection();
    
    console.log('[FixBannerRemovalPersistence] All banner removal persistence fixes applied');
    
  } catch (error) {
    console.error('[FixBannerRemovalPersistence] Error applying fixes:', error);
  }
}

// Apply fixes immediately
applyFixes();

// Apply fixes when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFixes);
} else {
  setTimeout(applyFixes, 100);
}

// DISABLED: Reapply fixes periodically to prevent excessive polling
// setInterval(applyFixes, 5000);
console.log('[FixBannerRemovalPersistence] Periodic fixes disabled to prevent performance issues');

console.log('[FixBannerRemovalPersistence] Fix script loaded');
