/**
 * Headers Fix
 * 
 * This script fixes issues with improper headers in fetch requests
 * by patching the global fetch function to ensure proper headers.
 */

(function() {
  console.log('[HeadersFix] Starting headers fix...');
  
  // Store the original fetch function
  const originalFetch = window.fetch;
  
  // Override the fetch function to add proper headers
  window.fetch = function(url, options = {}) {
    // 💀 SKIP HEADERS FIX FOR SUPABASE TO PREVENT CONTENT-TYPE DUPLICATION
    if (url && url.includes('supabase.co')) {
      console.log(`[HeadersFix] 💀 SKIPPING headers fix for Supabase URL: ${url}`);
      return originalFetch(url, options);
    }

    // Create a new options object with the original options
    const newOptions = { ...options };
    
    // Ensure headers object exists
    newOptions.headers = newOptions.headers || {};
    
    // Convert headers to a regular object if it's a Headers instance
    if (newOptions.headers instanceof Headers) {
      const headersObj = {};
      for (const [key, value] of newOptions.headers.entries()) {
        headersObj[key] = value;
      }
      newOptions.headers = headersObj;
    }
    
    // Add Content-Type header if not present and method is POST, PUT, or PATCH
    if (newOptions.method && ['POST', 'PUT', 'PATCH'].includes(newOptions.method.toUpperCase())) {
      // Check for any existing Content-Type header (case-insensitive)
      const hasContentType = Object.keys(newOptions.headers).some(key =>
        key.toLowerCase() === 'content-type'
      );

      if (!hasContentType) {
        newOptions.headers['Content-Type'] = 'application/json';
      } else {
        // Clean up duplicate Content-Type headers
        const contentTypeKeys = Object.keys(newOptions.headers).filter(key =>
          key.toLowerCase() === 'content-type'
        );

        if (contentTypeKeys.length > 1) {
          // Keep the first one, remove duplicates
          const firstValue = newOptions.headers[contentTypeKeys[0]];
          contentTypeKeys.slice(1).forEach(key => {
            delete newOptions.headers[key];
          });
          // Ensure the remaining one has the correct value
          newOptions.headers[contentTypeKeys[0]] = 'application/json';
        }
      }
    }
    
    // Add Accept header if not present
    if (!newOptions.headers['Accept'] && !newOptions.headers['accept']) {
      newOptions.headers['Accept'] = 'application/json';
    }
    
    // Log the request for debugging
    console.log(`[HeadersFix] Fetch request to ${url} with headers:`, newOptions.headers);
    
    // Call the original fetch with the new options
    return originalFetch(url, newOptions);
  };
  
  console.log('[HeadersFix] Fetch function patched successfully');
})();
