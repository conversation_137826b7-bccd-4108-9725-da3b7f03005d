# Migration Guide: From Original to Enhanced Vapi Components

This guide provides step-by-step instructions for migrating from the original Vapi components to the enhanced versions while keeping the original components for reference.

## Overview

The enhanced Vapi components provide a more modern and consistent UI for voice interactions with Vapi assistants. They use the `use-vapi` hook for consistent functionality and are styled to match the existing UI design.

## Migration Steps

### 1. Preparation

Before migrating to the enhanced components, make sure you have:

- Tested the enhanced components in the VapiDemo page
- Backed up your current implementation
- Understood the differences between the original and enhanced components

### 2. Component Mapping

Here's a mapping of original components to their enhanced counterparts:

| Original Component | Enhanced Component |
|-------------------|-------------------|
| `VapiCall` | `EnhancedVapiCall` |
| `SpeechParticles` | `EnhancedSpeechParticles` |
| `CallController` | `EnhancedCallController` |
| `PreviewTab` | `EnhancedPreviewTab` |

### 3. Gradual Migration Approach

#### Step 1: Add the VapiDemo Page

The VapiDemo page has already been added to your routes in `App.jsx` and a link has been added to the navigation in `Navbar.jsx`. This allows you to test the enhanced components before fully migrating.

#### Step 2: Implement Side-by-Side Testing in the Dashboard

Update your dashboard to allow toggling between original and enhanced components:

```jsx
// In your dashboard component
import EnhancedPreviewTab from '../components/dashboard/EnhancedPreviewTab';
import { useState } from 'react';

// Add state to toggle between original and enhanced components
const [useEnhancedComponents, setUseEnhancedComponents] = useState(false);

// Add a toggle button
<div className="component-toggle">
  <button 
    className={`toggle-button ${useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(true)}
  >
    Use Enhanced Components
  </button>
  <button 
    className={`toggle-button ${!useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(false)}
  >
    Use Original Components
  </button>
</div>

// Conditionally render components
{useEnhancedComponents ? (
  <EnhancedPreviewTab 
    attorney={attorney}
    isDarkTheme={isDarkTheme}
    onToggleTheme={toggleTheme}
  />
) : (
  <OriginalPreviewTab 
    attorney={attorney}
    isDarkTheme={isDarkTheme}
    onToggleTheme={toggleTheme}
  />
)}
```

#### Step 3: Implement Side-by-Side Testing in the Agent Page

Similarly, update your agent page to allow toggling between original and enhanced components:

```jsx
// In your agent page component
import EnhancedVapiCall from '../components/EnhancedVapiCall';
import EnhancedCallController from '../components/call/EnhancedCallController';
import { useState } from 'react';

// Add state to toggle between original and enhanced components
const [useEnhancedComponents, setUseEnhancedComponents] = useState(false);

// Add a toggle button
<div className="component-toggle">
  <button 
    className={`toggle-button ${useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(true)}
  >
    Use Enhanced Components
  </button>
  <button 
    className={`toggle-button ${!useEnhancedComponents ? 'active' : ''}`}
    onClick={() => setUseEnhancedComponents(false)}
  >
    Use Original Components
  </button>
</div>

// Conditionally render components
{useEnhancedComponents ? (
  <EnhancedCallController 
    assistantId={attorney.vapi_assistant_id}
    onCallStart={() => setCallActive(true)}
    showTranscript={true}
    showVisualization={true}
  />
) : (
  <OriginalCallController 
    assistantId={attorney.vapi_assistant_id}
    onCallStart={() => setCallActive(true)}
  />
)}
```

#### Step 4: Test and Gather Feedback

Test both the original and enhanced components with real users and gather feedback. This will help you identify any issues or improvements needed before fully migrating.

#### Step 5: Full Migration

Once you're satisfied with the enhanced components, you can fully migrate by:

1. Removing the toggle buttons
2. Using only the enhanced components
3. Moving the original components to the `src/components/legacy` directory for reference

### 4. Prop Mapping

Here's a mapping of props between original and enhanced components:

#### VapiCall → EnhancedVapiCall

```jsx
// Original
<VapiCall 
  assistantId={attorney.vapi_assistant_id}
  onEndCall={() => setCallActive(false)}
  customInstructions={{
    firmName: attorney.firm_name,
    welcomeMessage: attorney.welcome_message,
    voiceId: attorney.voice_id,
    voiceProvider: attorney.voice_provider
  }}
/>

// Enhanced
<EnhancedVapiCall 
  assistantId={attorney.vapi_assistant_id}
  onEndCall={() => setCallActive(false)}
  customInstructions={{
    firmName: attorney.firm_name,
    welcomeMessage: attorney.welcome_message,
    voiceId: attorney.voice_id,
    voiceProvider: attorney.voice_provider
  }}
  showTranscript={true}
  showVisualization={true}
/>
```

#### CallController → EnhancedCallController

```jsx
// Original
<CallController 
  assistantId={attorney.vapi_assistant_id}
  onCallStart={() => setCallActive(true)}
/>

// Enhanced
<EnhancedCallController 
  assistantId={attorney.vapi_assistant_id}
  onCallStart={() => setCallActive(true)}
  showTranscript={true}
  showVisualization={true}
  customInstructions={{
    firmName: attorney.firm_name,
    welcomeMessage: attorney.welcome_message,
    voiceId: attorney.voice_id,
    voiceProvider: attorney.voice_provider
  }}
/>
```

#### SpeechParticles → EnhancedSpeechParticles

```jsx
// Original
<SpeechParticles className="speech-particles" />

// Enhanced
<EnhancedSpeechParticles 
  className="speech-particles"
  userColor="#4CAF50" // Green
  assistantColor="#2196F3" // Light blue
/>
```

### 5. Common Issues and Solutions

#### Issue: Component doesn't receive audio levels

**Solution:** Make sure the `use-vapi` hook is properly connected to the component. Check that the component is receiving the `volumeLevel` prop.

#### Issue: Speech visualization doesn't match user preferences

**Solution:** Adjust the `userColor` and `assistantColor` props on the `EnhancedSpeechParticles` component to match user preferences.

#### Issue: Call doesn't start or end properly

**Solution:** Check that the `assistantId` prop is correctly passed to the component and that the `onCallStart` and `onEndCall` callbacks are properly implemented.

### 6. Testing Checklist

- [ ] Test the VapiDemo page to ensure all components work as expected
- [ ] Test the dashboard with both original and enhanced components
- [ ] Test the agent page with both original and enhanced components
- [ ] Test with different attorney configurations
- [ ] Test with different user scenarios (new user, returning user, etc.)
- [ ] Test with different devices and browsers
- [ ] Test with different network conditions

### 7. Final Steps

Once you've completed the migration and testing, you can:

1. Remove the toggle buttons from the dashboard and agent page
2. Update the documentation to reflect the new components
3. Train your team on the new components
4. Monitor user feedback and make adjustments as needed

## Resources

- [Enhanced Components README](./ENHANCED_COMPONENTS_README.md)
- [Dashboard Integration Example](./DASHBOARD_INTEGRATION_EXAMPLE.md)
- [Agent Page Integration Example](./AGENT_PAGE_INTEGRATION_EXAMPLE.md)
- [Vapi Blocks](https://vapiblocks.com) - A resource for Vapi implementation
- [Speech Particles Configuration Tool](https://codepen.io/Damon-Kost/pen/KwwjmNx) - Tool for configuring speech particles
