<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Simplified Implementation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.idle { background-color: #f3f4f6; color: #374151; }
        .status.connecting { background-color: #fef3c7; color: #92400e; }
        .status.connected { background-color: #d1fae5; color: #065f46; }
        .status.ended { background-color: #f3f4f6; color: #374151; }
        .status.error { background-color: #fee2e2; color: #991b1b; }
        
        button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background-color: #2563eb; }
        button:disabled { background-color: #9ca3af; cursor: not-allowed; }
        button.danger { background-color: #dc2626; }
        button.danger:hover { background-color: #b91c1c; }
        button.secondary { background-color: #6b7280; }
        button.secondary:hover { background-color: #4b5563; }
        
        .messages {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .message {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .message.user { background-color: #dbeafe; margin-left: 20px; }
        .message.assistant { background-color: #f3f4f6; margin-right: 20px; }
        .message-role { font-size: 12px; font-weight: bold; margin-bottom: 4px; }
        
        .volume-meter {
            width: 100px;
            height: 10px;
            background-color: #e5e7eb;
            border-radius: 5px;
            overflow: hidden;
            display: inline-block;
            margin-left: 10px;
        }
        .volume-bar {
            height: 100%;
            background-color: #10b981;
            transition: width 0.1s ease;
        }
        
        .config-section {
            background-color: #f9fafb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .config-section h3 { margin-top: 0; }
        
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            margin: 5px 0;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            padding: 15px;
            border-radius: 4px;
        }
        .old-way { background-color: #fef2f2; border-left: 4px solid #ef4444; }
        .new-way { background-color: #f0fdf4; border-left: 4px solid #22c55e; }
    </style>
</head>
<body>
    <h1>🎯 Vapi Simplified Implementation Test</h1>
    <p>This page tests the new simplified Vapi implementation that follows the official Web SDK pattern.</p>

    <div class="comparison">
        <div class="comparison-item old-way">
            <h3>❌ Old Complex Way</h3>
            <ul>
                <li>1,400+ lines of orchestration code</li>
                <li>Custom SDK loaders</li>
                <li>Complex event handling</li>
                <li>Multiple fallback mechanisms</li>
                <li>Custom audio management</li>
            </ul>
        </div>
        <div class="comparison-item new-way">
            <h3>✅ New Simplified Way</h3>
            <ul>
                <li>~200 lines following official pattern</li>
                <li>Direct Vapi Web SDK import</li>
                <li>Simple event listeners</li>
                <li>Built-in error handling</li>
                <li>Native audio management</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <h2>Configuration</h2>
        <div class="config-section">
            <h3>Assistant Settings</h3>
            <label>Assistant ID:</label>
            <input type="text" id="assistantId" placeholder="Enter assistant ID or leave blank for default">
            
            <label>First Message:</label>
            <input type="text" id="firstMessage" placeholder="Custom greeting message">
            
            <label>Voice Provider:</label>
            <select id="voiceProvider">
                <option value="11labs">ElevenLabs</option>
                <option value="openai">OpenAI</option>
            </select>
            
            <label>Voice ID:</label>
            <input type="text" id="voiceId" placeholder="Voice ID (e.g., 'alloy' for OpenAI)">
        </div>
    </div>

    <div class="container">
        <h2>Call Controls</h2>
        <div id="status" class="status idle">Ready to start call</div>
        
        <div>
            <button id="startBtn" onclick="startCall()">Start Call</button>
            <button id="stopBtn" onclick="stopCall()" disabled>Stop Call</button>
            <button id="muteBtn" onclick="toggleMute()" disabled>Mute</button>
        </div>
        
        <div style="margin: 10px 0;">
            Volume: <span id="volumeText">0%</span>
            <div class="volume-meter">
                <div id="volumeBar" class="volume-bar" style="width: 0%"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Message Controls</h2>
        <div>
            <input type="text" id="messageInput" placeholder="Send a message to the assistant">
            <button onclick="sendMessage()" disabled id="sendBtn">Send Message</button>
        </div>
        
        <div>
            <input type="text" id="sayInput" placeholder="Make assistant say something">
            <button onclick="makeAssistantSay()" disabled id="sayBtn">Make Assistant Say</button>
        </div>
    </div>

    <div class="container">
        <h2>Conversation</h2>
        <div id="messages" class="messages">
            <div style="text-align: center; color: #6b7280; padding: 20px;">
                No messages yet. Start a call to see the conversation.
            </div>
        </div>
    </div>

    <div class="container">
        <h2>Debug Information</h2>
        <pre id="debugInfo" style="background: #f3f4f6; padding: 10px; border-radius: 4px; font-size: 12px;">
Waiting for call to start...
        </pre>
    </div>

    <!-- Load Vapi Web SDK using ES modules -->
    <script type="module">
        // Global variables
        let vapi = null;
        let isCallActive = false;
        let isMuted = false;
        let messages = [];

        // Configuration - Using correct values from MAKE_VAPI_WORK.md
        const CONFIG = {
            apiKey: '6734febc-fc65-4669-93b0-929b31ff6564', // Correct public API key
            defaultAssistantId: '310f0d43-27c2-47a5-a76d-e55171d024f7' // Working assistant ID
        };

        // Import Vapi using ES modules
        async function initializeVapi() {
            try {
                console.log('Loading Vapi Web SDK...');

                // Import Vapi from CDN using ES modules
                const { default: Vapi } = await import('https://esm.sh/@vapi-ai/web@latest');

                console.log('Vapi SDK loaded, initializing...');

                // Create Vapi instance using official pattern
                vapi = new Vapi(CONFIG.apiKey);
                
                // Set up event listeners using official pattern
                vapi.on('call-start', () => {
                    console.log('Call started');
                    updateStatus('connected', 'Call active');
                    isCallActive = true;
                    updateButtons();
                });

                vapi.on('call-end', () => {
                    console.log('Call ended');
                    updateStatus('ended', 'Call ended');
                    isCallActive = false;
                    updateButtons();
                });

                vapi.on('speech-start', () => {
                    console.log('Assistant started speaking');
                });

                vapi.on('speech-end', () => {
                    console.log('Assistant stopped speaking');
                });

                vapi.on('volume-level', (volume) => {
                    updateVolumeLevel(volume);
                });

                vapi.on('message', (message) => {
                    console.log('Received message:', message);
                    handleMessage(message);
                });

                vapi.on('error', (error) => {
                    console.error('Vapi error:', error);
                    updateStatus('error', `Error: ${error.message || 'Unknown error'}`);
                    isCallActive = false;
                    updateButtons();
                });

                updateStatus('idle', 'Ready to start call');
                console.log('Vapi initialized successfully');

            } catch (error) {
                console.error('Failed to initialize Vapi:', error);
                updateStatus('error', `Initialization failed: ${error.message}`);
            }
        }

        // Initialize when page loads
        window.addEventListener('load', initializeVapi);

        // Make functions available globally for onclick handlers
        window.startCall = startCall;
        window.stopCall = stopCall;
        window.toggleMute = toggleMute;
        window.sendMessage = sendMessage;
        window.makeAssistantSay = makeAssistantSay;

        function startCall() {
            try {
                console.log('Starting call...');
                updateStatus('connecting', 'Connecting...');
                
                // Get configuration from form
                const assistantId = document.getElementById('assistantId').value || CONFIG.defaultAssistantId;
                const firstMessage = document.getElementById('firstMessage').value;
                const voiceProvider = document.getElementById('voiceProvider').value;
                const voiceId = document.getElementById('voiceId').value;
                
                // Prepare assistant overrides
                const assistantOverrides = {
                    recordingEnabled: true
                };
                
                if (firstMessage) {
                    assistantOverrides.firstMessage = firstMessage;
                }
                
                if (voiceId) {
                    assistantOverrides.voice = {
                        provider: voiceProvider,
                        voiceId: voiceId
                    };
                }
                
                console.log('Starting call with assistant:', assistantId);
                console.log('Assistant overrides:', assistantOverrides);
                
                // Start call using official pattern
                vapi.start(assistantId, assistantOverrides);
                
                // Clear messages
                messages = [];
                updateMessagesDisplay();
                
            } catch (error) {
                console.error('Failed to start call:', error);
                updateStatus('error', `Failed to start: ${error.message}`);
            }
        }

        function stopCall() {
            try {
                console.log('Stopping call...');
                vapi.stop();
            } catch (error) {
                console.error('Failed to stop call:', error);
            }
        }

        function toggleMute() {
            try {
                isMuted = !isMuted;
                vapi.setMuted(isMuted);
                document.getElementById('muteBtn').textContent = isMuted ? 'Unmute' : 'Mute';
                console.log('Mute toggled:', isMuted);
            } catch (error) {
                console.error('Failed to toggle mute:', error);
            }
        }

        function sendMessage() {
            try {
                const messageInput = document.getElementById('messageInput');
                const message = messageInput.value.trim();
                
                if (!message) return;
                
                console.log('Sending message:', message);
                
                vapi.send({
                    type: 'add-message',
                    message: {
                        role: 'system',
                        content: message
                    }
                });
                
                messageInput.value = '';
                
            } catch (error) {
                console.error('Failed to send message:', error);
            }
        }

        function makeAssistantSay() {
            try {
                const sayInput = document.getElementById('sayInput');
                const message = sayInput.value.trim();
                
                if (!message) return;
                
                console.log('Making assistant say:', message);
                vapi.say(message);
                
                sayInput.value = '';
                
            } catch (error) {
                console.error('Failed to make assistant speak:', error);
            }
        }

        function handleMessage(message) {
            if (message.type === 'transcript' && message.transcriptType === 'final') {
                addMessage('user', message.transcript, 'transcript');
            } else if (message.type === 'model-output' && message.output && message.output.content) {
                addMessage('assistant', message.output.content, 'response');
            }
            
            updateDebugInfo();
        }

        function addMessage(role, content, type) {
            const messageObj = {
                id: Date.now(),
                role,
                content,
                type,
                timestamp: new Date().toISOString()
            };
            
            messages.push(messageObj);
            updateMessagesDisplay();
            console.log('Added message:', messageObj);
        }

        function updateMessagesDisplay() {
            const messagesDiv = document.getElementById('messages');
            
            if (messages.length === 0) {
                messagesDiv.innerHTML = '<div style="text-align: center; color: #6b7280; padding: 20px;">No messages yet. Start a call to see the conversation.</div>';
                return;
            }
            
            messagesDiv.innerHTML = messages.map(msg => `
                <div class="message ${msg.role}">
                    <div class="message-role">${msg.role === 'user' ? 'You' : 'Assistant'} ${msg.type ? `(${msg.type})` : ''}</div>
                    <div>${msg.content}</div>
                </div>
            `).join('');
            
            // Scroll to bottom
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function updateStatus(status, text) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${status}`;
            statusDiv.textContent = text;
        }

        function updateVolumeLevel(volume) {
            const volumeText = document.getElementById('volumeText');
            const volumeBar = document.getElementById('volumeBar');
            
            const percentage = Math.round(volume * 100);
            volumeText.textContent = `${percentage}%`;
            volumeBar.style.width = `${percentage}%`;
        }

        function updateButtons() {
            document.getElementById('startBtn').disabled = isCallActive;
            document.getElementById('stopBtn').disabled = !isCallActive;
            document.getElementById('muteBtn').disabled = !isCallActive;
            document.getElementById('sendBtn').disabled = !isCallActive;
            document.getElementById('sayBtn').disabled = !isCallActive;
        }

        function updateDebugInfo() {
            const debugInfo = {
                isCallActive,
                isMuted,
                messageCount: messages.length,
                vapiInitialized: !!vapi,
                timestamp: new Date().toISOString()
            };
            
            document.getElementById('debugInfo').textContent = JSON.stringify(debugInfo, null, 2);
        }

        // Handle Enter key in input fields
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                if (e.target.id === 'messageInput') {
                    sendMessage();
                } else if (e.target.id === 'sayInput') {
                    makeAssistantSay();
                }
            }
        });

        // Update debug info periodically
        setInterval(updateDebugInfo, 1000);
    </script>
</body>
</html>
