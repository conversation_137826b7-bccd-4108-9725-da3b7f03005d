# Background Map Feature

## Overview
The Background Map feature provides a dynamic visual representation of case information during and after Vapi voice calls. It creates a transition where the call UI minimizes as the background map and case dossier become visible, displaying relevant case information in a visually engaging format.

## Key Components

### MapDossierView Component
This React component handles the display of:
* Interactive map (using Leaflet.js)
* Case information table with animated entries
* Location pinning based on client location

### Animation Workflow
1. Call starts with normal voice UI
2. When user provides location information, the map appears behind the call UI
3. "End Call" button minimizes and moves to the top-right corner
4. Case details animate into a stylized dossier table as they are collected

### UI States
* **Initial Call State**: Standard voice UI with no map
* **Map Background State**: Map visible with call controls minimized
* **Post-Call State**: Full map view with complete case dossier and attorney matches (if applicable)

### Responsive Considerations
* Desktop: Map and dossier side-by-side
* Mobile: Map with dossier overlay that can be expanded/collapsed

## Technical Requirements

### Dependencies
* Leaflet.js for mapping
* React for component architecture
* CSS animations for transitions

### Data Structure
Case data should include:
* Location (address and coordinates)
* Case type/practice area
* Client information
* Statement of facts
* Goals/objectives

### Implementation Phases
1. Create static MapDossierView component
2. Implement map initialization with Leaflet
3. Add animations for case data population
4. Integrate with call workflow
5. Add responsive design adjustments 