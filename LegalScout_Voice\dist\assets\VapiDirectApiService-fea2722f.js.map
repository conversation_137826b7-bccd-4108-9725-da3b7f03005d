{"version": 3, "file": "VapiDirectApiService-fea2722f.js", "sources": ["../../src/services/VapiDirectApiService.js"], "sourcesContent": ["/**\n * Vapi Direct API Service\n * \n * This service provides direct API access to Vapi when MCP doesn't return complete data.\n * Used as a fallback to ensure we get all assistant configuration fields.\n */\n\nclass VapiDirectApiService {\n  constructor() {\n    this.apiUrl = 'https://api.vapi.ai';\n    this.apiKey = import.meta.env.VITE_VAPI_PRIVATE_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';\n  }\n\n  /**\n   * Get complete assistant data using direct API\n   * @param {string} assistantId - Assistant ID\n   * @returns {Promise<Object>} Complete assistant data\n   */\n  async getAssistant(assistantId) {\n    try {\n      const response = await fetch(`${this.apiUrl}/assistant/${assistantId}`, {\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json'\n        }\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n      }\n\n      const assistant = await response.json();\n      \n      console.log('🔍 [VapiDirectApiService] Retrieved complete assistant data:', {\n        id: assistant.id,\n        name: assistant.name,\n        hasFirstMessage: !!assistant.firstMessage,\n        hasInstructions: !!assistant.model?.messages?.[0]?.content,\n        voice: assistant.voice,\n        model: assistant.model?.model\n      });\n\n      return assistant;\n    } catch (error) {\n      console.error('❌ [VapiDirectApiService] Error getting assistant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update assistant using direct API\n   * @param {string} assistantId - Assistant ID\n   * @param {Object} updateData - Data to update\n   * @returns {Promise<Object>} Updated assistant data\n   */\n  async updateAssistant(assistantId, updateData) {\n    try {\n      console.log('🔧 [VapiDirectApiService] Updating assistant:', {\n        assistantId: assistantId.substring(0, 8) + '...',\n        updateData\n      });\n\n      const response = await fetch(`${this.apiUrl}/assistant/${assistantId}`, {\n        method: 'PATCH',\n        headers: {\n          'Authorization': `Bearer ${this.apiKey}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(updateData)\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        throw new Error(`HTTP ${response.status}: ${errorText}`);\n      }\n\n      const updatedAssistant = await response.json();\n      \n      console.log('✅ [VapiDirectApiService] Assistant updated successfully');\n      return updatedAssistant;\n    } catch (error) {\n      console.error('❌ [VapiDirectApiService] Error updating assistant:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get assistant with fallback to MCP if direct API fails\n   * @param {string} assistantId - Assistant ID\n   * @param {Object} mcpService - MCP service instance for fallback\n   * @returns {Promise<Object>} Assistant data\n   */\n  async getAssistantWithFallback(assistantId, mcpService = null) {\n    try {\n      // Try direct API first for complete data\n      return await this.getAssistant(assistantId);\n    } catch (error) {\n      console.warn('🔄 [VapiDirectApiService] Direct API failed, trying MCP fallback:', error.message);\n      \n      if (mcpService) {\n        try {\n          return await mcpService.getAssistant(assistantId);\n        } catch (mcpError) {\n          console.error('❌ [VapiDirectApiService] Both direct API and MCP failed:', mcpError);\n          throw new Error(`Both direct API and MCP failed: ${error.message}, ${mcpError.message}`);\n        }\n      } else {\n        throw error;\n      }\n    }\n  }\n\n  /**\n   * Merge MCP data with direct API data to get complete configuration\n   * @param {string} assistantId - Assistant ID\n   * @param {Object} mcpService - MCP service instance\n   * @returns {Promise<Object>} Complete assistant data\n   */\n  async getCompleteAssistantData(assistantId, mcpService = null) {\n    try {\n      // Get data from both sources\n      const [directData, mcpData] = await Promise.allSettled([\n        this.getAssistant(assistantId),\n        mcpService ? mcpService.getAssistant(assistantId) : Promise.resolve(null)\n      ]);\n\n      // Use direct API data as primary source (more complete)\n      if (directData.status === 'fulfilled') {\n        console.log('✅ [VapiDirectApiService] Using direct API data (complete)');\n        return directData.value;\n      }\n\n      // Fallback to MCP data if direct API fails\n      if (mcpData.status === 'fulfilled' && mcpData.value) {\n        console.log('⚠️ [VapiDirectApiService] Using MCP data (may be incomplete)');\n        return mcpData.value;\n      }\n\n      throw new Error('Both direct API and MCP failed to retrieve assistant data');\n    } catch (error) {\n      console.error('❌ [VapiDirectApiService] Error getting complete assistant data:', error);\n      throw error;\n    }\n  }\n}\n\n// Create singleton instance\nexport const vapiDirectApiService = new VapiDirectApiService();\nexport default vapiDirectApiService;\n"], "names": ["VapiDirectApiService", "assistantId", "response", "assistant", "error", "updateData", "errorText", "updatedAssistant", "mcpService", "mcpE<PERSON>r", "directData", "mcpData", "vapiDirectApiService"], "mappings": "AAOA,MAAMA,CAAqB,CACzB,aAAc,CACZ,KAAK,OAAS,sBACd,KAAK,OAAyB,GAAA,uBAAyB,sCACxD,CAOD,MAAM,aAAaC,EAAa,CAC9B,GAAI,CACF,MAAMC,EAAW,MAAM,MAAM,GAAG,KAAK,MAAM,cAAcD,CAAW,GAAI,CACtE,QAAS,CACP,cAAiB,UAAU,KAAK,MAAM,GACtC,eAAgB,kBACjB,CACT,CAAO,EAED,GAAI,CAACC,EAAS,GACZ,MAAM,IAAI,MAAM,QAAQA,EAAS,MAAM,KAAKA,EAAS,UAAU,EAAE,EAGnE,MAAMC,EAAY,MAAMD,EAAS,OAEjC,eAAQ,IAAI,+DAAgE,CAC1E,GAAIC,EAAU,GACd,KAAMA,EAAU,KAChB,gBAAiB,CAAC,CAACA,EAAU,aAC7B,gBAAiB,CAAC,CAACA,EAAU,OAAO,WAAW,CAAC,GAAG,QACnD,MAAOA,EAAU,MACjB,MAAOA,EAAU,OAAO,KAChC,CAAO,EAEMA,CACR,OAAQC,EAAO,CACd,cAAQ,MAAM,oDAAqDA,CAAK,EAClEA,CACP,CACF,CAQD,MAAM,gBAAgBH,EAAaI,EAAY,CAC7C,GAAI,CACF,QAAQ,IAAI,gDAAiD,CAC3D,YAAaJ,EAAY,UAAU,EAAG,CAAC,EAAI,MAC3C,WAAAI,CACR,CAAO,EAED,MAAMH,EAAW,MAAM,MAAM,GAAG,KAAK,MAAM,cAAcD,CAAW,GAAI,CACtE,OAAQ,QACR,QAAS,CACP,cAAiB,UAAU,KAAK,MAAM,GACtC,eAAgB,kBACjB,EACD,KAAM,KAAK,UAAUI,CAAU,CACvC,CAAO,EAED,GAAI,CAACH,EAAS,GAAI,CAChB,MAAMI,EAAY,MAAMJ,EAAS,OACjC,MAAM,IAAI,MAAM,QAAQA,EAAS,MAAM,KAAKI,CAAS,EAAE,CACxD,CAED,MAAMC,EAAmB,MAAML,EAAS,OAExC,eAAQ,IAAI,yDAAyD,EAC9DK,CACR,OAAQH,EAAO,CACd,cAAQ,MAAM,qDAAsDA,CAAK,EACnEA,CACP,CACF,CAQD,MAAM,yBAAyBH,EAAaO,EAAa,KAAM,CAC7D,GAAI,CAEF,OAAO,MAAM,KAAK,aAAaP,CAAW,CAC3C,OAAQG,EAAO,CAGd,GAFA,QAAQ,KAAK,oEAAqEA,EAAM,OAAO,EAE3FI,EACF,GAAI,CACF,OAAO,MAAMA,EAAW,aAAaP,CAAW,CACjD,OAAQQ,EAAU,CACjB,cAAQ,MAAM,2DAA4DA,CAAQ,EAC5E,IAAI,MAAM,mCAAmCL,EAAM,OAAO,KAAKK,EAAS,OAAO,EAAE,CACxF,KAED,OAAML,CAET,CACF,CAQD,MAAM,yBAAyBH,EAAaO,EAAa,KAAM,CAC7D,GAAI,CAEF,KAAM,CAACE,EAAYC,CAAO,EAAI,MAAM,QAAQ,WAAW,CACrD,KAAK,aAAaV,CAAW,EAC7BO,EAAaA,EAAW,aAAaP,CAAW,EAAI,QAAQ,QAAQ,IAAI,CAChF,CAAO,EAGD,GAAIS,EAAW,SAAW,YACxB,eAAQ,IAAI,2DAA2D,EAChEA,EAAW,MAIpB,GAAIC,EAAQ,SAAW,aAAeA,EAAQ,MAC5C,eAAQ,IAAI,8DAA8D,EACnEA,EAAQ,MAGjB,MAAM,IAAI,MAAM,2DAA2D,CAC5E,OAAQP,EAAO,CACd,cAAQ,MAAM,kEAAmEA,CAAK,EAChFA,CACP,CACF,CACH,CAGY,MAACQ,EAAuB,IAAIZ"}