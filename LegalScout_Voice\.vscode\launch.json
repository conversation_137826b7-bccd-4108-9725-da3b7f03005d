{"version": "0.2.0", "configurations": [{"name": "Attach to Chrome", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}"}, {"type": "chrome", "request": "launch", "name": "Launch LegalScout", "url": "http://localhost:5173/", "webRoot": "${workspaceFolder}", "sourceMaps": true, "sourceMapPathOverrides": {"webpack://_N_E/*": "${webRoot}/*"}, "preLaunchTask": "Start Dev Server"}, {"type": "node", "request": "launch", "name": "Launch AI Meta MCP Server", "program": "${workspaceFolder}/ai-meta-mcp-server/build/index.js", "cwd": "${workspaceFolder}"}]}