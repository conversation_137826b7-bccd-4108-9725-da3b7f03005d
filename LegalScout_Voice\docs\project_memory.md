# Project Memory

## Current Architecture

The application uses a React frontend with a Vapi integration for the AI chat functionality. The main components include:

- `App.jsx`: Central component managing global state and routing
- `SimpleDemoPage.jsx`: Configuration interface for customizing the <PERSON> assistant
- `PreviewInterface.tsx`: The actual interface shown to end users with chat functionality
- `PreviewPage.jsx`: Container for preview functionality with iframe handling

## Key Features Implemented

- **Customization System**: Comprehensive theming including colors, opacity, and animations
- **Logo Management**: Upload, preview, and removal of custom logos
- **Improved UI Controls**: Streamlined color pickers and opacity sliders
- **Preview Functionality**: Real-time preview of customized interface
- **Vapi Integration**: Functional AI assistant with voice capabilities

## State Management

The application uses <PERSON>act's useState and useEffect hooks for state management. Key state variables include:

- Visual customization (colors, opacity, animations)
- Content customization (firm name, practice areas, descriptions)
- Logo and image management
- Preview and configuration modes

## Recent Improvements

- Redesigned the color picker controls to be more space-efficient with inline labels
- Added text background color customization with a color picker
- Improved spacing and layout in the configuration panel
- Streamlined the UI for better usability
- Enhanced visual styling with modern controls

## Deployment Information

- The application is deployed to Vercel
- Production URL: legalscout.ai
- Development URL: dev.legalscout.ai
- Deployment triggered via Git pushes to main branch

## Known Technical Debt

- Need to improve error handling throughout the application
- Mobile responsiveness requires additional work
- Some components could benefit from being broken down into smaller pieces
- Testing coverage is currently limited

## Required Environment Variables

- VITE_VAPI_API_KEY: API key for Vapi service
- VITE_DEFAULT_SUBDOMAIN: Default subdomain for the application
- VITE_ENVIRONMENT: Current environment (development, staging, production) 