# Project Memory

## Current State
- Basic conversation UI implemented with Vapi integration
- Message handling for different types (transcript, model-output, conversation-update)
- Typewriter effect for assistant messages
- Map view and attorney search functionality
- Voice and text input support

## Known Issues
1. Message Display and Animation
   - Timing of animations needs adjustment
   - Message chunking is too short
   - Unnecessary background/border elements on typed text
   - Scroll behavior needs improvement
   - Need to implement hover effect for conversation history

2. UI Elements
   - End call button not visible in top right during call
   - MapDossierView ref warning in React

3. Message Handling
   - Multiple duplicate messages being logged
   - Timing issues with assistant responses
   - Need to consolidate model-output chunks

4. Deployment
   - Need to set up Vercel deployment
   - Authentication system needed for preview access

## Recent Changes
- Implemented message type handling (transcript, model-output, conversation-update)
- Added typewriter effect for assistant messages
- Integrated voice and text input functionality
- Added map view for attorney locations

## Technical Debt
- React ref warning in MapDossierView component
- Message chunking optimization needed
- Styling cleanup required
- Authentication system implementation pending 