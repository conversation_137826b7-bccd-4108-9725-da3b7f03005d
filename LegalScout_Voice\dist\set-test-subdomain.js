/**
 * Set Test Subdomain Script
 * Automatically sets the correct subdomain for development testing
 */

(function() {
  'use strict';
  
  console.log('[set-test-subdomain] Script loaded');
  
  // Check if we're in development mode
  const isDevelopment = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname.includes('localhost');
  
  if (!isDevelopment) {
    console.log('[set-test-subdomain] Not in development mode, skipping');
    return;
  }
  
  // Key for localStorage
  const LOCAL_STORAGE_KEY = 'testSubdomain';
  
  // Function to get current test subdomain
  function getTestSubdomain() {
    return localStorage.getItem(LOCAL_STORAGE_KEY);
  }
  
  // Function to set test subdomain
  function setTestSubdomain(subdomain) {
    if (subdomain && subdomain !== 'default') {
      localStorage.setItem(LOCAL_STORAGE_KEY, subdomain);
      console.log('[set-test-subdomain] Set test subdomain to:', subdomain);
    } else {
      localStorage.removeItem(LOCAL_STORAGE_KEY);
      console.log('[set-test-subdomain] Cleared test subdomain');
    }
  }
  
  // Function to clear test subdomain
  function clearTestSubdomain() {
    localStorage.removeItem(LOCAL_STORAGE_KEY);
    console.log('[set-test-subdomain] Cleared test subdomain');
  }
  
  // Expose functions globally for debugging
  window.setTestSubdomain = setTestSubdomain;
  window.getTestSubdomain = getTestSubdomain;
  window.clearTestSubdomain = clearTestSubdomain;
  
  // Log current status
  const currentSubdomain = getTestSubdomain();
  if (currentSubdomain) {
    console.log('[set-test-subdomain] Current test subdomain:', currentSubdomain);
  } else {
    console.log('[set-test-subdomain] No test subdomain set (using default)');
  }
  
  // Auto-set subdomain for development if none is set
  // This helps with testing attorney-specific features
  if (!currentSubdomain && isDevelopment) {
    // You can uncomment and modify this line to auto-set a specific subdomain for testing
    // setTestSubdomain('attorney-71550');
    console.log('[set-test-subdomain] No auto-subdomain configured');
  }
  
})();
