/* SimpleVapiCall Component Styles */

.simple-vapi-call {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: rgba(14, 18, 36, 0.8);
  border-radius: 12px;
  padding: 20px;
  color: white;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* Call header with title and end call button */
.call-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 200;
}

.call-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
}

.end-call-button {
  background-color: #e74c3c;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.end-call-button:hover {
  background-color: #c0392b;
}

/* Main container for the call UI */
.call-ui-container {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20px;
}

.assistant-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.assistant-image {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 10px;
  border: 2px solid #4a90e2;
  box-shadow: 0 0 15px rgba(74, 144, 226, 0.5);
}

.assistant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.speaking-indicator {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 10px;
  background: linear-gradient(90deg, #4a90e2, #6ab0f3, #4a90e2);
  animation: pulse 1.5s infinite;
}

.assistant-status {
  font-size: 14px;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}

.connecting-status {
  color: #f39c12;
  animation: blink 1s infinite;
}

.speaking-status {
  color: #4a90e2;
}

/* Content container for messages and dossier */
.call-content {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 20px;
  position: relative;
  z-index: 5;
  max-height: 60vh;
}

/* Conversation area */
.conversation-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Dossier section */
.dossier-section {
  width: 30%;
  min-width: 250px;
  background-color: rgba(30, 40, 60, 0.6);
  border-radius: 10px;
  padding: 15px;
  position: relative;
  z-index: 150;
  height: fit-content;
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.dossier-section::-webkit-scrollbar {
  width: 8px;
}

.dossier-section::-webkit-scrollbar-track {
  background: transparent;
}

.dossier-section::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.dossier-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #fff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 8px;
}

.dossier-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dossier-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dossier-item:last-child {
  border-bottom: none;
}

.dossier-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.dossier-value {
  font-size: 16px;
  color: white;
}

/* Messages display area */
.messages-display {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  margin-bottom: 20px;
  position: relative;
  z-index: 50;
  background-color: rgba(20, 25, 40, 0.5);
  border-radius: 10px;
  height: 300px; /* Fixed height to ensure scrolling */
  max-height: 60vh;
  min-height: 300px;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.messages-display::-webkit-scrollbar {
  width: 8px;
}

.messages-display::-webkit-scrollbar-track {
  background: transparent;
}

.messages-display::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

/* Message bubbles */
.message-bubble {
  max-width: 90%;
  padding: 12px 16px;
  border-radius: 16px;
  margin-bottom: 12px;
  position: relative;
  animation: fadeIn 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 50;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Message header with role and timestamp */
.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.message-role {
  font-weight: bold;
  opacity: 0.8;
}

.message-timestamp {
  opacity: 0.6;
}

/* User messages */
.message-bubble.user {
  align-self: flex-end;
  background-color: #4a90e2;
  color: white;
  border-bottom-right-radius: 4px;
  margin-left: auto;
}

/* Assistant messages */
.message-bubble.assistant {
  align-self: flex-start;
  background-color: rgba(70, 80, 110, 0.8);
  color: white;
  border-bottom-left-radius: 4px;
  margin-right: auto;
}

/* Transcript bubble - for live transcription */
.message-bubble.transcript {
  align-self: flex-end;
  background-color: rgba(74, 144, 226, 0.6);
  color: white;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  animation: pulse 1.5s infinite alternate;
}

.message-content {
  font-size: 16px;
  line-height: 1.5;
  word-break: break-word;
}

/* Input container */
.input-container {
  display: flex;
  position: relative;
  z-index: 100; /* Higher z-index for input area */
  background-color: rgba(30, 35, 50, 0.7);
  border-radius: 30px;
  padding: 10px 15px;
  margin-top: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Input styling */
.message-input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 16px;
  outline: none;
  padding: 8px 10px;
  z-index: 101;
}

.message-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* Send button */
.send-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  z-index: 6;
}

.send-button:hover {
  background-color: #357abf;
}

.send-button:disabled {
  background-color: #666;
  cursor: not-allowed;
}

/* Ensure CallCard styling works properly */
.call-card {
  z-index: 10;
  position: relative;
}

/* Loading indicator styling inside the call card */
.loading-indicator {
  z-index: 5;
  position: relative;
}

/* Transcript area styling to ensure visibility */
.transcript-area {
  z-index: 15;
  position: relative;
  background-color: rgba(45, 55, 72, 0.8);
  padding: 10px;
  border-radius: 8px;
  margin: 10px 0;
}

/* Dark theme adjustments */
.dark-theme {
  background-color: rgba(14, 18, 36, 0.9);
}

.dark-theme .message-bubble.assistant {
  background-color: #1f2937;
}

.dark-theme .input-container {
  background-color: rgba(20, 25, 40, 0.8);
}

/* Light theme adjustments */
.light-theme {
  background-color: rgba(240, 240, 240, 0.9);
  color: #333;
}

.light-theme .message-bubble.assistant {
  background-color: #e2e8f0;
  color: #333;
}

.light-theme .message-input {
  color: #333;
}

.light-theme .message-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

/* Animation for transcript pulse */
@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Media queries for responsiveness */
@media (max-width: 992px) {
  .call-content {
    flex-direction: column;
  }
  
  .dossier-section {
    width: 100%;
    margin-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .messages-display {
    max-height: 250px;
  }
  
  .message-bubble {
    max-width: 95%;
  }
}

@media (max-width: 480px) {
  .messages-display {
    max-height: 200px;
  }
  
  .message-content {
    font-size: 14px;
  }
  
  .input-container {
    padding: 8px 12px;
  }
} 