{"version": 3, "file": "websiteScraper-71ea97cc.js", "sources": ["../../src/utils/websiteScraper.js"], "sourcesContent": ["/**\r\n * Website Scraper Utility\r\n *\r\n * Uses AI Meta MCP server to extract branding information\r\n * from law firm websites for configuring LegalScout agents.\r\n *\r\n * This implementation uses the web evaluation tools from the AI Meta MCP server\r\n * to scrape attorney websites and extract profile information.\r\n *\r\n * Enhanced with 1-click attorney configuration capabilities using:\r\n * - Firecrawl for advanced content extraction\r\n * - OpenAI for intelligent data processing\r\n * - Automated Vapi assistant configuration\r\n */\r\n\r\n/**\r\n * Enhanced 1-click attorney configuration\r\n *\r\n * @param {string} url - The website URL to scrape\r\n * @param {Object} options - Configuration options\r\n * @param {boolean} options.useFirecrawl - Use Firecrawl for enhanced extraction\r\n * @param {boolean} options.useAI - Use AI for intelligent processing\r\n * @param {boolean} options.autoConfig - Automatically configure Vapi assistant\r\n * @returns {Promise<object>} - Complete attorney configuration\r\n */\r\nexport const oneClickAttorneyConfig = async (url, options = {}) => {\r\n  const { useFirecrawl = true, useAI = true, autoConfig = true } = options;\r\n\r\n  try {\r\n    // Step 1: Enhanced web scraping with multiple methods\r\n    const scrapedData = await enhancedWebScraping(url, { useFirecrawl, useAI });\r\n\r\n    // Step 2: AI-powered data processing and enhancement\r\n    const processedData = useAI ? await aiEnhanceData(scrapedData) : scrapedData;\r\n\r\n    // Step 3: Generate complete attorney configuration\r\n    const attorneyConfig = await generateAttorneyConfiguration(processedData);\r\n\r\n    // Step 4: Auto-configure Vapi assistant if requested\r\n    if (autoConfig) {\r\n      attorneyConfig.vapiAssistant = await autoConfigureVapiAssistant(attorneyConfig);\r\n    }\r\n\r\n    return attorneyConfig;\r\n  } catch (error) {\r\n    console.error('1-click attorney configuration failed:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Main scraper function to extract information from a law firm website\r\n *\r\n * @param {string} url - The website URL to scrape\r\n * @returns {Promise<object>} - Extracted website data\r\n */\r\nexport const scrapeWebsite = async (url) => {\r\n  try {\r\n    // Step 1: Validate and format the URL\r\n    const formattedUrl = formatUrl(url);\r\n\r\n    // Step 2: Create the evaluation script for the AI Meta MCP server\r\n    const evaluationScript = `\r\n      // Extract attorney profile information\r\n      function extractAttorneyProfile() {\r\n        const doc = document;\r\n        const result = {\r\n          firmName: '',\r\n          attorneyName: '',\r\n          practiceAreas: [],\r\n          description: '',\r\n          contactInfo: {\r\n            phone: '',\r\n            email: '',\r\n            address: ''\r\n          },\r\n          socialMedia: {},\r\n          visualElements: {\r\n            logo: '',\r\n            colors: {\r\n              primary: '',\r\n              secondary: '',\r\n              accent: '',\r\n              background: '',\r\n              text: ''\r\n            },\r\n            fonts: {\r\n              heading: '',\r\n              body: ''\r\n            },\r\n            images: []\r\n          },\r\n          welcomeMessage: '',\r\n          informationGathering: '',\r\n          buttonText: '',\r\n          state: '',\r\n          subdomain: ''\r\n        };\r\n\r\n        try {\r\n          // Extract firm name\r\n          const possibleFirmNameElements = [\r\n            doc.querySelector('header h1, header .logo, #header h1, #header .logo'),\r\n            doc.querySelector('.firm-name, .logo-text, .site-title, .brand'),\r\n            doc.querySelector('h1'),\r\n            doc.querySelector('title')\r\n          ].filter(Boolean);\r\n\r\n          if (possibleFirmNameElements.length > 0) {\r\n            result.firmName = possibleFirmNameElements[0].textContent.trim();\r\n          }\r\n\r\n          // Extract attorney name\r\n          const possibleAttorneyElements = [\r\n            doc.querySelector('.attorney-name, .lawyer-name, .profile-name'),\r\n            doc.querySelector('h1:not(.firm-name), h2:not(.firm-name)'),\r\n            doc.querySelector('header h2, #header h2')\r\n          ].filter(Boolean);\r\n\r\n          if (possibleAttorneyElements.length > 0) {\r\n            result.attorneyName = possibleAttorneyElements[0].textContent.trim();\r\n          }\r\n\r\n          // Extract practice areas\r\n          const practiceAreaElements = Array.from(\r\n            doc.querySelectorAll('.practice-areas li, .areas-of-practice li, .services li, .practice-areas a, .areas-of-practice a')\r\n          );\r\n\r\n          if (practiceAreaElements.length > 0) {\r\n            result.practiceAreas = practiceAreaElements.map(el => el.textContent.trim());\r\n          } else {\r\n            // Try to find practice areas in headings\r\n            const headings = Array.from(doc.querySelectorAll('h2, h3'));\r\n            const practiceHeadings = headings.filter(h =>\r\n              /practice|areas|services/i.test(h.textContent)\r\n            );\r\n\r\n            if (practiceHeadings.length > 0) {\r\n              // Get the next element after the heading\r\n              const practiceList = practiceHeadings[0].nextElementSibling;\r\n              if (practiceList && practiceList.tagName === 'UL') {\r\n                result.practiceAreas = Array.from(practiceList.querySelectorAll('li'))\r\n                  .map(li => li.textContent.trim());\r\n              }\r\n            }\r\n          }\r\n\r\n          // Extract description\r\n          const possibleDescriptionElements = [\r\n            doc.querySelector('.about-us p, .about p, .bio p, .profile p, .description p'),\r\n            doc.querySelector('main p, #main p, .main p'),\r\n            doc.querySelector('p')\r\n          ].filter(Boolean);\r\n\r\n          if (possibleDescriptionElements.length > 0) {\r\n            result.description = possibleDescriptionElements[0].textContent.trim();\r\n          }\r\n\r\n          // Extract contact information\r\n          // Phone\r\n          const phoneRegex = /\\\\(\\\\d{3}\\\\)\\\\s?\\\\d{3}-\\\\d{4}|\\\\d{3}-\\\\d{3}-\\\\d{4}/;\r\n          const phoneElements = Array.from(doc.querySelectorAll('a[href^=\"tel:\"], .phone, .contact .phone, footer .phone'));\r\n\r\n          if (phoneElements.length > 0) {\r\n            result.contactInfo.phone = phoneElements[0].textContent.trim();\r\n          } else {\r\n            // Try to find phone in text\r\n            const allText = doc.body.textContent;\r\n            const phoneMatch = allText.match(phoneRegex);\r\n            if (phoneMatch) {\r\n              result.contactInfo.phone = phoneMatch[0];\r\n            }\r\n          }\r\n\r\n          // Email\r\n          const emailElements = Array.from(doc.querySelectorAll('a[href^=\"mailto:\"], .email, .contact .email, footer .email'));\r\n\r\n          if (emailElements.length > 0) {\r\n            result.contactInfo.email = emailElements[0].textContent.trim();\r\n          }\r\n\r\n          // Address\r\n          const addressElements = Array.from(doc.querySelectorAll('.address, .contact .address, footer .address, address'));\r\n\r\n          if (addressElements.length > 0) {\r\n            result.contactInfo.address = addressElements[0].textContent.trim();\r\n          }\r\n\r\n          // Social media\r\n          const socialLinks = Array.from(doc.querySelectorAll('a[href*=\"facebook.com\"], a[href*=\"twitter.com\"], a[href*=\"linkedin.com\"], a[href*=\"instagram.com\"]'));\r\n\r\n          socialLinks.forEach(link => {\r\n            const href = link.getAttribute('href');\r\n            if (href.includes('facebook.com')) {\r\n              result.socialMedia.facebook = href;\r\n            } else if (href.includes('twitter.com')) {\r\n              result.socialMedia.twitter = href;\r\n            } else if (href.includes('linkedin.com')) {\r\n              result.socialMedia.linkedin = href;\r\n            } else if (href.includes('instagram.com')) {\r\n              result.socialMedia.instagram = href;\r\n            }\r\n          });\r\n\r\n          // Extract logo URL\r\n          const logoElement = doc.querySelector('header img, #header img, .logo img, .site-logo img');\r\n          if (logoElement) {\r\n            const logoSrc = logoElement.getAttribute('src');\r\n            if (logoSrc) {\r\n              // Convert relative URL to absolute\r\n              const logoUrl = new URL(logoSrc, window.location.origin).href;\r\n              result.visualElements.logo = logoUrl;\r\n            }\r\n          }\r\n\r\n          // Extract colors\r\n          const computedStyles = {\r\n            headerBg: window.getComputedStyle(doc.querySelector('header, #header') || doc.body).backgroundColor,\r\n            headerColor: window.getComputedStyle(doc.querySelector('header, #header') || doc.body).color,\r\n            buttonBg: window.getComputedStyle(doc.querySelector('button, .button, a.btn, .cta, .btn-primary') || doc.body).backgroundColor,\r\n            buttonColor: window.getComputedStyle(doc.querySelector('button, .button, a.btn, .cta, .btn-primary') || doc.body).color,\r\n            linkColor: window.getComputedStyle(doc.querySelector('a') || doc.body).color,\r\n            accentColor: window.getComputedStyle(doc.querySelector('.accent, .highlight, .featured, .cta, .btn-secondary') || doc.body).backgroundColor,\r\n            bodyBg: window.getComputedStyle(doc.body).backgroundColor,\r\n            bodyColor: window.getComputedStyle(doc.body).color,\r\n            navBg: window.getComputedStyle(doc.querySelector('nav, .nav, .navbar, .navigation') || doc.body).backgroundColor,\r\n            footerBg: window.getComputedStyle(doc.querySelector('footer, .footer') || doc.body).backgroundColor\r\n          };\r\n\r\n          // Use the most prominent colors\r\n          const colors = Object.entries(computedStyles)\r\n            .filter(([_, value]) => value !== 'transparent' && value !== 'rgba(0, 0, 0, 0)')\r\n            .map(([key, value]) => ({ key, value }));\r\n\r\n          if (colors.length > 0) {\r\n            // Primary color (from header or buttons)\r\n            const primaryColorCandidates = colors.filter(c =>\r\n              c.key === 'headerBg' || c.key === 'buttonBg' || c.key === 'navBg'\r\n            );\r\n            if (primaryColorCandidates.length > 0) {\r\n              result.visualElements.colors.primary = primaryColorCandidates[0].value;\r\n            }\r\n\r\n            // Secondary color (from links)\r\n            const secondaryColorCandidates = colors.filter(c =>\r\n              c.key === 'linkColor' || c.key === 'buttonColor'\r\n            );\r\n            if (secondaryColorCandidates.length > 0) {\r\n              result.visualElements.colors.secondary = secondaryColorCandidates[0].value;\r\n            }\r\n\r\n            // Accent color\r\n            const accentColorCandidates = colors.filter(c =>\r\n              c.key === 'accentColor' || c.key === 'footerBg'\r\n            );\r\n            if (accentColorCandidates.length > 0) {\r\n              result.visualElements.colors.accent = accentColorCandidates[0].value;\r\n            } else if (secondaryColorCandidates.length > 0) {\r\n              // Use secondary as accent if no accent color found\r\n              result.visualElements.colors.accent = secondaryColorCandidates[0].value;\r\n            }\r\n\r\n            // Background color\r\n            const bgColorCandidates = colors.filter(c =>\r\n              c.key === 'bodyBg'\r\n            );\r\n            if (bgColorCandidates.length > 0) {\r\n              result.visualElements.colors.background = bgColorCandidates[0].value;\r\n            }\r\n\r\n            // Text color\r\n            const textColorCandidates = colors.filter(c =>\r\n              c.key === 'bodyColor' || c.key === 'headerColor'\r\n            );\r\n            if (textColorCandidates.length > 0) {\r\n              result.visualElements.colors.text = textColorCandidates[0].value;\r\n            }\r\n          }\r\n\r\n          // Extract fonts\r\n          const fontElements = {\r\n            heading: doc.querySelector('h1, h2, .heading, .title'),\r\n            body: doc.querySelector('p, .body, article, main')\r\n          };\r\n\r\n          if (fontElements.heading) {\r\n            const headingStyle = window.getComputedStyle(fontElements.heading);\r\n            result.visualElements.fonts.heading = headingStyle.fontFamily;\r\n          }\r\n\r\n          if (fontElements.body) {\r\n            const bodyStyle = window.getComputedStyle(fontElements.body);\r\n            result.visualElements.fonts.body = bodyStyle.fontFamily;\r\n          }\r\n\r\n          // Extract background images\r\n          const backgroundElements = [\r\n            doc.querySelector('header, #header, .hero, .banner, .jumbotron'),\r\n            doc.querySelector('body'),\r\n            doc.querySelector('main, .main-content')\r\n          ].filter(Boolean);\r\n\r\n          backgroundElements.forEach(element => {\r\n            const style = window.getComputedStyle(element);\r\n            const backgroundImage = style.backgroundImage;\r\n\r\n            if (backgroundImage && backgroundImage !== 'none') {\r\n              // Extract URL from the background-image property\r\n              const urlMatch = backgroundImage.match(/url\\\\(['\"]?([^'\"\\\\)]+)['\"]?\\\\)/);\r\n              if (urlMatch && urlMatch[1]) {\r\n                const imageUrl = new URL(urlMatch[1], window.location.origin).href;\r\n                result.visualElements.images.push({\r\n                  url: imageUrl,\r\n                  type: 'background',\r\n                  element: element.tagName.toLowerCase()\r\n                });\r\n              }\r\n            }\r\n          });\r\n\r\n          // Extract other images\r\n          const contentImages = Array.from(doc.querySelectorAll('img')).filter(img => {\r\n            const rect = img.getBoundingClientRect();\r\n            // Only include reasonably sized images\r\n            return rect.width > 200 && rect.height > 100;\r\n          });\r\n\r\n          contentImages.forEach(img => {\r\n            const src = img.getAttribute('src');\r\n            if (src) {\r\n              const imageUrl = new URL(src, window.location.origin).href;\r\n              result.visualElements.images.push({\r\n                url: imageUrl,\r\n                type: 'content',\r\n                alt: img.getAttribute('alt') || ''\r\n              });\r\n            }\r\n          });\r\n\r\n          // Generate welcome message\r\n          result.welcomeMessage = generateWelcomeMessage(result);\r\n\r\n          // Generate information gathering prompt\r\n          result.informationGathering = generateInformationGatheringPrompt(result);\r\n\r\n          // Generate button text\r\n          result.buttonText = generateButtonText(result);\r\n\r\n          // Generate suggested subdomain\r\n          result.subdomain = generateSubdomain(result.firmName);\r\n\r\n          // Extract state from address if available\r\n          if (result.contactInfo && result.contactInfo.address) {\r\n            const stateMatch = result.contactInfo.address.match(/[A-Z]{2}/);\r\n            if (stateMatch) {\r\n              result.state = stateMatch[0];\r\n            }\r\n          }\r\n\r\n          // Generate a suggested prompt based on the extracted data\r\n          result.suggestedPrompt = generatePromptFromData(result);\r\n        } catch (error) {\r\n          console.error('Error extracting attorney profile:', error);\r\n        }\r\n\r\n        return result;\r\n      }\r\n\r\n      // Helper function to generate a welcome message\r\n      function generateWelcomeMessage(data) {\r\n        let message = '';\r\n\r\n        if (data.firmName) {\r\n          message = 'Welcome to ' + data.firmName + '! ';\r\n        } else {\r\n          message = 'Welcome to our legal assistant! ';\r\n        }\r\n\r\n        if (data.practiceAreas && data.practiceAreas.length > 0) {\r\n          if (data.practiceAreas.length === 1) {\r\n            message += 'We specialize in ' + data.practiceAreas[0] + '. ';\r\n          } else if (data.practiceAreas.length === 2) {\r\n            message += 'We specialize in ' + data.practiceAreas[0] + ' and ' + data.practiceAreas[1] + '. ';\r\n          } else {\r\n            const lastArea = data.practiceAreas[data.practiceAreas.length - 1];\r\n            const otherAreas = data.practiceAreas.slice(0, -1).join(', ');\r\n            message += 'We specialize in ' + otherAreas + ', and ' + lastArea + '. ';\r\n          }\r\n        }\r\n\r\n        message += 'How can we assist you today?';\r\n\r\n        return message;\r\n      }\r\n\r\n      // Helper function to generate an information gathering prompt\r\n      function generateInformationGatheringPrompt(data) {\r\n        let prompt = 'To better assist you, I need to gather some information. ';\r\n\r\n        if (data.practiceAreas && data.practiceAreas.length > 0) {\r\n          prompt += 'Could you please tell me about your situation';\r\n\r\n          // Add specific questions based on practice areas\r\n          if (data.practiceAreas.some(area => /personal injury|accident|injury/i.test(area))) {\r\n            prompt += ', including when and how the injury occurred';\r\n          } else if (data.practiceAreas.some(area => /family|divorce/i.test(area))) {\r\n            prompt += ' and what family law matter you need assistance with';\r\n          } else if (data.practiceAreas.some(area => /criminal|defense/i.test(area))) {\r\n            prompt += ' and what legal charges or issues you're facing';\r\n          } else if (data.practiceAreas.some(area => /estate|will|trust/i.test(area))) {\r\n            prompt += ' and what estate planning needs you have';\r\n          } else if (data.practiceAreas.some(area => /business|corporate/i.test(area))) {\r\n            prompt += ' and what business legal matters you need help with';\r\n          } else if (data.practiceAreas.some(area => /immigration/i.test(area))) {\r\n            prompt += ' and what immigration matter you need assistance with';\r\n          }\r\n\r\n          prompt += '?';\r\n        } else {\r\n          prompt += 'Could you please tell me about your legal situation and how we might be able to help you?';\r\n        }\r\n\r\n        return prompt;\r\n      }\r\n\r\n      // Helper function to generate button text\r\n      function generateButtonText(data) {\r\n        // Default button text\r\n        let buttonText = 'Start Consultation';\r\n\r\n        // Customize based on practice areas if available\r\n        if (data.practiceAreas && data.practiceAreas.length > 0) {\r\n          if (data.practiceAreas.some(area => /personal injury|accident|injury/i.test(area))) {\r\n            buttonText = 'Discuss Your Case';\r\n          } else if (data.practiceAreas.some(area => /family|divorce/i.test(area))) {\r\n            buttonText = 'Get Family Law Help';\r\n          } else if (data.practiceAreas.some(area => /criminal|defense/i.test(area))) {\r\n            buttonText = 'Get Legal Defense';\r\n          } else if (data.practiceAreas.some(area => /estate|will|trust/i.test(area))) {\r\n            buttonText = 'Plan Your Estate';\r\n          } else if (data.practiceAreas.some(area => /business|corporate/i.test(area))) {\r\n            buttonText = 'Business Consultation';\r\n          }\r\n        }\r\n\r\n        return buttonText;\r\n      }\r\n\r\n      // Helper function to generate a subdomain from firm name\r\n      function generateSubdomain(firmName) {\r\n        if (!firmName) return '';\r\n\r\n        // Remove special characters, spaces, and common legal terms\r\n        return firmName\r\n          .toLowerCase()\r\n          .replace(/[^a-z0-9]/g, '')\r\n          .replace(/law(firm|office|group)?|llp|llc|pc|pa|associates|legal/g, '')\r\n          .substring(0, 20); // Limit to 20 characters\r\n      }\r\n\r\n      // Helper function to generate a prompt from the extracted data\r\n      function generatePromptFromData(data) {\r\n        let prompt = '';\r\n\r\n        if (data.firmName) {\r\n          prompt += 'You are an AI assistant for ' + data.firmName + '. ';\r\n        } else {\r\n          prompt += 'You are an AI legal assistant. ';\r\n        }\r\n\r\n        if (data.practiceAreas && data.practiceAreas.length > 0) {\r\n          prompt += 'You specialize in ' + data.practiceAreas.join(', ') + '. ';\r\n        }\r\n\r\n        prompt += 'Your role is to gather information from potential clients about their legal needs and provide helpful information about the firm\\\\'s services. ';\r\n\r\n        if (data.description) {\r\n          prompt += 'About the firm: ' + data.description + ' ';\r\n        }\r\n\r\n        if (data.contactInfo && (data.contactInfo.phone || data.contactInfo.email)) {\r\n          prompt += 'You can offer to connect the client with an attorney';\r\n          if (data.contactInfo.phone) {\r\n            prompt += ' at ' + data.contactInfo.phone;\r\n          }\r\n          if (data.contactInfo.email) {\r\n            prompt += ' or via email at ' + data.contactInfo.email;\r\n          }\r\n          prompt += '. ';\r\n        }\r\n\r\n        prompt += 'Be professional, courteous, and helpful at all times.';\r\n\r\n        return prompt;\r\n      }\r\n\r\n      return extractAttorneyProfile();\r\n    `;\r\n\r\n    // Step 3: Call the AI Meta MCP server API\r\n    const response = await fetch('/api/ai-meta-mcp', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        name: 'web_evaluate',\r\n        arguments: {\r\n          url: formattedUrl,\r\n          evaluationScript,\r\n          options: {\r\n            timeout: 15000,\r\n          },\r\n        },\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error('API error: ' + response.status);\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    // Parse the result\r\n    if (data && data.content && data.content[0] && data.content[0].text) {\r\n      try {\r\n        const parsedResult = JSON.parse(data.content[0].text);\r\n\r\n        if (parsedResult.success && parsedResult.result) {\r\n          // Map the extracted data to our expected format\r\n          const extractedData = {\r\n            // Basic information\r\n            firmName: parsedResult.result.firmName || 'Unknown Firm',\r\n            attorneyName: parsedResult.result.attorneyName || '',\r\n\r\n            // Visual elements\r\n            logo: {\r\n              url: parsedResult.result.visualElements?.logo || '',\r\n              width: 200, // Default values\r\n              height: 80,\r\n              backgroundColor: parsedResult.result.visualElements?.colors?.background || '#ffffff',\r\n              textColor: parsedResult.result.visualElements?.colors?.text || '#000000'\r\n            },\r\n            colors: {\r\n              primary: parsedResult.result.visualElements?.colors?.primary || '#123456',\r\n              secondary: parsedResult.result.visualElements?.colors?.secondary || '#789abc',\r\n              accent: parsedResult.result.visualElements?.colors?.accent || '#def012',\r\n              background: parsedResult.result.visualElements?.colors?.background || '#ffffff',\r\n              text: parsedResult.result.visualElements?.colors?.text || '#333333'\r\n            },\r\n            backgroundImages: parsedResult.result.visualElements?.images?.filter(img => img.type === 'background')?.map(img => img.url) || [],\r\n\r\n            // Content analysis\r\n            practiceAreas: parsedResult.result.practiceAreas || [],\r\n            attorneys: [\r\n              {\r\n                name: parsedResult.result.attorneyName || 'Unknown Attorney',\r\n                title: 'Attorney',\r\n                profileImage: '',\r\n                specialties: parsedResult.result.practiceAreas || [],\r\n                education: ''\r\n              }\r\n            ],\r\n            address: {\r\n              street: '',\r\n              city: '',\r\n              state: parsedResult.result.state || '',\r\n              zip: ''\r\n            },\r\n            contactInfo: parsedResult.result.contactInfo || {},\r\n            contentAnalysis: {\r\n              keyPhrases: [],\r\n              services: parsedResult.result.practiceAreas || [],\r\n              clientFocus: []\r\n            },\r\n\r\n            // Configuration values\r\n            welcomeMessage: parsedResult.result.welcomeMessage || 'Welcome! How can I assist you today?',\r\n            informationGathering: parsedResult.result.informationGathering || 'Could you tell me about your legal situation?',\r\n            buttonText: parsedResult.result.buttonText || 'Start Consultation',\r\n            subdomain: parsedResult.result.subdomain || '',\r\n\r\n            // Fonts\r\n            fonts: {\r\n              heading: parsedResult.result.visualElements?.fonts?.heading || '',\r\n              body: parsedResult.result.visualElements?.fonts?.body || ''\r\n            },\r\n\r\n            // Generate agent prompt\r\n            suggestedPrompt: parsedResult.result.suggestedPrompt || generatePrompt(parsedResult.result)\r\n          };\r\n\r\n          // Try to parse the address\r\n          if (parsedResult.result.contactInfo?.address) {\r\n            const addressParts = parsedResult.result.contactInfo.address.split(',');\r\n            if (addressParts.length >= 2) {\r\n              extractedData.address.street = addressParts[0].trim();\r\n              extractedData.address.city = addressParts[1].trim();\r\n\r\n              // Try to extract state and zip\r\n              if (addressParts.length >= 3) {\r\n                const stateZipParts = addressParts[2].trim().split(' ');\r\n                if (stateZipParts.length >= 2) {\r\n                  extractedData.address.state = stateZipParts[0].trim();\r\n                  extractedData.address.zip = stateZipParts[1].trim();\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          return extractedData;\r\n        } else {\r\n          throw new Error(parsedResult.error || 'Failed to extract profile information');\r\n        }\r\n      } catch (parseError) {\r\n        console.error('Error parsing result:', parseError);\r\n        throw new Error('Invalid response format');\r\n      }\r\n    } else {\r\n      throw new Error('Invalid response format');\r\n    }\r\n  } catch (error) {\r\n    console.error('Error scraping website:', error);\r\n    throw new Error('Failed to scrape website: ' + error.message);\r\n  }\r\n};\r\n\r\n/**\r\n * Validate and format URL to ensure it has proper protocol\r\n */\r\nconst formatUrl = (url) => {\r\n  if (!url) throw new Error('URL is required');\r\n\r\n  // Add https:// if no protocol specified\r\n  if (!url.startsWith('http://') && !url.startsWith('https://')) {\r\n    return 'https://' + url;\r\n  }\r\n\r\n  return url;\r\n};\r\n\r\n/**\r\n * Extract firm name from website\r\n * Look for logo alt text, header text, or title\r\n */\r\nconst extractFirmName = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Check for logo alt text\r\n  // 2. Look for main heading (h1) in header\r\n  // 3. Parse title tag content\r\n  // 4. Use heuristics to clean up text (remove \"Law Firm\", \"LLP\", etc.)\r\n  return \"Extracted Firm Name\";\r\n};\r\n\r\n/**\r\n * Extract logo information\r\n * Find primary logo image and its details\r\n */\r\nconst extractLogo = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Find logo in header (common selectors like .logo, #logo, etc.)\r\n  // 2. Get image src, width, height\r\n  // 3. Capture logo background and foreground colors\r\n  return {\r\n    url: \"logo.png\",\r\n    width: 200,\r\n    height: 80,\r\n    backgroundColor: \"#ffffff\",\r\n    textColor: \"#000000\"\r\n  };\r\n};\r\n\r\n/**\r\n * Extract color scheme from website\r\n * Analyze dominant colors in header, footer, buttons, etc.\r\n */\r\nconst extractColors = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Extract CSS variables if available\r\n  // 2. Get background colors from header, content areas, footer\r\n  // 3. Get text colors from headings, paragraphs\r\n  // 4. Get accent colors from buttons, links, highlights\r\n  // 5. Use color analysis to identify primary/secondary/accent\r\n  return {\r\n    primary: \"#123456\",\r\n    secondary: \"#789abc\",\r\n    accent: \"#def012\",\r\n    background: \"#ffffff\",\r\n    text: \"#333333\"\r\n  };\r\n};\r\n\r\n/**\r\n * Extract large background images from the website\r\n */\r\nconst extractBackgroundImages = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Find hero/banner sections\r\n  // 2. Extract background-image CSS properties\r\n  // 3. Get img tags with large dimensions\r\n  // 4. Filter for high-quality images only\r\n  return [\r\n    {\r\n      url: \"background1.jpg\",\r\n      position: \"hero\",\r\n      size: \"large\"\r\n    }\r\n  ];\r\n};\r\n\r\n/**\r\n * Extract practice areas mentioned on the website\r\n */\r\nconst extractPracticeAreas = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Look for common practice area section selectors\r\n  // 2. Extract list items or section headers\r\n  // 3. Match against known practice area terms\r\n  // 4. Clean and normalize terms\r\n  return [\"Practice Area 1\", \"Practice Area 2\"];\r\n};\r\n\r\n/**\r\n * Extract attorney information\r\n */\r\nconst extractAttorneyInfo = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Find attorney profiles/team section\r\n  // 2. Extract name, title, image for each attorney\r\n  // 3. Extract specialties and education if available\r\n  return [\r\n    {\r\n      name: \"Attorney Name\",\r\n      title: \"Title\",\r\n      profileImage: \"attorney.jpg\",\r\n      specialties: [\"Specialty 1\"],\r\n      education: \"Law School\"\r\n    }\r\n  ];\r\n};\r\n\r\n/**\r\n * Extract office address information\r\n */\r\nconst extractAddress = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Look for address in footer or contact section\r\n  // 2. Parse address components\r\n  // 3. Use regex to identify state code\r\n  return {\r\n    street: \"123 Main St\",\r\n    city: \"City\",\r\n    state: \"NY\",\r\n    zip: \"12345\"\r\n  };\r\n};\r\n\r\n/**\r\n * Analyze website content for key information\r\n */\r\nconst analyzeContent = async (/* page */) => {\r\n  // Implementation would:\r\n  // 1. Extract all page text\r\n  // 2. Identify key phrases and marketing language\r\n  // 3. Extract service offerings\r\n  // 4. Identify client focus\r\n  return {\r\n    keyPhrases: [\"key phrase 1\"],\r\n    services: [\"service 1\"],\r\n    clientFocus: [\"client type 1\"]\r\n  };\r\n};\r\n\r\n/**\r\n * Generate a suggested prompt for the LegalScout agent\r\n * based on extracted content\r\n */\r\nconst generatePrompt = async (extractedData) => {\r\n  // Implementation would:\r\n  // 1. Combine firm name, practice areas, key phrases\r\n  // 2. Structure into a natural-sounding prompt\r\n  // 3. Include specific instructions for gathering client information\r\n  return \"Generated prompt based on website content\";\r\n};\r\n\r\n/**\r\n * Save extracted data to a database (mock function for demo)\r\n * In production, this would connect to a real database\r\n */\r\nexport const saveExtractedData = async (data, userId) => {\r\n  // This is a mock implementation for the demo\r\n  console.log('Demo mode: Would save data to database', { data, userId });\r\n  return { success: true, message: 'Data saved (demo mode)' };\r\n};\r\n\r\n/**\r\n * Enhanced web scraping using multiple methods\r\n */\r\nconst enhancedWebScraping = async (url, options = {}) => {\r\n  const { useFirecrawl = true } = options;\r\n\r\n  try {\r\n    // Method 1: Use existing AI Meta MCP scraping\r\n    const basicData = await scrapeWebsite(url);\r\n\r\n    // Method 2: Use Firecrawl for enhanced extraction if available\r\n    let firecrawlData = {};\r\n    if (useFirecrawl) {\r\n      try {\r\n        firecrawlData = await scrapeWithFirecrawl(url);\r\n      } catch (error) {\r\n        console.warn('Firecrawl extraction failed, using basic data:', error.message);\r\n      }\r\n    }\r\n\r\n    // Merge and enhance the data\r\n    return mergeScrapedData(basicData, firecrawlData);\r\n  } catch (error) {\r\n    console.error('Enhanced web scraping failed:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Use Firecrawl for advanced content extraction\r\n */\r\nconst scrapeWithFirecrawl = async (url) => {\r\n  try {\r\n    const response = await fetch('/api/firecrawl-search', {\r\n      method: 'POST',\r\n      headers: { 'Content-Type': 'application/json' },\r\n      body: JSON.stringify({\r\n        query: `site:${new URL(url).hostname} attorney law firm profile`,\r\n        numResults: 1,\r\n        format: 'detailed',\r\n        legalFocus: true\r\n      })\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Firecrawl API error: ${response.status}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    return extractFirecrawlData(data);\r\n  } catch (error) {\r\n    console.error('Firecrawl scraping error:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * Extract structured data from Firecrawl response\r\n */\r\nconst extractFirecrawlData = (firecrawlResponse) => {\r\n  const results = firecrawlResponse.results || [];\r\n  if (results.length === 0) return {};\r\n\r\n  const result = results[0];\r\n  return {\r\n    enhancedDescription: result.summary || result.content?.substring(0, 500),\r\n    structuredContent: result.structured_data || {},\r\n    metadata: result.metadata || {},\r\n    additionalImages: result.images || [],\r\n    socialProfiles: result.social_links || {}\r\n  };\r\n};\r\n\r\n/**\r\n * Merge data from multiple scraping sources\r\n */\r\nconst mergeScrapedData = (basicData, firecrawlData) => {\r\n  return {\r\n    ...basicData,\r\n    // Enhanced description from Firecrawl\r\n    description: firecrawlData.enhancedDescription || basicData.description,\r\n    // Additional metadata\r\n    metadata: firecrawlData.metadata || {},\r\n    // Enhanced social profiles\r\n    socialMedia: {\r\n      ...basicData.socialMedia,\r\n      ...firecrawlData.socialProfiles\r\n    },\r\n    // Additional images\r\n    visualElements: {\r\n      ...basicData.visualElements,\r\n      images: [\r\n        ...(basicData.visualElements?.images || []),\r\n        ...(firecrawlData.additionalImages || [])\r\n      ]\r\n    },\r\n    // Raw Firecrawl data for AI processing\r\n    rawFirecrawlData: firecrawlData\r\n  };\r\n};\r\n\r\nexport default {\r\n  scrapeWebsite,\r\n  oneClickAttorneyConfig,\r\n  saveExtractedData\r\n};"], "names": ["oneClickAttorneyConfig", "url", "options", "useFirecrawl", "useAI", "autoConfig", "scrapedData", "enhancedWebScraping", "processedData", "attorney<PERSON><PERSON><PERSON>g", "error", "scrapeWebsite", "formattedUrl", "formatUrl", "response", "data", "parsedResult", "extractedData", "img", "generatePrompt", "addressParts", "stateZipParts", "parseError", "saveExtractedData", "userId", "basicData", "firecrawlData", "scrapeWithFirecrawl", "mergeScrapedData", "extractFirecrawlData", "firecrawlResponse", "results", "result", "websiteScraper"], "mappings": "AAyBY,MAACA,EAAyB,MAAOC,EAAKC,EAAU,KAAO,CACjE,KAAM,CAAE,aAAAC,EAAe,GAAM,MAAAC,EAAQ,GAAM,WAAAC,EAAa,EAAM,EAAGH,EAEjE,GAAI,CAEF,MAAMI,EAAc,MAAMC,EAAoBN,EAAK,CAAE,aAAAE,EAAc,MAAAC,CAAK,CAAE,EAGpEI,EAAgBJ,EAAQ,MAAM,cAAcE,CAAW,EAAIA,EAG3DG,EAAiB,MAAM,8BAA8BD,CAAa,EAGxE,OAAIH,IACFI,EAAe,cAAgB,MAAM,2BAA2BA,CAAc,GAGzEA,CACR,OAAQC,EAAO,CACd,cAAQ,MAAM,yCAA0CA,CAAK,EACvDA,CACP,CACH,EAQaC,EAAgB,MAAOV,GAAQ,CAC1C,GAAI,CAEF,MAAMW,EAAeC,EAAUZ,CAAG,EAyb5Ba,EAAW,MAAM,MAAM,mBAAoB,CAC/C,OAAQ,OACR,QAAS,CACP,eAAgB,kBACjB,EACD,KAAM,KAAK,UAAU,CACnB,KAAM,eACN,UAAW,CACT,IAAKF,EACL,iBA/bmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAgcnB,QAAS,CACP,QAAS,IACV,CACF,CACT,CAAO,CACP,CAAK,EAED,GAAI,CAACE,EAAS,GACZ,MAAM,IAAI,MAAM,cAAgBA,EAAS,MAAM,EAGjD,MAAMC,EAAO,MAAMD,EAAS,OAG5B,GAAIC,GAAQA,EAAK,SAAWA,EAAK,QAAQ,CAAC,GAAKA,EAAK,QAAQ,CAAC,EAAE,KAC7D,GAAI,CACF,MAAMC,EAAe,KAAK,MAAMD,EAAK,QAAQ,CAAC,EAAE,IAAI,EAEpD,GAAIC,EAAa,SAAWA,EAAa,OAAQ,CAE/C,MAAMC,EAAgB,CAEpB,SAAUD,EAAa,OAAO,UAAY,eAC1C,aAAcA,EAAa,OAAO,cAAgB,GAGlD,KAAM,CACJ,IAAKA,EAAa,OAAO,gBAAgB,MAAQ,GACjD,MAAO,IACP,OAAQ,GACR,gBAAiBA,EAAa,OAAO,gBAAgB,QAAQ,YAAc,UAC3E,UAAWA,EAAa,OAAO,gBAAgB,QAAQ,MAAQ,SAChE,EACD,OAAQ,CACN,QAASA,EAAa,OAAO,gBAAgB,QAAQ,SAAW,UAChE,UAAWA,EAAa,OAAO,gBAAgB,QAAQ,WAAa,UACpE,OAAQA,EAAa,OAAO,gBAAgB,QAAQ,QAAU,UAC9D,WAAYA,EAAa,OAAO,gBAAgB,QAAQ,YAAc,UACtE,KAAMA,EAAa,OAAO,gBAAgB,QAAQ,MAAQ,SAC3D,EACD,iBAAkBA,EAAa,OAAO,gBAAgB,QAAQ,OAAOE,GAAOA,EAAI,OAAS,YAAY,GAAG,IAAIA,GAAOA,EAAI,GAAG,GAAK,CAAE,EAGjI,cAAeF,EAAa,OAAO,eAAiB,CAAE,EACtD,UAAW,CACT,CACE,KAAMA,EAAa,OAAO,cAAgB,mBAC1C,MAAO,WACP,aAAc,GACd,YAAaA,EAAa,OAAO,eAAiB,CAAE,EACpD,UAAW,EACZ,CACF,EACD,QAAS,CACP,OAAQ,GACR,KAAM,GACN,MAAOA,EAAa,OAAO,OAAS,GACpC,IAAK,EACN,EACD,YAAaA,EAAa,OAAO,aAAe,CAAE,EAClD,gBAAiB,CACf,WAAY,CAAE,EACd,SAAUA,EAAa,OAAO,eAAiB,CAAE,EACjD,YAAa,CAAE,CAChB,EAGD,eAAgBA,EAAa,OAAO,gBAAkB,uCACtD,qBAAsBA,EAAa,OAAO,sBAAwB,gDAClE,WAAYA,EAAa,OAAO,YAAc,qBAC9C,UAAWA,EAAa,OAAO,WAAa,GAG5C,MAAO,CACL,QAASA,EAAa,OAAO,gBAAgB,OAAO,SAAW,GAC/D,KAAMA,EAAa,OAAO,gBAAgB,OAAO,MAAQ,EAC1D,EAGD,gBAAiBA,EAAa,OAAO,iBAAmBG,EAAeH,EAAa,MAAM,CACtG,EAGU,GAAIA,EAAa,OAAO,aAAa,QAAS,CAC5C,MAAMI,EAAeJ,EAAa,OAAO,YAAY,QAAQ,MAAM,GAAG,EACtE,GAAII,EAAa,QAAU,IACzBH,EAAc,QAAQ,OAASG,EAAa,CAAC,EAAE,OAC/CH,EAAc,QAAQ,KAAOG,EAAa,CAAC,EAAE,OAGzCA,EAAa,QAAU,GAAG,CAC5B,MAAMC,EAAgBD,EAAa,CAAC,EAAE,OAAO,MAAM,GAAG,EAClDC,EAAc,QAAU,IAC1BJ,EAAc,QAAQ,MAAQI,EAAc,CAAC,EAAE,OAC/CJ,EAAc,QAAQ,IAAMI,EAAc,CAAC,EAAE,OAEhD,CAEJ,CAED,OAAOJ,CACjB,KACU,OAAM,IAAI,MAAMD,EAAa,OAAS,uCAAuC,CAEhF,OAAQM,EAAY,CACnB,cAAQ,MAAM,wBAAyBA,CAAU,EAC3C,IAAI,MAAM,yBAAyB,CAC1C,KAED,OAAM,IAAI,MAAM,yBAAyB,CAE5C,OAAQZ,EAAO,CACd,cAAQ,MAAM,0BAA2BA,CAAK,EACxC,IAAI,MAAM,6BAA+BA,EAAM,OAAO,CAC7D,CACH,EAKMG,EAAaZ,GAAQ,CACzB,GAAI,CAACA,EAAK,MAAM,IAAI,MAAM,iBAAiB,EAG3C,MAAI,CAACA,EAAI,WAAW,SAAS,GAAK,CAACA,EAAI,WAAW,UAAU,EACnD,WAAaA,EAGfA,CACT,EA0IMkB,EAAiB,MAAOF,GAKrB,4CAOIM,EAAoB,MAAOR,EAAMS,KAE5C,QAAQ,IAAI,yCAA0C,CAAE,KAAAT,EAAM,OAAAS,CAAQ,CAAA,EAC/D,CAAE,QAAS,GAAM,QAAS,wBAAwB,GAMrDjB,EAAsB,MAAON,EAAKC,EAAU,KAAO,CACvD,KAAM,CAAE,aAAAC,EAAe,EAAM,EAAGD,EAEhC,GAAI,CAEF,MAAMuB,EAAY,MAAMd,EAAcV,CAAG,EAGzC,IAAIyB,EAAgB,CAAA,EACpB,GAAIvB,EACF,GAAI,CACFuB,EAAgB,MAAMC,EAAoB1B,CAAG,CAC9C,OAAQS,EAAO,CACd,QAAQ,KAAK,iDAAkDA,EAAM,OAAO,CAC7E,CAIH,OAAOkB,EAAiBH,EAAWC,CAAa,CACjD,OAAQhB,EAAO,CACd,cAAQ,MAAM,gCAAiCA,CAAK,EAC9CA,CACP,CACH,EAKMiB,EAAsB,MAAO1B,GAAQ,CACzC,GAAI,CACF,MAAMa,EAAW,MAAM,MAAM,wBAAyB,CACpD,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAoB,EAC/C,KAAM,KAAK,UAAU,CACnB,MAAO,QAAQ,IAAI,IAAIb,CAAG,EAAE,QAAQ,6BACpC,WAAY,EACZ,OAAQ,WACR,WAAY,EACpB,CAAO,CACP,CAAK,EAED,GAAI,CAACa,EAAS,GACZ,MAAM,IAAI,MAAM,wBAAwBA,EAAS,MAAM,EAAE,EAG3D,MAAMC,EAAO,MAAMD,EAAS,OAC5B,OAAOe,EAAqBd,CAAI,CACjC,OAAQL,EAAO,CACd,cAAQ,MAAM,4BAA6BA,CAAK,EAC1CA,CACP,CACH,EAKMmB,EAAwBC,GAAsB,CAClD,MAAMC,EAAUD,EAAkB,SAAW,GAC7C,GAAIC,EAAQ,SAAW,EAAG,MAAO,CAAA,EAEjC,MAAMC,EAASD,EAAQ,CAAC,EACxB,MAAO,CACL,oBAAqBC,EAAO,SAAWA,EAAO,SAAS,UAAU,EAAG,GAAG,EACvE,kBAAmBA,EAAO,iBAAmB,CAAE,EAC/C,SAAUA,EAAO,UAAY,CAAE,EAC/B,iBAAkBA,EAAO,QAAU,CAAE,EACrC,eAAgBA,EAAO,cAAgB,CAAE,CAC7C,CACA,EAKMJ,EAAmB,CAACH,EAAWC,KAC5B,CACL,GAAGD,EAEH,YAAaC,EAAc,qBAAuBD,EAAU,YAE5D,SAAUC,EAAc,UAAY,CAAE,EAEtC,YAAa,CACX,GAAGD,EAAU,YACb,GAAGC,EAAc,cAClB,EAED,eAAgB,CACd,GAAGD,EAAU,eACb,OAAQ,CACN,GAAIA,EAAU,gBAAgB,QAAU,GACxC,GAAIC,EAAc,kBAAoB,EACvC,CACF,EAED,iBAAkBA,CACtB,GAGeO,EAAA,CACb,cAAAtB,EACA,uBAAAX,EACA,kBAAAuB,CACF"}