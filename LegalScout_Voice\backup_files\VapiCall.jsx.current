import React, { useState, useEffect, useRef } from 'react';
import { withDevTools } from '../utils/debugConfig'
import useVapiCall from '../hooks/useVapiCall'
import { CALL_STATUS } from '../constants/vapiConstants'
import './VapiCall.css'
import TextShimmerWave from './TextShimmerWave'
import VolumeLevel from './call/VolumeLevel';
import AssistantSpeechIndicator from './call/AssistantSpeechIndicator';

/**
 * Component for handling voice calls with Vapi.ai assistant
 *
 * This component manages:
 * - Call initialization and connection
 * - Audio processing
 * - Message handling
 * - Dossier data collection
 *
 * @param {Object} props Component props
 * @param {Function} props.onEndCall Callback function when call ends
 * @param {string} props.subdomain Attorney subdomain for customization
 * @param {Object} props.dossierRef Ref to the dossier component
 * @param {Function} props.onStatusChange Callback for status changes
 * @param {Function} props.onMessageReceived Callback for new messages
 * @param {Function} props.onDossierUpdate Callback for dossier updates
 * @param {Function} props.onVolumeUpdate Callback for volume level updates
 * @param {Function} props.onSpeakingUpdate Callback for assistant speaking status updates
 */
const VapiCall = ({
  onEndCall,
  subdomain = 'default',
  dossierRef,
  onStatusChange,
  onMessageReceived,
  onDossierUpdate,
  onVolumeUpdate,
  onSpeakingUpdate
}) => {
  // Container ref for forcing visibility
  const containerRef = useRef(null);

  // Use our custom hook to manage call state and functionality
  const {
    status,
    dossierData,
    volumeLevel,
    assistantIsSpeaking,
    errorMessage,
    subdomainConfig,
    startCall,
    stopCall,
    vapi,
    messageHistory,
    isInitialized
  } = useVapiCall({
    subdomain,
    onEndCall,
    onCallStarted: () => {
      console.log("Call started callback fired");
      if (typeof onStatusChange === 'function') {
        onStatusChange(CALL_STATUS.CONNECTED);
      }
    },
    onMessageReceived: (message) => {
      console.log("Message received in component:", message);
      if (typeof onMessageReceived === 'function') {
        onMessageReceived(message);
      }
    },
    onVolumeUpdate: (level) => {
      if (typeof onVolumeUpdate === 'function') {
        onVolumeUpdate(level);
      }
    },
    onSpeakingUpdate: (isSpeaking) => {
      if (typeof onSpeakingUpdate === 'function') {
        onSpeakingUpdate(isSpeaking);
      }
    },
    isActive: true
  });

  // For message handling
  const [messageText, setMessageText] = useState('');
  const [messages, setMessages] = useState([
    { type: 'assistant', text: "Hello! I'm Scout, your legal assistant. Tell me about your situation and I'll help find the right attorney for you." }
  ]);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const messagesEndRef = useRef(null);

  // Auto-start the call when component mounts
  useEffect(() => {
    if (isInitialized && status === CALL_STATUS.IDLE) {
      setTimeout(() => {
        startCall();
      }, 500);
    }
  }, [isInitialized, status, startCall]);

  // Auto-scroll to bottom when messages change - improved to prevent global scroll interference
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      const conversationArea = messagesEndRef.current.closest('.conversation-area');
      if (conversationArea) {
        // Check if user is near the bottom (within 100px)
        const isNearBottom = conversationArea.scrollHeight - conversationArea.scrollTop - conversationArea.clientHeight < 100;

        // Only auto-scroll if user is near the bottom (not reading old messages)
        if (isNearBottom) {
          try {
            // Directly set scrollTop without any smooth scrolling or events
            conversationArea.scrollTop = conversationArea.scrollHeight;

            // Ensure the scroll stays within the conversation area
            conversationArea.style.scrollBehavior = 'auto';

            // Force a layout recalculation to ensure scroll position is applied
            conversationArea.offsetHeight;

            console.log('VapiCall: Scrolled conversation area to bottom');
          } catch (error) {
            console.warn('Error scrolling conversation area:', error);
          }
        }
      }
    }
  };

  // Scroll to bottom when messages change - with debouncing
  useEffect(() => {
    // Use a timeout to debounce scroll calls
    const scrollTimeout = setTimeout(() => {
      scrollToBottom();
    }, 50); // Small delay to batch multiple message updates

    return () => clearTimeout(scrollTimeout);
  }, [messages, currentTranscript]);

  // Update messages when messageHistory changes
  useEffect(() => {
    if (messageHistory && messageHistory.length > 0) {
      const latestMessage = messageHistory[messageHistory.length - 1];

      if (latestMessage && latestMessage.content) {
        // If it's a transcript, update the current transcript
        if (latestMessage.isTranscript) {
          setCurrentTranscript(latestMessage.content);
        } else {
          // If it's a regular message, add it to messages list
          const newMessage = {
            type: latestMessage.role === 'assistant' ? 'assistant' : 'user',
            text: latestMessage.content,
            timestamp: latestMessage.timestamp,
            animated: true
          };

          setMessages(prev => [...prev, newMessage]);
          // Clear the transcript when a new message is added
          setCurrentTranscript('');
        }
      }
    }
  }, [messageHistory]);

  // Track dossier data changes
  useEffect(() => {
    console.log("🔄 VapiCall component received dossier update:", dossierData);

    if (typeof onDossierUpdate === 'function' && Object.keys(dossierData).length > 0) {
      onDossierUpdate(dossierData);
    }

    // Force a re-render of the dossier items
    const dossierItems = document.querySelector('.dossier-items');
    if (dossierItems) {
      // Add a temporary class to trigger a re-render animation
      dossierItems.classList.add('updating');
      setTimeout(() => {
        dossierItems.classList.remove('updating');
      }, 100);
    }
  }, [dossierData, onDossierUpdate]);

  // Add CSS for the update animation
  useEffect(() => {
    // Add the CSS if it doesn't exist
    if (!document.querySelector('#dossier-animation-style')) {
      const style = document.createElement('style');
      style.id = 'dossier-animation-style';
      style.textContent = `
        .dossier-items.updating {
          animation: dossier-update 0.3s ease-in-out;
        }
        @keyframes dossier-update {
          0% { opacity: 0.7; transform: scale(0.98); }
          100% { opacity: 1; transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }

    // Cleanup
    return () => {
      const style = document.querySelector('#dossier-animation-style');
      if (style) {
        style.remove();
      }
    };
  }, []);

  // Notify parent of status changes
  useEffect(() => {
    if (typeof onStatusChange === 'function') {
      onStatusChange(status);
    }

    // Add or remove vapi-call-active class on body
    if (status === CALL_STATUS.CONNECTED) {
      document.body.classList.add('vapi-call-active');
    } else {
      document.body.classList.remove('vapi-call-active');
    }
  }, [status, onStatusChange]);

  // Force visibility of the container when it's hidden
  const forceVisibility = () => {
    if (containerRef.current) {
      containerRef.current.style.display = 'flex';
      containerRef.current.style.visibility = 'visible';
      containerRef.current.style.opacity = '1';
    }
  };

  // Function wrapper for ending the call
  const handleEndCall = () => {
    stopCall();

    // Pass the dossier data back to the parent component
    if (typeof onEndCall === 'function') {
      onEndCall(dossierData);
    }
  };

  // Render a message
  const renderMessage = (message, index) => (
    <div
      key={`message-${index}`}
      className={`message-bubble ${message.type} ${message.animated ? 'animated' : ''}`}
      style={{
        opacity: (message.isTranscript && message.text.trim() === '') ? 0 : 1
      }}
    >
      <div className="message-content">
        {message.animated ? (
          <TextShimmerWave text={message.text} shimmerColor="#4488ff" delay={50} />
        ) : (
          message.text
        )}
      </div>
    </div>
  );

  // Main render
  return (
    <div
      ref={containerRef}
      className={`vapi-call-container ${status === CALL_STATUS.CONNECTED ? 'connected' : ''}`}
      onClick={forceVisibility}
    >
      <div className="messages-container">
        {messages.map(renderMessage)}

        {/* Current transcript */}
        {currentTranscript && (
          <div className="message-bubble assistant transcript">
            <div className="message-content">
              <TextShimmerWave text={currentTranscript} shimmerColor="#44ccff" delay={30} />
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <div className="call-controls">
        <div className="call-status-indicators">
          <VolumeLevel level={volumeLevel} />
          <AssistantSpeechIndicator speaking={assistantIsSpeaking} />
        </div>

        <button
          className="end-call-button"
          onClick={handleEndCall}
        >
          End Call
        </button>
      </div>
    </div>
  );
};

// Export the component with DevTools if available
export default withDevTools(VapiCall, {
  displayName: 'VapiCall',
  type: 'component',
  description: 'Handles voice call interaction with Vapi.ai'
});