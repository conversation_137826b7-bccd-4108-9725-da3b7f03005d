{"version": 3, "mappings": "umFAGA,MAAMA,GAAiB,CAAC,CAAE,SAAAC,EAAU,qBAAAC,EAAsB,eAAAC,EAAgB,UAAAC,KAAgB,CACxF,KAAM,CAACC,EAAaC,CAAc,EAAIC,WAAS,EAAK,EAC9C,CAACC,EAAiBC,CAAkB,EAAIF,WAAS,EAAK,EACtD,CAACG,EAAqBC,CAAsB,EAAIJ,WAAS,EAAK,EAC9D,CAACK,EAAoBC,CAAqB,EAAIN,WAAS,EAAK,EAC5D,CAACO,EAAiBC,CAAkB,EAAIR,WAAS,EAAK,EACtD,CAACS,EAAmBC,CAAoB,EAAIV,WAAS,EAAK,EAC1D,CAACW,EAAqBC,CAAsB,EAAIZ,WAAS,EAAK,EACpBA,WAAS,EAAE,EAC3D,KAAM,CAACa,EAAWC,CAAY,EAAId,EAAA,SAAS,CAAE,GACvC,CAACe,EAAOC,CAAQ,EAAIhB,EAAA,SAAS,CAAE,GAC/B,CAACiB,EAAaC,CAAc,EAAIlB,EAAA,SAAS,CAAE,GAE3CmB,EAAaC,SAAO,IAAI,EACxBC,EAAmBD,SAAO,IAAI,EAGpCE,YAAU,IAAM,CACV5B,EACc6B,IAEAC,IAClB,EACC,CAAC9B,CAAQ,CAAC,EAGb,MAAM6B,EAAkB,IAAM,CAE5BxB,EAAe,EAAI,EAGnB,WAAW,IAAMK,EAAuB,EAAI,EAAG,GAAG,EACvC,eAAMqB,KAAmB,GAAG,EACvC,WAAW,IAAMnB,EAAsB,EAAI,EAAG,GAAG,EACjD,WAAW,IAAME,EAAmB,EAAI,EAAG,GAAG,EAC9C,WAAW,IAAM,CACfE,EAAqB,EAAI,EACLgB,KACnB,GAAI,EACP,WAAW,IAAMd,EAAuB,EAAI,EAAG,IAAI,EAGnD,WAAW,IAAM,CACXjB,GACmBA,KAEtB,IAAI,GAIH6B,GAAkB,IAAM,CAC5BzB,EAAe,EAAK,EACpBG,EAAmB,EAAK,EACxBE,EAAuB,EAAK,EAC5BE,EAAsB,EAAK,EAC3BE,EAAmB,EAAK,EACxBE,EAAqB,EAAK,EAC1BE,EAAuB,EAAK,EAC5BE,EAAa,CAAE,GACfE,EAAS,CAAE,GACXE,EAAe,CAAE,IAIbO,GAAkB,IAAM,CAC5B,MAAME,EAAe,GAGrB,QAASC,GAAI,EAAGA,GAAI,GAAeA,KAAK,CACtC,MAAMC,GAAQ,KAAK,OAAO,EAAI,KAAK,GAAK,EAClCC,EAAW,IAAM,KAAK,SAAW,IACjCC,GAAW,EAAI,KAAK,SAAW,EAC/BC,GAAQ,KAAK,SAAW,GACxBC,GAAO,EAAI,KAAK,SAAW,EAE3BC,EAAW,CACf,GAAIN,GACJ,MAAO,CACL,IAAK,MACL,KAAM,MACN,MAAO,GAAGK,EAAI,KACd,OAAQ,GAAGA,EAAI,KACf,UAAW,mCAAmC,KAAK,IAAIJ,EAAK,EAAIC,CAAQ,OAAO,KAAK,IAAID,EAAK,EAAIC,CAAQ,MACzG,QAAS,EACT,UAAW,gBAAgBC,EAAQ,sBACnC,eAAgB,GAAGC,EAAK,GAC1B,GAGFL,EAAa,KAAKO,CAAQ,CAC5B,CAEApB,EAAaa,CAAY,GAIrBD,EAAsB,IAAM,CAChC,GAAI,CAACL,EAAiB,QAAS,OAE/B,MAAMc,EAAYd,EAAiB,QAC7B,CAAE,MAAAe,EAAO,OAAAC,EAAO,EAAIF,EAAU,sBAAsB,EAGpDG,GAAY,GACZC,EAAW,GACXC,GAAUJ,EAAQ,EAClBK,GAAUJ,GAAS,EAGzB,QAAST,EAAI,EAAGA,EAAIU,GAAWV,IAAK,CAE5B,MAAAC,EAASD,EAAIU,GAAa,KAAK,GAAK,GAAK,KAAK,OAAO,EAAI,GAAM,KAC/DR,EAAW,GAAK,KAAK,UAAY,KAAK,IAAIM,EAAOC,EAAM,EAAI,KAC3DK,GAAIF,GAAU,KAAK,IAAIX,CAAK,EAAIC,EAChCa,EAAIF,GAAU,KAAK,IAAIZ,CAAK,EAAIC,EAGhCG,EAAO,EAAI,KAAK,SAAW,EAC3BW,EAAa,KAAK,SAAW,EAC7BC,GAAgB,IAAM,KAAK,SAAW,EAE5CN,EAAS,KAAK,CACZ,GAAIX,EACJ,EAAAc,GACA,EAAAC,EACA,KAAAV,EACA,MAAO,CACL,IAAK,GAAGU,CAAC,KACT,KAAM,GAAGD,EAAC,KACV,MAAO,GAAGT,CAAI,KACd,OAAQ,GAAGA,CAAI,KACf,eAAgB,GAAGW,CAAU,IAC7B,kBAAmB,GAAGC,EAAa,IACnC,UAAW,OAAOZ,EAAO,CAAC,6BAC1B,WAAY,4GACd,EACD,CACH,CAEAjB,EAASuB,CAAQ,EAGjB,MAAMO,GAAiB,GAGvB,QAASlB,EAAI,EAAGA,EAAIW,EAAS,OAAQX,IAAK,CAClC,MAAAmB,EAAYR,EAASX,CAAC,EAGtBoB,EAAYT,EACf,OAAeU,KAAK,KAAOF,EAAU,EAAE,EACvC,IAAYE,GAAA,CACL,MAAAC,EAAKD,EAAK,EAAIF,EAAU,EACxBI,EAAKF,EAAK,EAAIF,EAAU,EACvB,OACL,KAAAE,EACA,SAAU,KAAK,KAAKC,EAAKA,EAAKC,EAAKA,CAAE,EACvC,CACD,EACA,KAAK,CAACC,EAAGC,IAAMD,EAAE,SAAWC,EAAE,QAAQ,EAGnCC,GAAe,EAAI,KAAK,MAAM,KAAK,SAAW,CAAC,EAC5C,QAAAC,EAAI,EAAGA,EAAI,KAAK,IAAID,GAAcN,EAAU,MAAM,EAAGO,IAAK,CAC3D,MAAAC,EAAUR,EAAUO,CAAC,EAAE,KACvBL,EAAKM,EAAQ,EAAIT,EAAU,EAC3BI,GAAKK,EAAQ,EAAIT,EAAU,EAC3BjB,EAAW,KAAK,KAAKoB,EAAKA,EAAKC,GAAKA,EAAE,EACtCtB,EAAQ,KAAK,MAAMsB,GAAID,CAAE,GAAK,IAAM,KAAK,IAGzCO,GAAU,GAAM,KAAK,SAAW,GAChCC,GAAY,KAAK,SAAW,EAC5BC,GAAe,EAAI,KAAK,SAAW,EAEzCb,GAAe,KAAK,CAClB,GAAI,GAAGC,EAAU,EAAE,IAAIS,EAAQ,EAAE,GACjC,MAAO,CACL,IAAK,GAAGT,EAAU,EAAIA,EAAU,KAAO,CAAC,KACxC,KAAM,GAAGA,EAAU,EAAIA,EAAU,KAAO,CAAC,KACzC,MAAO,GAAGjB,CAAQ,KAClB,UAAW,UAAUD,CAAK,OAC1B,QAAA4B,GACA,eAAgB,GAAGC,EAAS,IAC5B,kBAAmB,GAAGC,EAAY,IAClC,OAAQ,MACR,WAAY,6CAA6CF,EAAO,4BAA4BA,GAAU,EAAG,SAC3G,EACD,CACH,CACF,CAEAvC,EAAe4B,EAAc,GAI7B,OAAAc,EAAA,KAAC,OACC,IAAKzC,EACL,UAAW,2BAA2BrB,EAAc,SAAW,EAAE,GAKhE,UAAAK,SACE,MAAI,WAAW,oBAAoBA,EAAsB,SAAW,EAAE,GAAI,QAI5E,MAAI,WAAU,sBACZ,SAAAU,EAAU,IACTqB,GAAA2B,EAAA,IAAC,OAEC,UAAU,WACV,MAAO3B,EAAS,OAFX,YAAYA,EAAS,EAAE,EAI/B,GACH,EAGA0B,EAAA,KAAC,OACC,IAAKvC,EACL,UAAW,kBAAkBZ,EAAoB,SAAW,EAAE,GAE7D,UAAAM,EAAM,IACLkC,GAAAY,EAAA,IAAC,OAEC,UAAU,aACV,MAAOZ,EAAK,OAFP,QAAQA,EAAK,EAAE,GAIvB,EAEAhC,EAAY,IACX6C,GAAAD,EAAA,IAAC,OAEC,UAAU,kBACV,MAAOC,EAAW,OAFb,cAAcA,EAAW,EAAE,GAInC,GACH,SAGC,MAAI,WAAW,mBAAmBzD,EAAqB,SAAW,EAAE,GAAI,+CAEtE,OAAK,WAAW,uBAAuBE,EAAkB,SAAW,EAAE,GACrE,UAACsD,EAAA,YAAK,UAAU,MAAM,SAAC,MACtBA,EAAA,YAAK,UAAU,MAAM,SAAC,MACtBA,EAAA,YAAK,UAAU,MAAM,SAAC,OACzB,GACF,SAGC,MAAI,WAAW,oBAAoBlD,EAAsB,SAAW,EAAE,GACrE,UAACkD,MAAA,OAAI,UAAU,aAAc,GAC5BA,EAAA,WAAI,UAAU,cAAc,SAA8B,oCAC7D,IAGN,ECpPA,OAAOE,GAAE,KAAK,QAAQ,UAAU,YAChCA,GAAE,KAAK,QAAQ,aAAa,CAC1B,QAASC,GACT,cAAeC,GACf,UAAWC,EACb,CAAC,EAGD,IAAIC,GAAiBC,GACrB,GAAI,CAEFD,GAAkB,IAAiD,+CACnEC,GAAkB,IAAiD,8CACrE,OAASC,EAAO,CACN,cAAM,2BAA4BA,CAAK,EAE7BF,GAAA,8DACAC,GAAA,6DACpB,CAGA,MAAME,GAAoB,CAAC,QAAS,QAAQ,EACtCC,GAAkB,EAGlBC,GAAeT,GAAE,KAAK,CAC1B,QAASI,GACT,SAAU,CAAC,GAAI,EAAE,EACjB,WAAY,CAAC,GAAI,EAAE,EACnB,YAAa,CAAC,EAAG,GAAG,CACtB,CAAC,EAGKM,GAAeV,GAAE,KAAK,CAC1B,QAASK,GACT,SAAU,CAAC,GAAI,EAAE,EACjB,WAAY,CAAC,GAAI,EAAE,EACnB,YAAa,CAAC,EAAG,GAAG,CACtB,CAAC,EAGD,SAAS,iBAAiB,mBAAoB,IAAM,CAEnB,SAAS,iBAAiB,2BAA2B,EAC7D,QAAeM,GAAA,CACpCA,EAAI,QAAU,UAAW,CACvB,KAAK,IAAM,uBACb,CACD,EAG8B,SAAS,iBAAiB,2BAA2B,EAC7D,QAAeA,GAAA,CACpCA,EAAI,QAAU,UAAW,CACvB,KAAK,IAAM,uBACb,CACD,CACH,CAAC,EAOD,MAAMC,GAAiB,CAAC,CAAE,UAAAC,EAAW,SAAAC,EAAU,UAAAC,EAAY,MAAS,CAC5D,MAAAC,EAAkB3D,SAAO,IAAI,EAC7B4D,EAAiB5D,SAAO,IAAI,EAC5B6D,EAAa7D,SAAO,CACxB,UAAW,CAAC,EACZ,eAAgB,KACjB,EAGDE,mBAAU,IAAM,CACV,MAACsD,GAAa,CAACG,EAAgB,SAG/B,OAACC,EAAe,UAElBA,EAAe,QAAUjB,GAAE,IAAIgB,EAAgB,QAAS,CAEtD,OAAQT,GACR,KAAMC,GACN,YAAa,GACb,mBAAoB,GACrB,EAGDR,GAAE,UAAU,qDAAsD,CAChE,QAAS,GACT,UAAW,YACZ,GAAE,MAAMiB,EAAe,OAAO,GAI7BH,GAAYA,EAAS,KAAOA,EAAS,MACxBG,EAAA,QAAQ,QAAQ,CAACH,EAAS,IAAKA,EAAS,GAAG,EAAG,EAAE,EAG3D,CAACI,EAAW,QAAQ,gBAAkBJ,EAAS,KAAOA,EAAS,MACtDI,EAAA,QAAQ,eAAiBlB,GAAE,OAAO,CAACc,EAAS,IAAKA,EAAS,GAAG,EAAG,CAAE,KAAMJ,EAAc,GAC9F,MAAMO,EAAe,OAAO,EAC5B,UAAUH,EAAS,SAAW,eAAe,IAK7C,IAAM,CACPG,EAAe,UACjBA,EAAe,QAAQ,SACvBA,EAAe,QAAU,KACzBC,EAAW,QAAU,CACnB,UAAW,CAAC,EACZ,eAAgB,MAEpB,CACF,EACC,CAACL,EAAWC,CAAQ,CAAC,EAGxBvD,YAAU,IAAM,CACV,CAAC0D,EAAe,SAAW,CAACF,EAAU,SAG/BG,EAAA,QAAQ,UAAU,QAAkBC,GAAA,CACzCF,EAAe,SACFA,EAAA,QAAQ,YAAYE,CAAM,CAC3C,CACD,EACUD,EAAA,QAAQ,UAAY,GAG/BH,EAAU,QAAoBK,GAAA,CACxB,GAAAA,EAAS,UAAYA,EAAS,UAAW,CACrC,MAAAD,EAASnB,GAAE,OAAO,CAACoB,EAAS,SAAUA,EAAS,SAAS,EAAG,CAAE,KAAMX,EAAc,GACpF,MAAMQ,EAAe,OAAO,EAC5B,UAAU,WAAWG,EAAS,IAAI,gBAAgBA,EAAS,cAAgB,EAAE,EAAE,EAEvEF,EAAA,QAAQ,UAAU,KAAKC,CAAM,CAC1C,EACD,IACA,CAACJ,CAAS,CAAC,EAGZjB,MAAC,MAAI,WAAU,mBACb,SAAAA,EAAA,IAAC,OAAI,IAAKkB,EAAiB,UAAU,eAAgB,EACvD,EAEJ,EC7JMK,GAAU,CAAC,CAAE,SAAAD,KAAe,CAChC,KAAM,CAACP,EAAWS,CAAY,EAAIrF,WAAS,EAAI,EAGzC6E,EAAWM,GAAU,UAAY,CACrC,IAAK,QACL,IAAK,SAIDL,EAAYK,EAAW,CAAC,CAC5B,KAAMA,EAAS,MAAQ,WACvB,aAAcA,EAAS,cAAgB,mBACvC,UAAWA,EAAS,UACpB,SAAUA,EAAS,UAAU,IAC7B,UAAWA,EAAS,UAAU,IAC9B,SAAUA,EAAS,UAAY,MAC/B,MAAOA,EAAS,MAChB,MAAOA,EAAS,MAChB,QAASA,EAAS,QAClB,QAASA,EAAS,OACnB,GAAI,GAGH,OAAAvB,EAAA,KAAC,MAAI,WAAU,qBACb,UAACC,MAAA,OAAI,UAAU,eACb,SAAAA,EAAA,IAAC,UACC,UAAU,oBACV,QAAS,IAAMwB,EAAa,CAACT,CAAS,EAErC,WAAY,WAAa,aAE9B,EAEAf,EAAA,IAACc,GAAA,CACC,UAAAC,EACA,SAAAC,EACA,UAAAC,CAAA,CACF,CACF,GAEJ,EAGeQ,GAAAC,GAAaH,GAAS,CACnC,YAAa,UACb,KAAM,YACN,YAAa,oEACb,eAAgB,CAAC,wBAAyB,0BAA0B,CACtE,CAAC,EClDKI,GAAkB,CAAC,CAAE,SAAAL,KACpBA,EAUHvB,EAAA,KAAC,MAAI,WAAU,mBACb,UAAAC,MAAC,MAAG,SAAoB,yBAExBD,OAAC,MAAI,WAAU,kBACb,UAACC,EAAA,UAAI,SAASsB,EAAA,MAAQ,WAAW,EACjCtB,MAAC,KAAE,UAAU,gBAAiB,WAAS,cAAgBsB,EAAS,WAAa,kBAAmB,GAE/FA,EAAS,UACPvB,OAAA,KAAE,UAAU,WACX,UAAAC,MAAC,UAAO,SAAS,cAAS,IAAEsB,EAAS,SAAS,UAChD,GAEJ,EAEAvB,OAAC,MAAI,WAAU,kBACb,UAAAC,MAAC,MAAG,SAAmB,wBACvBD,OAAC,KAAG,WAAU,eACX,UAASuB,EAAA,cACP,KACC,WAAAtB,MAAC,UAAO,SAAM,WAAS,IAACA,MAAC,KAAE,KAAM,OAAOsB,EAAS,KAAK,GAAK,WAAS,KAAM,IAC5E,EAGDA,EAAS,OACRvB,OAAC,KACC,WAAAC,MAAC,UAAO,SAAM,WAAS,IAACA,MAAC,KAAE,KAAM,UAAUsB,EAAS,KAAK,GAAK,WAAS,KAAM,IAC/E,EAGDA,EAAS,SACRvB,OAAC,KACC,WAAAC,MAAC,UAAO,SAAQ,aAAS,IAACA,MAAC,KAAE,KAAMsB,EAAS,QAAS,OAAO,SAAS,IAAI,sBAAsB,SAAa,mBAC9G,GAEJ,GACF,EAECA,EAAS,SACPvB,OAAA,OAAI,UAAU,kBACb,UAAAC,MAAC,MAAG,SAAc,mBAClBA,MAAC,UAAS,UAAAsB,EAAS,OAAQ,IAC7B,EAGDA,EAAS,KACPvB,OAAA,OAAI,UAAU,kBACb,UAAAC,MAAC,MAAG,SAAK,UACTA,MAAC,IAAG,UAAAsB,EAAS,GAAI,IACnB,EAGDA,EAAS,WAAaA,EAAS,UAAU,OAAS,GACjDvB,EAAA,KAAC,MAAI,WAAU,kBACb,UAAAC,MAAC,MAAG,SAAkB,uBACrBA,MAAA,MAAG,UAAU,iBACX,WAAS,UAAU,IAAI,CAAC4B,EAAMC,IAC5B7B,MAAA,MAAgB,SAAR4B,GAAAC,CAAa,CACvB,EACH,GACF,EAGF7B,MAAC,OAAI,UAAU,kBACb,eAAC,SAAO,WAAU,iBAAiB,iCAAqB,CAC1D,EACF,IAzEED,EAAA,KAAC,MAAI,WAAU,iCACb,UAAAC,MAAC,MAAG,SAAoB,yBACxBA,MAAC,KAAE,SAA+C,mDACpD,IA2ES8B,GAAAJ,GAAaC,GAAiB,CAC3C,YAAa,kBACb,KAAM,UACN,YAAa,0DACb,eAAgB,CAAC,2BAA4B,sBAAuB,mBAAmB,CACzF,CAAC,ECtFKI,GAAc,CAAC,CAAE,KAAAC,KAChBA,EAUHjC,EAAA,KAAC,MAAI,WAAU,eACb,UAAAC,MAAC,MAAG,SAAY,iBAEhBD,OAAC,MAAI,WAAU,kBACb,UAAAC,MAAC,MAAG,SAA8B,mCAClCA,MAAC,KAAE,SAAyD,+DAC9D,EAEAD,OAAC,MAAI,WAAU,kBACZ,UAAAiC,EAAK,OACJjC,OAAC,MAAI,WAAU,eACb,UAAAC,MAAC,UAAO,SAAY,iBAAS,IAAEgC,EAAK,OACtC,EAGDA,EAAK,cACHjC,OAAA,OAAI,UAAU,eACb,UAAAC,MAAC,UAAO,SAAc,mBAAS,IAAEgC,EAAK,cACxC,EAGDA,EAAK,UAAU,SACbjC,EAAA,YAAI,UAAU,eACb,UAAAC,MAAC,UAAO,SAAS,cAAS,IAAEgC,EAAK,SAAS,SAC5C,EAGDA,EAAK,YACHjC,OAAA,OAAI,UAAU,eACb,UAAAC,MAAC,UAAO,SAAa,kBAAS,IAAEgC,EAAK,YACvC,EAGDA,EAAK,SACHjC,OAAA,OAAI,UAAU,eACb,UAAAC,MAAC,UAAO,SAAc,mBAAS,IAAEgC,EAAK,SACxC,GAEJ,EAEAjC,OAAC,MAAI,WAAU,aACb,UAAAC,MAAC,MAAG,SAAU,eACbgC,EAAK,UAAY,OAChBhC,MAAC,KAAE,SAIH,gMAECA,MAAA,KAAE,SAIH,4KAEJ,EAEAD,OAAC,MAAI,WAAU,kBACb,UAACC,EAAA,cAAO,UAAU,uBAAuB,SAAa,kBACrDA,EAAA,cAAO,UAAU,0BAA0B,SAAY,kBAC1D,CACF,IArEED,EAAA,KAAC,MAAI,WAAU,qBACb,UAAAC,MAAC,MAAG,SAAyB,8BAC7BA,MAAC,KAAE,SAAiD,qDACtD,IAuESiC,GAAAP,GAAaK,GAAa,CACvC,YAAa,cACb,KAAM,UACN,YAAa,wDACb,eAAgB,CAAC,uBAAwB,2BAA4B,qBAAqB,CAC5F,CAAC,ECpFKG,GAAS,CAAC,CAAE,YAAAC,KAAkB,CAClC,KAAM,CAACC,EAAYC,CAAa,EAAIlG,WAAS,EAAK,EAC5C6E,EAAWsB,KACXC,EAAkBhF,SAAO,IAAI,EAG7BiF,EAAYC,KACZC,EAAsBF,IAAc,WAChBA,IAAc,OACdA,IAAc,IACdA,IAAc,KAElCG,EAAa,IAAM,CACvBN,EAAc,CAACD,CAAU,GAMzB,OAAArC,EAAA,KAAC,OAAI,UAAW,iBAAiBqC,EAAa,cAAgB,EAAE,GAAI,IAAKG,EACvE,UAAAxC,EAAA,KAAC,UACC,UAAW,kBAAkBqC,EAAa,SAAW,EAAE,GACvD,QAASO,EACT,aAAW,yBACX,gBAAeP,EAEf,UAAApC,EAAA,IAAC,OAAK,UACL,OAAK,UACL,OAAK,KACR,EAEAA,MAAC,OAAI,UAAW,YAAYoC,EAAa,SAAW,EAAE,GACpD,SAAArC,EAAA,KAAC,KAEE,YAAC2C,GAEE3C,OAAA6C,EAAA,oBAAA5C,MAAC,KACC,UAAAD,EAAA,KAAC8C,GAAA,CACC,GAAG,QACH,UAAW7B,EAAS,WAAa,SAAWA,EAAS,WAAa,IAAM,SAAW,GACnF,QAAS,IAAMqB,EAAc,EAAK,EAClC,YAAU,OAEV,UAACrC,MAAA,KAAE,UAAU,sBAAuB,GAAI,UAG5C,QACC,KACC,UAAAD,EAAA,KAAC8C,GAAA,CACC,GAAG,QACH,UAAW7B,EAAS,WAAa,QAAU,SAAW,GACtD,QAAS,IAAMqB,EAAc,EAAK,EAClC,YAAU,QAEV,UAACrC,MAAA,KAAE,UAAU,uBAAwB,GAAI,WAG7C,GACF,QAGD,KACC,UAAAD,EAAA,KAAC8C,GAAA,CACC,GAAG,SACH,UAAW7B,EAAS,WAAa,SAAW,SAAW,GACvD,QAAS,IAAMqB,EAAc,EAAK,EAClC,YAAU,QAEV,UAACrC,MAAA,KAAE,UAAU,6BAA8B,GAAI,WAGnD,GAGF,CACF,GAGAA,EAAA,IAAC,OACC,UAAW,eAAeoC,EAAa,SAAW,EAAE,GACpD,QAAS,IAAMC,EAAc,EAAK,EAClC,cAAY,OACd,CACF,GAEJ,ECnFsB,OAAO,OAAO,cCJpC,MAAMS,GAAqB,UAEtB,MAAI,WAAU,cACb,SAAC/C,EAAA,YAAI,UAAU,sBACb,UAAAC,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,QACP,OAAQ,QACR,KAAM,MACN,IAAK,MACL,WAAY,kFACd,EACF,EACAA,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,QACP,OAAQ,QACR,KAAM,MACN,IAAK,MACL,WAAY,kFACd,EACF,EACAA,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,QACP,OAAQ,QACR,KAAM,MACN,IAAK,MACL,WAAY,kFACd,EACF,EACAA,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,QACP,OAAQ,QACR,KAAM,MACN,IAAK,MACL,WAAY,kFACd,EACF,EAEAA,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,QACP,OAAQ,QACR,KAAM,MACN,IAAK,MACL,WAAY,mFACZ,eAAgB,IAClB,EACF,EACAA,EAAA,IAAC,OACC,UAAU,SACV,MAAO,CACL,MAAO,QACP,OAAQ,QACR,KAAM,MACN,IAAK,MACL,WAAY,mFACZ,eAAgB,IAClB,EACF,EACF,EACF,GCnEJ,MAAM+C,GAAc,CAAC,CAAE,OAAAC,EAAQ,SAAAC,KAE3BjD,MAAC,MAAI,WAAU,yBACb,SAAAD,EAAA,KAAC,UACC,UAAW,gBAAgBiD,EAAS,OAAS,OAAO,GACpD,QAASC,EACT,aAAW,eAEX,UAAClD,OAAA,OAAI,UAAU,eACb,UAAAA,OAAC,OAAI,UAAU,WAAW,MAAM,6BAA6B,QAAQ,YAAY,KAAK,OAAO,OAAO,eAAe,YAAY,IAAI,cAAc,QAAQ,eAAe,QACtK,UAAAC,MAAC,UAAO,GAAG,KAAK,GAAG,KAAK,EAAE,IAAG,EAC7BA,MAAC,QAAK,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,GACnCA,MAAC,QAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GACrCA,MAAC,QAAK,GAAG,OAAO,GAAG,OAAO,GAAG,OAAO,GAAG,MAAM,GAC7CA,MAAC,QAAK,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,OAAO,GACjDA,MAAC,QAAK,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,GACnCA,MAAC,QAAK,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG,IAAI,GACrCA,MAAC,QAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,OAAO,GAC/CA,MAAC,QAAK,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,IACjD,EACAA,MAAC,OAAI,UAAU,YAAY,MAAM,6BAA6B,QAAQ,YAAY,KAAK,OAAO,OAAO,eAAe,YAAY,IAAI,cAAc,QAAQ,eAAe,QACvK,SAACA,EAAA,YAAK,EAAE,kDAAiD,CAC3D,IACF,EACAA,MAAC,MAAI,WAAU,eAAgB,IAEnC,ICzBJ,MAAMkD,GAAe,CAAC,CAAE,QAAAC,KAGlBA,EAECnD,EAAA,cAAO,UAAU,iBAAiB,QAAAmD,EAAkB,SAErD,0BAMDN,GAAK,IAAG,SAAS,UAAU,iBAAiB,SAE7C,oBCJJ,MAAMO,GAAoB,CAAC,CAEzB,SAAAC,EACA,QAAAC,EACA,OAAAC,EACA,eAAAC,EAGA,aAAAC,EACA,eAAAC,EACA,YAAAC,EACA,gBAAAC,EACA,kBAAAC,EACA,WAAAC,EACA,cAAAC,EACA,8BAAAC,EACA,oBAAAC,EAGA,oBAAAC,EACA,eAAAC,EACA,qBAAAC,EACA,cAAAC,EACA,eAAAC,EACA,cAAAC,EAGA,iBAAAC,EACA,YAAAC,EACA,kBAAAC,EACA,QAAAC,EACA,QAAAC,EACA,UAAApC,EAAY,UAGZ,aAAAqC,EACA,cAAAC,EACA,qBAAAC,GACA,qBAAAC,GAGA,MAAAC,EAAQ,MACV,IAAM,CACJ,KAAM,CAACC,EAAYC,CAAa,EAAIhJ,WAAS,EAAK,EAC5C,CAACiJ,GAAWC,EAAY,EAAIlJ,WAAS,EAAK,EAC1C,CAACmJ,EAAmBC,EAAoB,EAAIpJ,WAAS,EAAI,EAEzD,CAACqJ,GAAgBC,EAAiB,EAAItJ,WAAS,EAAK,EACpD,CAACuJ,EAAaC,CAAc,EAAIxJ,WAAS,CAAC,EAG1CyJ,EAAYC,GAAQ,CAElBA,IAAI,QAAQ,IAAK,EAAE,EAGzB,MAAMC,EAAI,SAASD,EAAI,UAAU,EAAG,CAAC,EAAG,EAAE,EACpCE,GAAI,SAASF,EAAI,UAAU,EAAG,CAAC,EAAG,EAAE,EACpCrG,GAAI,SAASqG,EAAI,UAAU,EAAG,CAAC,EAAG,EAAE,EAE1C,MAAO,GAAGC,CAAC,KAAKC,EAAC,KAAKvG,EAAC,IAInBwG,GAAoBC,GAAa,CAErC,IAAIJ,EAAMI,EAAS,QAAQ,IAAK,EAAE,EAGlC,MAAMH,GAAI,SAASD,EAAI,UAAU,EAAG,CAAC,EAAG,EAAE,EACpCE,GAAI,SAASF,EAAI,UAAU,EAAG,CAAC,EAAG,EAAE,EACpCrG,GAAI,SAASqG,EAAI,UAAU,EAAG,CAAC,EAAG,EAAE,EAMnC,OAHY,KAAQC,GAAI,KAAQC,GAAI,KAAQvG,IAAK,IAGrC,GAAM,UAAY,WAIjC0G,EAAY,IAAM,CAEtB,GAAIV,IAAkBN,EAAY,CAChC,QAAQ,IAAI,mFAAmF,EAC/F,MACF,CAEQ,YAAI,uDAAwDR,CAAiB,EAGrFe,GAAkB,EAAI,EACtBJ,GAAa,EAAI,EAGFM,EAAAQ,GAAQA,EAAO,CAAC,EAG/B,WAAW,IAAM,CACfhB,EAAc,EAAI,EAClBE,GAAa,EAAK,EAGlB,WAAW,IAAM,CACfI,GAAkB,EAAK,GACtB,GAAI,GACN,GAAG,GAIFW,EAAU,IAAM,CACpBjB,EAAc,EAAK,GAIfkB,EAAkBC,GAClBA,GAAO,OAAOA,GAAQ,UAAYA,EAAI,SAAS,4BAA4B,GACrE,YAAI,4CAA6CA,CAAG,EACrD,sBAEFC,GAAgBD,CAAG,GAAK,qBAKjC7I,YAAU,IAAM,CACd8H,GAAqB,EAAI,EACzB,QAAQ,IAAI,wEAAwE,CACtF,EAAG,CAAE,GAGL9H,YAAU,IAAM,CACd,QAAQ,IAAI,2BAA4B,CACtC,SAAA4F,EACA,aAAAI,EACA,eAAAC,EACA,YAAAC,EACA,oBAAqBO,EAAsBA,EAAoB,UAAU,EAAG,GAAG,EAAI,MAAQ,OAC3F,kBAAAoB,CAAA,CAED,GACA,CAACjC,EAAUI,EAAcC,EAAgBC,EAAaO,EAAqBoB,CAAiB,CAAC,EAGhG,MAAMkB,GAAgB,CACpB,kBAAmB/C,EACnB,sBAAuBmC,EAASnC,CAAY,EAC5C,oBAAqBC,EACrB,wBAAyBkC,EAASlC,CAAc,EAChD,iBAAkBC,EAClB,qBAAsBiC,EAASjC,CAAW,EAC1C,sBAAuBqC,GAAiBrC,CAAW,EACnD,qBAAsB,QAAQiC,EAAShC,CAAe,CAAC,KAAKC,CAAiB,IAC7E,0BAA2B,QAAQ+B,EAAS3B,CAAmB,CAAC,KAAKD,CAA6B,IAClG,mBAAoBD,CAAA,EAGtB,OACG/D,EAAA,WAAI,UAAW,sBAAsBiF,CAAK,SAAU,MAAOuB,GAC1D,SAAAxG,MAAC,MAAI,WAAU,kBACZ,SAACkF,EAgFAlF,MAAC,MAAI,WAAU,yBACb,SAAAA,EAAA,IAACyG,GAAA,CAEC,UAAWL,EACX,UAAA5D,EACA,OAAQ,CAACkC,EACT,aAAcA,GAAqB,IAAM,CACvC,MAAM1C,EAAO,CACX,UAAWqB,EACX,kBAAmBmB,EACnB,aAAcC,EACd,gBAAiBN,EACjB,kBAAAO,EACA,SAAUC,EACV,SAAUC,EACV,UAAApC,EAEA,cAAeiB,EACf,gBAAiBC,EAEjB,cAAemB,EACf,eAAgBC,EAChB,uBAAwBC,GACxB,uBAAwBC,EAAA,EAE1B,eAAQ,IAAI,yEAA0E,CACpF,cAAevB,EACf,gBAAiBC,EACjB,SAAAL,EACA,UAAAb,CAAA,CACD,EACMR,MACF,KACP,mBAAoB,CAClB,SAAAqB,EACA,iBAAAmB,EACA,YAAAC,EACA,eAAgBN,GAAkB,yBAAyBd,CAAQ,8BACnE,YAAaqB,EACb,QAAAC,EACA,QAAAC,CACF,GAvCKc,CAAA,EAyCT,EAvHE3F,EAAA,KAAA6C,WAAA,WAAC7C,OAAA,OAAI,UAAU,+BACb,UAACC,MAAA,OAAI,UAAU,+BACb,SAAAA,EAAA,IAAC0G,GAAA,CACC,cAAe,CAACC,EAAS,EACzB,cAAe,CAACC,EAAS,EACzB,WAAY,CAEV,GAAI,CAAC,CAAC,KAAAxH,EAAM,GAAGyH,CAAW,IAAA7G,EAAA,IAAC,KAAG,OAAO,CAAC,MAAOyD,GAAgB,SAAS,EAAI,GAAGoD,CAAO,GACpF,GAAI,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAW,IAAA7G,EAAA,IAAC,KAAG,OAAO,CAAC,MAAO0D,GAAkB,SAAS,EAAI,GAAGmD,CAAO,GACtF,GAAI,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAW,IAAA7G,EAAA,IAAC,KAAG,OAAO,CAAC,MAAO0D,GAAkB,SAAS,EAAI,GAAGmD,CAAO,GACtF,EAAG,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAW,IAAA7G,EAAA,IAAC,IAAE,OAAO,CAAC,MAAO2D,GAAe,SAAS,EAAI,GAAGkD,CAAO,GACjF,OAAQ,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAW,IAAA7G,EAAA,IAAC,SAAO,OAAO,CAAC,MAAOyD,GAAgB,SAAS,EAAI,GAAGoD,CAAO,GAC5F,GAAI,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAK,IAAO7G,MAAA,MAAG,MAAO,CAAC,UAAW,QAAQ,EAAI,GAAG6G,CAAO,GACvE,WAAY,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CACrB,IAAA7G,EAAA,IAAC,cACC,MAAO,CACL,WAAY,aAAa0D,GAAkB,SAAS,GACpD,YAAa,OACb,WAAY,EACZ,UAAW,QACb,EACC,GAAGmD,CAAA,CACN,EAEF,GAAI,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAK,IAAO7G,MAAA,MAAG,MAAO,CAAC,WAAY,QAAQ,EAAI,GAAG6G,CAAO,GACxE,GAAI,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAK,IAAO7G,MAAA,MAAG,MAAO,CAAC,WAAY,QAAQ,EAAI,GAAG6G,CAAO,GACxE,GAAI,CAAC,CAAC,KAAAzH,EAAM,GAAGyH,CAAK,IAAO7G,MAAA,MAAG,MAAO,CAAC,aAAc,QAAQ,EAAI,GAAG6G,CAAO,EAC5E,EAEC,SAAA3C,CAAA,GAEL,EAGCK,GAAiBA,EAAc,OAAS,GACtCxE,OAAA,OAAI,UAAU,yBACb,UAAAC,MAAC,MAAG,SAAc,mBACjBA,EAAA,UAAG,UAAU,sBACX,WAAc,IAAI,CAAC8G,EAAMjF,IACvB7B,MAAA,MAAgB,SAAR8G,GAAAjF,CAAa,CACvB,EACH,GACF,EAIDwC,GACCtE,EAAA,KAAC,MAAI,WAAU,yBACb,UAAAC,MAAC,MAAG,SAAe,oBACnBA,MAAC,KAAG,SAAcqE,CAAA,IACpB,EAIDC,GACCvE,EAAA,KAAC,MAAI,WAAU,qBACb,UAAAC,MAAC,MAAG,SAAuB,4BAC3BA,MAAC,IAAE,MAAMsE,EAAgB,OAAO,SAAS,IAAI,sBAAsB,UAAU,kBAAkB,SAE/F,yBACF,GAEJ,EAEAtE,MAAC,MAAI,WAAU,2BACb,SAAAA,EAAA,IAAC+G,GAAA,CACC,QAASb,EACT,MAAOpC,GAAc,qBACrB,OAAQuC,EAAe7C,GAAkBD,CAAM,EAC/C,UAAA6B,GACA,YAAAzB,EACA,cAAAI,CAAA,GAEJ,EACF,GA+CJ,CACF,EAEJ,ECpSMiD,GAAqB,IAAM,CAC/B,KAAM,CAACC,EAAgBC,CAAiB,EAAI/K,WAAS,IAAI,EACnD,CAACgL,EAASC,CAAU,EAAIjL,WAAS,EAAI,EACrC,CAACqE,EAAO6G,CAAQ,EAAIlL,WAAS,IAAI,EACjC,CAACmL,EAAoBC,CAAqB,EAAIpL,WAAS,EAAK,EAC5D,CAACqL,EAAYC,CAAa,EAAItL,WAAS,EAAK,EAElDsB,YAAU,IAAM,EACa,SAAY,CACjC,IACF,QAAQ,IAAI,wDAAwD,EAGpE,MAAM+E,EAAYC,KACV,YAAI,0CAA2CD,CAAS,EAGxD,YAAI,qDAAsDA,CAAS,EACrE,MAAAkF,EAAS,MAAMC,GAAuBnF,CAAS,EAWrD,GAVA,QAAQ,IAAI,mDAAoD,CAC9D,UAAW,CAAC,CAACkF,EACb,SAAUA,GAAQ,SAClB,kBAAmBA,GAAQ,kBAC3B,GAAIA,GAAQ,GACZ,UAAWA,GAAQ,UACnB,WAAYA,GAAQ,WACpB,WAAYA,EAAS,OAAO,KAAKA,CAAM,EAAI,CAAC,EAC7C,EAEGA,EAAQ,CAEV,MAAME,EAAmBF,EAAO,YACPA,EAAO,WAAa,iBAAmB,CAACA,EAAO,IAC/C,CAACA,EAAO,IAAMlF,IAAc,QAErD,QAAQ,IAAI,wCAAyC,CACnD,iBAAAoF,EACA,MAAO,CAAC,CAACF,EAAO,GAChB,SAAUA,EAAO,SACjB,UAAAlF,CAAA,CACD,EAGK,MAAAqF,EAAgBC,GAAqBJ,CAAM,EACjD,QAAQ,IAAI,8CAA+C,CACzD,SAAUG,EAAc,SACxB,UAAWA,EAAc,UACzB,gBAAiBA,EAAc,gBAC/B,eAAgBA,EAAc,eAC/B,EAEiBX,EAAA,CAChB,GAAGW,EAEH,gBAAiBH,EAAO,mBAAqBG,EAAc,gBAE3D,GAAGH,EAEH,iBAAAE,CAAA,CACD,EAGGA,GAAoBpF,IAAc,UACpC,QAAQ,IAAI,kFAAkF,EAC9F+E,EAAsB,EAAI,EAC5B,MAEA,QAAQ,KAAK,+DAA+D,EAE1DL,EAAA,CAChB,SAAU,gBACV,UAAW,gBACX,aAAc,UACd,eAAgB,UAChB,gBAAiB,UACjB,kBAAmB,GACnB,WAAY,qBACZ,cAAe,EACf,8BAA+B,GAC/B,oBAAqB,UACrB,eAAgB,oEAChB,qBAAsB,+EACtB,QAAS,qBACT,OAAQ,qBACR,MAAO,OACP,gBAAiB,KAClB,QAEIa,EAAK,CACJ,cAAM,sDAAuDA,CAAG,EACxEV,EAASU,EAAI,OAAO,EAGFb,EAAA,CAChB,SAAU,gBACV,UAAW,gBACX,aAAc,UACd,eAAgB,UAChB,gBAAiB,UACjB,kBAAmB,GACnB,WAAY,qBACZ,cAAe,EACf,8BAA+B,GAC/B,oBAAqB,UACrB,eAAgB,oEAChB,qBAAsB,+EACtB,QAAS,qBACT,OAAQ,qBACR,MAAO,OACP,gBAAiB,KAClB,SACD,CACAE,EAAW,EAAK,CAClB,KAIJ,EAAG,CAAE,GAGL,MAAMY,EAAoB,SAAY,CACpCP,EAAc,EAAI,EACd,IACM,YAAI,2DAA4DhF,GAAqB,GAG7F,MAAMD,EAAYC,KACZiF,EAAS,MAAMC,GAAuBnF,CAAS,EASjD,GAPJ,QAAQ,IAAI,mDAAoD,CAC9D,UAAW,CAAC,CAACkF,EACb,SAAUA,GAAQ,SAClB,YAAaA,GAAQ,kBACrB,GAAIA,GAAQ,GACb,EAEGA,GAAUA,EAAO,kBAAmB,CAEhC,MAAAG,EAAgBC,GAAqBJ,CAAM,EAC/BR,EAAA,CAChB,GAAGW,EACH,gBAAiBH,EAAO,kBACxB,GAAGA,EACH,iBAAkB,GACnB,EACDH,EAAsB,EAAK,EAC3B,QAAQ,IAAI,uDAAuD,OAEnE,QAAQ,KAAK,gFAAgF,QAExF/G,EAAO,CACN,cAAM,mDAAoDA,CAAK,SACvE,CACAiH,EAAc,EAAK,CACrB,GAIF,OAAIN,EAEApH,EAAA,KAAC,OAAI,MAAO,CACV,QAAS,OACT,cAAe,SACf,WAAY,SACZ,eAAgB,SAChB,OAAQ,QACR,gBAAiB,UACjB,MAAO,OAEP,YAAAC,MAAC,OAAI,MAAO,CACV,MAAO,OACP,OAAQ,OACR,OAAQ,iBACR,UAAW,oBACX,aAAc,MACd,UAAW,2BACV,QACF,IAAE,OAAO,CAAE,UAAW,QAAU,SAAkB,6BAClD,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA,UAKN,CACJ,IAKAQ,EAEAT,EAAA,KAAC,OAAI,MAAO,CACV,QAAS,OACT,cAAe,SACf,WAAY,SACZ,eAAgB,SAChB,OAAQ,QACR,gBAAiB,UACjB,MAAO,UACP,QAAS,OACT,UAAW,QAEX,YAAAC,MAAC,MAAG,SAAa,yBAChB,IAAE,qDAAwCQ,CAAA,EAAM,EACjDR,MAAC,KAAE,MAAO,CAAE,MAAO,OAAQ,UAAW,MAAO,EAAG,SAEhD,wCACF,IAMFD,OAAC,MAAI,OAAO,CAAE,SAAU,WAAY,MAAO,OAAQ,OAAQ,SACzD,UAAAC,EAAA,IAACiI,GAAA,CACE,GAAGhB,CAAA,CACN,EAGCK,GACEvH,EAAA,YAAI,MAAO,CACV,SAAU,QACV,OAAQ,OAAO,YAAc,IAAM,OAAS,OAC5C,IAAK,OAAO,YAAc,IAAM,OAAS,OACzC,KAAM,OAAO,YAAc,IAAM,MAAQ,OACzC,MAAO,OAAO,YAAc,IAAM,OAAS,OAC3C,UAAW,OAAO,YAAc,IAAM,mBAAqB,OAC3D,OAAQ,KACR,gBAAiB,2BACjB,QAAS,OAAO,YAAc,IAAM,YAAc,OAClD,aAAc,OAAO,YAAc,IAAM,OAAS,MAClD,OAAQ,qCACR,MAAO,UACP,UAAW,SACX,SAAU,OAAO,YAAc,IAAM,OAAS,QAC9C,SAAU,OAAO,YAAc,IAAM,QAAU,OAC/C,UAAW,sCACX,eAAgB,YAEhB,YAAAC,MAAC,OAAI,MAAO,CACV,aAAc,OAAO,YAAc,IAAM,MAAQ,OACjD,SAAU,OAAO,YAAc,IAAM,OAAS,OAC9C,WAAY,QACX,SAEH,yBACC,OAAO,WAAa,KACnBA,EAAA,IAAC,OAAI,MAAO,CAAE,aAAc,OAAQ,SAAU,OAAQ,MAAO,QAAU,SAEvE,oEAEFA,EAAA,IAAC,UACC,QAASgI,EACT,SAAUR,EACV,MAAO,CACL,gBAAiBA,EAAa,2BAA6B,4BAC3D,MAAOA,EAAa,UAAY,UAChC,OAAQ,qCACR,QAAS,OAAO,YAAc,IAAM,YAAc,WAClD,aAAc,OAAO,YAAc,IAAM,OAAS,MAClD,OAAQA,EAAa,cAAgB,UACrC,SAAU,OAAO,YAAc,IAAM,OAAS,OAC9C,WAAY,OACZ,SAAU,OAAO,YAAc,IAAM,QAAU,OAC/C,WAAY,gBACZ,eAAgB,YAClB,EAEC,SAAaA,EAAA,gBAAkB,OAAO,YAAc,IAAM,WAAa,qBAC1E,GACF,CAEJ,GAEJ,ECpRMU,GAA0B,CAAC,CAAE,YAAAC,KAAkB,CACnD,KAAM,CAACC,EAAYC,CAAa,EAAIlM,WAAS,EAAK,EAC5C,CAACqL,EAAYC,CAAa,EAAItL,WAAS,EAAK,EAC5C,CAACmM,EAAUC,CAAW,EAAIpM,WAAS,EAAK,EAG9CsB,YAAU,IAAM,CACd,MAAM+K,EAAc,IAAM,CAClB,MAAAC,EAAS,OAAO,YAAc,IACpCF,EAAYE,CAAM,GAGR,OAAAD,IACL,wBAAiB,SAAUA,CAAW,EACtC,IAAM,OAAO,oBAAoB,SAAUA,CAAW,CAC/D,EAAG,CAAE,GAGL/K,YAAU,IAAM,CA8BV6K,GA7B0B,SAAY,CACpC,IACF,MAAM9F,EAAYC,KAGlB,GAAID,IAAc,QAAS,CACnB,MAAAkF,EAAS,MAAMC,GAAuBnF,CAAS,EAG/CkG,EAAkB,CAAChB,GACFA,EAAO,YACNA,EAAO,WAAa,iBAAmB,CAACA,EAAO,IAC/C,CAACA,EAAO,IAAMlF,IAAc,QAEpD,QAAQ,IAAI,8CAA+C,CACzD,UAAAA,EACA,gBAAAkG,EACA,UAAW,CAAC,CAAChB,EACb,MAAO,CAAC,CAACA,GAAQ,GACjB,SAAUA,GAAQ,SACnB,EAEDW,EAAcK,GAAmBJ,CAAQ,CAC3C,QACO9H,EAAO,CACN,cAAM,uDAAwDA,CAAK,CAC7E,MAMA6H,EAAc,EAAK,CACrB,EACC,CAACC,CAAQ,CAAC,EAGb,MAAMN,EAAoB,SAAY,CACpCP,EAAc,EAAI,EACd,IACF,QAAQ,IAAI,mDAAmD,EAE/D,MAAMjF,EAAYC,KACZiF,EAAS,MAAMC,GAAuBnF,CAAS,EAErD,QAAQ,IAAI,+CAAgD,CAC1D,UAAW,CAAC,CAACkF,EACb,SAAUA,GAAQ,SAClB,YAAaA,GAAQ,kBACrB,GAAIA,GAAQ,GACb,EAEGA,GAAUA,EAAO,mBACnBW,EAAc,EAAK,EACnB,QAAQ,IAAI,4DAA4D,EAGpEF,GACFA,EAAYT,CAAM,EAIpB,WAAW,IAAM,CACf,OAAO,SAAS,UACf,GAAI,IAEP,QAAQ,KAAK,qEAAqE,EAClF,MAAM,yDAAyD,SAE1DlH,EAAO,CACN,cAAM,wDAAyDA,CAAK,EAC5E,MAAM,+CAA+C,SACrD,CACAiH,EAAc,EAAK,CACrB,GAIE,OAACa,GAAY,CAACF,EACT,KAIPrI,EAAA,KAAC,OAAI,MAAO,CACV,SAAU,QACV,OAAQ,OACR,KAAM,MACN,UAAW,mBACX,OAAQ,IACR,gBAAiB,2BACjB,QAAS,YACT,aAAc,OACd,OAAQ,qCACR,MAAO,UACP,UAAW,SACX,SAAU,OACV,SAAU,QACV,UAAW,sCACX,eAAgB,aAChB,UAAW,uBAEX,YAAAC,MAAC,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAWN,EAEFA,MAAC,OAAI,MAAO,CACV,aAAc,MACd,SAAU,OACV,WAAY,QACX,SAEH,4BAEAA,MAAC,OAAI,MAAO,CACV,aAAc,OACd,SAAU,OACV,MAAO,OACP,WAAY,OACX,SAEH,6CAEAA,EAAA,IAAC,UACC,QAASgI,EACT,SAAUR,EACV,MAAO,CACL,gBAAiBA,EAAa,2BAA6B,4BAC3D,MAAOA,EAAa,UAAY,UAChC,OAAQ,qCACR,QAAS,YACT,aAAc,OACd,OAAQA,EAAa,cAAgB,UACrC,SAAU,OACV,WAAY,OACZ,SAAU,QACV,WAAY,gBACZ,UAAWA,EAAa,OAAS,sCACjC,eAAgB,YAClB,EAEC,WAAa,kBAAoB,iBACpC,CACF,GAEJ,EChLA,MAAMmB,GAAc,CAAC,CAAE,OAAAC,EAAQ,QAAAC,EAAS,UAAAC,KAAgB,CACtD,KAAM,CAACC,EAAYC,CAAa,EAAI7M,WAAS,IAAI,EAC3C,CAAC8M,EAAOC,CAAQ,EAAI/M,WAAS,EAAE,EAC/B,CAACgN,EAAUC,CAAW,EAAIjN,WAAS,EAAE,EACrC,CAACkH,EAAUgG,CAAW,EAAIlN,WAAS,EAAE,EACrC,CAACqG,EAAW8G,CAAY,EAAInN,WAAS,EAAE,EACvC,CAACgL,EAASC,CAAU,EAAIjL,WAAS,EAAK,EACtC,CAACqE,EAAO6G,CAAQ,EAAIlL,WAAS,IAAI,EACjC,CAACoN,EAAMC,CAAO,EAAIrN,WAAS,SAAS,EAE1CsB,YAAU,IAAM,CAEVmL,IACF,QAAQ,IAAI,oBAAoB,EAChCI,EAAc,IAAI,EAClBE,EAAS,EAAE,EACXE,EAAY,EAAE,EACdC,EAAY,EAAE,EACdC,EAAa,EAAE,EACfjC,EAAS,IAAI,EACbmC,EAAQ,SAAS,EACnB,EACC,CAACZ,CAAM,CAAC,EAGX,MAAMa,EAAqB,SAAY,CACrCrC,EAAW,EAAI,EACfC,EAAS,IAAI,EAGb,MAAMqC,EAA+B,GAIrC,GAHA,QAAQ,IAAI,4BAAqD,YAAY,EAGzE,CAACC,MAA0B,CAACD,EAAO,CACrC,QAAQ,MAAM,4CAA4C,EAC1DrC,EAAS,qEAAqE,EAC9ED,EAAW,EAAK,EAChB,MACF,CAEI,IAGF,MAAMwC,GAAiB,QAEhB7B,EAAK,CACJ,cAAM,wBAAyBA,CAAG,EAC1CV,EAAS,kDAAkD,EAC3DD,EAAW,EAAK,CAClB,GAIIyC,EAAoB,MAAOC,GAAM,CAMjC,GALJA,EAAE,eAAe,EACjB1C,EAAW,EAAI,EACfC,EAAS,IAAI,EAGT,CAACsC,KAAwB,CAC3BtC,EAAS,qEAAqE,EAC9ED,EAAW,EAAK,EAChB,MACF,CAEI,IAEF,GAAI,CAAC6B,GAAS,CAACE,GAAY,CAAC9F,GAAY,CAACb,EACjC,UAAI,MAAM,yBAAyB,EAI3C,GAAI,CAAC,eAAe,KAAKA,CAAS,EAC1B,UAAI,MAAM,oEAAoE,EAKtF,KAAM,CAAE,KAAMuH,EAAkB,MAAOC,CAAY,EAAI,MAAMC,EAC1D,KAAK,WAAW,EAChB,OAAO,IAAI,EACX,GAAG,YAAazH,CAAS,EACzB,SAGH,GAAIuH,EACI,UAAI,MAAM,6DAA6D,EAIzE,MAAE,KAAMG,GAAU,MAAOC,IAAc,MAAMF,EAAS,KAAK,OAAO,CACtE,MAAAhB,EACA,SAAAE,CAAA,CACD,EAGG,GAFJ,QAAQ,IAAI,4CAA6C,CAAE,SAAAe,GAAU,UAAAC,EAAW,GAE5EA,GAAiB,MAAAA,GAGf,MAAE,KAAMC,EAAc,MAAOC,CAAA,EAAkB,MAAMJ,EACxD,KAAK,WAAW,EAChB,OAAO,CACN,CACE,UAAAzH,EACA,UAAWa,EACX,MAAA4F,EACA,UAAW,GACX,QAASiB,GAAS,KAAK,GACvB,QAAS,GACT,kBAAmB,iCAAiC7G,CAAQ,0GAC9D,CACD,GACA,OAAO,GAAG,EACV,OAAO,EAGN,GAFJ,QAAQ,IAAI,yDAA0D,CAAE,aAAA+G,EAAc,cAAAC,CAAe,GAEjGA,EAAqB,MAAAA,EAGrB,IACF,MAAMC,EAAY,MAAMC,GAAqB,2BAA2BH,CAAY,EAC5E,YAAI,uCAAwCE,CAAS,QACtDE,EAAgB,CACf,cAAM,iCAAkCA,CAAc,CAGhE,CAGA,aAAa,QAAQ,WAAY,KAAK,UAAUJ,CAAY,CAAC,EAEhD,qBAAQ,cAAeA,EAAa,EAAE,EACtC,qBAAQ,oBAAqBA,EAAa,EAAE,EAGrDtB,EACFA,EAAUsB,CAAY,EAGtB,OAAO,SAAS,KAAO,mBAElBrC,EAAK,CACJ,cAAM,uBAAwBA,CAAG,EAChCV,EAAAU,EAAI,SAAW,6CAA6C,SACrE,CACAX,EAAW,EAAK,CAClB,GAIIqD,EAAsBX,GAAM,CAC5BA,EAAE,OAAO,UAAU,SAAS,cAAc,GACpCjB,GACV,EAGF,GAAI,CAACD,EAAe,YAGpB,MAAMc,EAA+B,GAG/BgB,EAAwB,CAACf,GAAqB,GAAK,CAACD,EAGxD,OAAA1J,EAAA,IAAC,OAAI,UAAU,eAAe,QAASyK,EACrC,SAAA1K,EAAA,KAAC,MAAI,WAAU,uBACb,UAAAC,MAAC,SAAO,WAAU,eAAe,QAAS6I,EAAS,SAAC,MAEnD6B,GACC3K,EAAA,KAAC,MAAI,WAAU,gBACb,UAAAC,MAAC,MAAG,SAA6B,kCACjCA,MAAC,KAAE,SAA2F,gGAC9FD,OAAC,MAAI,WAAU,mBACb,UAAAC,MAAC,MAAG,SAAoB,yBACxBA,EAAA,IAAC,UACC,UAAU,kBACV,QAAS,IAAM,CAEb,QAAQ,IAAI,2CAA2C,EAEvD,MAAM2K,EAAmB,CACvB,GAAI,OAAS,KAAK,IAAI,EACtB,UAAW,uBACX,UAAW,UACX,MAAO,kBACP,UAAW,GACX,WAAY,IAAI,KAAK,EAAE,YAAY,EACnC,kBAAmB,8JAGrB,aAAa,QAAQ,WAAY,KAAK,UAAUA,CAAgB,CAAC,EAE7D7B,EACFA,EAAU6B,CAAgB,EAG1B,OAAO,SAAS,KAAO,YAE3B,EACD,wCAED,EACA5K,OAAC,MAAI,WAAU,qBACb,UAAAC,MAAC,IAAE,UAAAA,EAAA,IAAC,SAAO,kCAAsB,GAAS,SACzC,KACC,WAAAD,OAAC,KAAG,uBAASC,MAAC,KAAE,KAAK,wBAAwB,OAAO,SAAS,IAAI,sBAAsB,SAAQ,aAAI,wBAAoB,SACtH,KAAG,uBAASA,MAAC,QAAK,SAAI,SAAO,mCAA+B,SAC5D,MAAI,uDACiC,KAAE,IAAE,4CAE1C,SACC,KAAG,wBAAUA,MAAC,QAAK,SAAS,cAAO,+CAA2C,GACjF,GACF,GACF,GACF,EAGD,CAAC0K,GAAyBnB,IAAS,WAEhCxJ,OAAA6C,WAAA,WAAA5C,MAAC,MAAG,SAA4B,iCAChCA,MAAC,KAAE,SAA4E,iFAE/ED,OAAC,MAAI,WAAU,eACb,UAAAA,EAAA,KAAC,UACC,UAAU,mCACV,QAAS0J,EACT,SAAUtC,EAEV,UAAAnH,EAAA,IAAC,MAAI,KAAI,mBAAmB,IAAI,SAAS,EAAE,wBAE7C,QAEC,MAAI,WAAU,UACb,SAACA,MAAA,QAAK,cAAE,CACV,GAEAD,EAAA,KAAC,UACC,UAAU,kCACV,QAAS,IAAMyJ,EAAQ,OAAO,EAC9B,SAAUrC,EAEV,UAAApH,OAAC,OAAI,MAAM,6BAA6B,MAAM,KAAK,OAAO,KAAK,QAAQ,YAAY,KAAK,OAAO,OAAO,eAAe,YAAY,IAAI,cAAc,QAAQ,eAAe,QACxK,UAACC,MAAA,QAAK,EAAE,IAAI,EAAE,IAAI,MAAM,KAAK,OAAO,KAAK,GAAG,GAAI,GAChDA,MAAC,OAAK,GAAE,iBAAkB,IAC5B,EAAM,uBAER,GACF,GACF,EAGD,CAAC0K,GAAyBnB,IAAS,SAEhCxJ,OAAA6C,WAAA,WAAA5C,MAAC,MAAG,SAA4B,iCAChCA,MAAC,KAAE,SAA+D,oEAEjED,EAAA,aAAK,SAAU8J,EAAmB,UAAU,YAC3C,UAAC9J,OAAA,OAAI,UAAU,aACb,UAACC,EAAA,aAAM,QAAQ,WAAW,SAAa,kBACvCA,EAAA,IAAC,SACC,KAAK,OACL,GAAG,WACH,MAAOqD,EACP,SAAWyG,GAAMT,EAAYS,EAAE,OAAO,KAAK,EAC3C,YAAY,qBACZ,SAAQ,GACV,GACF,EAEA/J,OAAC,MAAI,WAAU,aACb,UAACC,EAAA,aAAM,QAAQ,YAAY,SAAS,cACpCD,OAAC,MAAI,WAAU,kBACb,UAAAC,EAAA,IAAC,SACC,KAAK,OACL,GAAG,YACH,MAAOwC,EACP,SAAWsH,GAAMR,EAAaQ,EAAE,OAAO,MAAM,aAAa,EAC1D,YAAY,WACZ,SAAQ,GACV,EACC9J,EAAA,YAAK,UAAU,mBAAmB,SAAc,oBACnD,SACC,QAAM,oDAAuCwC,GAAa,WAAW,kBAAc,GACtF,EAEAzC,OAAC,MAAI,WAAU,aACb,UAACC,EAAA,aAAM,QAAQ,QAAQ,SAAa,kBACpCA,EAAA,IAAC,SACC,KAAK,QACL,GAAG,QACH,MAAOiJ,EACP,SAAWa,GAAMZ,EAASY,EAAE,OAAO,KAAK,EACxC,YAAY,kBACZ,SAAQ,GACV,GACF,EAEA/J,OAAC,MAAI,WAAU,aACb,UAACC,EAAA,aAAM,QAAQ,WAAW,SAAQ,aAClCA,EAAA,IAAC,SACC,KAAK,WACL,GAAG,WACH,MAAOmJ,EACP,SAAWW,GAAMV,EAAYU,EAAE,OAAO,KAAK,EAC3C,YAAY,2BACZ,SAAQ,GACR,UAAU,IACZ,GACF,EAECtJ,GAASR,EAAA,IAAC,MAAI,WAAU,gBAAiB,SAAMQ,EAAA,EAEhDT,OAAC,MAAI,WAAU,eACb,UAAAC,EAAA,IAAC,UACC,KAAK,SACL,UAAU,cACV,QAAS,IAAMwJ,EAAQ,SAAS,EAChC,SAAUrC,EACX,gBAED,EACAnH,EAAA,IAAC,UACC,KAAK,SACL,UAAU,gBACV,SAAUmH,EAET,WAAU,sBAAwB,iBACrC,GACF,GACF,GACF,EAGD,CAACuD,GAAyBnB,IAAS,WACjCxJ,OAAA,OAAI,UAAU,kBACb,UAAAA,OAAC,OAAI,MAAM,6BAA6B,MAAM,KAAK,OAAO,KAAK,QAAQ,YAAY,KAAK,OAAO,OAAO,eAAe,YAAY,IAAI,cAAc,QAAQ,eAAe,QACxK,UAACC,MAAA,QAAK,EAAE,oCAAqC,GAC7CA,MAAC,WAAS,QAAO,uBAAwB,IAC3C,EACAA,MAAC,MAAG,SAA6B,kCACjCA,MAAC,KAAE,SAAgC,qCACnCA,MAAC,MAAI,WAAU,iBAAkB,IACnC,EAEJ,EACF,EAEJ,ECzTO,SAAS4K,IAAoB,CAElC,MAAMC,EAAc,2CAGdC,EAAoB,EACxB,mNAMF,eAAQ,IAAI,8BAA8B,EAC1C,QAAQ,IAAI,2BAA4B,EAAQD,CAAY,EAC5D,QAAQ,IAAI,2BAA4BC,CAAiB,EAGvD,QAAQ,IAAI,gBAAiBD,CAAW,EAGnC,CACL,cAAe,EAAQA,EACvB,cAAeC,CACnB,CACA,CChDO,eAAeC,IAAuB,CAC3C,QAAQ,IAAI,wCAAwC,EAYpD,QAAQ,IAAI,yBATI,CACd,kBAAmB,2CACnB,uBAAwB,2CACxB,kBAAuD,WACvD,uBAAiE,WACjE,uBAAiE,WACjE,4BAA2E,UAC/E,CAE+C,EAG7C,MAAMF,EAAc,2CAGdG,EAAc,mNAKdC,EAAoBJ,IAAgB,oBACpCK,EAAoBF,IAAgB,gBAmB1C,GAAI,CACF,QAAQ,IAAI,gCAAgC,EAC5C,KAAM,CAAE,KAAAhJ,EAAM,MAAAxB,CAAO,EAAG,MAAMyJ,EAC3B,KAAK,WAAW,EAChB,OAAO,GAAG,EACV,MAAM,CAAC,EAEV,OAAIzJ,EACEA,EAAM,UAAY,mBACpB,QAAQ,MAAM,qCAAqC,EACnD,QAAQ,KAAK,kEAAkE,EAC/E,QAAQ,KAAK,4DAA4D,EAElE,CACL,QAAS,GACT,MAAO,4CACP,cAAe,GACf,YAAa,EACvB,IAEQ,QAAQ,MAAM,qCAAsCA,EAAM,OAAO,EAC1D,CACL,QAAS,GACT,MAAOA,EAAM,QACb,cAAeyK,GAAqBC,CAC9C,IAII,QAAQ,IAAI,wCAAwC,EAC7C,CACL,QAAS,GACT,KAAAlJ,EACA,cAAeiJ,GAAqBC,CAC1C,EACG,OAAQ1K,EAAO,CACd,eAAQ,MAAM,uCAAwCA,EAAM,OAAO,EAC5D,CACL,QAAS,GACT,MAAOA,EAAM,QACb,cAAeyK,GAAqBC,CAC1C,CACG,CACH,CAMO,SAASC,IAA2B,EAG5B,OAAO,SAAS,WAAa,aAC7B,OAAO,SAAS,WAAa,eAGxC,QAAQ,IAAI,sEAAsE,EAsBlFJ,GAAsB,EAAC,KAAKK,GAAU,CAChCA,EAAO,QACT,QAAQ,IAAI,+CAA+C,EAE3D,QAAQ,MAAM,gDAAiDA,EAAO,KAAK,CAEnF,CAAK,EAEL,CClJA,MAAMC,GAAgB,IAElBrL,EAAA,IAAC,OAAI,MAAO,CACV,QAAS,OACT,cAAe,SACf,WAAY,SACZ,eAAgB,SAChB,OAAQ,QACR,gBAAiB,UACjB,MAAO,OACP,QAAS,OACT,UAAW,QACb,EACE,SAACD,OAAA,OAAI,MAAO,CACV,SAAU,QACV,gBAAiB,QACjB,QAAS,OACT,aAAc,OACd,UAAW,8BAEX,YAAAC,MAAC,MAAG,MAAO,CAAE,MAAO,WAAa,SAAoB,yBACrDA,MAAC,KAAE,SAAqD,0DACxDA,MAAC,KAAE,SAA6F,yGAC/F,IAAE,4BAAe,IAAI,KAAK,EAAE,eAAe,GAAE,EAChD,EACF,GClBJ,MAAMsL,EAAmB,CACvB,aAAc,CACZ,KAAK,QAAU,GACf,KAAK,OAAS,KACd,KAAK,YAAc,GACnB,KAAK,WAAaC,EACnB,CAcD,MAAM,WAAWC,EAAQC,EAAY,GAAOC,EAAU,GAAI,CACxD,KAAM,CACJ,oBAAAC,EAAsB,GACtB,UAAAC,EAAY,GACZ,aAAAC,EAAe,GACf,aAAAC,EAAe,GACf,YAAAC,EAAc,EACf,EAAGL,EAGEM,EAAkB,OAAO,OAAW,KAAe,OAAO,kBAEhE,QAAQ,IAAI,kDAAmDR,EAAS,OAAS,OAC/E,wBAAwBG,CAAmB,cAAcC,CAAS,iBAAiBC,CAAY,gBAAgBC,CAAY,mBAAmBC,CAAW,mBAAmBC,CAAe,GAAG,EAEhM,KAAK,OAASR,EACd,KAAK,oBAAsBG,EAC3B,KAAK,UAAYC,EACjB,KAAK,aAAeC,EACpB,KAAK,aAAeC,EAMhBL,GACF,QAAQ,MAAM,0FAA0F,EAG1G,GAAI,CAEF,QAAQ,IAAI,6DAA6D,EAEzE,IAAIQ,EAAY,GA+ChB,GA5CID,GAAmBD,GACrB,QAAQ,IAAI,uEAAuE,EAC3D,MAAMR,GAAe,QAAQC,EAAQ,EAAI,IAG/D,QAAQ,IAAI,wEAAwE,EACpF,KAAK,QAAU,GACf,KAAK,WAAaD,GAClBU,EAAY,KAEL,KAAK,cACd,QAAQ,IAAI,4DAA4D,EACnD,MAAMV,GAAe,QAAQC,CAAM,IAGtD,QAAQ,IAAI,mDAAmD,EAC/D,KAAK,QAAU,GACf,KAAK,WAAaD,GAClBU,EAAY,KAIO,MAAMV,GAAe,QAAQC,CAAM,GAGtD,QAAQ,IAAI,mDAAmD,EAC/D,KAAK,QAAU,GACf,KAAK,WAAaD,GAClBU,EAAY,KAGZ,QAAQ,IAAI,+DAA+D,EACnD,MAAMV,GAAe,QAAQC,EAAQ,EAAI,IAG/D,QAAQ,IAAI,yDAAyD,EACrE,KAAK,QAAU,GACf,KAAK,WAAaD,GAClBU,EAAY,KAMd,CAACA,EACH,cAAQ,MAAM,uEAAuE,EACrF,KAAK,gBAAkB,GACvB,KAAK,kBAAoB,kGACnB,IAAI,MAAM,2DAA2D,EAG7E,YAAK,YAAc,GACZ,EACR,OAAQzL,EAAO,CACd,cAAQ,MAAM,8EAA+EA,CAAK,EAClG,KAAK,gBAAkB,GACvB,KAAK,kBAAoB,+EACnBA,CACP,CACF,CAMD,sBAAuB,CACrB,OAAO,KAAK,mBAAqB,IAClC,CAMD,eAAgB,CACd,OAAO,KAAK,UACb,CAQD,mBAAmBgL,EAAQE,EAAU,GAAI,CAIvC,GAHA,QAAQ,IAAI,iEAAiE,EAGzE,OAAO,OAAW,KAAe,OAAO,KAC1C,OAAO,OAAO,KAAK,OAAOF,EAAQE,CAAO,EACpC,GAAI,OAAO,KAAS,IACzB,OAAO,KAAK,OAAOF,EAAQE,CAAO,EAElC,cAAQ,MAAM,gEAAgE,EACxE,IAAI,MAAM,qDAAqD,CAExE,CAMD,aAAc,CACZ,OAAO,KAAK,OACb,CAMD,qBAAsB,CACpB,MAAO,CACL,YAAa,KAAK,YAClB,QAAS,GACT,UAAW,KAAK,WAAW,UAC3B,eAAgB,KAAK,WAAW,UAAY,SAAW,KAC7D,CACG,CACH,CAGO,MAAMQ,EAAqB,IAAIZ,GC7KtC,MAAMa,EAAuB,CAC3B,aAAc,CACZ,KAAK,gBAAkB,KACvB,KAAK,aAAe,KACpB,KAAK,UAAY,IAAI,IACrB,KAAK,cAAgB,GACrB,KAAK,aAAe,KACpB,KAAK,WAAa,CAAE,WAAY,GAAO,QAAS,wBAChD,KAAK,eAAiB,GACtB,KAAK,iBAAmB,EACxB,KAAK,sBAAwB,EAC7B,KAAK,eAAiB,IAAI,IAC1B,KAAK,YAAc,KAGnB,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,KAAK,qBAAuB,KAAK,qBAAqB,KAAK,IAAI,EAC/D,KAAK,yBAA2B,KAAK,yBAAyB,KAAK,IAAI,EACvE,KAAK,qBAAuB,KAAK,qBAAqB,KAAK,IAAI,EAG/D,KAAK,+BAA8B,CACpC,CAGD,gCAAiC,CAC/B,GAAI,CACF,MAAMC,EAAiB,KAAK,uBACxBA,GAAkBA,EAAe,KACnC,QAAQ,IAAI,8DAA8D,EAC1E,KAAK,gBAAkBA,EAGvB,KAAK,0BAA0BA,EAAe,EAAE,EAGhD,WAAW,SAAY,CACrB,GAAI,CAEF,IAAIC,EAAiBD,EACrB,GAAIA,EAAe,IAAM,OAAOA,EAAe,IAAO,UAAY,CAACA,EAAe,GAAG,WAAW,MAAM,EACpG,GAAI,CACF,MAAME,EAAoB,MAAM,KAAK,iBAAiBF,EAAe,EAAE,EACnEE,IACF,QAAQ,IAAI,kEAAmE,CAC7E,GAAIA,EAAkB,GACtB,kBAAmBA,EAAkB,iBACzD,CAAmB,EACDD,EAAiBC,EACjB,KAAK,gBAAkBA,EACvB,KAAK,mBAAmBA,CAAiB,EAE5C,OAAQC,EAAc,CACrB,QAAQ,KAAK,0EAA2EA,CAAY,CACrG,CAIH,QAAQ,IAAI,mGAAmG,CAChH,OAAQ/L,EAAO,CACd,QAAQ,MAAM,kDAAmDA,CAAK,CACvE,CACF,EAAE,GAAI,EAEV,OAAQA,EAAO,CACd,QAAQ,MAAM,kDAAmDA,CAAK,CACvE,CACF,CAGD,MAAM,WAAWgM,EAAQvD,EAAQ,KAAM,CAErC,GAAI,KAAK,YACP,OAAO,KAAK,YAId,KAAK,YAAc,KAAK,YAAYuD,EAAQvD,CAAK,EAEjD,GAAI,CAEF,OADe,MAAM,KAAK,WAEhC,QAAc,CAER,KAAK,YAAc,IACpB,CACF,CAGD,MAAM,YAAYuD,EAAQvD,EAAQ,KAAM,CAGtC,GAFA,QAAQ,IAAI,qDAAsDuD,EAAQ,SAAUvD,CAAK,EAErF,KAAK,eAAiB,KAAK,gBAC7B,eAAQ,IAAI,8DAA+D,KAAK,gBAAgB,EAAE,EAC3F,KAAK,gBAGd,GAAI,CAEF,IAAI3H,EAAW,KAGf,GAAIkL,EACF,GAAI,CACFlL,EAAW,MAAM,KAAK,qBAAqBkL,CAAM,EAC7ClL,GACF,QAAQ,IAAI,qDAAsDA,EAAS,EAAE,CAEhF,OAAQd,EAAO,CACd,QAAQ,KAAK,oDAAqDA,CAAK,CACxE,CAIH,GAAI,CAACc,GAAY2H,EACf,GAAI,CACF3H,EAAW,MAAM,KAAK,oBAAoB2H,CAAK,EAC3C3H,IACF,QAAQ,IAAI,oDAAqDA,EAAS,EAAE,EAGxEA,EAAS,mBAAqB,CAACA,EAAS,kBAAkB,SAAS,MAAM,GAC3E,QAAQ,IAAI,mEAAoEA,EAAS,iBAAiB,EAIxGkL,IAAW,CAAClL,EAAS,SAAWA,EAAS,UAAYkL,KACvD,QAAQ,IAAI,uDAAwDA,CAAM,EAC1ElL,EAAW,MAAM,KAAK,yBAAyB,CAC7C,GAAIA,EAAS,GACb,QAASkL,CACzB,CAAe,GAGN,OAAQhM,EAAO,CACd,QAAQ,KAAK,mDAAoDA,CAAK,CACvE,CAIH,GAAI,CAACc,EAAU,CACb,MAAM8K,EAAiB,KAAK,uBAC5B,GAAIA,GAAkBA,EAAe,GAAI,CACvC,QAAQ,IAAI,2DAA4DA,EAAe,EAAE,EAGzF,GAAI,CACF9K,EAAW,MAAM,KAAK,iBAAiB8K,EAAe,EAAE,EACpD9K,IACF,QAAQ,IAAI,iFAAiF,EAGzFkL,IAAW,CAAClL,EAAS,SAAWA,EAAS,UAAYkL,KACvD,QAAQ,IAAI,uDAAwDA,CAAM,EAC1ElL,EAAW,MAAM,KAAK,yBAAyB,CAC7C,GAAIA,EAAS,GACb,QAASkL,CAC3B,CAAiB,GAGN,OAAQhM,EAAO,CACd,QAAQ,KAAK,kEAAmEA,CAAK,EAGrFc,EAAW8K,CACZ,CACF,CACF,CAGD,GAAI,CAAC9K,IAAakL,GAAUvD,GAAQ,CAClC,QAAQ,IAAI,2EAA2E,EAEvF,MAAMwD,EAAYxD,GAAS,QAAQuD,CAAM,eACnCE,EAAOD,EAAU,MAAM,GAAG,EAAE,CAAC,EAC7BE,EAASF,EAAU,MAAM,GAAG,EAAE,CAAC,EAC/BpJ,EAAWsJ,EAAS,GAAGA,EAAO,MAAM,GAAG,EAAE,CAAC,CAAC,SAAW,cAG5D,GAAI,CACF,KAAM,CAAE,SAAA1C,CAAQ,EAAK,MAAM2C,GAAA,WAAO,qBAAiB,OAAAC,KAAA,uMAE7CzC,EAAe,CACnB,UAAW,GAAGsC,CAAI,IAAI,KAAK,KAAK,GAAG,YAAa,EAAC,QAAQ,cAAe,GAAG,EAC3E,UAAWrJ,EACX,KAAMqJ,EACN,MAAOD,EACP,QAASD,EACT,UAAW,GACX,kBAAmB,iCAAiCnJ,CAAQ,2GAC5D,gBAAiB,yBAAyBA,CAAQ,oDAClD,sBAAuB,uGACvB,cAAe,UACf,gBAAiB,UACjB,iBAAkB,UAClB,eAAgB,SAChB,SAAU,SACV,SAAU,QACtB,EAEgB,CAAE,KAAMyJ,EAAa,MAAAtM,CAAK,EAAK,MAAMyJ,EACxC,KAAK,WAAW,EAChB,OAAO,CAACG,CAAY,CAAC,EACrB,OAAQ,EACR,SAEH,GAAI,CAAC5J,GAASsM,EACZ,QAAQ,IAAI,6DAA8DA,EAAY,EAAE,EACxFxL,EAAWwL,MAEX,eAAQ,KAAK,kEAAmEtM,CAAK,EAC/EA,CAET,OAAQuM,EAAe,CACtB,QAAQ,KAAK,8EAA+EA,CAAa,EAIzGzL,EAAW,CACT,GAFY,OAAO,KAAK,IAAG,CAAE,GAG7B,QAASkL,EACT,MAAOC,EACP,UAAWpJ,EACX,KAAMqJ,EACN,WAAY,IAAI,KAAM,EAAC,YAAa,EACpC,WAAY,IAAI,KAAM,EAAC,YAAa,EACpC,kBAAmB,iCAAiCrJ,CAAQ,2GAC5D,gBAAiB,yBAAyBA,CAAQ,oDAClD,eAAgB,EAC5B,CACS,CAED,QAAQ,IAAI,yDAA0D/B,EAAS,EAAE,CAClF,CAED,GAAIA,EAAU,CAeZ,GAbA,KAAK,gBAAkBA,EAGvB,KAAK,mBAAmBA,CAAQ,EAG5BA,EAAS,IAAM,OAAOA,EAAS,IAAO,UAAY,CAACA,EAAS,GAAG,WAAW,MAAM,EAClF,KAAK,0BAA0BA,EAAS,EAAE,EAE1C,QAAQ,IAAI,6FAA6F,EAIvGA,EAAS,IAAM,OAAOA,EAAS,IAAO,UAAY,CAACA,EAAS,GAAG,WAAW,MAAM,EAClF,GAAI,CACF,MAAM0L,EAAiB,MAAM,KAAK,iBAAiB1L,EAAS,EAAE,EAC1D0L,IACF,QAAQ,IAAI,qEAAsE,CAChF,GAAIA,EAAe,GACnB,kBAAmBA,EAAe,iBAClD,CAAe,EACD1L,EAAW0L,EACX,KAAK,gBAAkBA,EACvB,KAAK,mBAAmBA,CAAc,EAEzC,OAAQT,EAAc,CACrB,QAAQ,KAAK,4DAA6DA,CAAY,CACvF,CAOH,eAAQ,IAAI,mGAAmG,EAG3GjL,EAAS,mBAAqB,CAACA,EAAS,kBAAkB,SAAS,MAAM,GAC3E,QAAQ,IAAI,2DAA4DA,EAAS,iBAAiB,EAGpG,KAAK,cAAgB,GACrB,KAAK,gBAAe,EAEbA,CACR,CAED,eAAQ,KAAK,8FAA8F,EACpG,IACR,OAAQd,EAAO,CACd,QAAQ,MAAM,iDAAkDA,CAAK,EAGrE,MAAM4L,EAAiB,KAAK,uBAC5B,GAAIA,EACF,eAAQ,IAAI,uDAAuD,EACnE,KAAK,gBAAkBA,EACvB,KAAK,gBAAe,EACbA,EAGT,MAAM5L,CACP,CACF,CAGD,MAAM,qBAAqBgM,EAAQ,CACjC,GAAI,CACF,QAAQ,IAAI,uDAAwDA,CAAM,EAC1E,KAAM,CAAE,KAAAxK,EAAM,MAAAxB,CAAO,EAAG,MAAMyJ,EAC3B,KAAK,WAAW,EAChB,OAAO,GAAG,EACV,GAAG,UAAWuC,CAAM,EACpB,SAEH,GAAIhM,EAAO,CACT,GAAIA,EAAM,OAAS,WAEjB,OAAO,KAET,MAAMA,CACP,CAED,OAAOwB,CACR,OAAQxB,EAAO,CACd,cAAQ,MAAM,6DAA8DA,CAAK,EAC3EA,CACP,CACF,CAGD,MAAM,oBAAoByI,EAAO,CAC/B,GAAI,CACF,QAAQ,IAAI,sDAAuDA,CAAK,EAGxE,KAAM,CAAE,KAAMhI,EAAW,MAAAT,CAAK,EAAK,MAAMyJ,EACtC,KAAK,WAAW,EAChB,OAAO,GAAG,EACV,GAAG,QAAShB,CAAK,EACjB,MAAM,aAAc,CAAE,UAAW,EAAO,GAE3C,GAAIzI,EACF,MAAMA,EAGR,GAAIS,GAAaA,EAAU,OAAS,EAAG,CACrC,GAAIA,EAAU,OAAS,EAAG,CACxB,QAAQ,KAAK,kCAAkCA,EAAU,MAAM,wBAAwBgI,CAAK,EAAE,EAG9F,MAAMgE,EAAwBhM,EAAU,KAAK,GAC3C,EAAE,mBACF,CAAC,EAAE,kBAAkB,SAAS,MAAM,GACpC,CAAC,EAAE,kBAAkB,SAAS,WAAW,CACrD,EAEU,GAAIgM,EACF,eAAQ,IAAI,oEAAoEA,EAAsB,iBAAiB,EAAE,EAClHA,CAEV,CACD,OAAOhM,EAAU,CAAC,CACnB,CAED,OAAO,IACR,OAAQT,EAAO,CACd,cAAQ,MAAM,4DAA6DA,CAAK,EAC1EA,CACP,CACF,CAGD,MAAM,iBAAiB0M,EAAI,CACzB,GAAI,CACF,QAAQ,IAAI,mDAAoDA,CAAE,EAClE,KAAM,CAAE,KAAAlL,EAAM,MAAAxB,CAAO,EAAG,MAAMyJ,EAC3B,KAAK,WAAW,EAChB,OAAO,GAAG,EACV,GAAG,KAAMiD,CAAE,EACX,SAEH,GAAI1M,EAAO,CACT,GAAIA,EAAM,OAAS,WAEjB,OAAO,KAET,MAAMA,CACP,CAED,OAAOwB,CACR,OAAQxB,EAAO,CACd,cAAQ,MAAM,yDAA0DA,CAAK,EACvEA,CACP,CACF,CAGD,0BAA0B2M,EAAY,CACpC,GAAI,CACF,QAAQ,IAAI,0EAA2EA,CAAU,EAG7F,OAAOlD,EAAS,SAAY,YAE1B,KAAK,UACPA,EAAS,cAAc,KAAK,OAAO,EACnC,KAAK,QAAU,MAIjB,KAAK,QAAUA,EACZ,QAAQ,gBAAgBkD,CAAU,EAAE,EACpC,GAAG,mBACF,CACE,MAAO,SACP,OAAQ,SACR,MAAO,YACP,OAAQ,SAASA,CAAU,EAC5B,EACDC,GAAW,KAAK,qBAAqBA,CAAO,CAC7C,EACA,GAAG,mBACF,CACE,MAAO,SACP,OAAQ,SACR,MAAO,YACP,OAAQ,SAASD,CAAU,EAC5B,EACDC,GAAW,KAAK,qBAAqBA,CAAO,CAC7C,EACA,YAEH,QAAQ,IAAI,yEAAyE,GAG9E,OAAOnD,EAAS,MAAS,YACzB,OAAOA,EAAS,KAAK,WAAW,EAAE,IAAO,YAE5C,KAAK,eACPA,EAAS,mBAAmB,KAAK,YAAY,EAC7C,KAAK,aAAe,MAItB,KAAK,aAAeA,EACjB,KAAK,mBAAmBkD,CAAU,EAAE,EACpC,GAAG,SAAUC,GAAW,KAAK,qBAAqBA,CAAO,CAAC,EAC1D,GAAG,SAAUA,GAAW,KAAK,qBAAqBA,CAAO,CAAC,EAC1D,YAEH,QAAQ,IAAI,6EAA6E,IAGzF,QAAQ,KAAK,qFAAqF,EAG9F,KAAK,iBACP,cAAc,KAAK,eAAe,EAGpC,KAAK,gBAAkB,YAAY,SAAY,CAC7C,GAAI,CACF,GAAI,KAAK,iBAAmB,KAAK,gBAAgB,GAAI,CACnD,KAAM,CAAE,KAAApL,EAAM,MAAAxB,CAAO,EAAG,MAAMyJ,EAC3B,KAAK,WAAW,EAChB,OAAO,GAAG,EACV,GAAG,KAAM,KAAK,gBAAgB,EAAE,EAChC,SAEH,GAAIzJ,EAAO,MAAMA,EAGbwB,GAAQ,KAAK,UAAUA,CAAI,IAAM,KAAK,UAAU,KAAK,eAAe,IACtE,QAAQ,IAAI,uDAAuD,EACnE,KAAK,qBAAqB,CAAE,IAAKA,CAAM,GAE1C,CACF,OAAQxB,EAAO,CACd,QAAQ,MAAM,+DAAgEA,CAAK,CACpF,CACF,EAAE,GAAK,EAER,QAAQ,IAAI,kDAAkD,EAEjE,OAAQA,EAAO,CACd,QAAQ,MAAM,mEAAoEA,CAAK,EAGvF,WAAW,IAAM,CACX,KAAK,iBAAmB,KAAK,gBAAgB,IAC/C,KAAK,0BAA0B,KAAK,gBAAgB,EAAE,CAEzD,EAAE,GAAI,CACR,CACF,CAGD,MAAM,qBAAqB4M,EAAS,CAClC,QAAQ,IAAI,mEAAoEA,CAAO,EAEvF,GAAI,CACF,MAAMC,EAAkBD,EAAQ,IAG1BE,EAAkBD,EAAgB,GAAK,IAAMA,EAAgB,WAC/D,KAAK,eAAe,IAAIC,CAAe,GACzC,QAAQ,IAAI,qFAAqF,EACjG,KAAK,eAAe,OAAOA,CAAe,GAG1C,QAAQ,IAAI,kHAAkH,EAIhI,KAAK,gBAAkBD,EAGvB,KAAK,mBAAmBA,CAAe,EAGvC,KAAK,gBAAe,CACrB,OAAQ7M,EAAO,CACd,QAAQ,MAAM,2DAA4DA,CAAK,CAChF,CACF,CAGD,qBAAqB4M,EAAS,CAC5B,QAAQ,IAAI,mEAAoEA,CAAO,EAGvF,KAAK,gBAAkB,KAGvB,GAAI,CACF,aAAa,WAAW,UAAU,EAClC,aAAa,WAAW,uBAAuB,CAChD,OAAQ5M,EAAO,CACd,QAAQ,MAAM,wDAAyDA,CAAK,CAC7E,CAGD,KAAK,gBAAe,CACrB,CAGD,mBAAmB6M,EAAiB,CAClC,GAAI,CAAC,KAAK,gBAGR,OAAKA,EAAgB,mBAInB,QAAQ,IAAI,kGAAkG,EACvG,KAJP,QAAQ,IAAI,oGAAoG,EACzG,IAQX,MAAME,EAAqB,CACzB,YACA,kBACA,oBACA,iBACA,WACA,WACA,mBACN,EAGUC,EAAgB,CACpB,WACA,gBACA,eACA,gBACA,kBACA,eACA,mBACA,UACA,QACA,iBACA,uBACA,kBACA,gBACA,iBACA,yBACA,wBACN,EAGI,UAAWC,KAASF,EAClB,GAAI,KAAK,gBAAgBE,CAAK,IAAMJ,EAAgBI,CAAK,EACvD,eAAQ,IAAI,yDAAyDA,CAAK,EAAE,EAC5E,QAAQ,IAAI,UAAU,KAAK,gBAAgBA,CAAK,CAAC,EAAE,EACnD,QAAQ,IAAI,UAAUJ,EAAgBI,CAAK,CAAC,EAAE,EACvC,GAKX,MAAMC,EAAgB,GACtB,UAAWD,KAASJ,EACd,KAAK,gBAAgBI,CAAK,IAAMJ,EAAgBI,CAAK,GACvDC,EAAc,KAAKD,CAAK,EAI5B,GAAIC,EAAc,OAAS,EAAG,CAC5B,MAAMC,EAAiBD,EAAc,OAAOD,GAASD,EAAc,SAASC,CAAK,CAAC,EAC9EE,EAAe,OAAS,GAC1B,QAAQ,IAAI,0DAA0DA,EAAe,KAAK,IAAI,CAAC,EAAE,CAEpG,CAGD,MAAO,EACR,CAGD,MAAM,yBAAyBrM,EAAU,CACvC,GAAI,CAACA,EAAU,CACb,QAAQ,KAAK,oEAAoE,EACjF,MACD,CAGD,GAAI,MAAM,QAAQA,CAAQ,EAExB,GADA,QAAQ,IAAI,6EAA6E,EACrFA,EAAS,OAAS,GAAKA,EAAS,CAAC,GAAKA,EAAS,CAAC,EAAE,GACpDA,EAAWA,EAAS,CAAC,EACrB,QAAQ,IAAI,sDAAuDA,EAAS,EAAE,MACzE,CACL,QAAQ,MAAM,sDAAuDA,CAAQ,EAC7E,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,8BACT,YAAa,IAAI,KACjB,MAAO,oCACjB,EACQ,MACD,CAIH,GAAI,CAACA,EAAS,GAAI,CAChB,QAAQ,MAAM,uEAAwEA,CAAQ,EAC9F,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,sDACT,YAAa,IAAI,KACjB,MAAO,uBACf,EACM,MACD,CAED,QAAQ,IAAI,uEAAwEA,EAAS,EAAE,EAE/F,GAAI,CAEF,MAAMsM,EAAc1B,EAAmB,gBAIvC,GAAI,CADc,MAAM0B,EAAY,mBACpB,CACd,QAAQ,KAAK,+EAA+E,EAE5F,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,gDACT,YAAa,IAAI,IAC3B,EAEQ,KAAK,aAAe,IAAI,KACxB,MACD,CAGD,GAAItM,EAAS,mBAAqBA,EAAS,kBAAkB,WAAW,OAAO,EAAG,CAChF,QAAQ,KAAK,0EAA2EA,EAAS,iBAAiB,EAGlH,MAAMuK,EAAe,OAAO,OAAW,MACpC,OAAO,SAAS,WAAa,4BAC7B,OAAO,SAAS,SAAS,SAAS,iBAAiB,GAEtD,GAAI,CAGF,MAAMgC,GADa,MAAMD,EAAY,kBACJ,KAAKrO,GACpCA,EAAE,MAAQA,EAAE,KAAK,SAAS+B,EAAS,SAAS,GAAK,CAAC/B,EAAE,GAAG,WAAW,OAAO,CACrF,EAEU,GAAIsO,EAAe,CACjB,QAAQ,IAAI,0DAA2DA,EAAc,EAAE,EAEvF,MAAM,KAAK,yBAAyB,CAClC,GAAIvM,EAAS,GACb,kBAAmBuM,EAAc,EAC/C,CAAa,EAED,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,uDACT,YAAa,IAAI,IAC/B,EACY,KAAK,aAAe,IAAI,KACxB,MACD,SAAUhC,EAAc,CAEvB,QAAQ,IAAI,wEAAwE,EACpF,MAAMiC,EAAe,MAAM,KAAK,oBAAoBxM,CAAQ,EAE5D,GAAIwM,GAAgBA,EAAa,IAAM,CAACA,EAAa,GAAG,WAAW,OAAO,EAAG,CAC3E,MAAM,KAAK,yBAAyB,CAClC,GAAIxM,EAAS,GACb,kBAAmBwM,EAAa,EAChD,CAAe,EAED,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,6CACT,YAAa,IAAI,IACjC,EACc,KAAK,aAAe,IAAI,KACxB,MACD,CACF,CACF,OAAQtN,EAAO,CACd,QAAQ,MAAM,2DAA4DA,CAAK,CAChF,CAGD,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,oDACT,YAAa,IAAI,KACjB,QAAS,iDACnB,EACQ,KAAK,aAAe,IAAI,KACxB,MACD,CAGD,GAAKc,EAAS,kBA4BP,CAEL,IAAIgJ,EACJ,GAAI,CACFA,EAAY,MAAM4B,EAAmB,cAAa,EAAG,aAAa5K,EAAS,iBAAiB,CAC7F,OAAQd,EAAO,CACd,QAAQ,MAAM,yDAA0DA,CAAK,EAGzEA,EAAM,QAAQ,SAAS,YAAY,GAAKA,EAAM,QAAQ,SAAS,YAAY,GAC7E,QAAQ,KAAK,yEAAyE,EAEtF8J,EAAY,CACV,GAAIhJ,EAAS,kBACb,KAAM,GAAGA,EAAS,SAAS,aAC3B,aAAcA,EAAS,kBACvB,aAAcA,EAAS,gBACvB,KAAM,EACpB,GAEYgJ,EAAY,IAEf,CAED,GAAKA,EAiBE,CAEL,MAAMyD,EAAgB,KAAK,kBAAkBzM,EAAUgJ,CAAS,EAE5D,OAAO,KAAKyD,CAAa,EAAE,OAAS,GACtC,QAAQ,IAAI,oEAAqEA,CAAa,EAG9F,MAAM,KAAK,oBAAoBzM,CAAQ,EAEvC,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,yBACT,YAAa,IAAI,IAC/B,IAEY,QAAQ,IAAI,0EAA0E,EAEtF,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,+BACT,YAAa,IAAI,IAC/B,EAES,KAzCe,CACd,QAAQ,IAAI,gEAAgE,EAG5E,MAAMwM,EAAe,MAAM,KAAK,oBAAoBxM,CAAQ,EAG5D,MAAM,KAAK,yBAAyB,CAClC,GAAIA,EAAS,GACb,kBAAmBwM,EAAa,EAC5C,CAAW,EAED,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,qCACT,YAAa,IAAI,IAC7B,CACA,CAyBO,KA9FgC,CAC/B,QAAQ,IAAI,oFAAoF,EAGhG,MAAMxD,EAAY,MAAM,KAAK,oBAAoBhJ,CAAQ,EAGrDgJ,GAAaA,EAAU,IAAM,CAACA,EAAU,GAAG,WAAW,OAAO,GAE/D,MAAM,KAAK,yBAAyB,CAClC,GAAIhJ,EAAS,GACb,kBAAmBgJ,EAAU,EACzC,CAAW,EAED,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,6BACT,YAAa,IAAI,IAC7B,IAEU,QAAQ,KAAK,yEAAyE,EACtF,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,0DACT,YAAa,IAAI,KACjB,QAAS,2DACrB,EAEA,CAoEM,KAAK,aAAe,IAAI,KACxB,KAAK,eAAiB,GACtB,KAAK,iBAAmB,CACzB,OAAQ9J,EAAO,CACd,QAAQ,MAAM,gEAAiEA,CAAK,EAGhFA,EAAM,QAAQ,SAAS,YAAY,GAAKA,EAAM,QAAQ,SAAS,YAAY,GAC7E,QAAQ,KAAK,2FAA2F,EAExG,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,mDACT,YAAa,IAAI,KACjB,QAASA,EAAM,OACzB,EAEQ,KAAK,aAAe,IAAI,OAExB,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,eAAeA,EAAM,OAAO,GACrC,YAAa,IAAI,KACjB,MAAOA,EAAM,OACvB,EAGa,KAAK,iBACR,KAAK,eAAiB,GACtB,KAAK,iBAAmB,EACxB,KAAK,gBAAe,GAGzB,CACF,CAGD,MAAM,iBAAkB,CACtB,GAAI,KAAK,kBAAoB,KAAK,sBAAuB,CACvD,QAAQ,KAAK,mEAAmE,EAChF,KAAK,eAAiB,GACtB,MACD,CAED,KAAK,mBACL,QAAQ,IAAI,6CAA6C,KAAK,gBAAgB,IAAI,KAAK,qBAAqB,EAAE,EAE9G,GAAI,CAEF,OAAQ,KAAK,iBAAgB,CAC3B,IAAK,GAEH,GAAI,KAAK,iBAAmB,KAAK,gBAAgB,GAAI,CACnD,MAAM8L,EAAoB,MAAM,KAAK,iBAAiB,KAAK,gBAAgB,EAAE,EACzEA,IACF,KAAK,gBAAkBA,EACvB,KAAK,mBAAmBA,CAAiB,EACzC,MAAM,KAAK,yBAAyBA,CAAiB,EAExD,CACD,MAEF,IAAK,GAEH,GAAI,KAAK,iBAAmB,KAAK,gBAAgB,GAAI,CACnD,MAAMwB,EAAe,MAAM,KAAK,oBAAoB,KAAK,eAAe,EACxE,MAAM,KAAK,yBAAyB,CAClC,GAAI,KAAK,gBAAgB,GACzB,kBAAmBA,EAAa,EAC9C,CAAa,CACb,MACY,QAAQ,KAAK,yFAAyF,EAExG,MAEF,IAAK,GAEC,KAAK,iBAAmB,KAAK,gBAAgB,GAC/C,MAAM,KAAK,yBAAyB,CAClC,GAAI,KAAK,gBAAgB,GACzB,kBAAmB,IACjC,CAAa,EAID,QAAQ,KAAK,2FAA2F,EAE1G,KACH,CACF,OAAQtN,EAAO,CACd,QAAQ,MAAM,6CAA6C,KAAK,gBAAgB,WAAYA,CAAK,EAGjG,WAAW,IAAM,CACf,KAAK,gBAAe,CAC5B,EAAS,IAAO,KAAK,gBAAgB,CAChC,CACF,CAGD,MAAM,sBAAuB,CAC3B,QAAQ,IAAI,kDAAkD,EAE9D,GAAI,CACF,GAAI,CAAC,KAAK,gBACR,MAAM,IAAI,MAAM,4BAA4B,EAG9C,GAAI,CAAC,KAAK,gBAAgB,GACxB,MAAM,IAAI,MAAM,0CAA0C,EAI5D,IAAIwN,EACJ,GAAI,CACF,QAAQ,IAAI,4EAA6E,KAAK,gBAAgB,EAAE,EAEhH,KAAM,CAAE,KAAAhM,EAAM,MAAAxB,CAAO,EAAG,MAAMyJ,EAC3B,KAAK,WAAW,EAChB,OAAO,GAAG,EACV,GAAG,KAAM,KAAK,gBAAgB,EAAE,EAChC,SAEH,GAAIzJ,EAAO,MAAMA,EACjBwN,EAAehM,EAEf,QAAQ,IAAI,kDAAmD,CAC7D,GAAIA,EAAK,GACT,UAAWA,EAAK,UAChB,kBAAmBA,EAAK,kBACxB,eAAgBA,EAAK,eACrB,SAAUA,EAAK,QACzB,CAAS,EAGD,KAAK,gBAAkBA,EAGvB,KAAK,mBAAmBA,CAAI,CAC7B,OAAQ+K,EAAe,CACtB,QAAQ,MAAM,iEAAkEA,CAAa,EAG7F,QAAQ,KAAK,kEAAmE,CAC9E,GAAI,KAAK,gBAAgB,GACzB,UAAW,KAAK,gBAAgB,UAChC,kBAAmB,KAAK,gBAAgB,iBAClD,CAAS,EACDiB,EAAe,KAAK,eACrB,CAGD,GAAIA,EAAa,kBACf,GAAI,CACF,MAAM,KAAK,oBAAoBA,CAAY,CAC5C,OAAQC,EAAW,CAClB,QAAQ,MAAM,0DAA2DA,CAAS,EAG9EA,EAAU,QAAQ,SAAS,YAAY,GAAKA,EAAU,QAAQ,SAAS,YAAY,GACrF,QAAQ,KAAK,+EAA+E,EAE5F,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,gDACT,YAAa,IAAI,KACjB,QAASA,EAAU,OACjC,GAGY,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,6BAA6BA,EAAU,OAAO,GACvD,YAAa,IAAI,KACjB,MAAOA,EAAU,OAC/B,CAES,KACI,CACL,QAAQ,KAAK,6EAA6E,EAG1F,GAAI,CACF,QAAQ,IAAI,kEAAkE,EAC9E,MAAMH,EAAe,MAAM,KAAK,oBAAoBE,CAAY,EAE5DF,GAAgBA,EAAa,KAE/B,MAAM,KAAK,yBAAyB,CAClC,GAAIE,EAAa,GACjB,kBAAmBF,EAAa,EAC9C,CAAa,EAED,QAAQ,IAAI,iEAAkEA,EAAa,EAAE,EAE7F,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,8BACT,YAAa,IAAI,IAC/B,EAES,OAAQI,EAAa,CACpB,QAAQ,MAAM,8DAA+DA,CAAW,EAExF,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,wDACT,YAAa,IAAI,KACjB,QAAS,sDACrB,CACS,CACF,CAED,YAAK,aAAe,IAAI,MAGpB,CAAC,KAAK,YAAc,CAAC,KAAK,WAAW,aACrC,KAAK,WAAW,YAAc,KAAK,gBACrC,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,oCACT,YAAa,IAAI,IAC3B,GAIM,KAAK,gBAAe,EAEb,CAAE,QAAS,GAAM,SAAUF,CAAY,CAC/C,OAAQxN,EAAO,CACd,eAAQ,MAAM,0DAA2DA,CAAK,EAE9E,KAAK,WAAa,CAChB,WAAY,GACZ,QAAS,eAAeA,EAAM,OAAO,GACrC,YAAa,IAAI,KACjB,MAAOA,EAAM,OACrB,EAGM,KAAK,gBAAe,EAEb,CAAE,QAAS,GAAO,MAAOA,EAAM,OAAO,CAC9C,CACF,CAGD,MAAM,oBAAoBc,EAAU,CAClC,GAAI,CAEF,GAAI,CAACA,EACH,MAAM,IAAI,MAAM,6BAA6B,EAI/C,MAAM6M,EAAqB7M,EAAS,IAAMA,EAAS,WAAa,UAChE,QAAQ,IAAI,iEAAkE6M,CAAkB,EAG3F7M,EAAS,YACZA,EAAS,UAAY,iBAIvB,MAAMuK,EAAe,OAAO,OAAW,MACpC,OAAO,SAAS,WAAa,4BAC7B,OAAO,SAAS,SAAS,SAAS,iBAAiB,GAGhD+B,EAAc1B,EAAmB,gBAGvC,MAAM0B,EAAY,mBAElB,MAAMQ,EAAkB,CACtB,KAAM,GAAG9M,EAAS,SAAS,aAC3B,aAAcA,EAAS,iBAAmB,yBAAyBA,EAAS,SAAS,8BACrF,iBAAkB,yBAClB,MAAO,CACL,SAAU,SACV,MAAOA,EAAS,UAAY,SAC5B,SAAU,CACR,CACE,KAAM,SACN,QAASA,EAAS,mBAAqB,iCAAiCA,EAAS,SAAS,0GAC3F,CACF,CACF,EACD,MAAO,CACL,SAAUA,EAAS,gBAAkB,SACrC,QAASA,EAAS,UAAY,OAC/B,EACD,YAAa,CACX,SAAU,WACV,MAAO,QACR,CACT,EAGYgJ,EAAY,MAAMsD,EAAY,gBAAgBQ,CAAe,EAGnE,GAAI9D,GAAaA,EAAU,IAAMA,EAAU,GAAG,WAAW,OAAO,EAAG,CACjE,GAAIuB,EACF,MAAM,IAAI,MAAM,kDAAkD,EAEpE,QAAQ,KAAK,uFAAuF,CACrG,CAED,eAAQ,IAAI,mDAAoDvB,EAAU,EAAE,EACrEA,CACR,OAAQ9J,EAAO,CASd,GARA,QAAQ,MAAM,0DAA2DA,CAAK,EAGzD,OAAO,OAAW,MACpC,OAAO,SAAS,WAAa,4BAC7B,OAAO,SAAS,SAAS,SAAS,iBAAiB,GAIpD,MAAMA,EAIR,GAAIA,EAAM,QAAQ,SAAS,YAAY,GAAKA,EAAM,QAAQ,SAAS,YAAY,EAC7E,eAAQ,KAAK,4EAA4E,EAElF,CACL,GAAI,QAAU,KAAK,IAAK,EACxB,KAAM,GAAGc,EAAS,SAAS,aAC3B,aAAcA,EAAS,kBACvB,aAAcA,EAAS,gBACvB,KAAM,EAChB,EAGM,MAAMd,CACP,CACF,CAGD,MAAM,oBAAoBc,EAAU,CAClC,GAAI,CAEF,GAAI,CAACA,EACH,MAAM,IAAI,MAAM,6BAA6B,EAI/C,MAAM6M,EAAqB7M,EAAS,IAAMA,EAAS,WAAa,UAGhE,GAFA,QAAQ,IAAI,iEAAkE6M,CAAkB,EAE5F,CAAC7M,EAAS,kBACZ,MAAM,IAAI,MAAM,sBAAsB,EAIxC,MAAMsM,EAAc1B,EAAmB,gBAIvC,GAAI,CADc,MAAM0B,EAAY,mBAElC,eAAQ,KAAK,uFAAuF,EAE7F,CACL,GAAItM,EAAS,kBACb,KAAM,GAAGA,EAAS,SAAS,aAC3B,aAAcA,EAAS,kBACvB,aAAcA,EAAS,gBACvB,KAAM,EAChB,EAIM,IAAIgJ,EACJ,GAAI,CACFA,EAAY,MAAMsD,EAAY,aAAatM,EAAS,iBAAiB,CACtE,OAAQkJ,EAAgB,CAIvB,GAHA,QAAQ,MAAM,oDAAqDA,CAAc,EAG7EA,EAAe,QAAQ,SAAS,YAAY,GAAKA,EAAe,QAAQ,SAAS,YAAY,EAC/F,eAAQ,KAAK,uFAAuF,EAE7F,CACL,GAAIlJ,EAAS,kBACb,KAAM,GAAGA,EAAS,SAAS,aAC3B,aAAcA,EAAS,kBACvB,aAAcA,EAAS,gBACvB,KAAM,EAClB,EAIQ,GAAIkJ,EAAe,QAAQ,SAAS,WAAW,EAC7C,eAAQ,KAAK,kEAAkE,EACxE,MAAM,KAAK,oBAAoBlJ,CAAQ,EAGhD,MAAMkJ,CACP,CAED,GAAI,CAACF,EACH,eAAQ,KAAK,kEAAkE,EACxE,MAAM,KAAK,oBAAoBhJ,CAAQ,EAIhD,IAAI+M,EAAgB/M,EAAS,iBAAmBgJ,EAAU,MAAQA,EAAU,MAAM,SAAW,UACzF3F,EAAUrD,EAAS,WAAagJ,EAAU,MAAQA,EAAU,MAAM,QAAU,SAG5E+D,IAAkB,UAAY1J,IAAY,UAC5C,QAAQ,KAAK,wGAAwG,EACrH0J,EAAgB,UAIlB,MAAMC,EAAe,CACnB,KAAM,GAAGhN,EAAS,SAAS,aAC3B,aAAcA,EAAS,iBAAmB,yBAAyBA,EAAS,SAAS,8BACrF,iBAAkB,yBAClB,aAAcA,EAAS,mBAAqB,iCAAiCA,EAAS,SAAS,2GAC/F,IAAK,CACH,GAAIgJ,EAAU,KAAOA,EAAU,OAAS,CAAE,SAAU,SAAU,MAAO,UACrE,MAAOhJ,EAAS,WAAagJ,EAAU,IAAMA,EAAU,IAAI,MAASA,EAAU,MAAQA,EAAU,MAAM,MAAQ,SAC/G,EACD,MAAO,CACL,GAAIA,EAAU,OAAS,CAAE,SAAU,SAAU,QAAS,SACtD,SAAU+D,EACV,QAAS1J,CACV,CACT,EAEM,QAAQ,IAAI,0DAA2D,KAAK,UAAU2J,EAAc,KAAM,CAAC,CAAC,EAG5G,MAAMC,EAAmB,MAAMX,EAAY,gBAAgBtM,EAAS,kBAAmBgN,CAAY,EAEnG,eAAQ,IAAI,mDAAoDC,EAAiB,EAAE,EAC5EA,CACR,OAAQ/N,EAAO,CAkBd,GAjBA,QAAQ,MAAM,0DAA2DA,CAAK,EAG1EA,EAAM,QAAQ,SAAS,KAAK,IAC9B,QAAQ,MAAM,6CAA6C,EAC3D,QAAQ,MAAM,kBAAmBc,EAAS,iBAAiB,EAC3D,QAAQ,MAAM,mBAAoB,KAAK,UAAU,CAC/C,UAAWA,EAAS,UACpB,gBAAiBA,EAAS,gBAC1B,kBAAmBA,EAAS,kBAC5B,SAAUA,EAAS,SACnB,eAAgBA,EAAS,eACzB,SAAUA,EAAS,QAC7B,EAAW,KAAM,CAAC,CAAC,GAITd,EAAM,QAAQ,SAAS,YAAY,GAAKA,EAAM,QAAQ,SAAS,YAAY,EAC7E,eAAQ,KAAK,qFAAqF,EAE3F,CACL,GAAIc,EAAS,kBACb,KAAM,GAAGA,EAAS,SAAS,aAC3B,aAAcA,EAAS,kBACvB,aAAcA,EAAS,gBACvB,KAAM,EAChB,EAGM,MAAMd,CACP,CACF,CAGD,MAAM,yBAAyBwB,EAAM,CACnC,GAAI,CAEF,GAAI,CAACA,EACH,MAAM,IAAI,MAAM,sCAAsC,EAGxD,GAAI,CAACA,EAAK,GACR,cAAQ,MAAM,0DAA2DA,CAAI,EACvE,IAAI,MAAM,6CAA6C,EAM/D,GAHA,QAAQ,IAAI,0DAA2DA,EAAK,EAAE,EAG1EA,EAAK,IAAMA,EAAK,GAAG,WAAW,MAAM,EAAG,CACzC,QAAQ,IAAI,yEAAyE,EAGrF,MAAMwM,EAAa,CACjB,GAAG,KAAK,gBACR,GAAGxM,EACH,WAAY,IAAI,KAAM,EAAC,YAAa,CAC9C,EAGQ,YAAK,gBAAkBwM,EAGvB,KAAK,mBAAmBA,CAAU,EAGlC,KAAK,gBAAe,EAEbA,CACR,CAGD,MAAMA,EAAa,CACjB,GAAGxM,EACH,WAAY,IAAI,KAAM,EAAC,YAAa,CAC5C,EAGYsL,EAAkBtL,EAAK,GAAK,IAAMwM,EAAW,WACnD,KAAK,eAAe,IAAIlB,EAAiBkB,CAAU,EAEnD,KAAM,CAAE,KAAMC,EAAa,MAAAjO,CAAK,EAAK,MAAMyJ,EACxC,KAAK,WAAW,EAChB,OAAOuE,CAAU,EACjB,GAAG,KAAMxM,EAAK,EAAE,EAChB,OAAQ,EACR,SAEH,GAAIxB,EAAO,MAAMA,EAEjB,eAAQ,IAAI,oEAAoE,EACzEiO,CACR,OAAQjO,EAAO,CACd,QAAQ,MAAM,gEAAiEA,CAAK,EAGpF,MAAM8M,EAAkBtL,EAAK,GAAK,KAAOA,EAAK,YAAc,IAAI,KAAI,EAAG,YAAW,GAClF,WAAK,eAAe,OAAOsL,CAAe,EAEpC9M,CACP,CACF,CAGD,mBAAmBc,EAAU,CAC3B,GAAI,CAACA,EAAU,CACb,QAAQ,KAAK,yEAAyE,EACtF,MACD,CAED,GAAI,CACF,aAAa,QAAQ,WAAY,KAAK,UAAUA,CAAQ,CAAC,EACzD,aAAa,QAAQ,wBAAyB,IAAI,KAAM,EAAC,YAAW,CAAE,EACtE,QAAQ,IAAI,2DAA4DA,EAAS,IAAM,SAAS,CACjG,OAAQd,EAAO,CACd,QAAQ,MAAM,kEAAmEA,CAAK,CACvF,CACF,CAGD,sBAAuB,CACrB,GAAI,CACF,MAAMc,EAAW,aAAa,QAAQ,UAAU,EAChD,GAAI,CAACA,EAAU,OAAO,KAEtB,MAAMoN,EAAS,KAAK,MAAMpN,CAAQ,EAClC,eAAQ,IAAI,8DAA+DoN,EAAO,EAAE,EAC7EA,CACR,OAAQlO,EAAO,CACd,eAAQ,MAAM,qEAAsEA,CAAK,EAClF,IACR,CACF,CAGD,kBAAkBc,EAAUgJ,EAAW,CACrC,MAAMyD,EAAgB,GAEtB,OAAIzD,EAAU,OAAS,GAAGhJ,EAAS,SAAS,eAC1CyM,EAAc,KAAO,CACnB,QAASzD,EAAU,KACnB,SAAU,GAAGhJ,EAAS,SAAS,YACvC,GAGQA,EAAS,mBAAqBgJ,EAAU,eAAiBhJ,EAAS,oBACpEyM,EAAc,aAAe,CAC3B,QAASzD,EAAU,aACnB,SAAUhJ,EAAS,iBAC3B,GAGQA,EAAS,iBAAmBgJ,EAAU,eAAiBhJ,EAAS,kBAClEyM,EAAc,aAAe,CAC3B,QAASzD,EAAU,aACnB,SAAUhJ,EAAS,eAC3B,GAGQA,EAAS,UAAYgJ,EAAU,OAASA,EAAU,MAAM,UAAYhJ,EAAS,WAC/EyM,EAAc,QAAU,CACtB,QAASzD,EAAU,MAAQA,EAAU,MAAM,QAAU,KACrD,SAAUhJ,EAAS,QAC3B,GAGWyM,CACR,CAGD,YAAYY,EAAU,CAIpB,GAHA,KAAK,UAAU,IAAIA,CAAQ,EAGvB,KAAK,gBACP,GAAI,CACFA,EAAS,KAAK,eAAe,CAC9B,OAAQnO,EAAO,CACd,QAAQ,MAAM,mEAAoEA,CAAK,CACxF,CAEJ,CAGD,eAAemO,EAAU,CACvB,KAAK,UAAU,OAAOA,CAAQ,CAC/B,CAGD,iBAAkB,CAChB,UAAWA,KAAY,KAAK,UAC1B,GAAI,CACFA,EAAS,KAAK,eAAe,CAC9B,OAAQnO,EAAO,CACd,QAAQ,MAAM,8DAA+DA,CAAK,CACnF,CAEJ,CAGD,SAAU,CAER,GAAI,KAAK,aAAc,CACrB,GAAI,CACFyJ,EAAS,mBAAmB,KAAK,YAAY,CAC9C,OAAQzJ,EAAO,CACd,QAAQ,KAAK,wDAAyDA,CAAK,CAC5E,CACD,KAAK,aAAe,IACrB,CAGD,GAAI,KAAK,QAAS,CAChB,GAAI,CACFyJ,EAAS,cAAc,KAAK,OAAO,CACpC,OAAQzJ,EAAO,CACd,QAAQ,KAAK,mDAAoDA,CAAK,CACvE,CACD,KAAK,QAAU,IAChB,CAGG,KAAK,kBACP,cAAc,KAAK,eAAe,EAClC,KAAK,gBAAkB,MAGzB,KAAK,UAAU,QACf,QAAQ,IAAI,+CAA+C,CAC5D,CACH,CAGO,MAAMoO,EAAyB,IAAIzC,GAGtC,OAAO,OAAW,MACpB,OAAO,uBAAyByC,GCn+C3B,MAAMC,GAAqB,IAAM,CAEtC,KAAM,CAAE,KAAAC,EAAM,gBAAAC,CAAiB,EAAGC,GAAO,EAGnC,CAAC1N,EAAU2N,CAAW,EAAI9S,WAASyS,EAAuB,eAAe,EACzE,CAACzH,EAASC,CAAU,EAAIjL,WAAS,CAACmF,CAAQ,EAC1C,CAACd,EAAO6G,CAAQ,EAAIlL,EAAQ,SAAC,IAAI,EACjC,CAAC+S,EAAYC,CAAa,EAAIhT,WAASyS,EAAuB,UAAU,EACxE,CAACQ,EAAcC,CAAe,EAAIlT,WAASyS,EAAuB,YAAY,EAGpFnR,YAAU,IAAM,CACVsR,GAAmBD,GAAM,KAC3B1H,EAAW,EAAI,EACfC,EAAS,IAAI,EAEbuH,EAAuB,WAAWE,EAAK,GAAIA,EAAK,KAAK,EAClD,KAAK1D,GAAU,CAEdhE,EAAW,EAAK,CAC1B,CAAS,EACA,MAAMW,GAAO,CACZ,QAAQ,MAAM,4DAA6DA,CAAG,EAC9EV,EAASU,EAAI,OAAO,EACpBX,EAAW,EAAK,CAC1B,CAAS,EAET,EAAK,CAAC2H,EAAiBD,GAAM,GAAIA,GAAM,KAAK,CAAC,EAG3CrR,YAAU,IAAM,CACd,MAAM6R,EAAgBjC,GAAoB,CACxC4B,EAAY5B,CAAe,EAC3B8B,EAAcP,EAAuB,UAAU,EAC/CS,EAAgBT,EAAuB,YAAY,CACzD,EAEI,OAAAA,EAAuB,YAAYU,CAAY,EAExC,IAAM,CACXV,EAAuB,eAAeU,CAAY,CACxD,CACG,EAAE,CAAE,GAGL,MAAMC,EAAYC,cAAY,SAAY,CACxC,GAAI,CACF,OAAApI,EAAW,EAAI,EACf,MAAMwH,EAAuB,uBAC7BO,EAAcP,EAAuB,UAAU,EAC/CS,EAAgBT,EAAuB,YAAY,EAC5C,CAAE,QAAS,GACnB,OAAQpO,EAAO,CACd,eAAQ,MAAM,sDAAuDA,CAAK,EAC1E6G,EAAS7G,EAAM,OAAO,EACf,CAAE,QAAS,GAAO,MAAOA,EAAM,OAAO,CACnD,QAAc,CACR4G,EAAW,EAAK,CACjB,CACF,EAAE,CAAE,GAGCqI,EAAkBD,cAAY,SAAY,CAC9C,GAAI,CACF,GAAI,CAAClO,EACH,MAAM,IAAI,MAAM,4BAA4B,EAG9C,aAAMsN,EAAuB,yBAAyBtN,CAAQ,EAC9D6N,EAAcP,EAAuB,UAAU,EAC/CS,EAAgBT,EAAuB,YAAY,EAC5C,CAAE,QAAS,GACnB,OAAQpO,EAAO,CACd,eAAQ,MAAM,mDAAoDA,CAAK,EACvE6G,EAAS7G,EAAM,OAAO,EACf,CAAE,QAAS,GAAO,MAAOA,EAAM,OAAO,CAC9C,CACL,EAAK,CAACc,CAAQ,CAAC,EAGPoO,EAAiBF,cAAY,MAAOxN,GAAS,CACjD,GAAI,CACF,GAAI,CAACV,EACH,MAAM,IAAI,MAAM,4BAA4B,EAG9C,OAAA8F,EAAW,EAAI,EAUR,CAAE,QAAS,GAAM,SAPA,MAAMwH,EAAuB,yBAAyB,CAC5E,GAAG5M,EACH,GAAIV,EAAS,EACrB,CAAO,CAIgD,CAClD,OAAQd,EAAO,CACd,eAAQ,MAAM,gDAAiDA,CAAK,EACpE6G,EAAS7G,EAAM,OAAO,EACf,CAAE,QAAS,GAAO,MAAOA,EAAM,OAAO,CACnD,QAAc,CACR4G,EAAW,EAAK,CACjB,CACL,EAAK,CAAC9F,CAAQ,CAAC,EAEb,MAAO,CACL,SAAAA,EACA,QAAA6F,EACA,MAAA3G,EACA,WAAA0O,EACA,aAAAE,EACA,UAAAG,EACA,gBAAAE,EACA,eAAAC,CACJ,CACA,EChIMC,GAAsB,IAAM,CAC1B,MACJ,SAAArO,EACA,QAAA6F,EACA,MAAA3G,EACA,WAAA0O,EACA,aAAAE,EACA,UAAAG,EACA,gBAAAE,EACA,eAAAC,GACEb,GAAmB,EAEjB,CAACe,EAAaC,CAAc,EAAI1T,EAAA,SAAS,CAAE,GAG3C2T,EAAgB,CAACpD,EAAMqD,EAASC,IAAY,CAChDH,EAAuB1J,GAAA,CACrB,CACE,KAAAuG,EACA,QAAAqD,EACA,QAAAC,EACA,UAAW,IAAI,KAAK,EAAE,YAAY,CACpC,EACA,GAAG7J,CAAA,CACJ,GAIG8J,EAAwB,SAAY,CACpC,IACF,MAAMzE,EAAU,OAAO,YAAgB,KAAgC,wCACxD,OAAO,OAAW,KAAe,OAAO,sBACzC,aAAa,QAAQ,cAAc,EAEjD,GAAI,CAACA,EAAQ,CACGsE,EAAA,sBAAuB,GAAO,kBAAkB,EAC9D,MACF,CAGM,MAAA5D,EAAmB,WAAWV,CAAM,EAGpC,MAAA0E,EAAahE,EAAmB,gBAGlC,GAAAA,EAAmB,cAAe,CACtB4D,EAAA,sBAAuB,GAAM,6CAA6C,EACxF,MACF,CAGII,EAAW,WAAa,CAACA,EAAW,UACxBJ,EAAA,sBAAuB,GAAM,wBAAwB,EAErDA,EAAA,sBAAuB,GAAO,mBAAmB,QAE1DtP,EAAO,CACdsP,EAAc,sBAAuB,GAAO,UAAUtP,EAAM,OAAO,EAAE,CACvE,GAII2P,EAA2B,SAAY,CACvC,IACF,MAAM3E,EAAU,OAAO,YAAgB,KAAgC,wCACxD,OAAO,OAAW,KAAe,OAAO,sBACzC,aAAa,QAAQ,cAAc,EAEjD,GAAI,CAACA,EAAQ,CACGsE,EAAA,kBAAmB,GAAO,kBAAkB,EAC1D,MACF,CAGM,MAAAI,EAAahE,EAAmB,gBAGlC,GAAAA,EAAmB,cAAe,CACtB4D,EAAA,kBAAmB,GAAM,6CAA6C,EACpF,MACF,CAGkB,MAAMI,EAAW,QAAQ1E,EAAQ,EAAI,GAEtC0E,EAAW,UACZJ,EAAA,kBAAmB,GAAM,yCAAyC,EAElEA,EAAA,kBAAmB,GAAO,oCAAoC,QAEvEtP,EAAO,CACdsP,EAAc,kBAAmB,GAAO,UAAUtP,EAAM,OAAO,EAAE,CACnE,GAII4P,EAAmB,SAAY,CAC/B,IACF,MAAM5E,EAAU,OAAO,YAAgB,KAAgC,wCACxD,OAAO,OAAW,KAAe,OAAO,sBACzC,aAAa,QAAQ,cAAc,EAEjD,GAAI,CAACA,EAAQ,CACGsE,EAAA,iBAAkB,GAAO,kBAAkB,EACzD,MACF,CAMI,GAHE,MAAA5D,EAAmB,WAAWV,EAAQ,EAAI,EAG5CU,EAAmB,cAAe,CACtB4D,EAAA,iBAAkB,GAAM,gCAAgC,EAGtE,MAAMO,EAAa,MAAMnE,EAAmB,gBAAgB,eAAe,EACnE,YAAI,mBAAoBmE,CAAU,EAG1C,MAAMC,EAAgB,MAAMpE,EAAmB,gBAAgB,gBAAgB,CAC7E,KAAM,sBACN,aAAc,gDACd,aAAc,wBACf,EAEO,YAAI,0BAA2BoE,CAAa,OAEtCR,EAAA,iBAAkB,GAAO,4BAA4B,QAE9DtP,EAAO,CACdsP,EAAc,iBAAkB,GAAO,UAAUtP,EAAM,OAAO,EAAE,CAClE,GAII+P,EAA4B,SAAY,CACxC,IACF,MAAM/E,EAAU,OAAO,YAAgB,KAAgC,wCACxD,OAAO,OAAW,KAAe,OAAO,sBACzC,aAAa,QAAQ,cAAc,EAEjD,GAAI,CAACA,EAAQ,CACGsE,EAAA,0BAA2B,GAAO,kBAAkB,EAClE,MACF,CAGM,MAAA5D,EAAmB,WAAWV,EAAQ,GAAO,CACjD,oBAAqB,GACrB,UAAW,GACZ,EAGK,MAAAgF,EAAStE,EAAmB,sBAC5BuE,EAAUvE,EAAmB,uBAE/BuE,EACFX,EAAc,0BAA2B,GAAO,YAAYW,CAAO,EAAE,EAC5DvE,EAAmB,cACd4D,EAAA,0BAA2B,GAAM,mCAAmC,EAElFA,EAAc,0BAA2B,GAAM,mBAAmBU,EAAO,cAAc,OAAO,EAI5F,IACF,MAAMH,EAAa,MAAMnE,EAAmB,gBAAgB,eAAe,EACnE,YAAI,yCAA0CmE,CAAU,EAE5DA,GAAcA,EAAW,OAAS,EACpCP,EAAc,8BAA+B,GAC3C,SAASO,EAAW,MAAM,cAAcnE,EAAmB,YAAY,EAAI,UAAY,EAAE,IAE7E4D,EAAA,8BAA+B,GAAM,qBAAqB,QAEnEY,EAAW,CAClBZ,EAAc,8BAA+B,GAAO,UAAUY,EAAU,OAAO,EAAE,CACnF,QACOlQ,EAAO,CACdsP,EAAc,0BAA2B,GAAO,UAAUtP,EAAM,OAAO,EAAE,CAC3E,GAIImQ,EAAqB,SAAY,CACjC,IAII,MAAAN,EAAa,MAFCnE,EAAmB,gBAEF,iBAEjCmE,GAAcA,EAAW,OAAS,EACtBP,EAAA,kBAAmB,GAAM,SAASO,EAAW,MAAM,cAAcnE,EAAmB,YAAY,EAAI,UAAY,EAAE,EAAE,EAEpH4D,EAAA,kBAAmB,GAAM,qBAAqB,QAEvDtP,EAAO,CACdsP,EAAc,kBAAmB,GAAO,UAAUtP,EAAM,OAAO,EAAE,CACnE,GAIIoQ,EAAgB,SAAY,CAC5B,IACI,MAAAxF,EAAS,MAAMmE,IAEjBnE,EAAO,QACK0E,EAAA,aAAc,GAAM,iBAAiB,EAEnDA,EAAc,aAAc,GAAO,gBAAgB1E,EAAO,KAAK,EAAE,QAE5D5K,EAAO,CACdsP,EAAc,aAAc,GAAO,UAAUtP,EAAM,OAAO,EAAE,CAC9D,GAIIqQ,EAAsB,SAAY,CAClC,IACI,MAAAzF,EAAS,MAAMqE,IAEjBrE,EAAO,QACK0E,EAAA,oBAAqB,GAAM,kBAAkB,EAE3DA,EAAc,oBAAqB,GAAO,iBAAiB1E,EAAO,KAAK,EAAE,QAEpE5K,EAAO,CACdsP,EAAc,oBAAqB,GAAO,UAAUtP,EAAM,OAAO,EAAE,CACrE,GAIIsQ,EAAqB,SAAY,CACjC,IACF,GAAI,CAACxP,EAAU,CACCwO,EAAA,kBAAmB,GAAO,uBAAuB,EAC/D,MACF,CAGM,MAAA1E,EAAS,MAAMsE,EAAe,CAClC,gBAAiB,yBAAyBpO,EAAS,SAAS,yCAA6C,WAAO,aAAa,IAC9H,EAEG8J,EAAO,QACK0E,EAAA,kBAAmB,GAAM,mBAAmB,EAE1DA,EAAc,kBAAmB,GAAO,kBAAkB1E,EAAO,KAAK,EAAE,QAEnE5K,EAAO,CACdsP,EAAc,kBAAmB,GAAO,UAAUtP,EAAM,OAAO,EAAE,CACnE,GAIIuQ,EAAsBC,GAAS,CACnC,GAAI,CAACA,EAAa,cAGlB,MAAMC,MADU,KACKD,EACfE,EAAU,KAAK,MAAMD,EAAS,GAAI,EAClCE,EAAU,KAAK,MAAMD,EAAU,EAAE,EACjCE,EAAW,KAAK,MAAMD,EAAU,EAAE,EAExC,OAAID,EAAU,GACL,WACEC,EAAU,GACZ,GAAGA,CAAO,UAAUA,IAAY,EAAI,GAAK,GAAG,OAC1CC,EAAW,GACb,GAAGA,CAAQ,QAAQA,IAAa,EAAI,GAAK,GAAG,OAE5CJ,EAAK,gBACd,EAIA,OAAAjR,EAAA,KAAC,MAAI,WAAU,wBACb,UAAAC,MAAC,MAAG,SAAqB,0BAEzBD,OAAC,MAAI,WAAU,eACb,UAAAC,MAAC,MAAG,SAAa,kBACjBD,OAAC,MAAI,WAAU,YACb,UAACA,OAAA,OAAI,UAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAQ,mBACzC,OAAK,WAAU,kBAAmB,SAAAmH,EAAU,MAAQ,KAAK,GAC5D,EACApH,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAM,WACvCA,EAAA,YAAK,UAAU,kBAAmB,YAAS,OAAO,GACrD,EACAD,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAY,iBAC7CA,EAAA,YAAK,UAAW,mBAAmBkP,GAAY,WAAa,YAAc,cAAc,GACtF,SAAAA,GAAY,WAAa,eAAiB,cAC7C,GACF,EACAnP,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAU,eAC5CA,MAAC,QAAK,UAAU,kBACb,WAAe+Q,EAAmB3B,CAAY,EAAI,QACrD,GACF,EACArP,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAa,wBAC9C,OAAK,WAAU,kBAAmB,SAAAkP,GAAY,SAAW,OAAO,GACnE,EACCA,GAAY,SACVnP,OAAA,OAAI,UAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAa,kBAC9CA,EAAA,YAAK,UAAU,0BAA2B,WAAW,QAAQ,GAChE,GAEJ,GACF,EAEAD,OAAC,MAAI,WAAU,eACb,UAAAC,MAAC,MAAG,SAAsB,2BAC1BD,OAAC,MAAI,WAAU,YACZ,UAAAmM,EAAmB,YAAY,EAC7BnM,EAAA,YAAI,UAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAK,UACtCA,EAAA,YAAK,UAAU,4BAA4B,SAA0B,gCACxE,EAGED,OAAA6C,EAAA,oBAAC7C,OAAA,OAAI,UAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAU,qBAC3C,OAAK,WAAW,mBAAmBkM,EAAmB,cAAgB,YAAY,YAAc,cAAc,GAC5G,SAAmBA,EAAA,cAAgB,YAAY,MAAQ,KAC1D,GACF,EACAnM,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAgB,qBACjDA,EAAA,YAAK,UAAU,kBACb,WAAmB,cAAc,EAAE,UAAY,aAC/CkM,EAAmB,gBAAgB,UAAY,aAAe,gBACjE,GACF,GACF,EAEFnM,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAkB,6BACnD,OAAK,WAAW,mBAAmBkM,EAAmB,cAAgB,SAAS,YAAc,cAAc,GACzG,SAAmBA,EAAA,cAAgB,SAAS,MAAQ,KACvD,GACF,EACAnM,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAoB,yBACtDA,MAAC,QAAK,UAAU,kBAAmB,WAAmB,cAAc,EAAE,oBAAsB,EAAE,GAChG,EACAD,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAqB,gCACtD,OAAK,WAAU,kBACb,SAAAkM,EAAmB,gBAAgB,mBACnC6E,EAAmB,IAAI,KAAK7E,EAAmB,gBAAgB,kBAAkB,CAAC,EAAI,QACzF,GACF,EACCA,EAAmB,qBAAqB,GACtCnM,EAAA,YAAI,UAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAQ,mBACzC,OAAK,WAAU,0BAA2B,SAAAkM,EAAmB,uBAAuB,GACvF,EAEFnM,OAAC,MAAI,WAAU,iBACb,UAACC,EAAA,YAAK,UAAU,kBAAkB,SAAQ,aAC1CA,MAAC,OAAK,WAAU,kBACb,SAAAkM,EAAmB,oBAAsB,qBACzCA,EAAmB,UAAY,eAAiB,cACnD,IACF,GACF,GACF,EAEAnM,OAAC,MAAI,WAAU,eACb,UAAAC,MAAC,MAAG,SAAgB,qBACnBsB,EACCvB,EAAA,KAAC,MAAI,WAAU,gBACb,UAACA,OAAA,OAAI,UAAU,qBACb,UAACC,EAAA,YAAK,UAAU,sBAAsB,SAAG,QACxCA,EAAA,YAAK,UAAU,sBAAuB,WAAS,GAAG,GACrD,EACAD,OAAC,MAAI,WAAU,qBACb,UAACC,EAAA,YAAK,UAAU,sBAAsB,SAAU,eAC/CA,EAAA,YAAK,UAAU,sBAAuB,WAAS,UAAU,GAC5D,EACAD,OAAC,MAAI,WAAU,qBACb,UAACC,EAAA,YAAK,UAAU,sBAAsB,SAAkB,6BACvD,OAAK,WAAU,sBAAuB,SAAAsB,EAAS,mBAAqB,OAAO,GAC9E,EACAvB,OAAC,MAAI,WAAU,qBACb,UAACC,EAAA,YAAK,UAAU,sBAAsB,SAAgB,2BACrD,OAAK,WAAU,sBAAuB,SAAAsB,EAAS,iBAAmB,OAAO,GAC5E,GACF,EAEAtB,EAAA,IAAC,MAAI,WAAU,cAAc,SAA0B,gCAE3D,EAEAD,OAAC,MAAI,WAAU,eACb,UAAAC,MAAC,MAAG,SAAY,iBAChBD,OAAC,MAAI,WAAU,eACb,UAAAC,MAAC,SAAO,SAASiQ,EAAuB,SAAU9I,EAAS,SAE3D,mCACC,SAAO,SAASgJ,EAA0B,SAAUhJ,EAAS,SAE9D,+BACC,SAAO,SAASiJ,EAAkB,SAAUjJ,EAAS,SAEtD,8BACC,SAAO,SAASoJ,EAA2B,SAAUpJ,EAAS,SAE/D,uCACC,SAAO,SAASwJ,EAAoB,SAAUxJ,EAAS,SAExD,+BACC,SAAO,SAASyJ,EAAe,SAAUzJ,EAAS,SAEnD,0BACC,SAAO,SAAS0J,EAAqB,SAAU1J,EAAS,SAEzD,iCACC,SAAO,SAAS2J,EAAoB,SAAU3J,EAAS,SAExD,0BACF,GACF,EAEApH,OAAC,MAAI,WAAU,eACb,UAAAC,MAAC,MAAG,SAAY,iBACf4P,EAAY,OAAS,EACpB5P,MAAC,OAAI,UAAU,eACZ,WAAY,IAAI,CAACoL,EAAQvJ,IACxB9B,OAAC,OAAgB,UAAW,eAAeqL,EAAO,QAAU,UAAY,SAAS,GAC/E,UAACrL,OAAA,OAAI,UAAU,qBACb,UAAAC,EAAA,IAAC,OAAK,WAAU,mBAAoB,SAAAoL,EAAO,KAAK,QAC/C,OAAK,WAAU,qBAAsB,SAAOA,EAAA,QAAU,UAAY,UAAU,GAC/E,EACCpL,EAAA,WAAI,UAAU,sBAAuB,WAAO,QAAQ,EACrDA,MAAC,MAAI,WAAU,wBAAyB,aAAI,KAAKoL,EAAO,SAAS,EAAE,mBAAqB,GANhF,GAAAvJ,CAOV,CACD,CACH,SAEC,MAAI,WAAU,aAAa,SAAmB,yBAEnD,EAEA7B,MAAC,QAAM,KAAG,GAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAqHV,CACJ,GAEJ,ECnhBMqR,EAAQC,GAAe,KAAK,EAM5BC,GAAcC,EAAA,KAAK,WAAM,OAAO,qBAAqB,eAAC,qMAEtDC,GAAiBD,OAAK,IAAM,CAC5B,IACF,cAAO,OAAO,qBAAwB,OAAA3E,KAAA,6MAC/BrM,EAAO,CACN,qBAAM,gCAAiCA,CAAK,EAC7C,QAAQ,QAAQ,CAAE,QAASkR,EAAwB,EAC5D,CACF,CAAC,EACKC,GAAcH,EAAA,KAAK,WAAM,OAAO,qBAAqB,eAAC,qMAGtDI,GAAO,CAAC,CAAE,UAAAxM,EAAW,WAAAF,EAAY,iBAAA2M,EAAkB,gBAAAC,EAAiB,gBAAAC,EAAiB,UAAA7L,EAAW,QAAAE,EAAS,SAAA4L,EAAU,UAAAxP,EAAW,oBAAAyP,EAAqB,mBAAAC,EAAoB,WAAApO,EAAY,oBAAApB,EAAqB,sBAAAyP,EAAwB,GAAO,YAAAhQ,EAAa,YAAAuD,KAAkB,CAC1Q,KAAM,CAAC0M,EAAgBC,CAAiB,EAAIlW,WAAS,EAAK,EACpD,CAACJ,EAAgBuW,CAAiB,EAAInW,WAAS,IAAI,EACnDoW,EAAYhV,SAAO,IAAI,EAGvB,CAACiV,EAAeC,CAAgB,EAAItW,WAAS,IAAI,EAGvDsB,YAAU,IAAM,CAEViF,GAAuBqP,GAAmB,CAAC3M,GAAa2M,EAAgB,UAAYvP,GAAaA,IAAc,WAC1GoK,GAAA,gCAAuB,OAAAC,KAAA,uMAAE,KAAe6F,GAAA,CAC7CD,EAAiBC,CAAM,EACxB,GAEF,CAAChQ,EAAqBqP,EAAiB3M,EAAW5C,CAAS,CAAC,EAG/D,MAAMmQ,EAAkB,IAAM,CAE5B,GAAIJ,EAAU,QAAS,CACf,MAAAK,EAAOL,EAAU,QAAQ,sBAAsB,EACnCD,EAAA,CAChB,IAAKM,EAAK,IACV,KAAMA,EAAK,KACX,MAAOA,EAAK,MACZ,OAAQA,EAAK,OACd,CACH,CAGAP,EAAkB,EAAI,EAGhBhB,EAAA,IAAI,0BAA2B,CAAE,cAAe,KAAK,EAAE,YAAY,EAAG,EAC5EwB,GAAiB,yBAAyB,GAMtCC,EAA2B,IAAM,CAGrCT,EAAkB,EAAK,EAGvB,WAAW,IAAM,CACLnM,KACT,GAAG,GAeJ,GAXJ,QAAQ,IAAI,gCAAiC,CAC3C,oBAAAxD,EACA,mBAAoB,CAAC,CAACqP,EACtB,UAAA3M,EACA,SAAU2M,GAAiB,SAC3B,UAAWA,GAAiB,UAC5B,UAAAvP,EACA,oBAAqBA,IAAc,UACpC,EAGGE,GAAuBqP,GAAmB,CAAC3M,IAAc2M,EAAgB,UAAYA,EAAgB,YAAcvP,GAAaA,IAAc,UAAW,CAK3J,GAJQ,YAAI,8DAA+DA,CAAS,EAE1DuQ,GAAM,KAAK,IAAMnG,GAAA,WAAO,qBAAwC,OAAAC,KAAA,sMAAC,EAEvF,CAAC2F,EAED,OAAAzS,EAAA,KAAC,OAAI,MAAO,CACV,QAAS,OACT,cAAe,SACf,WAAY,SACZ,eAAgB,SAChB,OAAQ,QACR,gBAAiB,SAEjB,YAAAC,MAAC,OAAI,MAAO,CACV,MAAO,OACP,OAAQ,OACR,OAAQ,oBACR,UAAW,oBACX,aAAc,MACd,UAAW,2BACV,EACHA,MAAC,KAAE,MAAO,CAAE,UAAW,OAAQ,MAAO,MAAO,EAAG,SAA2B,sCAC1E,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA,YAKN,CACJ,IAKE,MAAAgT,EAAa,6BAA6B,mBAAmBxQ,CAAS,CAAC,UAAUL,EAAc,OAAS,OAAO,2BAErH,OACGnC,MAAA,OAAI,UAAU,0BAA0B,MAAO,CAAE,MAAO,OAAQ,OAAQ,QAAS,OAAQ,EAAG,QAAS,GACpG,SAAAA,EAAA,IAAC,UACC,IAAMiT,GAAW,CACf,GAAIA,EAAQ,CAGJ,MAAAC,GAAgB,MAAOC,IAAU,CACrC,GAAIA,GAAM,MAAQA,GAAM,KAAK,OAAS,gBAAiB,CACrD,QAAQ,IAAI,8EAA8E,EAEtF,IACF,GAAI,CAACF,EAAO,cAAe,OAG3B,KAAM,CAAE,cAAApL,CAAc,EAAI,aAAM,OAAO,qBAA8B,EAAE,KAAAgF,KAAA,4MAAK6F,GAC1EA,EAAO,4BAA4BX,CAAe,GAItClK,EAAA,MAAQ1F,EAAc,OAAS,QAC7C0F,EAAc,mBAAqB,GAG/BkK,EAAgB,oBAClBlK,EAAc,kBAAoBkK,EAAgB,kBAClDlK,EAAc,gBAAkBkK,EAAgB,mBAIlDkB,EAAO,cAAc,YAAY,CAC/B,KAAM,wBACN,OAAQpL,GACP,GAAG,EAEE,YAAI,yEAA0EA,CAAa,QAC5FrH,EAAO,CACN,cAAM,qDAAsDA,CAAK,CAC3E,CAGO,2BAAoB,UAAW0S,EAAa,CACrD,GAIK,wBAAiB,UAAWA,EAAa,EAGhDD,EAAO,OAAS,IAAM,CACpB,QAAQ,IAAI,4EAA4E,EAE5F,CACF,EACA,IAAKD,EACL,MAAO,CACL,MAAO,OACP,OAAQ,OACR,OAAQ,OACR,OAAQ,EACR,QAAS,CACX,EACA,MAAO,GAAGjB,EAAgB,WAAa,UAAU,WAErD,GAEJ,CAIE,OAAA/R,MAAA4C,WAAA,CACG,WACE5C,EAAA,WAAI,UAAU,oBAAoB,qBAAU,GAG1CD,OAAA6C,WAAA,WAAC,CAAAsC,GAAc,CAAC2M,GAAoB,CAACC,SACnC,MAAI,WAAU,yBAAyB,IAAKS,EAC3C,SAAAvS,EAAA,IAAC+G,GAAA,CACC,QAAS4L,EACT,MAAM,cACN,OAAO,qBACP,UAAW,KAEf,EAIF3S,EAAA,IAACpE,GAAA,CACC,SAAUwW,EACV,qBAAsBU,EACtB,eAAA/W,EACA,UAAU,qBACZ,EAECmJ,GACClF,EAAA,IAAC,MAAI,WAAW,uBAAuBkF,EAAa,SAAW,EAAE,GAC/D,SAAAlF,MAAC,MAAI,WAAU,YACb,SAAAA,EAAA,IAACyG,GAAA,CAEC,UAAWL,EACX,UAAA5D,EACA,YAAa4Q,GACb,sBAAuB,GACvB,oBAAqB,KACrB,eAAgB,IANX1N,GAQT,CACF,GAGF1F,EAAA,IAACqT,GAAA,CACC,iBAAAxB,EACA,SAAAG,EACA,gBAAAF,EACA,oBAAAG,EACA,mBAAAC,CAAA,CACF,EACF,EAEJ,EAEJ,EAGMmB,GAAc,CAAC,CAAE,iBAAAxB,EAAkB,SAAAG,EAAU,gBAAAF,EAAiB,oBAAAG,EAAqB,mBAAAC,KAEpFnS,OAAA6C,EAAA,oBAAAiP,GAAoBG,GACnBjS,OAAC,MAAI,WAAU,0BACb,UAACA,OAAA,OAAI,UAAU,wBACb,UAACC,MAAAuB,GAAA,CAAQ,SAAUyQ,EAAS,QAAU,GACrChS,MAAA2B,GAAA,CAAgB,SAAUqQ,EAAS,QAAU,IAChD,EACAhS,MAAC,UAAO,UAAU,cAAc,QAAS,IAAMiS,EAAoB,EAAK,EAAG,SAE3E,mBACF,EAGDH,GAAmBE,GACjBjS,OAAA,OAAI,UAAU,yBACb,UAACC,MAAA+B,GAAA,CAAY,KAAMiQ,EAAS,OAAS,GACrChS,MAAC,UAAO,UAAU,cAAc,QAAS,IAAMkS,EAAmB,EAAK,EAAG,SAE1E,mBACF,EAEJ,GAGF,SAASoB,IAAM,CACb,MAAMtS,EAAWsB,KACX,CAAE,KAAAwM,GAASE,KACX,CAAC9J,EAAYC,CAAa,EAAIhJ,WAAS,EAAK,EAC5C,CAAC0V,EAAkBI,CAAmB,EAAI9V,WAAS,EAAK,EACxD,CAAC2V,EAAiBI,CAAkB,EAAI/V,WAAS,EAAK,EACtD,CAAC6V,EAAUuB,CAAW,EAAIpX,WAAS,IAAI,EACvC,CAACqG,EAAW8G,CAAY,EAAInN,WAAS,IAAI,EACzC,CAACuG,EAAqB8Q,CAAsB,EAAIrX,WAAS,EAAK,EAC9D,CAAC4V,EAAiB0B,CAAkB,EAAItX,WAAS,IAAI,EACrD,CAACuX,EAAeC,CAAgB,EAAIxX,WAAS,EAAK,EAClD,CAACiJ,EAAWC,CAAY,EAAIlJ,WAAS,EAAI,EACzC,CAACyX,EAAqBC,CAAsB,EAAI1X,WAAS,CAAC,SAAS,CAAC,EACpE,CAACgG,EAAa2R,CAAc,EAAI3X,WAAS,EAAI,EACPA,WAAS,EAAK,EAC1D,KAAM,CAAC4X,EAAsBC,CAAuB,EAAI7X,WAAS,EAAE,EAC7D,CAAC8X,EAAaC,EAAc,EAAI/X,WAAS,EAAK,EAC9C,CAACgY,GAAiBC,CAAkB,EAAIjY,WAAS,MAAM,EACvD,CAACkY,EAAiBC,CAAkB,EAAInY,WAAS,EAAK,EACtD,CAACoY,GAAYC,EAAa,EAAIrY,WAAS,KAAK,EAG5C,CAACkH,EAAUgG,EAAW,EAAIlN,WAAS,yBAAyB,EAC5D,CAACmH,GAASmR,EAAU,EAAItY,WAAS,EAAE,EACnC,CAACuY,EAAOC,CAAQ,EAAIxY,WAAS,EAAE,EAC/B,CAACsH,EAAcmR,EAAe,EAAIzY,WAAS,SAAS,EACpD,CAACuH,EAAgBmR,CAAiB,EAAI1Y,WAAS,SAAS,EACxD,CAACwH,EAAamR,EAAc,EAAI3Y,WAAS,SAAS,EAGlD,CAACyH,EAAiBmR,CAAkB,EAAI5Y,WAAS,SAAS,EAC1D,CAAC0H,GAAmBmR,EAAoB,EAAI7Y,WAAS,EAAG,EACxD,CAAC2H,GAAYmR,EAAa,EAAI9Y,WAAS,oBAAoB,EAC3D,CAAC4H,GAAemR,EAAgB,EAAI/Y,WAAS,CAAC,EAC9C,CAACgZ,GAAeC,EAAgB,EAAIjZ,WAAS,GAAG,EAChD,CAAC+H,GAAqBmR,EAAsB,EAAIlZ,WAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iCAAsX,EAC/a,CAACgI,GAAgBmR,EAAiB,EAAInZ,WAAS,+EAAgF,EAC/H,CAACiI,GAAsBmR,EAAuB,EAAIpZ,WAAS,qEAAsE,EACjI,CAACqZ,GAAcC,EAAe,EAAItZ,WAAS,YAAY,EACvD,CAAC6H,GAA+B0R,EAAgC,EAAIvZ,WAAS,EAAG,EAChF,CAAC8H,GAAqB0R,EAAsB,EAAIxZ,WAAS,SAAS,EAElE,CAACyZ,GAASC,EAAU,EAAI1Z,WAAS,EAAE,EACnC,CAAC2Z,GAAcC,EAAe,EAAI5Z,WAAS,EAAK,EAEhD6Z,GAAYzY,SAAO,IAAI,EAGvBgH,GAAgB,CACpB,kBAAmB,CACjB,UAAW,wKACX,oBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iEACrB,eAAgB,yIAChB,qBAAsB,sKACxB,EACA,aAAc,CACZ,UAAW,wKACX,oBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mFACrB,eAAgB,8HAChB,qBAAsB,qKACxB,EACA,mBAAoB,CAClB,UAAW,0JACX,oBAAqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kEACrB,eAAgB,mIAChB,qBAAsB,oKACxB,GAII0R,GAA4BnM,GAAM,CAChC,MAAAhD,EAAOgD,EAAE,OAAO,MACtBkK,EAAwBlN,CAAI,EAExBA,GAAQvC,GAAcuC,CAAI,IACVwO,GAAA/Q,GAAcuC,CAAI,EAAE,cAAc,EAC5ByO,GAAAhR,GAAcuC,CAAI,EAAE,oBAAoB,EACzCuO,GAAA9Q,GAAcuC,CAAI,EAAE,mBAAmB,EAChE,EAIIoP,GAAoBpM,GAAM,CAC9B,MAAMqM,EAAOrM,EAAE,OAAO,MAAM,CAAC,EAC7B,GAAIqM,EAAM,CACF,MAAAC,EAAS,IAAI,WACnBA,EAAO,UAAY,IAAM,CAEjB,MAAAC,EAAUC,GAAWF,EAAO,MAAM,EAChC,YAAI,wBAAyBC,CAAO,EAE5C5B,GAAW4B,CAAO,GAEpBD,EAAO,cAAcD,CAAI,CAC3B,GAIII,GAAmB,IAAM,CAC7B9B,GAAW,EAAE,GAGT+B,GAAc,IAAM,CACxBtC,GAAe,EAAI,GAIfuC,GAAkB,MAAO3M,GAAM,CAC/BA,GAAKA,EAAE,gBACTA,EAAE,eAAe,EAInB,IAAI4M,EAAed,GAUnB,GAPI9L,GAAKA,EAAE,QAAUA,EAAE,OAAO,MAC5B,QAAQ,IAAI,wBAAyBA,EAAE,OAAO,GAAG,EACjD4M,EAAe5M,EAAE,OAAO,IACxB+L,GAAWa,CAAY,GAIrB,CAACA,EAAc,CACX,MAAAC,EAAY,aAAa,QAAQ,sBAAsB,EACzDA,IACM,YAAI,+BAAgCA,CAAS,EACtCD,EAAAC,EACfd,GAAWa,CAAY,EAE3B,CAEA,GAAI,CAACA,EAAc,CACjB,MAAM,oBAAoB,EAC1B,MACF,CAEQ,YAAI,kBAAmBA,CAAY,EAG3CX,GAAgB,EAAI,EAGpBa,GAAM,KAAK,mCAAoC,CAC7C,SAAU,aACV,UAAW,GACX,gBAAiB,GACjB,aAAc,GACd,aAAc,GACd,UAAW,GACX,SAAU,OACV,QAAS,uBACV,EAEG,IAEF,KAAM,CAAE,cAAAC,CAAA,EAAkB,MAAAjK,GAAA,IAAM,OAAO,8BAAwB,MAGzDkK,EAAgB,MAAMD,EAAcjB,EAAO,EAIjD,GAHQ,YAAI,0BAA2BkB,CAAa,EAGhDA,IAEUzN,GAAAyN,EAAc,UAAY,eAAe,EACrCrB,GAAAqB,EAAc,cAAgB,EAAE,EAG5CA,EAAc,MAAQA,EAAc,KAAK,KAChCrC,GAAAqC,EAAc,KAAK,GAAG,EAI/BA,EAAc,SACAlC,GAAAkC,EAAc,OAAO,SAAW,SAAS,EACvCjC,EAAAiC,EAAc,OAAO,WAAa,SAAS,EAC7DhC,GAAegC,EAAc,OAAO,QAAUA,EAAc,OAAO,WAAa,SAAS,EACtE/B,EAAA+B,EAAc,OAAO,YAAc,SAAS,GAIjEzB,GAAuByB,EAAc,iBAAiB,UAAU,KAAK,IAAI,GAAK,uEAAuE,EAGrJxB,GAAkBwB,EAAc,gBAAmB,mCAAsCA,EAAc,SAAW,6BAA8B,EACxHvB,GAAAuB,EAAc,sBAAwB,qEAAsE,EAGtH7B,GAAA6B,EAAc,YAAc,oBAAoB,EAG1DA,EAAc,SAAWA,EAAc,QAAQ,OACxCnC,EAAAmC,EAAc,QAAQ,KAAK,EAIlCA,EAAc,eAAiBA,EAAc,cAAc,OAAS,GAAG,CAEzE,MAAMC,EAAkB,CACtB,kBAAmB,kBACnB,SAAY,kBACZ,OAAU,aACV,QAAW,aACX,SAAY,mBACZ,QAAW,oBAIF,UAAAjQ,KAAQgQ,EAAc,cAAe,CACxC,MAAAE,EAAYlQ,EAAK,cACvB,SAAW,CAACmQ,GAASC,EAAU,IAAK,OAAO,QAAQH,CAAe,EAChE,GAAIC,EAAU,SAASC,EAAO,GAAK1S,GAAc2S,EAAU,EAAG,CAC5DlD,EAAwBkD,EAAU,EAG7BJ,EAAc,gBACCxB,GAAA/Q,GAAc2S,EAAU,EAAE,cAAc,EAEvDJ,EAAc,sBACOvB,GAAAhR,GAAc2S,EAAU,EAAE,oBAAoB,EAExE,KACF,CAEJ,CACF,QAEK1W,EAAO,CACN,cAAM,0BAA2BA,CAAK,EAIxC,MAAA2W,EADSvB,GAAQ,QAAQ,eAAgB,EAAE,EAAE,QAAQ,SAAU,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAElF,MAAM,MAAM,EACZ,IAAIwB,GAAQA,EAAK,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAK,MAAM,CAAC,CAAC,EACxD,KAAK,GAAG,EAAI,OAEf/N,GAAY8N,CAAiB,EACX7B,GAAA,mCAAsC6B,EAAoB,6BAA6B,EAGzGP,GAAM,QAAQ,sBAAsB,EACpCA,GAAM,QAAQ,6EAA8E,CAC1F,SAAU,aACV,UAAW,IACX,gBAAiB,GACjB,aAAc,GACd,aAAc,GACd,UAAW,GACZ,EAGDpC,GAAc,QAAQ,SACtB,CACAuB,GAAgB,EAAK,EAGrB,WAAW,IAAM,CACfa,GAAM,QAAQ,sBAAsB,EAGhCrC,KAAe,QACjBqC,GAAM,QAAQ,uCAAwC,CACpD,SAAU,aACV,UAAW,IACX,gBAAiB,GACjB,aAAc,GACd,aAAc,GACd,UAAW,GACZ,EAGDpC,GAAc,QAAQ,IAEvB,GAAI,CACT,GAiCF/W,YAAU,IAAM,CACd,SAAS,gBAAgB,aAAa,aAAc0E,EAAc,OAAS,OAAO,EAG9EA,EACO,cAAK,UAAU,IAAI,YAAY,EAE/B,cAAK,UAAU,OAAO,YAAY,CAC7C,EACC,CAACA,CAAW,CAAC,EAEhB,MAAMkV,GAAc,IAAM,CACTvD,EAAA3N,GAAQ,CAACA,CAAI,GAI9B1I,YAAU,IAAM,CACd4T,EAAM,IAAI,uBAAuB,EAG3B,MAAAiG,EAAa,aAAa,QAAQ,sBAAsB,EACxDC,EAAY,aAAa,QAAQ,wBAAwB,EAE/D,GAAID,GAAcC,EAAW,CAErB,MAAAC,EAAM,KAAK,MACXC,GAAe,SAASF,EAAW,EAAE,EAEvC,CAAC,MAAME,EAAY,GAAKD,EAAMC,GAAe,KACvC,YAAI,wCAAyCH,CAAU,EAC/DzB,GAAWyB,CAAU,EAGrB,aAAa,WAAW,sBAAsB,EAC9C,aAAa,WAAW,wBAAwB,EAGhD,WAAW,IAAM,CACCb,MACf,GAAG,IAGN,aAAa,WAAW,sBAAsB,EAC9C,aAAa,WAAW,wBAAwB,EAEpD,CAGyBtL,KAGPP,KACGG,GAAA,EAAE,KAAeK,GAAA,CAChCA,EAAO,SACD,YAAI,iDAAkDA,EAAO,IAAI,EACrEA,EAAO,eACT,QAAQ,KAAK,+GAA+G,IAGtH,aAAK,8BAA+BA,EAAO,KAAK,EAEpDA,EAAO,cACT,QAAQ,KAAK,oDAAoD,EACjE,QAAQ,KAAK,+FAA+F,EAG5G,OAAO,gBAAkB,IAE7B,CACD,EAGG,OAAO,iCACF,sCAA+B,WAAa,IAI/C,MAAA1B,EACO,OAAO,SAAS,WAAa,aAC7B,OAAO,SAAS,WAAa,YAC1CiK,EAAiBjK,CAAK,EAGtB,MAAMgO,EAAiBjV,KACjB4O,EAAA,IAAI,sBAAuBqG,CAAc,EAC/CpO,EAAaoO,CAAc,EAGrB,MAAAC,EAAgBC,GAAyBF,CAAc,EAC7DlE,EAAuBmE,CAAa,EACpCtG,EAAM,IAAI,yBAA0BsG,EAAe,mBAAoBD,CAAc,EAGhFC,GACHlE,EAAmB,IAAI,EAIzB,MAAMoE,EAAsB,SAAY,CACtCxS,EAAa,EAAI,EACT,YAAI,6DAA8DqS,CAAc,EAClFrG,EAAA,IAAI,mDAAoDqG,CAAc,EAExE,IAKE,GAJI,YAAI,8DAA+DA,CAAc,EACnFrG,EAAA,IAAI,oDAAqDqG,CAAc,EAGzE,OAAO/P,IAA2B,WAC9B,UAAI,MAAM,0CAA0C,EAGtD,MAAAmQ,EAAU,MAAMnQ,GAAuB+P,CAAc,EAC3D,QAAQ,IAAI,oDAAqD,CAC/D,WAAY,CAAC,CAACI,EACd,SAAUA,GAAS,SACnB,GAAIA,GAAS,GACb,UAAWA,GAAS,UACpB,WAAYA,GAAS,WACtB,EACDzG,EAAM,IAAI,0CAA2C,CACnD,WAAY,CAAC,CAACyG,EACd,SAAUA,GAAS,SACnB,GAAIA,GAAS,GACb,UAAWA,GAAS,UACpB,WAAYA,GAAS,WACtB,EACDrE,EAAmBqE,CAAO,EAItB,CAACA,GAAW,CAACA,EAAQ,UACvB,QAAQ,IAAI,2EAA2E,EACvFzG,EAAM,IAAI,iEAAiE,EAC3EmC,EAAuB,EAAK,IAE5B,QAAQ,IAAI,sEAAsE,EAClFnC,EAAM,IAAI,4DAA4D,SAEjE7Q,EAAO,CACd,QAAQ,MAAM,8CAA+C,CAC3D,QAASA,EAAM,QACf,MAAOA,EAAM,MACb,UAAWkX,EACX,eAAgB,OAAO/P,IAA2B,WACnD,EAED6L,EAAuB,EAAK,EAC5BC,EAAmB,IAAI,SACvB,CACApO,EAAa,EAAK,EAClB,QAAQ,IAAI,gDAAgD,EAC5DgM,EAAM,IAAI,sCAAsC,CAClD,GAIF,OAAIsG,GACF,QAAQ,IAAI,6DAA6D,EACrDE,MAEpB,QAAQ,IAAI,uEAAuE,EACnFxS,EAAa,EAAK,GAGb,IAAM,CACXgM,EAAM,IAAI,yBAAyB,EAEvC,EAAG,CAAE,GAGL5T,YAAU,IAAM,CACd,GAAI,CAACiW,EAAe,QAGI,SAAY,CAC9B,IAEF,KAAM,CAAE,SAAAzJ,CAAA,EAAa,MAAM2C,GAAA,WAAO,qBAAgB,mNAG5C,CAAE,KAAA5K,EAAM,MAAAxB,CAAM,EAAI,MAAMyJ,EAC3B,KAAK,WAAW,EAChB,OAAO,WAAW,EAClB,IAAI,YAAa,KAAM,IAAI,EAE9B,GAAIzJ,EAAO,CACD,aAAK,sCAAuCA,CAAK,EAClCqT,EAAA,CAAC,SAAS,CAAC,EAClC,MACF,CAGM,MAAAkE,EAAa/V,EAAK,IAAIJ,GAAQA,EAAK,SAAS,EAAE,OAAO,OAAO,EAE5DyP,EAAA,IAAI,oCAAqC0G,CAAU,EACzDlE,EAAuB,CAAC,UAAW,GAAGkE,CAAU,CAAC,QAC1CvX,EAAO,CACN,aAAK,0DAA2DA,CAAK,EACtDqT,EAAA,CAAC,SAAS,CAAC,CACpC,KAGc,EACf,CAACH,CAAa,CAAC,EAGlB,KAAM,CAAClO,GAAgBC,EAAiB,EAAItJ,WAAS,EAAK,EAEpD+J,GAAY,IAAM,CAKtB,GAJMmL,EAAA,IAAI,eAAgB,CAAE,cAAe,KAAK,EAAE,YAAY,EAAG,EACjEwB,GAAiB,cAAc,EAG3BrN,GAAgB,CAClB,QAAQ,IAAI,mFAAmF,EAC/F,MACF,CAGAC,GAAkB,EAAI,EAGtBwM,EAAoB,EAAK,EACzBC,EAAmB,EAAK,EAGxB,OAAO,eAAiB,GAGpB,IACI,MAAA8F,EAAkB,SAAS,iBAAiB,wBAAwB,EACtEA,EAAgB,OAAS,IAC3B,QAAQ,IAAI,eAAeA,EAAgB,MAAM,mEAAmE,EACpHA,EAAgB,QAAkB/E,GAAA,CACzBA,EAAA,WAAW,YAAYA,CAAM,EACrC,SAEIgF,EAAc,CACb,aAAK,wDAAyDA,CAAY,CACpF,CAGetS,GAAAQ,GAAQA,EAAO,CAAC,EAG/B,QAAQ,IAAI,kDAAkD,EAC9DhB,EAAc,EAAI,EAGlB,WAAW,IAAM,CACf,QAAQ,IAAI,iDAAiD,EACvD,MAAA+S,EAAoB,SAAS,cAAc,sBAAsB,EACnEA,IACgBA,EAAA,UAAU,IAAI,QAAQ,EACxCA,EAAkB,MAAM,QAAU,OAClCA,EAAkB,MAAM,WAAa,UACrCA,EAAkB,MAAM,QAAU,IAClCA,EAAkB,MAAM,OAAS,QAInC,WAAW,IAAM,CACfzS,GAAkB,EAAK,GACtB,GAAI,GACN,GAAG,GAGF0S,GAAmB,IAAM,CAE7B,QAAQ,IAAI,oCAAoC,EAChD7D,EAAmB,EAAI,GAIzB7W,YAAU,IAAM,CACd,OAAO,iBAAmB0a,GAC1B,OAAO,gBAAkB1B,GACzB,OAAO,mBAAqBnC,EAGtB,MAAA8D,EAAuBjF,GAAU,CAC7B,YAAI,gCAAiCA,EAAM,MAAM,EACrDA,EAAM,QAAUA,EAAM,OAAO,MACpB0C,GAAA1C,EAAM,OAAO,GAAG,EACXsD,GAAA,CAAE,OAAQ,CAAE,IAAKtD,EAAM,OAAO,KAAO,EACvD,EAIIkF,EAA8BlF,GAAU,CACpC,YAAI,uCAAwCA,EAAM,MAAM,EAC5DA,EAAM,QAAUA,EAAM,OAAO,MACpB0C,GAAA1C,EAAM,OAAO,GAAG,EACXsD,GAAA,CAAE,OAAQ,CAAE,IAAKtD,EAAM,OAAO,KAAO,EACvD,EAIImF,EAA+BnF,GAAU,CAE7C,GAAIA,EAAM,SACLA,EAAM,OAAO,YAAY,SAAS,gBAAgB,GACjDA,EAAM,OAAO,eAAiBA,EAAM,OAAO,cAAc,YAAY,SAAS,gBAAgB,GAAK,CACvG,QAAQ,IAAI,sCAAsC,EAG5C,MAAAoF,EAAW,SAAS,eAAe,SAAS,EAC9CA,GAAYA,EAAS,QACf,YAAI,8BAA+BA,EAAS,KAAK,EACzD1C,GAAW0C,EAAS,KAAK,EACzB9B,GAAgB,CAAE,OAAQ,CAAE,IAAK8B,EAAS,OAAS,EAEvD,GAGO,iCAAiB,gBAAiBH,CAAmB,EACrD,0BAAiB,uBAAwBC,CAA0B,EACnE,0BAAiB,QAASC,EAA6B,EAAI,EAE7D,IAAM,CAEX,OAAO,OAAO,iBACd,OAAO,OAAO,gBACd,OAAO,OAAO,mBACL,6BAAoB,gBAAiBF,CAAmB,EACxD,6BAAoB,uBAAwBC,CAA0B,EACtE,6BAAoB,QAASC,EAA6B,EAAI,EACzE,EACC,CAAC7B,EAAe,CAAC,EAEd,MAAA+B,GAAqBpO,GAAiB,CAElC,YAAI,4BAA6BA,CAAY,EAG3BA,GAAgBA,EAAa,IAAMA,EAAa,GAAG,SAAW,aAAW,MAAM,IAGvG,QAAQ,IAAI,wDAAwD,EAEpEkK,EAAmB,EAAK,GAExB,OAAO,SAAS,KAAO,YAIzB,EAII,CAACmE,GAAcC,EAAe,EAAIvc,WAAS,EAAK,EAEhD,CAACuJ,GAAaC,EAAc,EAAIxJ,WAAS,CAAC,EAE1CiK,GAAWpE,GAAS,CAKxB,GAJMqP,EAAA,IAAI,aAAc,CAAE,UAAW,IAAI,OAAO,cAAe,KAAArP,CAAA,CAAM,EACrE6Q,GAAiB,YAAY,EAGzB4F,GAAc,CAChB,QAAQ,IAAI,+EAA+E,EAC3F,MACF,CAGAC,GAAgB,EAAI,EAGd,MAAAC,EAAoB,OAAO,iBAAmB,GAG9CC,EAA2B5W,GAAQA,EAAK,oBAG1C,IACI,MAAAgW,EAAkB,SAAS,iBAAiB,wBAAwB,EACtEA,EAAgB,OAAS,IAC3B,QAAQ,IAAI,mBAAmBA,EAAgB,MAAM,yDAAyD,EAC9GA,EAAgB,QAAkB/E,GAAA,CACzBA,EAAA,WAAW,YAAYA,CAAM,EACrC,SAEIgF,EAAc,CACb,aAAK,sDAAuDA,CAAY,CAClF,CAII,IAACjW,GAAS,OAAO,KAAKA,CAAI,EAAE,SAAW,GAAKA,EAAK,cAAgB,OAAS,CAC5E,QAAQ,IAAI,0FAA0F,EAGtGmD,EAAc,EAAK,EACnB,OAAO,eAAiB,GAGxB,WAAW,IAAM,CACfuT,GAAgB,EAAK,GACpB,GAAG,EAEN,MACF,CAEA,GAAIC,GAAqBC,EAA0B,CACjD,QAAQ,IAAI,kGAAkG,EACtG,YAAI,oCAAqC,OAAO,cAAc,EAMhE,MAAAV,EAAoB,SAAS,cAAc,sBAAsB,EACnEA,IACgBA,EAAA,UAAU,IAAI,QAAQ,EACxCA,EAAkB,MAAM,QAAU,OAClCA,EAAkB,MAAM,WAAa,UACrCA,EAAkB,MAAM,QAAU,IAClCA,EAAkB,MAAM,OAAS,OACjC,QAAQ,IAAI,uEAAuE,GAKrF,IAAIW,EAAa,EACjB,MAAMC,EAAY,GACZC,EAAoB,YAAY,IAAM,CAI1C,GAHAF,IACQ,YAAI,mBAAmBA,CAAU,IAAIC,CAAS,4BAA4B,OAAO,cAAc,EAAE,EAErG,OAAO,iBAAmB,IAASD,GAAcC,EAAW,CAC9D,QAAQ,IAAI,uEAAuE,EACnF,cAAcC,CAAiB,EAG3BF,GAAcC,IAChB,QAAQ,IAAI,sEAAsE,EAClF,OAAO,eAAiB,IAI1B3T,EAAc,EAAK,EAGfnD,GAAQA,EAAK,UACf,QAAQ,IAAI,iCAAiC,EAC7CiQ,EAAoB,EAAI,EACxBC,EAAmB,EAAK,EACxBqB,EAAYvR,CAAI,GAETA,GAAQA,EAAK,SACpB,QAAQ,IAAI,gCAAgC,EAC5CiQ,EAAoB,EAAK,EACzBC,EAAmB,EAAI,EACvBqB,EAAYvR,CAAI,IAGhB,QAAQ,IAAI,gEAAgE,EAC5EiQ,EAAoB,EAAK,EACzBC,EAAmB,EAAK,GAItB,IACI,MAAA8F,GAAkB,SAAS,iBAAiB,wBAAwB,EACtEA,GAAgB,OAAS,IAC3B,QAAQ,IAAI,mBAAmBA,GAAgB,MAAM,yDAAyD,EAC9GA,GAAgB,QAAkB/E,IAAA,CACzBA,GAAA,WAAW,YAAYA,EAAM,EACrC,SAEIgF,GAAc,CACb,aAAK,sDAAuDA,EAAY,CAClF,CAGAS,GAAgB,EAAK,CACvB,GACC,GAAI,EAEP,MACF,CAGAvT,EAAc,EAAK,EAGX,YAAI,kCAAmCnD,CAAI,EAG/CA,GAAQA,EAAK,UACf,QAAQ,IAAI,iCAAiC,EAC7CiQ,EAAoB,EAAI,EACxBC,EAAmB,EAAK,EACxBqB,EAAYvR,CAAI,GAETA,GAAQA,EAAK,SACpB,QAAQ,IAAI,gCAAgC,EAC5CiQ,EAAoB,EAAK,EACzBC,EAAmB,EAAI,EACvBqB,EAAYvR,CAAI,IAGhB,QAAQ,IAAI,gEAAgE,EAC5EiQ,EAAoB,EAAK,EACzBC,EAAmB,EAAK,GAI1B,WAAW,IAAM,CACf,QAAQ,IAAI,4CAA4C,EACxD/M,EAAcuP,GAERA,IAAU,GAAa,GACpBA,CACR,EAGD,OAAO,eAAiB,GACxB,QAAQ,IAAI,2EAA2E,EAGnF,IACI,MAAAsD,EAAkB,SAAS,iBAAiB,wBAAwB,EACtEA,EAAgB,OAAS,IAC3B,QAAQ,IAAI,mBAAmBA,EAAgB,MAAM,2DAA2D,EAChHA,EAAgB,QAAkB/E,GAAA,CACzBA,EAAA,WAAW,YAAYA,CAAM,EACrC,SAEIgF,EAAc,CACb,aAAK,wDAAyDA,CAAY,CACpF,CAGAS,GAAgB,EAAK,GACpB,GAAG,GAIRjb,mBAAU,IAAM,CACV6F,IACM,YAAI,6BAA8BA,EAAO,CACnD,EACC,CAACA,EAAO,CAAC,EAGVvD,EAAA,KAAC,MAAI,WAAU,cACb,UAAAC,EAAA,IAAC8C,GAAmB,IACpB9C,EAAA,IAACgZ,GAAA,CACC,SAAS,aACT,UAAW,IACX,gBAAiB,GACjB,YAAW,GACX,aAAY,GACZ,IAAK,GACL,iBAAgB,GAChB,UAAS,GACT,aAAY,GACZ,MAAO7W,EAAc,OAAS,QAChC,EAECnB,EAAS,WAAa,cAAgB,CAAC0B,GACrC3C,OAAA,UAAO,UAAU,SAChB,UAAAC,MAAC,OAAI,UAAU,iBACb,eAAC6C,GAAK,IAAG,IAAI,aAAW,kBACtB,SAAC7C,MAAA,OAAI,IAAI,iBAAiB,IAAI,kBAAkB,UAAU,OAAO,EACnE,CACF,GACAA,MAACkC,IAAO,YAAAC,EAA0B,EAEjCnB,EAAS,WAAa,SACrBhB,MAACkD,IAAa,QAAS,IAAMoR,EAAmB,EAAI,EAAG,EAExDtU,EAAA,IAAA+C,GAAA,CAAY,OAAQZ,EAAa,SAAUkV,GAAa,GAC3D,EAGDrX,MAAA,QAAK,UAAU,qBACd,gBAACiZ,GAEC,WAACjZ,MAAAkZ,EAAA,CAAM,KAAK,IAAI,QAEd1W,IAAc,MAAQ4C,SACnB,MAAI,WAAU,oBAAoB,MAAO,CACxC,QAAS,OACT,cAAe,SACf,WAAY,SACZ,eAAgB,SAChB,OAAQ,QACR,MAAO,MAEP,YAACpF,MAAA,OAAI,UAAU,kBAAkB,MAAO,CACtC,OAAQ,oBACR,UAAW,oBACX,aAAc,MACd,MAAO,OACP,OAAQ,OACR,UAAW,0BACX,aAAc,QACb,EACHA,MAAC,KAAE,SAAU,eACf,GACE0C,EACF1C,EAAA,IAAC4R,GAAA,CACC,UAAAxM,EACA,WAAAF,EACA,iBAAA2M,EACA,gBAAAC,EACA,gBAAAC,EACA,UAAA7L,GACA,QAAAE,GACA,SAAA4L,EACA,UAAAxP,EACA,oBAAAyP,EACA,mBAAAC,EACA,WAAApO,GAAA,oBACApB,EACA,sBAAuB,GACvB,YAAAP,EACA,YAAAuD,EAAA,CACF,EAGAoJ,EAAO9O,EAAA,IAACmZ,GAAS,IAAG,aAAa,QAAO,EAAC,GAAMnZ,EAAA,IAAAmZ,GAAA,CAAS,GAAG,QAAQ,QAAO,GAAC,EAE7E,EAGFnZ,EAAA,IAACkZ,EAAA,CACC,KAAK,QACL,QACElZ,EAAA,IAAC4R,GAAA,CACC,UAAAxM,EACA,WAAAF,EACA,iBAAA2M,EACA,gBAAAC,EACA,gBAAAC,EACA,UAAA7L,GACA,QAAAE,GACA,SAAA4L,EACA,UAAAxP,EACA,oBAAAyP,EACA,mBAAAC,EACA,WAAApO,GAAA,oBACApB,EACA,sBAAuB,GACvB,YAAAP,EACA,YAAAuD,EAAA,CACF,EAEJ,QACCwT,EAAM,MAAK,SAAS,QAASlZ,MAACoZ,IAAU,GAAI,EAC7CpZ,MAACkZ,GAAM,KAAK,WAAW,QAAUlZ,MAAA,OAAI,oCAAwB,CAAQ,SACpEkZ,EAAM,MAAK,iBAAiB,QAASlZ,MAACqZ,IAAa,GAAI,QACvDH,EAAM,MAAK,SAAS,QAASlZ,MAACsZ,IAAU,GAAI,QAC5CJ,EAAM,MAAK,QAAQ,QAASlZ,MAACqL,IAAc,GAAI,QAC/C6N,EAAM,MAAK,kBAAkB,QAASlZ,MAACuZ,IAAkB,GAAI,QAC7DL,EAAM,MAAK,yBAAyB,QAASlZ,MAAC2P,IAAoB,GAAI,QACtEuJ,EAAM,MAAK,aAAa,QAASlZ,MAACwZ,IAAa,GAAI,QACnDN,EAAM,MAAK,mBAAmB,QAASlZ,MAACyZ,IAAmB,GAAI,QAC/DP,EAAM,MAAK,oBAAoB,QAASlZ,MAAC0Z,IAAsB,GAAI,QACnER,EAAM,MAAK,aAAa,QAASlZ,MAAC2Z,IAAU,GAAI,QAChDT,EAAM,MAAK,YAAY,QAASlZ,MAAC4Z,IAAQ,GAAI,EAC9C5Z,EAAA,IAACkZ,EAAM,MAAK,gBAAgB,QACzBlZ,MAAA6Z,WAAA,CAAS,SACR9Z,EAAA,KAAC,MAAI,WAAU,oBACb,UAACC,MAAA,OAAI,UAAU,iBAAkB,GACjCA,MAAC,KAAE,SAAiC,sCACtC,GAEA,SAAAA,EAAA,IAAC2R,GAAY,GACf,GACA,EACF3R,EAAA,IAACkZ,EAAM,MAAK,QAAQ,QACjBlZ,MAAA6Z,WAAA,CAAS,SACR9Z,EAAA,KAAC,MAAI,WAAU,oBACb,UAACC,MAAA,OAAI,UAAU,iBAAkB,GACjCA,MAAC,KAAE,SAAyB,8BAC9B,GAEA,SAAAA,EAAA,IAACyR,GAAA,CACC,SAAApO,EACA,QAAAC,GACA,MAAAoR,EACA,aAAAjR,EACA,eAAAC,EACA,YAAAC,EACA,eAAAmR,GACA,gBAAAlR,EACA,kBAAAC,GACA,eAAAM,GACA,qBAAAC,GACA,oBAAAF,GACA,cAAAiR,GACA,iBAAAC,GACA,aAAAI,GACA,qBAAAzB,EACA,yBAAAkC,GACA,YAAAhC,EACA,eAAAC,GACA,iBAAAgC,GACA,iBAAAK,GACA,cAAAhS,GACA,gBAAA4P,GACA,mBAAAC,EACA,WAAAtQ,GACA,cAAAmR,GACA,cAAAlR,GACA,iBAAAmR,GACA,8BAAAlR,GACA,iCAAA0R,GACA,oBAAAzR,GACA,uBAAA0R,GACA,YAAAa,GACA,YAAAnN,GACA,gBAAAoM,GACA,uBAAAJ,GACA,SAAAV,EACA,kBAAAW,GACA,wBAAAC,GACA,gBAAAX,GACA,kBAAAC,EACA,mBAAAE,EACA,qBAAAC,GACA,UAAAgB,GACA,QAAAJ,GACA,WAAAC,GACA,UAAWC,GACX,gBAAAW,GACA,YAAAtU,EACA,iBAAkB,IAAMmS,EAAmB,EAAI,IAEnD,CACA,GACFtU,EAAA,IAACkZ,EAAM,MAAK,gBAAgB,QACzBlZ,MAAA6Z,WAAA,CAAS,SACR9Z,EAAA,KAAC,MAAI,WAAU,oBACb,UAACC,MAAA,OAAI,UAAU,iBAAkB,GACjCA,MAAC,KAAE,SAAkB,uBACvB,GAEA,SAAAA,EAAA,IAACuR,GAAA,CACC,SAAAlO,EACA,aAAAmS,GACA,SAAUrT,EACV,iBAAkB,IAAM2R,EAAe,CAAC3R,CAAW,IAEvD,CACA,GAEFnC,EAAA,IAACkZ,EAAM,MAAK,WAAW,QACpBlZ,MAAA6Z,WAAA,CAAS,SACR9Z,EAAA,KAAC,MAAI,WAAU,oBACb,UAACC,MAAA,OAAI,UAAU,iBAAkB,GACjCA,MAAC,KAAE,SAAkB,uBACvB,GAEA,SAAAA,EAAA,IAACoD,GAAA,CACC,SAAAC,EACA,aAAAI,EACA,eAAAC,EACA,YAAAC,EACA,gBAAAC,EACA,kBAAAC,GACA,oBAAAK,GACA,eAAAC,GACA,qBAAAC,GACA,MAAOjC,EAAc,OAAS,QAC9B,QAASmB,IAAW,qBACpB,WAAYQ,IAAc,qBAC1B,cAAAC,GACA,8BAAAC,GACA,oBAAAC,GACA,OAAO,qBACP,iBAAiB,4IAErB,CACA,SAEDiV,EAAM,MAAK,kBAAkB,QAASlZ,MAAC8Z,IAAkB,GAAI,QAG7DZ,EAAM,MAAK,iBAAiB,QAC3BlZ,MAACgH,IAAmB,GACpB,QAGDkS,EAAM,MAAK,sBAAsB,QAChCnZ,EAAA,KAAC,OAAI,MAAO,CACV,QAAS,OACT,QAAS,OACT,cAAe,SACf,WAAY,SACZ,IAAK,MAEL,YAAAC,MAAC,MAAG,SAAmB,wBACvBA,MAAC,KAAE,SAA2D,gEAC9DA,MAAC,OAAI,MAAO,CACV,MAAO,OACP,SAAU,QACV,OAAQ,QACR,OAAQ,oBACR,aAAc,MACd,SAAU,QAEV,WAAAA,EAAA,IAAC,UACC,IAAI,6FACJ,MAAM,sBACN,MAAO,CACL,MAAO,OACP,OAAQ,OACR,OAAQ,MACV,IAEJ,SACC,MACC,WAAAA,MAAC,MAAG,SAAe,2BAClB,KACC,WAAAA,MAAC,MAAG,SAA4G,iHAChHA,MAAC,MAAG,SAAqG,0GACzGA,MAAC,MAAG,SAA6E,mFACnF,GACF,GACF,CACA,GAGDA,EAAA,IAAAkZ,EAAA,CAAM,KAAK,cAAc,QACxBnZ,OAAC,MAAI,OAAO,CAAE,QAAS,OAAQ,UAAW,QACxC,YAAAC,MAAC,MAAG,SAAiB,sBACrBA,MAAC,KAAE,SAAkD,wDACvD,CACA,IAGJ,CACF,GAKAA,EAAA,IAAC2I,GAAA,CACC,OAAQ0L,EACR,QAAS,IAAMC,EAAmB,EAAK,EACvC,UAAWkE,EAAA,CACb,EAGAxY,EAAA,IAACkI,GAAA,CACC,YAAcR,GAAW,CACf,YAAI,6BAA8BA,CAAM,EAE5CA,GAAUA,EAAO,IACnB+L,EAAmB/L,CAAM,CAE7B,EACF,CAGF,GAEJ,CAEA,MAAeqS,GAAArY,GAAa4R,GAAK,CAC/B,YAAa,gBACb,KAAM,YACN,YAAa,qDACb,eAAgB,CAAC,kBAAmB,oBAAqB,eAAe,CAC1E,CAAC,ECz9CD,MAAM0G,WAAsBjH,GAAM,SAAU,CAC1C,YAAYlM,EAAO,CACjB,MAAMA,CAAK,EACX,KAAK,MAAQ,CACX,SAAU,GACV,MAAO,KACP,UAAW,KAEf,CAEA,OAAO,yBAAyBrG,EAAO,CAE9B,OAAE,SAAU,GACrB,CAEA,kBAAkBA,EAAOyZ,EAAW,CAE1B,cAAM,iCAAkCzZ,EAAOyZ,CAAS,EAChE,KAAK,SAAS,CACZ,MAAAzZ,EACA,UAAAyZ,CAAA,CACD,CAIH,CAEA,QAAS,CACH,YAAK,MAAM,SAGXla,EAAA,KAAC,MAAI,WAAU,0BACb,UAAAC,MAAC,MAAG,SAAqB,0BACxB,KAAK,MAAM,aACVD,OAAC,WAAQ,MAAO,CAAE,WAAY,UAC5B,YAAAC,MAAC,WAAQ,SAAa,kBACtBA,MAAC,KAAG,SAAK,WAAM,OAAS,KAAK,MAAM,MAAM,SAAW,WACnD,IAAE,+BAAkB,KAAK,MAAM,WAAa,KAAK,MAAM,UAAU,gBAAe,GACnF,EAED,KAAK,MAAM,SACTA,EAAA,cAAO,QAAS,IAAM,CACrB,KAAK,SAAS,CAAE,SAAU,EAAO,GACjC,KAAK,MAAM,SAAQ,EAClB,SAEH,aAEJ,IAIG,KAAK,MAAM,QACpB,CACF,yyFCnCA,MAAMka,GAAoB,CAAC,CAAE,SAAAC,KAAe,CAE1C,MAAMC,EAAYC,KAGhB,OAAAra,EAAA,IAACsa,GAAa,WAAAF,EACX,SAAAD,CACH,EAEJ,EAYMI,GAAmB,CAAC,CAAE,SAAAJ,KAEvBna,EAAA,IAAAwa,GAAA,CACC,SAACxa,MAAAka,GAAA,CACE,SAAAC,EACH,CACF,GCxCEM,GAAqB,CACzB,oBACA,oBACA,sBACF,EAMaC,GAAoB,IAAM,CACrC,MAAMC,EAAU,CACd,oBAAqB,GACrB,iBAAkB,CAAE,EACpB,UAAW,CAAE,CACjB,EAGE,OAAAF,GAAmB,QAAQG,GAAW,CACpC,IAAIC,EAAQ,KACZ,GAAI,CACFA,EAAQ,CAAe,skDAACD,CAAO,CAChC,MAAW,CAEX,CAED,MAAME,EAAY,EAAQD,EACpBE,EAAWH,EAAQ,SAAS,KAAK,GAAKA,EAAQ,SAAS,OAAO,EAEpED,EAAQ,UAAUC,CAAO,EAAI,CAC3B,UAAAE,EACA,MAAOC,EAAYD,EAAY,OAAS,OAAaD,CAC3D,EAESC,IACHH,EAAQ,oBAAsB,GAC9BA,EAAQ,iBAAiB,KAAKC,CAAO,EAE3C,CAAG,EAEMD,CACT,EAKaK,GAAuB,IAAM,CACxC,MAAML,EAAUD,KAEhB,eAAQ,MAAM,mCAAmC,EACjD,QAAQ,IAAI,kCAAmCC,EAAQ,oBAAsB,QAAU,MAAM,EAExFA,EAAQ,qBACX,QAAQ,KAAK,qBAAsBA,EAAQ,iBAAiB,KAAK,IAAI,CAAC,EAGxE,QAAQ,IAAI,mBAAmB,EAC/B,OAAO,QAAQA,EAAQ,SAAS,EAAE,QAAQ,CAAC,CAACjO,EAAMuO,CAAI,IAAM,CAC1D,QAAQ,IAAI,KAAKvO,CAAI,KAAKuO,EAAK,UAAY,YAAc,WAAW,IAAIA,EAAK,MAAQ,IAAIA,EAAK,KAAK,IAAM,EAAE,EAAE,CACjH,CAAG,EAED,QAAQ,SAAQ,EAETN,CACT,EAMaO,GAA8B,IAAM,CAC/C,MAAMP,EAAUK,KAGhB,GAAI,CAACL,EAAQ,oBAAqB,CAChC,QAAQ,KAAK,2DAA2D,EAGxE,MAAMQ,EAAwB,2CACxBC,EAAwB,mNACxBC,EAAoB,uCAG1B,IAAIC,EAAiB,GACrB,GAAI,CACFA,EAAiB,EAAQ,0CAC1B,MAAW,CAEX,CAEIA,IACH,OAAO,kBAAoBH,GAG7B,IAAII,EAAiB,GACrB,GAAI,CACFA,EAAiB,EAAQ,kNAC1B,MAAW,CAEX,CAEIA,IACH,OAAO,kBAAoBH,GAG7B,IAAII,EAAa,GACjB,GAAI,CACFA,EAAa,EAAQ,sCACtB,MAAW,CAEX,CAEIA,IACH,OAAO,qBAAuBH,EAEjC,CAED,OAAOV,CACT,EChHac,GAA6B,SAAY,CACpD,QAAQ,IAAI,oEAAoE,EAEhF,GAAI,CAEF,MAAMjQ,EAAU,OAAO,YAAgB,KAA8B,wCACtD,OAAO,OAAW,KAAe,OAAO,sBACzC,aAAa,QAAQ,cAAc,EAEjD,GAAIA,EAAQ,CACV,QAAQ,IAAI,gEAAgE,EAE5E,GAAI,CAEF,MAAMG,EAAsB,OAAO,SAAS,SAAS,SAAS,YAAY,GAC/C,OAAO,SAAS,SAAS,SAAS,QAAQ,EAC/DC,EAAY,OAAO,SAAS,SAAS,SAAS,UAAU,EAGxDC,EAAe,OAAO,OAAW,MAClB,OAAO,SAAS,SAAS,SAAS,MAAM,GACxC,OAAO,SAAS,SAAS,SAAS,MAAM,GACxC,OAAO,SAAS,SAAS,SAAS,MAAM,GACxC,OAAO,SAAS,SAAS,SAAS,KAAK,GACvC,CAAC,OAAO,SAAS,SAAS,SAAS,WAAW,GAG7DC,EAAe,GAGrB,MAAMI,EAAmB,WAAWV,EAAQ,GAAO,CACjD,oBAAAG,EACA,UAAAC,EACA,aAAAC,EACA,aAAAC,CACV,CAAS,EAGD,MAAM0E,EAAStE,EAAmB,sBAClC,QAAQ,IAAI,iEAAkEsE,CAAM,EAGpF,MAAMkL,EAAoBxP,EAAmB,uBACzCwP,IACF,QAAQ,KAAK,wDAAyDA,CAAiB,EAGnF/P,GAAuB,CAAC,OAAO,SAAS,SAAS,SAAS,OAAO,EAEnE,WAAW,IAAM,CACf,GAAI,CAEF,IAAIgQ,EAAgB,SAAS,eAAe,0BAA0B,EACjEA,IACHA,EAAgB,SAAS,cAAc,KAAK,EAC5CA,EAAc,GAAK,2BACnBA,EAAc,MAAM,gBAAkB,UACtCA,EAAc,MAAM,MAAQ,UAC5BA,EAAc,MAAM,QAAU,OAC9BA,EAAc,MAAM,OAAS,SAC7BA,EAAc,MAAM,aAAe,MACnCA,EAAc,MAAM,OAAS,oBAC7BA,EAAc,MAAM,SAAW,QAC/BA,EAAc,MAAM,IAAM,OAC1BA,EAAc,MAAM,MAAQ,OAC5BA,EAAc,MAAM,OAAS,OAC7BA,EAAc,MAAM,SAAW,QAC/B,SAAS,KAAK,YAAYA,CAAa,GAEzCA,EAAc,YAAcD,CAC7B,OAAQE,EAAU,CACjB,QAAQ,MAAM,6DAA8DA,CAAQ,CACrF,CACF,EAAE,GAAI,EAGP,QAAQ,IAAI,uEAAwEF,CAAiB,GAIrGlL,EAAO,QACL7E,GAAuBC,EACzB,QAAQ,KAAK,yFAAyF,EAEtG,QAAQ,IAAI,sDAAsD,EAGpE,QAAQ,IAAI,wDAAwD4E,EAAO,cAAc,OAAO,CAEnG,OAAQhQ,EAAO,CACd,QAAQ,KAAK,0EAA2EA,CAAK,EAC7F,QAAQ,IAAI,6FAA6F,CAC1G,CACP,MACM,QAAQ,KAAK,oDAAoD,EAInE,MAAM4L,EAAiB,aAAa,QAAQ,UAAU,EACtD,GAAIA,EACF,GAAI,CACF,MAAM9K,EAAW,KAAK,MAAM8K,CAAc,EACtC9K,GAAYA,EAAS,IACvB,QAAQ,IAAI,+DAAgEA,EAAS,EAAE,CAK1F,OAAQd,EAAO,CACd,QAAQ,MAAM,8DAA+DA,CAAK,CACnF,CAGH,QAAQ,IAAI,sDAAsD,CACnE,OAAQA,EAAO,CACd,QAAQ,MAAM,qDAAsDA,CAAK,CAC1E,CACH,EAGAib,GAA4B,EC1H5B,MAAMI,GAAiB,CACrB,QAAS,GACT,aAAc,GACd,YAAa,GACb,cAAe,IACf,eAAgB,CACd,QAAS,GACT,eAAgB,GAChB,YAAa,GACb,cAAe,GAChB,CACH,EAGMC,GAAiB,GACjBC,GAAc,GACdC,GAAS,GAGf,IAAItU,EAAS,CAAE,GAAGmU,IAMX,MAAMI,GAAoB,CAACC,EAAY,KAAO,CACnDxU,EAAS,CAAE,GAAGmU,GAAgB,GAAGK,CAAS,EAGtC,OAAO,OAAW,MACpB,OAAO,gBAAkB,CACvB,OAAAxU,EACA,eAAAoU,GACA,YAAAC,GACA,OAAAC,GACA,QAAS,IAAM,CAAC,GAAGF,EAAc,EACjC,eAAgB,IAAM,CAAC,GAAGC,EAAW,EACrC,UAAW,IAAM,CAAC,GAAGC,EAAM,EAC3B,UAAW,IAAM,CACfF,GAAe,OAAS,EACxB,QAAQ,IAAI,kCAAkC,CAC/C,EACD,iBAAkB,IAAM,CACtBC,GAAY,OAAS,EACrB,QAAQ,IAAI,+BAA+B,CAC5C,EACD,YAAa,IAAM,CACjBC,GAAO,OAAS,EAChB,QAAQ,IAAI,yBAAyB,CACtC,EACD,SAAU,IAAM,CACdF,GAAe,OAAS,EACxBC,GAAY,OAAS,EACrBC,GAAO,OAAS,EAChB,QAAQ,IAAI,2BAA2B,CACxC,EAED,iBAAkB,IAAMG,GAAsB,EAE9C,eAAgB,SAAY,CAC1B,GAAI,CACF,MAAM/Q,EAAS,MAAM6E,KACrB,eAAQ,IAAI,mCAAoC7E,CAAM,EAC/CA,CACR,OAAQ5K,EAAO,CACd,eAAQ,MAAM,mCAAoCA,CAAK,EAChD,CAAE,QAAS,GAAO,MAAOA,EAAM,OAAO,CAC9C,CACF,CACP,EAEA,EAQa4b,GAAgB,CAAC5L,EAAQR,EAASqM,EAAU,KAAO,CAC9D,GAAI,CAAC3U,EAAO,QAAS,OAErB,MAAM4U,EAAQ,CACZ,UAAW,IAAI,KAAM,EAAC,YAAa,EACnC,OAAA9L,EACA,QAAAR,EACA,QAAS,CAAE,GAAGqM,CAAS,CAC3B,EAWE,GARAP,GAAe,KAAKQ,CAAK,EAGrBR,GAAe,OAASpU,EAAO,eACjCoU,GAAe,OAAO,EAAGA,GAAe,OAASpU,EAAO,aAAa,EAInEA,EAAO,aAAc,CACvB,MAAM6U,EACJ/L,IAAW,UAAY,kCACvBA,IAAW,QAAU,gCACrBA,IAAW,UAAY,mCACvB,iCAEF,QAAQ,eAAe,gBAAgBA,EAAO,aAAa,KAAKR,CAAO,GAAIuM,CAAW,EACtF,QAAQ,IAAI,WAAYF,CAAO,EAC/B,QAAQ,IAAI,aAAcC,EAAM,SAAS,EACzC,QAAQ,SAAQ,CACjB,CAGG9L,IAAW,SACbwL,GAAO,KAAKM,CAAK,CAErB,EAWaE,GAAa,CAACC,EAAMnW,EAAKoW,EAAQlM,EAAQmM,EAASC,IAAS,CACtE,GAAI,CAAClV,EAAO,SAAW,CAACA,EAAO,eAAe,QAAS,OAEvD,MAAM4U,EAAQ,CACZ,UAAW,IAAI,KAAM,EAAC,YAAa,EACnC,KAAAG,EACA,IAAAnW,EACA,OAAAoW,EACA,OAAAlM,EACA,QAAS9I,EAAO,eAAe,eAAiBiV,EAAU,OAC1D,KAAMjV,EAAO,eAAe,YAC1B,OAAOkV,GAAS,UACXA,EAAK,OAASlV,EAAO,eAAe,cACjCkV,EAAK,UAAU,EAAGlV,EAAO,eAAe,aAAa,EAAI,MAE7DkV,EACF,MACR,EAWE,GARAb,GAAY,KAAKO,CAAK,EAGlBP,GAAY,OAASrU,EAAO,eAC9BqU,GAAY,OAAO,EAAGA,GAAY,OAASrU,EAAO,aAAa,EAI7DA,EAAO,aAAc,CACvB,MAAMmV,EAAYJ,IAAS,UAAY,cAAgB,eACjDF,EACH/L,GAAU,KAAOA,EAAS,IAAO,eACjCA,GAAU,IAAO,aAClB,gBAEEiM,IAAS,WACX,QAAQ,eAAe,gBAAgBC,CAAM,IAAIpW,CAAG,GAAIuW,CAAS,EACjE,QAAQ,IAAI,WAAYF,CAAO,EAC/B,QAAQ,IAAI,QAASC,CAAI,EACzB,QAAQ,SAAQ,IAEhB,QAAQ,eAAe,4BAA4BpM,CAAM,MAAMlK,CAAG,GAAIuW,EAAWN,EAAa,cAAc,EAC5G,QAAQ,IAAI,WAAYI,CAAO,EAC/B,QAAQ,IAAI,QAASC,CAAI,EACzB,QAAQ,SAAQ,EAEnB,CACH,EAMaT,GAAuB,IAAM,CACxC,MAAMW,EAAc,CAClB,qBAAsB,OAAO,YAAgB,IAAuD,MAAQ,UAC5G,qBAAsB,OAAO,YAAgB,IAAuD,MAAQ,UAC5G,WAAY,OAAO,QAAY,KAAe,QAAQ,KAAK,WAAa,MAAQ,UAChF,0BAA2B,OAAO,aAAiB,KAAe,aAAa,QAAQ,cAAc,EAAI,MAAQ,UACjH,cAAe,OAAO,YAAgB,KAAe,GACrD,OAAQ,OAAO,OAAW,IAAc,OAAO,SAAS,OAAS,SACrE,EAGE,OAAIpV,EAAO,eACT,QAAQ,MAAM,8BAA8B,EAC5C,QAAQ,MAAMoV,CAAW,EACzB,QAAQ,SAAQ,GAGXA,CACT,EAMa7M,GAAwB,SAAY,CAC/C,MAAMzE,EAAU,OAAO,YAAgB,KAA8B,wCACtD,OAAO,OAAW,KAAe,OAAO,sBACxC,OAAO,aAAiB,KAAe,aAAa,QAAQ,cAAc,EAEzF,GAAI,CAACA,EACH,OAAA4Q,GAAc,QAAS,0CAA0C,EAC1D,CAAE,QAAS,GAAO,MAAO,sBAAsB,EAIxD,GAAI,CACFA,GAAc,OAAQ,wCAAyC,CAAE,OAAQ,GAAG5Q,EAAO,UAAU,EAAG,CAAC,CAAC,KAAO,GAEzG,MAAMuR,EAAY,CAChB,4CACA,yCACA,gDACA,4CACN,EAEI,UAAWC,KAAYD,EACrB,GAAI,CACFP,GAAW,UAAWQ,EAAU,MAAO,KAAM,CAAE,cAAiB,UAAUxR,CAAM,EAAI,EAAE,IAAI,EAE1F,MAAMyR,EAAW,MAAM,MAAMD,EAAU,CACrC,OAAQ,MACR,QAAS,CACP,cAAiB,UAAUxR,CAAM,GACjC,eAAgB,mBAChB,OAAU,kBACX,CACX,CAAS,EAEK0R,EAAe,MAAMD,EAAS,OAIpC,GAFAT,GAAW,WAAYQ,EAAU,MAAOC,EAAS,OAAQA,EAAS,QAASC,CAAY,EAEnFD,EAAS,GACX,OAAAb,GAAc,UAAW,+CAA+CY,CAAQ,EAAE,EAC3E,CACL,QAAS,GACT,SAAAA,EACA,KAAM,SACN,OAAQC,EAAS,OACjB,KAAMC,CAClB,EAEUd,GAAc,UAAW,YAAYY,CAAQ,qBAAqBC,EAAS,MAAM,EAAE,CAEtF,OAAQzc,EAAO,CACd4b,GAAc,UAAW,uBAAuBY,CAAQ,GAAI,CAAE,MAAOxc,EAAM,OAAO,CAAE,CACrF,CAGH,OAAA4b,GAAc,QAAS,iCAAiC,EACjD,CAAE,QAAS,GAAO,MAAO,kCAAmC,KAAM,SAC1E,OAAQ5b,EAAO,CACd,OAAA4b,GAAc,QAAS,gCAAiC,CAAE,MAAO5b,EAAM,OAAO,CAAE,EACzE,CAAE,QAAS,GAAO,MAAOA,EAAM,OAAO,CAC9C,CACH,EAGAyb,GAAmB,EC7QnB,MAAMkB,GAAgB,OAAO,MCsB7BjC,KAeAkC,GAAS,WAAW,SAAS,eAAe,MAAM,CAAC,EAAE,OAClDpd,MAAA+S,GAAM,WAAN,CACC,eAACsK,GACC,UAAArd,MAACga,GAAc,aAAa,GAAM,QAAS,IAAM,OAAO,SAAS,SAC/D,SAACha,EAAA,IAAAsd,GAAA,CACC,SAACtd,MAAAud,GAAA,CACC,SAACvd,EAAA,IAAAwd,GAAA,CACC,eAACjD,GACC,UAAAva,MAACsT,GAAI,IACP,CACF,EACF,GACF,EACF,CACF,GACF,CACF", "names": ["CallTransition", "isActive", "onTransitionComplete", "buttonPosition", "mascotUrl", "showOverlay", "setShowOverlay", "useState", "showButtonClone", "setShowButtonClone", "showExpandingCircle", "setShowExpandingCircle", "showConnectionText", "setShowConnectionText", "showLoadingDots", "setShowLoadingDots", "showNeuralNetwork", "setShowNeuralNetwork", "showStatusIndicator", "setShowStatusIndicator", "particles", "setParticles", "nodes", "setNodes", "connections", "setConnections", "overlayRef", "useRef", "neuralNetworkRef", "useEffect", "startTransition", "resetTransition", "createParticles", "createNeuralNetwork", "newParticles", "i", "angle", "distance", "duration", "delay", "size", "particle", "container", "width", "height", "nodeCount", "newNodes", "centerX", "centerY", "x", "y", "pulseDelay", "pulseDuration", "newConnections", "startNode", "distances", "node", "dx", "dy", "a", "b", "connectCount", "j", "endNode", "opacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "animDuration", "jsxs", "jsx", "connection", "L", "icon", "iconRetina", "iconShadow", "attorneyIconUrl", "locationIconUrl", "error", "DEFAULT_US_CENTER", "DEFAULT_US_ZOOM", "attorneyIcon", "locationIcon", "img", "MapDossierView", "isVisible", "location", "attorneys", "mapContainerRef", "mapInstanceRef", "markersRef", "marker", "attorney", "MapView", "setIsVisible", "MapView$1", "withDevTools", "<PERSON><PERSON><PERSON><PERSON>", "item", "index", "AttorneyDossier$1", "CallSummary", "data", "CallSummary$1", "<PERSON><PERSON><PERSON>", "isDarkTheme", "isMenuOpen", "setIsMenuOpen", "useLocation", "navContainerRef", "subdomain", "getCurrentSubdomain", "isAttorneySubdomain", "toggleMenu", "Fragment", "Link", "AnimatedBackground", "ThemeToggle", "isDark", "onToggle", "SignInButton", "onClick", "SimplifiedPreview", "firmName", "logoUrl", "mascot", "buttonImageUrl", "primaryColor", "secondaryColor", "buttonColor", "backgroundColor", "backgroundOpacity", "buttonText", "buttonOpacity", "practiceAreaBackgroundOpacity", "textBackgroundColor", "practiceDescription", "welcomeMessage", "informationGathering", "officeAddress", "schedulingLink", "practiceAreas", "vapiInstructions", "vapiContext", "vapi_assistant_id", "voiceId", "aiModel", "customFields", "summaryPrompt", "structuredDataPrompt", "structuredDataSchema", "theme", "callActive", "setCallActive", "isLoading", "setIsLoading", "showPreviewHeader", "setShowPreviewHeader", "isStartingCall", "setIsStartingCall", "vapiCallKey", "setVapiCallKey", "hexToRgb", "hex", "r", "g", "getContrastColor", "hexColor", "startCall", "prev", "endCall", "handleImageUrl", "url", "processImageUrl", "previewStyles", "VapiCall", "ReactMarkdown", "remarkGfm", "rehypeRaw", "props", "area", "<PERSON><PERSON>", "PreviewFrameLoader", "attorney<PERSON><PERSON><PERSON>g", "setAttorneyConfig", "loading", "setLoading", "setError", "showActivateButton", "setShowActivateButton", "activating", "setActivating", "config", "getAttorneyConfigAsync", "isFallbackConfig", "previewConfig", "mapDatabaseToPreview", "err", "activateAssistant", "EnhancedPreviewNew", "MobileActivateAssistant", "onActivated", "showButton", "setShowButton", "isMobile", "setIsMobile", "checkMobile", "mobile", "needsActivation", "AuthOverlay", "isOpen", "onClose", "onSuccess", "authMethod", "setAuthMethod", "email", "setEmail", "password", "setPassword", "setFirmName", "setSubdomain", "step", "setStep", "handleGoogleSignIn", "isDev", "isSupabaseConfigured", "signInWithGoogle", "handleEmailSignUp", "e", "existingAttorney", "lookupError", "supabase", "authData", "authError", "attorneyData", "attorney<PERSON><PERSON><PERSON>", "assistant", "vapiAssistantService", "assistant<PERSON><PERSON><PERSON>", "handleOverlayClick", "supabaseNotConfigured", "mockAttorneyData", "logSupabaseConfig", "supabaseUrl", "supabaseKeyExists", "verifySupabaseConfig", "supabase<PERSON>ey", "hasPlaceholderUrl", "hasPlaceholderKey", "initializeSupabaseConfig", "result", "TestComponent", "VapiServiceManager", "vapiMcpService", "<PERSON><PERSON><PERSON><PERSON>", "forceMock", "options", "isAttorneyDashboard", "isPreview", "isProduction", "forceMcpMode", "forceDirect", "fastLoadingMode", "connected", "vapiServiceManager", "AttorneyP<PERSON><PERSON>leManager", "storedAttorney", "attorney<PERSON>oSync", "refreshed<PERSON><PERSON><PERSON><PERSON>", "refreshError", "userId", "userEmail", "name", "domain", "__vitePreload", "n", "newAttorney", "supabaseError", "latestAttorney", "attorneyWithAssistant", "id", "attorneyId", "payload", "updatedAttorney", "pendingUpdateId", "vapiRelevantFields", "nonVapiFields", "field", "changed<PERSON>ields", "nonVapiChanges", "vapiService", "firmAssistant", "newAssistant", "discrepancies", "supabaseData", "vapiError", "createError", "attorneyIdentifier", "assistant<PERSON><PERSON><PERSON><PERSON>", "voiceProvider", "updateConfig", "updatedAssistant", "updateData", "updatedData", "parsed", "listener", "attorney<PERSON><PERSON><PERSON><PERSON>Manager", "useAttorneyProfile", "user", "isAuthenticated", "useAuth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "syncStatus", "setSyncStatus", "lastSyncTime", "setLastSyncTime", "handleUpdate", "forceSync", "useCallback", "checkSyncStatus", "updateAttorney", "AttorneyProfileTest", "testResults", "setTestResults", "addTestResult", "success", "message", "testVapiMcpConnection", "mcpService", "testVapiDirectConnection", "testVapiMockMode", "assistants", "testAssistant", "testAttorneyDashboardMode", "status", "warning", "listError", "testListAssistants", "testForceSync", "testCheckSyncStatus", "testUpdateA<PERSON><PERSON>y", "formatRelativeTime", "date", "diffMs", "diffSec", "diffMin", "diffHour", "debug", "createDebugger", "PreviewPage", "lazy", "SimpleDemoPage", "SimpleDemoPageFallback", "CallControl", "Home", "showAttorneyInfo", "showCallSummary", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "callData", "setShowAttorneyInfo", "setShowCallSummary", "hideCreateAgentButton", "showTransition", "setShowTransition", "setButtonPosition", "buttonRef", "configMapping", "setConfigMapping", "module", "handleStartCall", "rect", "trackUserJourney", "handleTransitionComplete", "React", "previewUrl", "iframe", "handleMessage", "event", "DEFAULT_ASSISTANT_ID", "HomeContent", "App", "setCallData", "setIsAttorneySubdomain", "setAttorneyProfile", "isDevelopment", "setIsDevelopment", "availableSubdomains", "setAvailableSubdomains", "setIsDarkTheme", "selectedPracticeArea", "setSelectedPracticeArea", "showPreview", "setShowPreview", "activeConfigTab", "setActiveConfigTab", "showAuthOverlay", "setShowAuthOverlay", "configMode", "setConfigMode", "setLogoUrl", "state", "setState", "setPrimaryColor", "setSecondaryColor", "setButtonColor", "setBackgroundColor", "setBackgroundOpacity", "setButtonText", "setButtonOpacity", "previewHeight", "setPreviewHeight", "setPracticeDescription", "setWelcomeMessage", "setInformationGathering", "<PERSON><PERSON><PERSON>", "setAttorneyName", "setPracticeAreaBackgroundOpacity", "setTextBackgroundColor", "firmUrl", "setFirmUrl", "isUrlLoading", "setIsUrlLoading", "iframeRef", "handlePracticeAreaChange", "handleLogoUpload", "file", "reader", "imageId", "storeImage", "handleRemoveLogo", "goToPreview", "handleUrlSubmit", "urlToProcess", "storedUrl", "toast", "scrapeWebsite", "extractedData", "practiceAreaMap", "lowerArea", "keyword", "mappedArea", "generatedFirmName", "word", "toggleTheme", "pendingUrl", "timestamp", "now", "timestampNum", "subdomainValue", "isAttorneySub", "checkIsAttorneySubdomain", "loadAttorneyProfile", "profile", "subdomains", "existingIframes", "cleanupError", "callCardContainer", "handleGetStarted", "handleUrlAutoConfig", "handleAutoConfigureClicked", "handleAutoConfigButtonClick", "urlInput", "handleAuthSuccess", "isEndingCall", "setIsEndingCall", "isCallStillActive", "isForcedEndWhileSpeaking", "checkCount", "max<PERSON><PERSON><PERSON>", "checkCallInterval", "ToastContainer", "Routes", "Route", "Navigate", "AboutPage", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LoginPage", "SubdomainTestPage", "VapiTestPage", "VapiComparisonTest", "SimpleCompleteProfile", "Dashboard", "CrmDemo", "Suspense", "SimplePreviewPage", "App$1", "Error<PERSON>ou<PERSON><PERSON>", "errorInfo", "InnerAuthProvider", "children", "syncTools", "useSync", "<PERSON>th<PERSON><PERSON><PERSON>", "SyncAuthProvider", "Sync<PERSON>rovider", "REQUIRED_VARIABLES", "verifyEnvironment", "results", "varName", "value", "isPresent", "isMasked", "logEnvironmentStatus", "info", "initEnvironmentVerification", "FALLBACK_SUPABASE_URL", "FALLBACK_SUPABASE_KEY", "FALLBACK_VAPI_KEY", "hasSupabaseUrl", "has<PERSON><PERSON><PERSON>se<PERSON>ey", "hasVapi<PERSON>ey", "initAttorneyProfileManager", "connectionWarning", "warningBanner", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DEFAULT_CONFIG", "connectionLogs", "networkLogs", "errors", "configure<PERSON><PERSON>ugger", "newConfig", "checkVapiEnvironment", "logConnection", "details", "entry", "statusColor", "logNetwork", "type", "method", "headers", "body", "typeColor", "environment", "endpoints", "endpoint", "response", "responseBody", "originalFetch", "ReactDOM", "ProductionErrorBoundary", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThemeProvider", "Attorney<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../src/components/CallTransition.jsx", "../../src/components/MapDossierView.jsx", "../../src/components/MapView.jsx", "../../src/components/AttorneyDossier.jsx", "../../src/components/CallSummary.jsx", "../../src/components/Navbar.jsx", "../../src/components/GlobeDossierView.jsx", "../../src/components/AnimatedBackground.jsx", "../../src/components/ThemeToggle.jsx", "../../src/components/SignInButton.jsx", "../../src/components/SimplifiedPreview.jsx", "../../src/components/preview/PreviewFrameLoader.jsx", "../../src/components/mobile/MobileActivateAssistant.jsx", "../../src/components/AuthOverlay.jsx", "../../src/testSupabase.js", "../../src/utils/supabaseConfigVerifier.js", "../../src/components/TestComponent.jsx", "../../src/services/vapiServiceManager.js", "../../src/services/AttorneyProfileManager.js", "../../src/hooks/useAttorneyProfile.js", "../../src/components/AttorneyProfileTest.jsx", "../../src/App.jsx", "../../src/utils/ErrorBoundary.jsx", "../../src/components/SyncAuthProvider.jsx", "../../src/utils/environmentVerifier.js", "../../src/utils/initAttorneyProfileManager.js", "../../src/utils/vapiMcpDebugger.js", "../../src/utils/vapiNetworkInterceptor.js", "../../src/main.jsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport './CallTransition.css';\r\n\r\nconst CallTransition = ({ isActive, onTransitionComplete, buttonPosition, mascotUrl }) => {\r\n  const [showOverlay, setShowOverlay] = useState(false);\r\n  const [showButtonClone, setShowButtonClone] = useState(false);\r\n  const [showExpandingCircle, setShowExpandingCircle] = useState(false);\r\n  const [showConnectionText, setShowConnectionText] = useState(false);\r\n  const [showLoadingDots, setShowLoadingDots] = useState(false);\r\n  const [showNeuralNetwork, setShowNeuralNetwork] = useState(false);\r\n  const [showStatusIndicator, setShowStatusIndicator] = useState(false);\r\n  const [buttonCloneStyle, setButtonCloneStyle] = useState({});\r\n  const [particles, setParticles] = useState([]);\r\n  const [nodes, setNodes] = useState([]);\r\n  const [connections, setConnections] = useState([]);\r\n\r\n  const overlayRef = useRef(null);\r\n  const neuralNetworkRef = useRef(null);\r\n\r\n  // Initialize the transition when isActive changes to true\r\n  useEffect(() => {\r\n    if (isActive) {\r\n      startTransition();\r\n    } else {\r\n      resetTransition();\r\n    }\r\n  }, [isActive]);\r\n\r\n  // Start the transition sequence\r\n  const startTransition = () => {\r\n    // Show overlay\r\n    setShowOverlay(true);\r\n\r\n    // Sequence of animations - removed button clone animation\r\n    setTimeout(() => setShowExpandingCircle(true), 100);\r\n    setTimeout(() => createParticles(), 300);\r\n    setTimeout(() => setShowConnectionText(true), 600);\r\n    setTimeout(() => setShowLoadingDots(true), 800);\r\n    setTimeout(() => {\r\n      setShowNeuralNetwork(true);\r\n      createNeuralNetwork();\r\n    }, 1000);\r\n    setTimeout(() => setShowStatusIndicator(true), 1300);\r\n\r\n    // Complete transition\r\n    setTimeout(() => {\r\n      if (onTransitionComplete) {\r\n        onTransitionComplete();\r\n      }\r\n    }, 2300);\r\n  };\r\n\r\n  // Reset all transition states\r\n  const resetTransition = () => {\r\n    setShowOverlay(false);\r\n    setShowButtonClone(false);\r\n    setShowExpandingCircle(false);\r\n    setShowConnectionText(false);\r\n    setShowLoadingDots(false);\r\n    setShowNeuralNetwork(false);\r\n    setShowStatusIndicator(false);\r\n    setParticles([]);\r\n    setNodes([]);\r\n    setConnections([]);\r\n  };\r\n\r\n  // Create particle effects\r\n  const createParticles = () => {\r\n    const newParticles = [];\r\n    const particleCount = 30;\r\n\r\n    for (let i = 0; i < particleCount; i++) {\r\n      const angle = Math.random() * Math.PI * 2;\r\n      const distance = 100 + Math.random() * 150;\r\n      const duration = 1 + Math.random() * 2;\r\n      const delay = Math.random() * 0.5;\r\n      const size = 2 + Math.random() * 4;\r\n\r\n      const particle = {\r\n        id: i,\r\n        style: {\r\n          top: '50%',\r\n          left: '50%',\r\n          width: `${size}px`,\r\n          height: `${size}px`,\r\n          transform: `translate(-50%, -50%) translate(${Math.cos(angle) * distance}px, ${Math.sin(angle) * distance}px)`,\r\n          opacity: 0,\r\n          animation: `particleMove ${duration}s ease-out forwards`,\r\n          animationDelay: `${delay}s`\r\n        }\r\n      };\r\n\r\n      newParticles.push(particle);\r\n    }\r\n\r\n    setParticles(newParticles);\r\n  };\r\n\r\n  // Create neural network visualization\r\n  const createNeuralNetwork = () => {\r\n    if (!neuralNetworkRef.current) return;\r\n\r\n    const container = neuralNetworkRef.current;\r\n    const { width, height } = container.getBoundingClientRect();\r\n\r\n    // Create nodes in a more structured pattern\r\n    const nodeCount = 25; // Increased node count\r\n    const newNodes = [];\r\n    const centerX = width / 2;\r\n    const centerY = height / 2;\r\n\r\n    // Create nodes in a circular pattern around the center\r\n    for (let i = 0; i < nodeCount; i++) {\r\n      // Create a more structured pattern with some randomness\r\n      const angle = (i / nodeCount) * Math.PI * 2 + (Math.random() * 0.5 - 0.25);\r\n      const distance = 50 + Math.random() * (Math.min(width, height) * 0.35);\r\n      const x = centerX + Math.cos(angle) * distance;\r\n      const y = centerY + Math.sin(angle) * distance;\r\n\r\n      // Vary the size more for visual interest\r\n      const size = 3 + Math.random() * 6;\r\n      const pulseDelay = Math.random() * 3;\r\n      const pulseDuration = 1.5 + Math.random() * 2;\r\n\r\n      newNodes.push({\r\n        id: i,\r\n        x,\r\n        y,\r\n        size,\r\n        style: {\r\n          top: `${y}px`,\r\n          left: `${x}px`,\r\n          width: `${size}px`,\r\n          height: `${size}px`,\r\n          animationDelay: `${pulseDelay}s`,\r\n          animationDuration: `${pulseDuration}s`,\r\n          boxShadow: `0 0 ${size * 2}px rgba(41, 121, 255, 0.8)`,\r\n          background: `radial-gradient(circle, rgba(41, 121, 255, 1) 0%, rgba(41, 121, 255, 0.7) 70%, rgba(41, 121, 255, 0) 100%)`\r\n        }\r\n      });\r\n    }\r\n\r\n    setNodes(newNodes);\r\n\r\n    // Create more meaningful connections between nodes\r\n    const newConnections = [];\r\n\r\n    // Connect each node to its 2-3 nearest neighbors\r\n    for (let i = 0; i < newNodes.length; i++) {\r\n      const startNode = newNodes[i];\r\n\r\n      // Calculate distances to all other nodes\r\n      const distances = newNodes\r\n        .filter(node => node.id !== startNode.id)\r\n        .map(node => {\r\n          const dx = node.x - startNode.x;\r\n          const dy = node.y - startNode.y;\r\n          return {\r\n            node,\r\n            distance: Math.sqrt(dx * dx + dy * dy)\r\n          };\r\n        })\r\n        .sort((a, b) => a.distance - b.distance);\r\n\r\n      // Connect to 2-3 closest nodes\r\n      const connectCount = 2 + Math.floor(Math.random() * 2);\r\n      for (let j = 0; j < Math.min(connectCount, distances.length); j++) {\r\n        const endNode = distances[j].node;\r\n        const dx = endNode.x - startNode.x;\r\n        const dy = endNode.y - startNode.y;\r\n        const distance = Math.sqrt(dx * dx + dy * dy);\r\n        const angle = Math.atan2(dy, dx) * (180 / Math.PI);\r\n\r\n        // Add some variation to connection appearance\r\n        const opacity = 0.3 + Math.random() * 0.4;\r\n        const animDelay = Math.random() * 2;\r\n        const animDuration = 1 + Math.random() * 3;\r\n\r\n        newConnections.push({\r\n          id: `${startNode.id}-${endNode.id}`,\r\n          style: {\r\n            top: `${startNode.y + startNode.size / 2}px`,\r\n            left: `${startNode.x + startNode.size / 2}px`,\r\n            width: `${distance}px`,\r\n            transform: `rotate(${angle}deg)`,\r\n            opacity: opacity,\r\n            animationDelay: `${animDelay}s`,\r\n            animationDuration: `${animDuration}s`,\r\n            height: '1px', // Thinner lines\r\n            background: `linear-gradient(90deg, rgba(41, 121, 255, ${opacity}) 0%, rgba(41, 121, 255, ${opacity * 0.5}) 100%)`\r\n          }\r\n        });\r\n      }\r\n    }\r\n\r\n    setConnections(newConnections);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      ref={overlayRef}\r\n      className={`call-transition-overlay ${showOverlay ? 'active' : ''}`}\r\n    >\r\n      {/* Removed button clone with image to prevent warping */}\r\n\r\n      {/* Expanding circle animation */}\r\n      {showExpandingCircle && (\r\n        <div className={`expanding-circle ${showExpandingCircle ? 'active' : ''}`}></div>\r\n      )}\r\n\r\n      {/* Particles animation */}\r\n      <div className=\"particles-container\">\r\n        {particles.map(particle => (\r\n          <div\r\n            key={`particle-${particle.id}`}\r\n            className=\"particle\"\r\n            style={particle.style}\r\n          ></div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Neural network visualization */}\r\n      <div\r\n        ref={neuralNetworkRef}\r\n        className={`neural-network ${showNeuralNetwork ? 'active' : ''}`}\r\n      >\r\n        {nodes.map(node => (\r\n          <div\r\n            key={`node-${node.id}`}\r\n            className=\"node pulse\"\r\n            style={node.style}\r\n          ></div>\r\n        ))}\r\n\r\n        {connections.map(connection => (\r\n          <div\r\n            key={`connection-${connection.id}`}\r\n            className=\"connection-line\"\r\n            style={connection.style}\r\n          ></div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* Connection text with integrated loading dots */}\r\n      <div className={`connection-text ${showConnectionText ? 'active' : ''}`}>\r\n        Connecting to LegalScout AI\r\n        <span className={`loading-dots-inline ${showLoadingDots ? 'active' : ''}`}>\r\n          <span className=\"dot\">.</span>\r\n          <span className=\"dot\">.</span>\r\n          <span className=\"dot\">.</span>\r\n        </span>\r\n      </div>\r\n\r\n      {/* Status indicator */}\r\n      <div className={`status-indicator ${showStatusIndicator ? 'active' : ''}`}>\r\n        <div className=\"status-icon\"></div>\r\n        <div className=\"status-text\">Establishing secure connection</div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CallTransition;\r\n", "import React, { useState, useEffect, useRef } from 'react';\r\nimport L from 'leaflet';\r\nimport 'leaflet/dist/leaflet.css'; // Re-add Leaflet CSS import\r\nimport './MapDossierView.css';\r\nimport './Map.css';\r\nimport { withDevTools, createDebugger, trackUserJourney } from '../utils/debugConfig';\r\n\r\n// Create debugger for MapDossierView component\r\nconst debug = createDebugger('MapDossierView');\r\n\r\n// Import marker icons to avoid missing marker issue\r\nimport icon from 'leaflet/dist/images/marker-icon.png';\r\nimport iconRetina from 'leaflet/dist/images/marker-icon-2x.png';\r\nimport iconShadow from 'leaflet/dist/images/marker-shadow.png';\r\n\r\n// Fix default icon issue in Leaflet\r\ndelete L.Icon.Default.prototype._getIconUrl;\r\nL.Icon.Default.mergeOptions({\r\n  iconUrl: icon,\r\n  iconRetinaUrl: iconRetina,\r\n  shadowUrl: iconShadow\r\n});\r\n\r\n// Try to import SVG files for custom markers\r\nlet attorneyIconUrl, locationIconUrl;\r\ntry {\r\n  // Set the dynamic paths for the SVG files\r\n  attorneyIconUrl = new URL('/attorney-marker.svg', import.meta.url).href;\r\n  locationIconUrl = new URL('/location-marker.svg', import.meta.url).href;\r\n} catch (error) {\r\n  console.error('Error loading SVG icons:', error);\r\n  // Use fallback icons (Leaflet default) if SVG loading fails\r\n  attorneyIconUrl = 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png';\r\n  locationIconUrl = 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png';\r\n}\r\n\r\n// Define constants for the Continental US view\r\nconst DEFAULT_US_CENTER = [39.8283, -98.5795]; // Center of the continental US\r\nconst DEFAULT_US_ZOOM = 4; // Zoom level for continental US view\r\n\r\n// Define custom icon for attorneys\r\nconst attorneyIcon = L.icon({\r\n  iconUrl: attorneyIconUrl,\r\n  iconSize: [32, 32], // size of the icon\r\n  iconAnchor: [16, 32], // point of the icon which will correspond to marker's location\r\n  popupAnchor: [0, -32] // point from which the popup should open relative to the iconAnchor\r\n});\r\n\r\n// Define custom icon for client location\r\nconst locationIcon = L.icon({\r\n  iconUrl: locationIconUrl,\r\n  iconSize: [32, 32], // size of the icon\r\n  iconAnchor: [16, 32], // point of the icon which will correspond to marker's location\r\n  popupAnchor: [0, -32] // point from which the popup should open relative to the iconAnchor\r\n});\r\n\r\n// Add error handler for SVG loading - fallback to PNG\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n  // Set up fallback for attorney marker\r\n  const attorneyMarkerElements = document.querySelectorAll('.attorney-marker-icon img');\r\n  attorneyMarkerElements.forEach(img => {\r\n    img.onerror = function() {\r\n      this.src = '/attorney-marker.png';\r\n    };\r\n  });\r\n  \r\n  // Set up fallback for location marker\r\n  const locationMarkerElements = document.querySelectorAll('.location-marker-icon img');\r\n  locationMarkerElements.forEach(img => {\r\n    img.onerror = function() {\r\n      this.src = '/location-marker.png';\r\n    };\r\n  });\r\n});\r\n\r\n// Helper function to get a user-friendly location name\r\nconst getLocationName = (location) => {\r\n  return location.address || `${location.lat.toFixed(4)}, ${location.lng.toFixed(4)}`;\r\n};\r\n\r\nconst MapDossierView = ({ isVisible, location, attorneys = [] }) => {\r\n  const mapContainerRef = useRef(null);\r\n  const mapInstanceRef = useRef(null);\r\n  const markersRef = useRef({\r\n    attorneys: [],\r\n    clientLocation: null\r\n  });\r\n\r\n  // Initialize map when component becomes visible\r\n  useEffect(() => {\r\n    if (!isVisible || !mapContainerRef.current) return;\r\n\r\n    // Initialize map if it doesn't exist yet\r\n    if (!mapInstanceRef.current) {\r\n      // Create map instance\r\n      mapInstanceRef.current = L.map(mapContainerRef.current, {\r\n        // Always initialize with continental US view\r\n        center: DEFAULT_US_CENTER,\r\n        zoom: DEFAULT_US_ZOOM,\r\n        zoomControl: true,\r\n        attributionControl: false\r\n      });\r\n\r\n      // Add tile layer\r\n      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\r\n        maxZoom: 19,\r\n        className: 'dark-tiles' // Apply dark styling\r\n      }).addTo(mapInstanceRef.current);\r\n    }\r\n\r\n    // Update the map view if we have a more specific location\r\n    if (location && location.lat && location.lng) {\r\n      mapInstanceRef.current.setView([location.lat, location.lng], 11);\r\n      \r\n      // Add client location marker if we have location data\r\n      if (!markersRef.current.clientLocation && location.lat && location.lng) {\r\n        markersRef.current.clientLocation = L.marker([location.lat, location.lng], { icon: locationIcon })\r\n          .addTo(mapInstanceRef.current)\r\n          .bindPopup(location.address || 'Your Location');\r\n      }\r\n    }\r\n\r\n    // Cleanup function to destroy map when component unmounts\r\n    return () => {\r\n      if (mapInstanceRef.current) {\r\n        mapInstanceRef.current.remove();\r\n        mapInstanceRef.current = null;\r\n        markersRef.current = {\r\n          attorneys: [],\r\n          clientLocation: null\r\n        };\r\n      }\r\n    };\r\n  }, [isVisible, location]);\r\n\r\n  // Add attorney markers when attorneys data changes\r\n  useEffect(() => {\r\n    if (!mapInstanceRef.current || !attorneys.length) return;\r\n\r\n    // Clear existing attorney markers\r\n    markersRef.current.attorneys.forEach(marker => {\r\n      if (mapInstanceRef.current) {\r\n        mapInstanceRef.current.removeLayer(marker);\r\n      }\r\n    });\r\n    markersRef.current.attorneys = [];\r\n\r\n    // Add new attorney markers\r\n    attorneys.forEach(attorney => {\r\n      if (attorney.latitude && attorney.longitude) {\r\n        const marker = L.marker([attorney.latitude, attorney.longitude], { icon: attorneyIcon })\r\n          .addTo(mapInstanceRef.current)\r\n          .bindPopup(`<strong>${attorney.name}</strong><br>${attorney.practiceArea || ''}`);\r\n        \r\n        markersRef.current.attorneys.push(marker);\r\n      }\r\n    });\r\n  }, [attorneys]);\r\n\r\n  return (\r\n    <div className=\"map-dossier-view\">\r\n      <div ref={mapContainerRef} className=\"map-container\"></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MapDossierView;", "import React, { useState } from 'react';\r\nimport MapDossierView from './MapDossierView.jsx';\r\nimport { withDevTools, createDebugger } from '../utils/debugConfig';\r\n\r\n// Create debugger for MapView component\r\nconst debug = createDebugger('MapView');\r\n\r\nconst MapView = ({ attorney }) => {\r\n  const [isVisible, setIsVisible] = useState(true);\r\n  \r\n  // Extract location from attorney data\r\n  const location = attorney?.location || {\r\n    lat: 40.7128, // Default to NYC if no location\r\n    lng: -74.0060\r\n  };\r\n  \r\n  // Format attorneys array from single attorney object\r\n  const attorneys = attorney ? [{\r\n    name: attorney.name || 'Attorney',\r\n    practiceArea: attorney.practiceArea || 'General Practice',\r\n    specialty: attorney.specialty,\r\n    latitude: attorney.location?.lat,\r\n    longitude: attorney.location?.lng,\r\n    distance: attorney.distance || '< 5',\r\n    phone: attorney.phone,\r\n    email: attorney.email,\r\n    website: attorney.website,\r\n    address: attorney.address\r\n  }] : [];\r\n  \r\n  return (\r\n    <div className=\"map-view-container\">\r\n      <div className=\"map-controls\">\r\n        <button \r\n          className=\"toggle-map-button\"\r\n          onClick={() => setIsVisible(!isVisible)}\r\n        >\r\n          {isVisible ? 'Hide Map' : 'Show Map'}\r\n        </button>\r\n      </div>\r\n      \r\n      <MapDossierView \r\n        isVisible={isVisible} \r\n        location={location} \r\n        attorneys={attorneys}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\n// Export the component with enhanced DevTools support\r\nexport default withDevTools(MapView, {\r\n  displayName: 'MapView',\r\n  type: 'container',\r\n  description: 'Container component for the map display with attorney information',\r\n  responsibleFor: ['map visibility toggle', 'attorney data formatting']\r\n}); ", "import React from 'react';\r\nimport { withDevTools, createDebugger } from '../utils/debugConfig';\r\n\r\n// Create debugger for AttorneyDossier component\r\nconst debug = createDebugger('AttorneyDossier');\r\n\r\nconst AttorneyDossier = ({ attorney }) => {\r\n  if (!attorney) {\r\n    return (\r\n      <div className=\"attorney-dossier empty-dossier\">\r\n        <h2>No Attorney Selected</h2>\r\n        <p>Please select an attorney from the map or list.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"attorney-dossier\">\r\n      <h2>Attorney Information</h2>\r\n      \r\n      <div className=\"dossier-section\">\r\n        <h3>{attorney.name || 'Attorney'}</h3>\r\n        <p className=\"practice-area\">{attorney.practiceArea || attorney.specialty || 'General Practice'}</p>\r\n        \r\n        {attorney.distance && (\r\n          <p className=\"distance\">\r\n            <strong>Distance:</strong> {attorney.distance} miles\r\n          </p>\r\n        )}\r\n      </div>\r\n      \r\n      <div className=\"dossier-section\">\r\n        <h4>Contact Information</h4>\r\n        <ul className=\"contact-list\">\r\n          {attorney.phone && (\r\n            <li>\r\n              <strong>Phone:</strong> <a href={`tel:${attorney.phone}`}>{attorney.phone}</a>\r\n            </li>\r\n          )}\r\n          \r\n          {attorney.email && (\r\n            <li>\r\n              <strong>Email:</strong> <a href={`mailto:${attorney.email}`}>{attorney.email}</a>\r\n            </li>\r\n          )}\r\n          \r\n          {attorney.website && (\r\n            <li>\r\n              <strong>Website:</strong> <a href={attorney.website} target=\"_blank\" rel=\"noopener noreferrer\">Visit Website</a>\r\n            </li>\r\n          )}\r\n        </ul>\r\n      </div>\r\n      \r\n      {attorney.address && (\r\n        <div className=\"dossier-section\">\r\n          <h4>Office Address</h4>\r\n          <address>{attorney.address}</address>\r\n        </div>\r\n      )}\r\n      \r\n      {attorney.bio && (\r\n        <div className=\"dossier-section\">\r\n          <h4>About</h4>\r\n          <p>{attorney.bio}</p>\r\n        </div>\r\n      )}\r\n      \r\n      {attorney.expertise && attorney.expertise.length > 0 && (\r\n        <div className=\"dossier-section\">\r\n          <h4>Areas of Expertise</h4>\r\n          <ul className=\"expertise-list\">\r\n            {attorney.expertise.map((item, index) => (\r\n              <li key={index}>{item}</li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      )}\r\n      \r\n      <div className=\"dossier-actions\">\r\n        <button className=\"contact-button\">Contact This Attorney</button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Export the component with enhanced DevTools support\r\nexport default withDevTools(AttorneyDossier, {\r\n  displayName: 'AttorneyDossier',\r\n  type: 'display',\r\n  description: 'Displays detailed information about a selected attorney',\r\n  responsibleFor: ['attorney profile display', 'contact information', 'expertise listing']\r\n}); ", "import React from 'react';\r\nimport { withDevTools, createDebugger } from '../utils/debugConfig';\r\n\r\n// Create debugger for CallSummary component\r\nconst debug = createDebugger('CallSummary');\r\n\r\nconst CallSummary = ({ data }) => {\r\n  if (!data) {\r\n    return (\r\n      <div className=\"call-summary-empty\">\r\n        <h2>No Call Summary Available</h2>\r\n        <p>There is no summary data available for this call.</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"call-summary\">\r\n      <h2>Call Summary</h2>\r\n      \r\n      <div className=\"summary-section\">\r\n        <h3>Thank you for using LegalScout</h3>\r\n        <p>We've gathered the following information about your case:</p>\r\n      </div>\r\n      \r\n      <div className=\"summary-details\">\r\n        {data.issue && (\r\n          <div className=\"summary-item\">\r\n            <strong>Legal Issue:</strong> {data.issue}\r\n          </div>\r\n        )}\r\n        \r\n        {data.practiceArea && (\r\n          <div className=\"summary-item\">\r\n            <strong>Practice Area:</strong> {data.practiceArea}\r\n          </div>\r\n        )}\r\n        \r\n        {data.location?.address && (\r\n          <div className=\"summary-item\">\r\n            <strong>Location:</strong> {data.location.address}\r\n          </div>\r\n        )}\r\n        \r\n        {data.noteworthy && (\r\n          <div className=\"summary-item\">\r\n            <strong>Case Details:</strong> {data.noteworthy}\r\n          </div>\r\n        )}\r\n        \r\n        {data.urgency && (\r\n          <div className=\"summary-item\">\r\n            <strong>Urgency Level:</strong> {data.urgency}\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      <div className=\"next-steps\">\r\n        <h3>Next Steps</h3>\r\n        {data.urgency === \"High\" ? (\r\n          <p>\r\n            Based on the urgency of your case, we recommend speaking with an attorney \r\n            as soon as possible. You can use the \"Start New Consultation\" button below \r\n            to connect with an available attorney.\r\n          </p>\r\n        ) : (\r\n          <p>\r\n            Thank you for your consultation. We've sent additional resources to your \r\n            email (if provided). You can start a new consultation anytime if you need\r\n            further assistance.\r\n          </p>\r\n        )}\r\n      </div>\r\n      \r\n      <div className=\"summary-actions\">\r\n        <button className=\"email-summary-button\">Email Summary</button>\r\n        <button className=\"download-summary-button\">Download PDF</button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Export the component with enhanced DevTools support\r\nexport default withDevTools(CallSummary, {\r\n  displayName: 'CallSummary',\r\n  type: 'display',\r\n  description: 'Displays a summary of the call details and next steps',\r\n  responsibleFor: ['call summary display', 'case information display', 'next steps guidance']\r\n}); ", "import React, { useState, useRef } from 'react';\r\nimport { Link, useLocation } from 'react-router-dom';\r\nimport { getCurrentSubdomain } from '../utils/subdomainTester';\r\n\r\nconst Navbar = ({ isDarkTheme }) => {\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const location = useLocation();\r\n  const navContainerRef = useRef(null);\r\n\r\n  // Check if we're on an attorney subdomain\r\n  const subdomain = getCurrentSubdomain();\r\n  const isAttorneySubdomain = subdomain !== 'default' &&\r\n                            subdomain !== 'www' &&\r\n                            subdomain !== '' &&\r\n                            subdomain !== null;\r\n\r\n  const toggleMenu = () => {\r\n    setIsMenuOpen(!isMenuOpen);\r\n  };\r\n\r\n  // Removed bone cursor effect for cleaner navigation experience\r\n\r\n  return (\r\n    <div className={`nav-container ${isMenuOpen ? 'menu-active' : ''}`} ref={navContainerRef}>\r\n      <button\r\n        className={`hamburger-menu ${isMenuOpen ? 'active' : ''}`}\r\n        onClick={toggleMenu}\r\n        aria-label=\"Toggle navigation menu\"\r\n        aria-expanded={isMenuOpen}\r\n      >\r\n        <span></span>\r\n        <span></span>\r\n        <span></span>\r\n      </button>\r\n\r\n      <nav className={`main-nav ${isMenuOpen ? 'active' : ''}`}>\r\n        <ul>\r\n          {/* Only show Home and Agent links if not on an attorney subdomain */}\r\n          {!isAttorneySubdomain && (\r\n            <>\r\n              <li>\r\n                <Link\r\n                  to=\"/home\"\r\n                  className={location.pathname === '/home' || location.pathname === '/' ? 'active' : ''}\r\n                  onClick={() => setIsMenuOpen(false)}\r\n                  data-text=\"Home\"\r\n                >\r\n                  <i className=\"nav-icon fas fa-home\"></i>\r\n                  Home\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link\r\n                  to=\"/demo\"\r\n                  className={location.pathname === '/demo' ? 'active' : ''}\r\n                  onClick={() => setIsMenuOpen(false)}\r\n                  data-text=\"Agent\"\r\n                >\r\n                  <i className=\"nav-icon fas fa-gavel\"></i>\r\n                  Agent\r\n                </Link>\r\n              </li>\r\n            </>\r\n          )}\r\n          {/* Always show About link */}\r\n          <li>\r\n            <Link\r\n              to=\"/about\"\r\n              className={location.pathname === '/about' ? 'active' : ''}\r\n              onClick={() => setIsMenuOpen(false)}\r\n              data-text=\"About\"\r\n            >\r\n              <i className=\"nav-icon fas fa-info-circle\"></i>\r\n              About\r\n            </Link>\r\n          </li>\r\n\r\n\r\n        </ul>\r\n      </nav>\r\n\r\n      {/* Add overlay for mobile menu */}\r\n      <div\r\n        className={`nav-overlay ${isMenuOpen ? 'active' : ''}`}\r\n        onClick={() => setIsMenuOpen(false)}\r\n        aria-hidden=\"true\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Navbar;", "import React, { useEffect, useRef, useState } from 'react';\r\nimport './GlobeDossierView.css';\r\nimport './Map.css';\r\n\r\n// Use global THREE object loaded from CDN to avoid multiple instances\r\nconst THREE = window.THREE;\r\nconst OrbitControls = window.THREE?.OrbitControls;\r\n\r\nconst GlobeDossierView = ({\r\n  isVisible = true,\r\n  caseData = {},\r\n  attorneys = [],\r\n  onSelectAttorney = () => {}\r\n}) => {\r\n  console.log(\"GlobeDossierView render called, isVisible:\", isVisible, \"caseData:\", caseData, \"attorneys:\", attorneys);\r\n\r\n  const mountRef = useRef(null);\r\n  const sceneRef = useRef(null);\r\n  const globeRef = useRef(null);\r\n  const [hasLocation, setHasLocation] = useState(false);\r\n  const [locationName, setLocationName] = useState(null);\r\n\r\n  // Set up Three.js scene\r\n  useEffect(() => {\r\n    if (!isVisible || !mountRef.current) return;\r\n\r\n    // Check if Three.js is available\r\n    if (!window.THREE) {\r\n      console.error(\"THREE is not loaded. Make sure it's included in your HTML.\");\r\n      return;\r\n    }\r\n\r\n    // Clear any existing canvas\r\n    while (mountRef.current.firstChild) {\r\n      mountRef.current.removeChild(mountRef.current.firstChild);\r\n    }\r\n\r\n    // Scene setup\r\n    const scene = new THREE.Scene();\r\n\r\n    // Get container dimensions - use smallest viewport dimension\r\n    const container = mountRef.current;\r\n    const vw = Math.min(window.innerWidth * 0.8, 600); // 80% of viewport width, max 600px\r\n    const vh = Math.min(window.innerHeight - 120, 600); // Viewport height minus margins, max 600px\r\n    const size = Math.min(vw, vh); // Use the smaller dimension\r\n\r\n    // Camera setup with fixed aspect ratio\r\n    const camera = new THREE.PerspectiveCamera(\r\n      45, // Standard FOV\r\n      1, // Force 1:1 aspect ratio\r\n      0.1,\r\n      1000\r\n    );\r\n    camera.position.z = 250; // Adjust camera distance\r\n\r\n    // Renderer setup\r\n    const renderer = new THREE.WebGLRenderer({\r\n      antialias: true,\r\n      alpha: true,\r\n      logarithmicDepthBuffer: true,\r\n      powerPreference: \"high-performance\"\r\n    });\r\n\r\n    renderer.setSize(size, size);\r\n    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));\r\n    renderer.setClearColor(0x000000, 0);\r\n\r\n    // Append renderer\r\n    container.appendChild(renderer.domElement);\r\n\r\n    // Create globe\r\n    const Globe = window.ThreeGlobe;\r\n    if (!Globe) {\r\n      console.error(\"ThreeGlobe is not loaded\");\r\n      return;\r\n    }\r\n\r\n    const globe = new Globe()\r\n      .globeImageUrl('//unpkg.com/three-globe/example/img/earth-blue-marble.jpg')\r\n      .bumpImageUrl('//unpkg.com/three-globe/example/img/earth-topology.png')\r\n      .hexPolygonsData([])\r\n      .hexPolygonResolution(3)\r\n      .hexPolygonMargin(0.7)\r\n      .showAtmosphere(true)\r\n      .atmosphereColor('#4169E1')\r\n      .atmosphereAltitude(0.15);\r\n\r\n    // Scale globe\r\n    globe.scale.set(1, 1, 1);\r\n    scene.add(globe);\r\n    globeRef.current = globe;\r\n\r\n    // Lighting\r\n    const ambientLight = new THREE.AmbientLight(0xffffff, 0.8);\r\n    scene.add(ambientLight);\r\n\r\n    const directionalLight = new THREE.DirectionalLight(0xffffff, 1);\r\n    directionalLight.position.set(1, 1, 1);\r\n    scene.add(directionalLight);\r\n\r\n    // Controls\r\n    const controls = new OrbitControls(camera, renderer.domElement);\r\n    controls.enableDamping = true;\r\n    controls.dampingFactor = 0.05;\r\n    controls.rotateSpeed = 0.05;\r\n    controls.enableZoom = false;\r\n    controls.enablePan = false;\r\n    controls.autoRotate = true;\r\n    controls.autoRotateSpeed = 0.5;\r\n    controls.minPolarAngle = Math.PI / 3;\r\n    controls.maxPolarAngle = Math.PI * 2/3;\r\n\r\n    // Animation loop\r\n    let frameId;\r\n    const animate = () => {\r\n      controls.update();\r\n      renderer.render(scene, camera);\r\n      frameId = requestAnimationFrame(animate);\r\n    };\r\n\r\n    // Start animation\r\n    animate();\r\n\r\n    // Handle resize\r\n    const handleResize = () => {\r\n      const newVw = Math.min(window.innerWidth * 0.8, 600);\r\n      const newVh = Math.min(window.innerHeight - 120, 600);\r\n      const newSize = Math.min(newVw, newVh);\r\n\r\n      // Update renderer size\r\n      renderer.setSize(newSize, newSize);\r\n\r\n      // Update renderer and canvas style\r\n      const canvas = renderer.domElement;\r\n      canvas.style.width = `${newSize}px`;\r\n      canvas.style.height = `${newSize}px`;\r\n\r\n      // Don't update camera aspect ratio to maintain sphere shape\r\n      camera.updateProjectionMatrix();\r\n    };\r\n\r\n    // Add resize observer for more reliable size updates\r\n    const resizeObserver = new ResizeObserver(() => {\r\n      handleResize();\r\n    });\r\n    resizeObserver.observe(container);\r\n\r\n    // Also listen for window resize\r\n    window.addEventListener('resize', handleResize);\r\n\r\n    // Store scene reference\r\n    sceneRef.current = { scene, camera, renderer, controls, frameId };\r\n\r\n    // Cleanup\r\n    return () => {\r\n      resizeObserver.disconnect();\r\n      window.removeEventListener('resize', handleResize);\r\n      if (frameId) {\r\n        cancelAnimationFrame(frameId);\r\n      }\r\n      if (mountRef.current && renderer.domElement) {\r\n        mountRef.current.removeChild(renderer.domElement);\r\n      }\r\n      if (globe) {\r\n        scene.remove(globe);\r\n        globe.dispose();\r\n      }\r\n      renderer.dispose();\r\n      controls.dispose();\r\n    };\r\n  }, [isVisible]);\r\n\r\n  // Track selected location for highlighting\r\n  useEffect(() => {\r\n    // Check if we have location data in caseData\r\n    if (caseData && caseData.location) {\r\n      console.log(\"Location data detected:\", caseData.location);\r\n      setHasLocation(true);\r\n\r\n      // Try to get a display name for the location\r\n      if (caseData.location.address) {\r\n        setLocationName(caseData.location.address);\r\n      } else if (caseData.location.city) {\r\n        setLocationName(caseData.location.city + (caseData.location.state ? `, ${caseData.location.state}` : ''));\r\n      } else if (caseData.location.state) {\r\n        setLocationName(caseData.location.state);\r\n      } else {\r\n        setLocationName(\"Selected Location\");\r\n      }\r\n    } else {\r\n      setHasLocation(false);\r\n      setLocationName(null);\r\n    }\r\n  }, [caseData]);\r\n\r\n  // Get attorneys for this location\r\n  const locationAttorneys = attorneys.filter(attorney => attorney.location);\r\n\r\n  return (\r\n    <div\r\n      className={`globe-background ${isVisible ? 'visible' : 'hidden'}`}\r\n      style={{\r\n        position: 'fixed',\r\n        top: 60,\r\n        left: 0,\r\n        width: '100vw',\r\n        height: 'calc(100vh - 60px)',\r\n        backgroundColor: '#000',\r\n        color: 'white',\r\n        zIndex: 0,\r\n        overflow: 'hidden',\r\n        pointerEvents: 'none',\r\n        margin: 0,\r\n        padding: 0,\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        justifyContent: 'center'\r\n      }}\r\n    >\r\n      <div\r\n        className=\"globe-renderer-container\"\r\n        ref={mountRef}\r\n        style={{\r\n          position: 'relative',\r\n          width: 'min(80vh, 80vw)',\r\n          height: 'min(80vh, 80vw)',\r\n          margin: 'auto',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          aspectRatio: '1',\r\n          overflow: 'visible'\r\n        }}\r\n      />\r\n\r\n      {/* Location Information - only show when hasLocation is true (but disabled temporarily) */}\r\n      {false && hasLocation && (\r\n        <div className=\"location-info\">\r\n          <h3>{locationName}</h3>\r\n          <p>\r\n            {locationAttorneys.length > 0\r\n              ? `${locationAttorneys.length} attorneys available in this area`\r\n              : 'Searching for attorneys in this area...'}\r\n          </p>\r\n        </div>\r\n      )}\r\n\r\n      {/* Debug information - minimized */}\r\n      {process.env.NODE_ENV === 'development' && (\r\n        <div className=\"debug-info minimal\">\r\n          <div>L: {hasLocation ? '✓' : '✗'}</div>\r\n          <div>A: {attorneys.length}</div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GlobeDossierView;", "import React from 'react';\r\n\r\nconst AnimatedBackground = () => {\r\n  return (\r\n    <div className=\"gradient-bg\">\r\n      <div className=\"gradients-container\">\r\n        <div\r\n          className=\"bubble\"\r\n          style={{\r\n            width: '150px',\r\n            height: '150px',\r\n            left: '30%',\r\n            top: '40%',\r\n            background: 'radial-gradient(circle, rgba(var(--color2), 0.8) 0%, rgba(var(--color2), 0) 50%)'\r\n          }}\r\n        />\r\n        <div\r\n          className=\"bubble\"\r\n          style={{\r\n            width: '200px',\r\n            height: '200px',\r\n            left: '50%',\r\n            top: '60%',\r\n            background: 'radial-gradient(circle, rgba(var(--color3), 0.8) 0%, rgba(var(--color3), 0) 50%)'\r\n          }}\r\n        />\r\n        <div\r\n          className=\"bubble\"\r\n          style={{\r\n            width: '180px',\r\n            height: '180px',\r\n            left: '70%',\r\n            top: '20%',\r\n            background: 'radial-gradient(circle, rgba(var(--color4), 0.8) 0%, rgba(var(--color4), 0) 50%)'\r\n          }}\r\n        />\r\n        <div\r\n          className=\"bubble\"\r\n          style={{\r\n            width: '130px',\r\n            height: '130px',\r\n            left: '20%',\r\n            top: '70%',\r\n            background: 'radial-gradient(circle, rgba(var(--color5), 0.8) 0%, rgba(var(--color5), 0) 50%)'\r\n          }}\r\n        />\r\n        {/* Add more bubbles for better visibility */}\r\n        <div\r\n          className=\"bubble\"\r\n          style={{\r\n            width: '220px',\r\n            height: '220px',\r\n            left: '40%',\r\n            top: '30%',\r\n            background: 'radial-gradient(circle, rgba(var(--color1), 0.8) 0%, rgba(var(--color1), 0) 50%)',\r\n            animationDelay: '2s'\r\n          }}\r\n        />\r\n        <div\r\n          className=\"bubble\"\r\n          style={{\r\n            width: '170px',\r\n            height: '170px',\r\n            left: '60%',\r\n            top: '40%',\r\n            background: 'radial-gradient(circle, rgba(var(--color4), 0.8) 0%, rgba(var(--color4), 0) 50%)',\r\n            animationDelay: '4s'\r\n          }}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AnimatedBackground;", "import React from 'react';\r\nimport './ThemeToggle.css';\r\n\r\nconst ThemeToggle = ({ isDark, onToggle }) => {\r\n  return (\r\n    <div className=\"theme-toggle-container\">\r\n      <button \r\n        className={`theme-toggle ${isDark ? 'dark' : 'light'}`}\r\n        onClick={onToggle}\r\n        aria-label=\"Toggle theme\"\r\n      >\r\n        <div className=\"toggle-icons\">\r\n          <svg className=\"sun-icon\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"5\"/>\r\n            <line x1=\"12\" y1=\"1\" x2=\"12\" y2=\"3\"/>\r\n            <line x1=\"12\" y1=\"21\" x2=\"12\" y2=\"23\"/>\r\n            <line x1=\"4.22\" y1=\"4.22\" x2=\"5.64\" y2=\"5.64\"/>\r\n            <line x1=\"18.36\" y1=\"18.36\" x2=\"19.78\" y2=\"19.78\"/>\r\n            <line x1=\"1\" y1=\"12\" x2=\"3\" y2=\"12\"/>\r\n            <line x1=\"21\" y1=\"12\" x2=\"23\" y2=\"12\"/>\r\n            <line x1=\"4.22\" y1=\"19.78\" x2=\"5.64\" y2=\"18.36\"/>\r\n            <line x1=\"18.36\" y1=\"5.64\" x2=\"19.78\" y2=\"4.22\"/>\r\n          </svg>\r\n          <svg className=\"moon-icon\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n            <path d=\"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z\"/>\r\n          </svg>\r\n        </div>\r\n        <div className=\"toggle-circle\"></div>\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ThemeToggle; ", "import React from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport './SignInButton.css';\r\n\r\nconst SignInButton = ({ onClick }) => {\r\n  // If onClick is provided, use it (for the auth overlay)\r\n  // Otherwise, navigate to the login page\r\n  if (onClick) {\r\n    return (\r\n      <button className=\"sign-in-button\" onClick={onClick}>\r\n        Sign In/Sign Up\r\n      </button>\r\n    );\r\n  }\r\n\r\n  // Use Link for navigation to the login page\r\n  return (\r\n    <Link to=\"/login\" className=\"sign-in-button\">\r\n      Sign In/Sign Up\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default SignInButton;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport VapiCall from './VapiCall';\r\nimport Button from './Button';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport rehypeRaw from 'rehype-raw';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { processImageUrl } from '../utils/imageStorage';\r\nimport { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';\r\nimport { createDemoPreviewConfig, createAttorneyPreviewConfig } from '../utils/previewConfigHandler';\r\nimport './SimplifiedPreview.css';\r\n\r\n/**\r\n * SimplifiedPreview component - A simplified version of the preview component\r\n * used for attorney subdomains to display the attorney's customized assistant\r\n */\r\nconst SimplifiedPreview = ({\r\n  // Basic information\r\n  firmName,\r\n  logoUrl,\r\n  mascot,\r\n  buttonImageUrl, // Add buttonImageUrl prop\r\n\r\n  // Colors and styling\r\n  primaryColor,\r\n  secondaryColor,\r\n  buttonColor,\r\n  backgroundColor,\r\n  backgroundOpacity,\r\n  buttonText,\r\n  buttonOpacity,\r\n  practiceAreaBackgroundOpacity,\r\n  textBackgroundColor,\r\n\r\n  // Content\r\n  practiceDescription,\r\n  welcomeMessage,\r\n  informationGathering,\r\n  officeAddress,\r\n  schedulingLink,\r\n  practiceAreas,\r\n\r\n  // Vapi configuration\r\n  vapiInstructions,\r\n  vapiContext,\r\n  vapi_assistant_id,\r\n  voiceId,\r\n  aiModel,\r\n  subdomain = 'default', // Add subdomain prop\r\n\r\n  // Custom fields configuration\r\n  customFields,\r\n  summaryPrompt,\r\n  structuredDataPrompt,\r\n  structuredDataSchema,\r\n\r\n  // Theme\r\n  theme = 'dark'\r\n}) => {\r\n  const [callActive, setCallActive] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [showPreviewHeader, setShowPreviewHeader] = useState(true);\r\n  // Add state management for preventing mount/unmount cycles\r\n  const [isStartingCall, setIsStartingCall] = useState(false);\r\n  const [vapiCallKey, setVapiCallKey] = useState(0);\r\n\r\n  // Convert hex colors to RGB for opacity support\r\n  const hexToRgb = (hex) => {\r\n    // Remove # if present\r\n    hex = hex.replace('#', '');\r\n\r\n    // Parse hex values\r\n    const r = parseInt(hex.substring(0, 2), 16);\r\n    const g = parseInt(hex.substring(2, 4), 16);\r\n    const b = parseInt(hex.substring(4, 6), 16);\r\n\r\n    return `${r}, ${g}, ${b}`;\r\n  };\r\n\r\n  // Get contrast color (black or white) based on background\r\n  const getContrastColor = (hexColor) => {\r\n    // Convert hex to RGB\r\n    let hex = hexColor.replace('#', '');\r\n\r\n    // Convert hex to RGB\r\n    const r = parseInt(hex.substring(0, 2), 16);\r\n    const g = parseInt(hex.substring(2, 4), 16);\r\n    const b = parseInt(hex.substring(4, 6), 16);\r\n\r\n    // Calculate luminance - standard formula\r\n    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;\r\n\r\n    // Return black for bright colors, white for dark colors\r\n    return luminance > 0.5 ? '#000000' : '#ffffff';\r\n  };\r\n\r\n  // Start the call\r\n  const startCall = () => {\r\n    // Prevent multiple simultaneous call starts\r\n    if (isStartingCall || callActive) {\r\n      console.log('[SimplifiedPreview] Call already starting or active, ignoring duplicate startCall');\r\n      return;\r\n    }\r\n\r\n    console.log('[SimplifiedPreview] Starting call with assistant ID:', vapi_assistant_id);\r\n\r\n    // Set the flag to indicate we're starting a call\r\n    setIsStartingCall(true);\r\n    setIsLoading(true);\r\n\r\n    // Increment the VapiCall key to force a fresh component instance\r\n    setVapiCallKey(prev => prev + 1);\r\n\r\n    // Simulate a short loading time\r\n    setTimeout(() => {\r\n      setCallActive(true);\r\n      setIsLoading(false);\r\n\r\n      // Reset the starting call flag after a delay to allow for initialization\r\n      setTimeout(() => {\r\n        setIsStartingCall(false);\r\n      }, 2000); // Allow time for VapiCall initialization\r\n    }, 500);\r\n  };\r\n\r\n  // End the call\r\n  const endCall = () => {\r\n    setCallActive(false);\r\n  };\r\n\r\n  // Handle production URLs in local development for button images\r\n  const handleImageUrl = (url) => {\r\n    if (url && typeof url === 'string' && url.includes('legalscout.ai/static/media')) {\r\n      console.log('Converting production URL to local asset:', url);\r\n      return '/PRIMARY CLEAR.png';\r\n    }\r\n    return processImageUrl(url) || '/PRIMARY CLEAR.png';\r\n  };\r\n\r\n  // Always show the firm name header for iframe content\r\n  // This ensures embedded widgets and attorney subdomains have proper branding\r\n  useEffect(() => {\r\n    setShowPreviewHeader(true);\r\n    console.log('SimplifiedPreview: Always showing firm name header for proper branding');\r\n  }, []);\r\n\r\n  // Log the props for debugging\r\n  useEffect(() => {\r\n    console.log('SimplifiedPreview props:', {\r\n      firmName,\r\n      primaryColor,\r\n      secondaryColor,\r\n      buttonColor,\r\n      practiceDescription: practiceDescription ? practiceDescription.substring(0, 100) + '...' : 'None',\r\n      showPreviewHeader,\r\n      // ... other props can be added here\r\n    });\r\n  }, [firmName, primaryColor, secondaryColor, buttonColor, practiceDescription, showPreviewHeader]);\r\n\r\n  // Dynamic styles\r\n  const previewStyles = {\r\n    '--primary-color': primaryColor,\r\n    '--primary-color-rgb': hexToRgb(primaryColor),\r\n    '--secondary-color': secondaryColor,\r\n    '--secondary-color-rgb': hexToRgb(secondaryColor),\r\n    '--button-color': buttonColor,\r\n    '--button-color-rgb': hexToRgb(buttonColor),\r\n    '--button-text-color': getContrastColor(buttonColor),\r\n    '--background-color': `rgba(${hexToRgb(backgroundColor)}, ${backgroundOpacity})`,\r\n    '--text-background-color': `rgba(${hexToRgb(textBackgroundColor)}, ${practiceAreaBackgroundOpacity})`,\r\n    '--button-opacity': buttonOpacity,\r\n  };\r\n\r\n  return (\r\n    <div className={`simplified-preview ${theme}-theme`} style={previewStyles}>\r\n      <div className=\"preview-content\">\r\n        {!callActive ? (\r\n          <>\r\n            {/* Firm name header removed - only show the header with logo and navigation */}\r\n\r\n            <div className=\"preview-practice-description\">\r\n              <div className=\"practice-description-content\">\r\n                <ReactMarkdown\r\n                  remarkPlugins={[remarkGfm]}\r\n                  rehypePlugins={[rehypeRaw]}\r\n                  components={{\r\n                    // Define custom components for markdown elements\r\n                    h1: ({node, ...props}) => <h1 style={{color: primaryColor || '#4B74AA'}} {...props} />,\r\n                    h2: ({node, ...props}) => <h2 style={{color: secondaryColor || '#2C3E50'}} {...props} />,\r\n                    h3: ({node, ...props}) => <h3 style={{color: secondaryColor || '#2C3E50'}} {...props} />,\r\n                    a: ({node, ...props}) => <a style={{color: buttonColor || '#3498db'}} {...props} />,\r\n                    strong: ({node, ...props}) => <strong style={{color: primaryColor || '#4B74AA'}} {...props} />,\r\n                    em: ({node, ...props}) => <em style={{fontStyle: 'italic'}} {...props} />,\r\n                    blockquote: ({node, ...props}) => (\r\n                      <blockquote\r\n                        style={{\r\n                          borderLeft: `4px solid ${secondaryColor || '#3498db'}`,\r\n                          paddingLeft: '1rem',\r\n                          marginLeft: 0,\r\n                          fontStyle: 'italic'\r\n                        }}\r\n                        {...props}\r\n                      />\r\n                    ),\r\n                    ul: ({node, ...props}) => <ul style={{marginLeft: '1.5rem'}} {...props} />,\r\n                    ol: ({node, ...props}) => <ol style={{marginLeft: '1.5rem'}} {...props} />,\r\n                    li: ({node, ...props}) => <li style={{marginBottom: '0.5rem'}} {...props} />,\r\n                  }}\r\n                >\r\n                  {practiceDescription}\r\n                </ReactMarkdown>\r\n              </div>\r\n\r\n              {/* Display practice areas if available */}\r\n              {practiceAreas && practiceAreas.length > 0 && (\r\n                <div className=\"practice-areas-section\">\r\n                  <h3>Practice Areas</h3>\r\n                  <ul className=\"practice-areas-list\">\r\n                    {practiceAreas.map((area, index) => (\r\n                      <li key={index}>{area}</li>\r\n                    ))}\r\n                  </ul>\r\n                </div>\r\n              )}\r\n\r\n              {/* Display office address if available */}\r\n              {officeAddress && (\r\n                <div className=\"office-address-section\">\r\n                  <h3>Office Location</h3>\r\n                  <p>{officeAddress}</p>\r\n                </div>\r\n              )}\r\n\r\n              {/* Display scheduling link if available */}\r\n              {schedulingLink && (\r\n                <div className=\"scheduling-section\">\r\n                  <h3>Schedule a Consultation</h3>\r\n                  <a href={schedulingLink} target=\"_blank\" rel=\"noopener noreferrer\" className=\"scheduling-link\">\r\n                    Book an Appointment\r\n                  </a>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"preview-button-container\">\r\n              <Button\r\n                onClick={startCall}\r\n                label={buttonText || \"Start Consultation\"}\r\n                mascot={handleImageUrl(buttonImageUrl || mascot)} /* Use buttonImageUrl if available, otherwise use mascot */\r\n                isLoading={isLoading}\r\n                buttonColor={buttonColor}\r\n                buttonOpacity={buttonOpacity}\r\n              />\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <div className=\"preview-call-container\">\r\n            <VapiCall\r\n              key={vapiCallKey} // Use stable key to prevent unnecessary unmounting\r\n              onEndCall={endCall}\r\n              subdomain={subdomain} // Pass the subdomain to VapiCall\r\n              isDemo={!vapi_assistant_id} // Use demo mode if no assistant ID is provided\r\n              attorneyData={vapi_assistant_id ? (() => {\r\n                const data = {\r\n                  firm_name: firmName,\r\n                  vapi_instructions: vapiInstructions,\r\n                  vapi_context: vapiContext,\r\n                  welcome_message: welcomeMessage,\r\n                  vapi_assistant_id: vapi_assistant_id,\r\n                  voice_id: voiceId,\r\n                  ai_model: aiModel,\r\n                  subdomain: subdomain, // Include subdomain in attorney data\r\n                  // Include appearance colors for speech particles\r\n                  primary_color: primaryColor,\r\n                  secondary_color: secondaryColor,\r\n                  // Include custom fields data\r\n                  custom_fields: customFields,\r\n                  summary_prompt: summaryPrompt,\r\n                  structured_data_prompt: structuredDataPrompt,\r\n                  structured_data_schema: structuredDataSchema\r\n                };\r\n                console.log('🎨 SimplifiedPreview: Passing attorney data with colors and subdomain:', {\r\n                  primary_color: primaryColor,\r\n                  secondary_color: secondaryColor,\r\n                  firmName: firmName,\r\n                  subdomain: subdomain\r\n                });\r\n                return data;\r\n              })() : null}\r\n              customInstructions={{\r\n                firmName: firmName,\r\n                vapiInstructions: vapiInstructions,\r\n                vapiContext: vapiContext,\r\n                initialMessage: welcomeMessage || `Hello, I'm Scout from ${firmName}. How can I help you today?`,\r\n                assistantId: vapi_assistant_id,\r\n                voiceId: voiceId,\r\n                aiModel: aiModel\r\n              }}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SimplifiedPreview;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { getCurrentSubdomain } from '../../utils/subdomainTester';\r\nimport { getAttorneyConfigAsync } from '../../config/attorneys';\r\nimport { mapDatabaseToPreview } from '../../utils/configMapping';\r\nimport EnhancedPreviewNew from './EnhancedPreviewNew';\r\n\r\n/**\r\n * PreviewFrameLoader - Loads attorney configuration and renders the preview\r\n * This component is used in the /preview-frame route to show the actual attorney's\r\n * configuration instead of hardcoded default values.\r\n */\r\nconst PreviewFrameLoader = () => {\r\n  const [attorneyConfig, setAttorneyConfig] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [showActivateButton, setShowActivateButton] = useState(false);\r\n  const [activating, setActivating] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const loadAttorneyConfig = async () => {\r\n      try {\r\n        console.log('[PreviewFrameLoader] Loading attorney configuration...');\r\n\r\n        // Get the current subdomain\r\n        const subdomain = getCurrentSubdomain();\r\n        console.log('[PreviewFrameLoader] Current subdomain:', subdomain);\r\n\r\n        // Load attorney configuration from the subdomain\r\n        console.log('[PreviewFrameLoader] Loading config for subdomain:', subdomain);\r\n        const config = await getAttorneyConfigAsync(subdomain);\r\n        console.log('[PreviewFrameLoader] Raw attorney config loaded:', {\r\n          hasConfig: !!config,\r\n          firmName: config?.firmName,\r\n          vapi_assistant_id: config?.vapi_assistant_id,\r\n          id: config?.id,\r\n          subdomain: config?.subdomain,\r\n          isFallback: config?.isFallback,\r\n          configKeys: config ? Object.keys(config) : []\r\n        });\r\n\r\n        if (config) {\r\n          // Check if this is a fallback configuration (indicating database lookup failed)\r\n          const isFallbackConfig = config.isFallback ||\r\n                                  (config.firmName === 'Your Law Firm' && !config.id) ||\r\n                                  (!config.id && subdomain === 'damon');\r\n\r\n          console.log('[PreviewFrameLoader] Config analysis:', {\r\n            isFallbackConfig,\r\n            hasId: !!config.id,\r\n            firmName: config.firmName,\r\n            subdomain: subdomain\r\n          });\r\n\r\n          // Map the database configuration to preview props\r\n          const previewConfig = mapDatabaseToPreview(config);\r\n          console.log('[PreviewFrameLoader] Mapped preview config:', {\r\n            firmName: previewConfig.firmName,\r\n            titleText: previewConfig.titleText,\r\n            vapiAssistantId: previewConfig.vapiAssistantId,\r\n            welcomeMessage: previewConfig.welcomeMessage\r\n          });\r\n\r\n          setAttorneyConfig({\r\n            ...previewConfig,\r\n            // Ensure we have the Vapi assistant ID\r\n            vapiAssistantId: config.vapi_assistant_id || previewConfig.vapiAssistantId,\r\n            // Include all the original config for fallback\r\n            ...config,\r\n            // Add flag to indicate if this is fallback config\r\n            isFallbackConfig\r\n          });\r\n\r\n          // Show activate button for damon subdomain if using fallback config\r\n          if (isFallbackConfig && subdomain === 'damon') {\r\n            console.log('[PreviewFrameLoader] Fallback config detected for damon, showing activate button');\r\n            setShowActivateButton(true);\r\n          }\r\n        } else {\r\n          console.warn('[PreviewFrameLoader] No attorney config found, using defaults');\r\n          // Use default configuration if no attorney config is found\r\n          setAttorneyConfig({\r\n            firmName: \"Your Law Firm\",\r\n            titleText: \"Your Law Firm\",\r\n            primaryColor: \"#4B74AA\",\r\n            secondaryColor: \"#2C3E50\",\r\n            backgroundColor: \"#1a1a1a\",\r\n            backgroundOpacity: 0.9,\r\n            buttonText: \"Start Consultation\",\r\n            buttonOpacity: 1,\r\n            practiceAreaBackgroundOpacity: 0.1,\r\n            textBackgroundColor: \"#634C38\",\r\n            welcomeMessage: \"Hello! I'm Scout, your legal assistant. How can I help you today?\",\r\n            informationGathering: \"Tell me about your situation, and I'll help find the right solution for you.\",\r\n            logoUrl: \"/PRIMARY CLEAR.png\",\r\n            mascot: \"/PRIMARY CLEAR.png\",\r\n            theme: \"dark\",\r\n            vapiAssistantId: null\r\n          });\r\n        }\r\n      } catch (err) {\r\n        console.error('[PreviewFrameLoader] Error loading attorney config:', err);\r\n        setError(err.message);\r\n\r\n        // Use default configuration on error\r\n        setAttorneyConfig({\r\n          firmName: \"Your Law Firm\",\r\n          titleText: \"Your Law Firm\",\r\n          primaryColor: \"#4B74AA\",\r\n          secondaryColor: \"#2C3E50\",\r\n          backgroundColor: \"#1a1a1a\",\r\n          backgroundOpacity: 0.9,\r\n          buttonText: \"Start Consultation\",\r\n          buttonOpacity: 1,\r\n          practiceAreaBackgroundOpacity: 0.1,\r\n          textBackgroundColor: \"#634C38\",\r\n          welcomeMessage: \"Hello! I'm Scout, your legal assistant. How can I help you today?\",\r\n          informationGathering: \"Tell me about your situation, and I'll help find the right solution for you.\",\r\n          logoUrl: \"/PRIMARY CLEAR.png\",\r\n          mascot: \"/PRIMARY CLEAR.png\",\r\n          theme: \"dark\",\r\n          vapiAssistantId: null\r\n        });\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadAttorneyConfig();\r\n  }, []);\r\n\r\n  // Function to activate the assistant for the current subdomain\r\n  const activateAssistant = async () => {\r\n    setActivating(true);\r\n    try {\r\n      console.log('[PreviewFrameLoader] Activating assistant for subdomain:', getCurrentSubdomain());\r\n\r\n      // Force a fresh load of the attorney configuration\r\n      const subdomain = getCurrentSubdomain();\r\n      const config = await getAttorneyConfigAsync(subdomain);\r\n\r\n      console.log('[PreviewFrameLoader] Activation - loaded config:', {\r\n        hasConfig: !!config,\r\n        firmName: config?.firmName,\r\n        assistantId: config?.vapi_assistant_id,\r\n        id: config?.id\r\n      });\r\n\r\n      if (config && config.vapi_assistant_id) {\r\n        // If we have a valid config with assistant ID, update the preview\r\n        const previewConfig = mapDatabaseToPreview(config);\r\n        setAttorneyConfig({\r\n          ...previewConfig,\r\n          vapiAssistantId: config.vapi_assistant_id,\r\n          ...config,\r\n          isFallbackConfig: false\r\n        });\r\n        setShowActivateButton(false);\r\n        console.log('[PreviewFrameLoader] Assistant activated successfully');\r\n      } else {\r\n        console.warn('[PreviewFrameLoader] Activation failed - no valid config or assistant ID found');\r\n      }\r\n    } catch (error) {\r\n      console.error('[PreviewFrameLoader] Error activating assistant:', error);\r\n    } finally {\r\n      setActivating(false);\r\n    }\r\n  };\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return (\r\n      <div style={{\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        height: '100vh',\r\n        backgroundColor: '#1a1a1a',\r\n        color: 'white'\r\n      }}>\r\n        <div style={{\r\n          width: '40px',\r\n          height: '40px',\r\n          border: '4px solid #333',\r\n          borderTop: '4px solid #4B74AA',\r\n          borderRadius: '50%',\r\n          animation: 'spin 1s linear infinite'\r\n        }}></div>\r\n        <p style={{ marginTop: '20px' }}>Loading preview...</p>\r\n        <style>{`\r\n          @keyframes spin {\r\n            0% { transform: rotate(0deg); }\r\n            100% { transform: rotate(360deg); }\r\n          }\r\n        `}</style>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (error) {\r\n    return (\r\n      <div style={{\r\n        display: 'flex',\r\n        flexDirection: 'column',\r\n        alignItems: 'center',\r\n        justifyContent: 'center',\r\n        height: '100vh',\r\n        backgroundColor: '#1a1a1a',\r\n        color: '#ff6b6b',\r\n        padding: '20px',\r\n        textAlign: 'center'\r\n      }}>\r\n        <h2>Preview Error</h2>\r\n        <p>Failed to load attorney configuration: {error}</p>\r\n        <p style={{ color: '#ccc', marginTop: '20px' }}>\r\n          Using default configuration instead.\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render the preview with the loaded configuration\r\n  return (\r\n    <div style={{ position: 'relative', width: '100%', height: '100vh' }}>\r\n      <EnhancedPreviewNew\r\n        {...attorneyConfig}\r\n      />\r\n\r\n      {/* Activate Assistant Button Overlay - Mobile Responsive */}\r\n      {showActivateButton && (\r\n        <div style={{\r\n          position: 'fixed',\r\n          bottom: window.innerWidth <= 768 ? '20px' : 'auto',\r\n          top: window.innerWidth <= 768 ? 'auto' : '20px',\r\n          left: window.innerWidth <= 768 ? '50%' : 'auto',\r\n          right: window.innerWidth <= 768 ? 'auto' : '20px',\r\n          transform: window.innerWidth <= 768 ? 'translateX(-50%)' : 'none',\r\n          zIndex: 9999,\r\n          backgroundColor: 'rgba(173, 216, 230, 0.1)', // Hollow light blue\r\n          padding: window.innerWidth <= 768 ? '12px 20px' : '15px',\r\n          borderRadius: window.innerWidth <= 768 ? '25px' : '8px',\r\n          border: '1px solid rgba(173, 216, 230, 0.8)', // Thin light blue piping\r\n          color: '#87CEEB', // Light blue text\r\n          textAlign: 'center',\r\n          maxWidth: window.innerWidth <= 768 ? '90vw' : '250px',\r\n          minWidth: window.innerWidth <= 768 ? '280px' : 'auto',\r\n          boxShadow: '0 2px 15px rgba(173, 216, 230, 0.2)',\r\n          backdropFilter: 'blur(15px)'\r\n        }}>\r\n          <div style={{\r\n            marginBottom: window.innerWidth <= 768 ? '8px' : '10px',\r\n            fontSize: window.innerWidth <= 768 ? '16px' : '14px',\r\n            fontWeight: 'bold'\r\n          }}>\r\n            Assistant Not Active\r\n          </div>\r\n          {window.innerWidth > 768 && (\r\n            <div style={{ marginBottom: '15px', fontSize: '12px', color: '#ccc' }}>\r\n              Your assistant configuration is not synced with this subdomain.\r\n            </div>\r\n          )}\r\n          <button\r\n            onClick={activateAssistant}\r\n            disabled={activating}\r\n            style={{\r\n              backgroundColor: activating ? 'rgba(173, 216, 230, 0.2)' : 'rgba(173, 216, 230, 0.15)',\r\n              color: activating ? '#B0C4DE' : '#87CEEB',\r\n              border: '1px solid rgba(173, 216, 230, 0.6)',\r\n              padding: window.innerWidth <= 768 ? '12px 24px' : '8px 16px',\r\n              borderRadius: window.innerWidth <= 768 ? '20px' : '4px',\r\n              cursor: activating ? 'not-allowed' : 'pointer',\r\n              fontSize: window.innerWidth <= 768 ? '14px' : '12px',\r\n              fontWeight: 'bold',\r\n              minWidth: window.innerWidth <= 768 ? '120px' : 'auto',\r\n              transition: 'all 0.3s ease',\r\n              backdropFilter: 'blur(10px)'\r\n            }}\r\n          >\r\n            {activating ? 'Activating...' : window.innerWidth <= 768 ? 'Activate' : 'Activate Assistant'}\r\n          </button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PreviewFrameLoader;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { getCurrentSubdomain } from '../../utils/subdomainTester';\r\nimport { getAttorneyConfigAsync } from '../../config/attorneys';\r\nimport { mapDatabaseToPreview } from '../../utils/configMapping';\r\n\r\n/**\r\n * MobileActivateAssistant - Global mobile component for activating assistant\r\n * Shows across all pages when on mobile and assistant needs activation\r\n */\r\nconst MobileActivateAssistant = ({ onActivated }) => {\r\n  const [showButton, setShowButton] = useState(false);\r\n  const [activating, setActivating] = useState(false);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Check if device is mobile\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      const mobile = window.innerWidth <= 768;\r\n      setIsMobile(mobile);\r\n    };\r\n\r\n    checkMobile();\r\n    window.addEventListener('resize', checkMobile);\r\n    return () => window.removeEventListener('resize', checkMobile);\r\n  }, []);\r\n\r\n  // Check if assistant needs activation\r\n  useEffect(() => {\r\n    const checkActivationNeeded = async () => {\r\n      try {\r\n        const subdomain = getCurrentSubdomain();\r\n\r\n        // Only check for specific subdomains that might need activation\r\n        if (subdomain === 'damon') {\r\n          const config = await getAttorneyConfigAsync(subdomain);\r\n\r\n          // Check if this is a fallback configuration\r\n          const needsActivation = !config ||\r\n                                 config.isFallback ||\r\n                                 (config.firmName === 'Your Law Firm' && !config.id) ||\r\n                                 (!config.id && subdomain === 'damon');\r\n\r\n          console.log('[MobileActivateAssistant] Activation check:', {\r\n            subdomain,\r\n            needsActivation,\r\n            hasConfig: !!config,\r\n            hasId: !!config?.id,\r\n            firmName: config?.firmName\r\n          });\r\n\r\n          setShowButton(needsActivation && isMobile);\r\n        }\r\n      } catch (error) {\r\n        console.error('[MobileActivateAssistant] Error checking activation:', error);\r\n      }\r\n    };\r\n\r\n    if (isMobile) {\r\n      checkActivationNeeded();\r\n    } else {\r\n      setShowButton(false);\r\n    }\r\n  }, [isMobile]);\r\n\r\n  // Function to activate the assistant\r\n  const activateAssistant = async () => {\r\n    setActivating(true);\r\n    try {\r\n      console.log('[MobileActivateAssistant] Activating assistant...');\r\n\r\n      const subdomain = getCurrentSubdomain();\r\n      const config = await getAttorneyConfigAsync(subdomain);\r\n\r\n      console.log('[MobileActivateAssistant] Activation result:', {\r\n        hasConfig: !!config,\r\n        firmName: config?.firmName,\r\n        assistantId: config?.vapi_assistant_id,\r\n        id: config?.id\r\n      });\r\n\r\n      if (config && config.vapi_assistant_id) {\r\n        setShowButton(false);\r\n        console.log('[MobileActivateAssistant] Assistant activated successfully');\r\n\r\n        // Notify parent component if callback provided\r\n        if (onActivated) {\r\n          onActivated(config);\r\n        }\r\n\r\n        // Reload the page to refresh all components with new config\r\n        setTimeout(() => {\r\n          window.location.reload();\r\n        }, 1000);\r\n      } else {\r\n        console.warn('[MobileActivateAssistant] Activation failed - no valid config found');\r\n        alert('Activation failed. Please try again or contact support.');\r\n      }\r\n    } catch (error) {\r\n      console.error('[MobileActivateAssistant] Error activating assistant:', error);\r\n      alert('Error activating assistant. Please try again.');\r\n    } finally {\r\n      setActivating(false);\r\n    }\r\n  };\r\n\r\n  // Don't render if not mobile or button not needed\r\n  if (!isMobile || !showButton) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div style={{\r\n      position: 'fixed',\r\n      bottom: '20px',\r\n      left: '50%',\r\n      transform: 'translateX(-50%)',\r\n      zIndex: 10000, // Higher than preview overlay\r\n      backgroundColor: 'rgba(173, 216, 230, 0.1)', // Hollow light blue\r\n      padding: '12px 20px',\r\n      borderRadius: '25px',\r\n      border: '1px solid rgba(173, 216, 230, 0.8)', // Thin light blue piping\r\n      color: '#87CEEB', // Light blue text\r\n      textAlign: 'center',\r\n      maxWidth: '90vw',\r\n      minWidth: '280px',\r\n      boxShadow: '0 2px 15px rgba(173, 216, 230, 0.2)',\r\n      backdropFilter: 'blur(15px)',\r\n      animation: 'slideUp 0.3s ease-out'\r\n    }}>\r\n      <style>{`\r\n        @keyframes slideUp {\r\n          from {\r\n            transform: translateX(-50%) translateY(100px);\r\n            opacity: 0;\r\n          }\r\n          to {\r\n            transform: translateX(-50%) translateY(0);\r\n            opacity: 1;\r\n          }\r\n        }\r\n      `}</style>\r\n\r\n      <div style={{\r\n        marginBottom: '8px',\r\n        fontSize: '16px',\r\n        fontWeight: 'bold'\r\n      }}>\r\n        🚀 Assistant Not Active\r\n      </div>\r\n\r\n      <div style={{\r\n        marginBottom: '12px',\r\n        fontSize: '13px',\r\n        color: '#ccc',\r\n        lineHeight: '1.3'\r\n      }}>\r\n        Tap to sync your assistant configuration\r\n      </div>\r\n\r\n      <button\r\n        onClick={activateAssistant}\r\n        disabled={activating}\r\n        style={{\r\n          backgroundColor: activating ? 'rgba(173, 216, 230, 0.2)' : 'rgba(173, 216, 230, 0.15)',\r\n          color: activating ? '#B0C4DE' : '#87CEEB',\r\n          border: '1px solid rgba(173, 216, 230, 0.6)',\r\n          padding: '12px 24px',\r\n          borderRadius: '20px',\r\n          cursor: activating ? 'not-allowed' : 'pointer',\r\n          fontSize: '14px',\r\n          fontWeight: 'bold',\r\n          minWidth: '120px',\r\n          transition: 'all 0.3s ease',\r\n          boxShadow: activating ? 'none' : '0 2px 10px rgba(173, 216, 230, 0.2)',\r\n          backdropFilter: 'blur(10px)'\r\n        }}\r\n      >\r\n        {activating ? '⏳ Activating...' : '✨ Activate Now'}\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MobileActivateAssistant;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { supabase, isSupabaseConfigured, signInWithGoogle } from '../lib/supabase';\r\nimport { vapiAssistantService } from '../services/vapiAssistantService';\r\nimport './AuthOverlay.css';\r\n\r\nconst AuthOverlay = ({ isOpen, onClose, onSuccess }) => {\r\n  const [authMethod, setAuthMethod] = useState(null);\r\n  const [email, setEmail] = useState('');\r\n  const [password, setPassword] = useState('');\r\n  const [firmName, setFirmName] = useState('');\r\n  const [subdomain, setSubdomain] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [step, setStep] = useState('methods'); // methods, email, success\r\n\r\n  useEffect(() => {\r\n    // Reset state when overlay is opened\r\n    if (isOpen) {\r\n      console.log('AuthOverlay opened');\r\n      setAuthMethod(null);\r\n      setEmail('');\r\n      setPassword('');\r\n      setFirmName('');\r\n      setSubdomain('');\r\n      setError(null);\r\n      setStep('methods');\r\n    }\r\n  }, [isOpen]);\r\n\r\n  // Handle Google sign-in\r\n  const handleGoogleSignIn = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    // Check if we're in development mode\r\n    const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';\r\n    console.log('Current environment mode:', isDev ? 'development' : 'production');\r\n\r\n    // Check if Supabase is configured\r\n    if (!isSupabaseConfigured() && !isDev) {\r\n      console.error('Supabase not configured for Google sign-in');\r\n      setError('Authentication is not configured. Please contact the administrator.');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Use the helper function from supabase.js\r\n      // This will handle both production and development modes\r\n      await signInWithGoogle();\r\n      // The redirect will happen automatically after successful sign-in\r\n    } catch (err) {\r\n      console.error('Google sign-in error:', err);\r\n      setError('Failed to sign in with Google. Please try again.');\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle email sign-up\r\n  const handleEmailSignUp = async (e) => {\r\n    e.preventDefault();\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    // Check if Supabase is configured\r\n    if (!isSupabaseConfigured()) {\r\n      setError('Authentication is not configured. Please contact the administrator.');\r\n      setLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Validate inputs\r\n      if (!email || !password || !firmName || !subdomain) {\r\n        throw new Error('All fields are required');\r\n      }\r\n\r\n      // Validate subdomain format (letters, numbers, hyphens only)\r\n      if (!/^[a-z0-9-]+$/.test(subdomain)) {\r\n        throw new Error('Subdomain can only contain lowercase letters, numbers, and hyphens');\r\n      }\r\n\r\n      // Check if subdomain is available\r\n      if (import.meta.env.DEV) console.log('AuthOverlay: checking subdomain availability:', subdomain);\r\n      const { data: existingAttorney, error: lookupError } = await supabase\r\n        .from('attorneys')\r\n        .select('id')\r\n        .eq('subdomain', subdomain)\r\n        .single();\r\n      if (import.meta.env.DEV) console.log('AuthOverlay: existingAttorney, lookupError:', existingAttorney, lookupError);\r\n\r\n      if (existingAttorney) {\r\n        throw new Error('This subdomain is already taken. Please choose another one.');\r\n      }\r\n\r\n      // Sign up with email\r\n      const { data: authData, error: authError } = await supabase.auth.signUp({\r\n        email,\r\n        password,\r\n      });\r\n      console.log('AuthOverlay: supabase.auth.signUp result:', { authData, authError });\r\n\r\n      if (authError) throw authError;\r\n\r\n      // Create attorney record in the database\r\n      const { data: attorneyData, error: attorneyError } = await supabase\r\n        .from('attorneys')\r\n        .insert([\r\n          {\r\n            subdomain,\r\n            firm_name: firmName,\r\n            email,\r\n            is_active: true,\r\n            user_id: authData.user.id,\r\n            address: '',\r\n            vapi_instructions: `You are a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information for consultation.`\r\n          }\r\n        ])\r\n        .select('*')\r\n        .single();\r\n      console.log('AuthOverlay: supabase.from(\"attorneys\").insert result:', { attorneyData, attorneyError });\r\n\r\n      if (attorneyError) throw attorneyError;\r\n\r\n      // Create a Vapi assistant for the attorney\r\n      try {\r\n        const assistant = await vapiAssistantService.createAssistantForAttorney(attorneyData);\r\n        console.log('Created Vapi assistant for attorney:', assistant);\r\n      } catch (assistantError) {\r\n        console.error('Error creating Vapi assistant:', assistantError);\r\n        // Continue with account creation even if assistant creation fails\r\n        // We can create the assistant later\r\n      }\r\n\r\n      // Store attorney data in localStorage\r\n      localStorage.setItem('attorney', JSON.stringify(attorneyData));\r\n      // Also persist the attorney ID for retrieval\r\n      localStorage.setItem('attorney_id', attorneyData.id);\r\n      localStorage.setItem('currentAttorneyId', attorneyData.id);\r\n\r\n      // Call the success callback with the attorney data\r\n      if (onSuccess) {\r\n        onSuccess(attorneyData);\r\n      } else {\r\n        // Redirect directly to dashboard if no callback\r\n        window.location.href = '/dashboard';\r\n      }\r\n    } catch (err) {\r\n      console.error('Email sign-up error:', err);\r\n      setError(err.message || 'Failed to create account. Please try again.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle overlay click (close if clicking outside the content)\r\n  const handleOverlayClick = (e) => {\r\n    if (e.target.classList.contains('auth-overlay')) {\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  // Check if we're in development mode\r\n  const isDev = import.meta.env.DEV || import.meta.env.MODE === 'development';\r\n\r\n  // Show a message if Supabase is not configured and we're not in development mode\r\n  const supabaseNotConfigured = !isSupabaseConfigured() && !isDev;\r\n\r\n  return (\r\n    <div className=\"auth-overlay\" onClick={handleOverlayClick}>\r\n      <div className=\"auth-overlay-content\">\r\n        <button className=\"close-button\" onClick={onClose}>×</button>\r\n\r\n        {supabaseNotConfigured && (\r\n          <div className=\"error-message\">\r\n            <h3>Authentication Not Configured</h3>\r\n            <p>The authentication system is not properly configured. This is expected in development mode.</p>\r\n            <div className=\"dev-mode-options\">\r\n              <h4>Development Options:</h4>\r\n              <button\r\n                className=\"dev-mode-button\"\r\n                onClick={() => {\r\n                  // Simulate successful authentication for development\r\n                  console.log('Using mock authentication for development');\r\n                  // Create mock attorney data\r\n                  const mockAttorneyData = {\r\n                    id: 'dev-' + Date.now(),\r\n                    firm_name: 'Development Law Firm',\r\n                    subdomain: 'devmode',\r\n                    email: '<EMAIL>',\r\n                    is_active: true,\r\n                    created_at: new Date().toISOString(),\r\n                    vapi_instructions: 'You are a legal assistant for Development Law Firm. Help potential clients understand their legal needs and collect relevant information for consultation.'\r\n                  };\r\n                  // Store mock data in localStorage\r\n                  localStorage.setItem('attorney', JSON.stringify(mockAttorneyData));\r\n                  // Call the success callback with mock data and redirect to dashboard\r\n                  if (onSuccess) {\r\n                    onSuccess(mockAttorneyData);\r\n                  } else {\r\n                    // Redirect directly to dashboard if no callback\r\n                    window.location.href = '/dashboard';\r\n                  }\r\n                }}\r\n              >\r\n                Continue in Development Mode\r\n              </button>\r\n              <div className=\"setup-instructions\">\r\n                <p><strong>To configure Supabase:</strong></p>\r\n                <ol>\r\n                  <li>Create a <a href=\"https://supabase.com/\" target=\"_blank\" rel=\"noopener noreferrer\">Supabase</a> account and project</li>\r\n                  <li>Create a <code>.env</code> file in the project root with:</li>\r\n                  <pre>\r\n                    VITE_SUPABASE_URL=your-supabase-url<br/>\r\n                    VITE_SUPABASE_KEY=your-supabase-anon-key\r\n                  </pre>\r\n                  <li>Create an <code>attorneys</code> table in Supabase with the required fields</li>\r\n                </ol>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {!supabaseNotConfigured && step === 'methods' && (\r\n          <>\r\n            <h2>Create Your Attorney Account</h2>\r\n            <p>Choose a sign-up method to get started with your customized legal assistant.</p>\r\n\r\n            <div className=\"auth-methods\">\r\n              <button\r\n                className=\"auth-method-button google-button\"\r\n                onClick={handleGoogleSignIn}\r\n                disabled={loading}\r\n              >\r\n                <img src=\"/google-icon.svg\" alt=\"Google\" />\r\n                Continue with Google\r\n              </button>\r\n\r\n              <div className=\"divider\">\r\n                <span>or</span>\r\n              </div>\r\n\r\n              <button\r\n                className=\"auth-method-button email-button\"\r\n                onClick={() => setStep('email')}\r\n                disabled={loading}\r\n              >\r\n                <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n                  <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" />\r\n                  <path d=\"M22 7l-10 7L2 7\" />\r\n                </svg>\r\n                Continue with Email\r\n              </button>\r\n            </div>\r\n          </>\r\n        )}\r\n\r\n        {!supabaseNotConfigured && step === 'email' && (\r\n          <>\r\n            <h2>Create Your Attorney Account</h2>\r\n            <p>Fill in your details to set up your customized legal assistant.</p>\r\n\r\n            <form onSubmit={handleEmailSignUp} className=\"auth-form\">\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"firmName\">Law Firm Name</label>\r\n                <input\r\n                  type=\"text\"\r\n                  id=\"firmName\"\r\n                  value={firmName}\r\n                  onChange={(e) => setFirmName(e.target.value)}\r\n                  placeholder=\"Your Law Firm, LLC\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"subdomain\">Subdomain</label>\r\n                <div className=\"subdomain-input\">\r\n                  <input\r\n                    type=\"text\"\r\n                    id=\"subdomain\"\r\n                    value={subdomain}\r\n                    onChange={(e) => setSubdomain(e.target.value.toLowerCase())}\r\n                    placeholder=\"yourfirm\"\r\n                    required\r\n                  />\r\n                  <span className=\"subdomain-suffix\">.legalscout.ai</span>\r\n                </div>\r\n                <small>This will be your unique URL: https://{subdomain || 'yourfirm'}.legalscout.ai</small>\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"email\">Email Address</label>\r\n                <input\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  value={email}\r\n                  onChange={(e) => setEmail(e.target.value)}\r\n                  placeholder=\"<EMAIL>\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div className=\"form-group\">\r\n                <label htmlFor=\"password\">Password</label>\r\n                <input\r\n                  type=\"password\"\r\n                  id=\"password\"\r\n                  value={password}\r\n                  onChange={(e) => setPassword(e.target.value)}\r\n                  placeholder=\"Create a secure password\"\r\n                  required\r\n                  minLength=\"8\"\r\n                />\r\n              </div>\r\n\r\n              {error && <div className=\"error-message\">{error}</div>}\r\n\r\n              <div className=\"form-actions\">\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"back-button\"\r\n                  onClick={() => setStep('methods')}\r\n                  disabled={loading}\r\n                >\r\n                  Back\r\n                </button>\r\n                <button\r\n                  type=\"submit\"\r\n                  className=\"submit-button\"\r\n                  disabled={loading}\r\n                >\r\n                  {loading ? 'Creating Account...' : 'Create Account'}\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </>\r\n        )}\r\n\r\n        {!supabaseNotConfigured && step === 'success' && (\r\n          <div className=\"success-message\">\r\n            <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\r\n              <path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path>\r\n              <polyline points=\"22 4 12 14.01 9 11.01\"></polyline>\r\n            </svg>\r\n            <h2>Account Created Successfully!</h2>\r\n            <p>Redirecting to your dashboard...</p>\r\n            <div className=\"loading-spinner\"></div>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AuthOverlay;\r\n", "import { supabase, isSupabaseConfigured } from './lib/supabase';\r\n\r\n/**\r\n * Test function to verify Supabase connection\r\n * This can be imported and called from any component to test the connection\r\n */\r\nexport async function testSupabaseConnection() {\r\n  console.log('=== SUPABASE CONNECTION TEST ===');\r\n  \r\n  // Check if Supabase is configured\r\n  const configured = isSupabaseConfigured();\r\n  console.log('Supabase configured:', configured);\r\n  \r\n  if (!configured) {\r\n    console.error('Supabase is not configured properly');\r\n    return { success: false, error: 'Not configured' };\r\n  }\r\n  \r\n  try {\r\n    // Try to query the public.attorneys table\r\n    console.log('Testing query to attorneys table...');\r\n    const { data, error } = await supabase\r\n      .from('attorneys')\r\n      .select('*')\r\n      .limit(1);\r\n    \r\n    if (error) {\r\n      console.error('Supabase query error:', error);\r\n      return { success: false, error };\r\n    }\r\n    \r\n    console.log('Supabase query successful!');\r\n    console.log('Data:', data);\r\n    \r\n    return { success: true, data };\r\n  } catch (e) {\r\n    console.error('Unexpected error testing Supabase:', e);\r\n    return { success: false, error: e };\r\n  }\r\n}\r\n\r\n// Export a function to log Supabase environment variables (without revealing sensitive info)\r\nexport function logSupabaseConfig() {\r\n  // Import environment variables using import.meta.env\r\n  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || \r\n                      import.meta.env.REACT_APP_SUPABASE_URL;\r\n  \r\n  const supabaseKeyExists = Boolean(\r\n    import.meta.env.VITE_SUPABASE_KEY || \r\n    import.meta.env.VITE_SUPABASE_ANON_KEY || \r\n    import.meta.env.REACT_APP_SUPABASE_KEY || \r\n    import.meta.env.REACT_APP_SUPABASE_ANON_KEY\r\n  );\r\n  \r\n  console.log('=== SUPABASE CONFIG TEST ===');\r\n  console.log('Supabase URL configured:', Boolean(supabaseUrl));\r\n  console.log('Supabase Key configured:', supabaseKeyExists);\r\n  \r\n  if (supabaseUrl) {\r\n    console.log('Supabase URL:', supabaseUrl);\r\n  }\r\n  \r\n  return {\r\n    urlConfigured: Boolean(supabaseUrl),\r\n    keyConfigured: supabaseKeyExists\r\n  };\r\n}\r\n", "/**\r\n * Supabase Configuration Verifier\r\n *\r\n * This utility verifies that the Supabase configuration is valid and provides\r\n * fallback values if needed. It's designed to be used in development to prevent\r\n * common configuration issues.\r\n */\r\n\r\nimport { supabase } from '../lib/supabase';\r\n\r\n// Fallback values for development\r\nconst FALLBACK_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';\r\nconst FALLBACK_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTc0NTM2NzcsImV4cCI6MjAzMzAyOTY3N30.Yd-Yk-Hs-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs-Yd-Hs';\r\n\r\n/**\r\n * Verify Supabase configuration and test connection\r\n * @returns {Promise<Object>} Result of the verification\r\n */\r\nexport async function verifySupabaseConfig() {\r\n  console.log('🔍 Verifying Supabase configuration...');\r\n\r\n  // Get environment variables\r\n  const envVars = {\r\n    VITE_SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL,\r\n    REACT_APP_SUPABASE_URL: import.meta.env.REACT_APP_SUPABASE_URL,\r\n    VITE_SUPABASE_KEY: import.meta.env.VITE_SUPABASE_KEY ? '[HIDDEN]' : 'missing',\r\n    VITE_SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY ? '[HIDDEN]' : 'missing',\r\n    REACT_APP_SUPABASE_KEY: import.meta.env.REACT_APP_SUPABASE_KEY ? '[HIDDEN]' : 'missing',\r\n    REACT_APP_SUPABASE_ANON_KEY: import.meta.env.REACT_APP_SUPABASE_ANON_KEY ? '[HIDDEN]' : 'missing',\r\n  };\r\n\r\n  console.log('Environment variables:', envVars);\r\n\r\n  // Check for placeholder values\r\n  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||\r\n                     import.meta.env.REACT_APP_SUPABASE_URL;\r\n\r\n  const supabaseKey = import.meta.env.VITE_SUPABASE_KEY ||\r\n                     import.meta.env.VITE_SUPABASE_ANON_KEY ||\r\n                     import.meta.env.REACT_APP_SUPABASE_KEY ||\r\n                     import.meta.env.REACT_APP_SUPABASE_ANON_KEY;\r\n\r\n  const hasPlaceholderUrl = supabaseUrl === 'your-supabase-url';\r\n  const hasPlaceholderKey = supabaseKey === 'your-anon-key';\r\n\r\n  if (hasPlaceholderUrl) {\r\n    console.warn('⚠️ Detected placeholder Supabase URL in environment variables');\r\n    console.log('Using fallback URL:', FALLBACK_SUPABASE_URL);\r\n\r\n    // Set the URL in window for potential use by other components\r\n    window.VITE_SUPABASE_URL = FALLBACK_SUPABASE_URL;\r\n  }\r\n\r\n  if (hasPlaceholderKey) {\r\n    console.warn('⚠️ Detected placeholder Supabase key in environment variables');\r\n    console.log('Using fallback key (hidden)');\r\n\r\n    // Set the key in window for potential use by other components\r\n    window.VITE_SUPABASE_KEY = FALLBACK_SUPABASE_KEY;\r\n  }\r\n\r\n  // Test connection\r\n  try {\r\n    console.log('Testing Supabase connection...');\r\n    const { data, error } = await supabase\r\n      .from('attorneys')\r\n      .select('*')\r\n      .limit(1);\r\n\r\n    if (error) {\r\n      if (error.message === 'Invalid API key') {\r\n        console.error('❌ Invalid Supabase API key detected');\r\n        console.warn('The application will use mock data instead of real Supabase data');\r\n        console.warn('To fix this, update your Supabase API key in the .env file');\r\n\r\n        return {\r\n          success: false,\r\n          error: 'Invalid API key - using mock data instead',\r\n          usingFallback: true,\r\n          useMockData: true\r\n        };\r\n      } else {\r\n        console.error('❌ Supabase connection test failed:', error.message);\r\n        return {\r\n          success: false,\r\n          error: error.message,\r\n          usingFallback: hasPlaceholderUrl || hasPlaceholderKey\r\n        };\r\n      }\r\n    }\r\n\r\n    console.log('✅ Supabase connection test successful!');\r\n    return {\r\n      success: true,\r\n      data,\r\n      usingFallback: hasPlaceholderUrl || hasPlaceholderKey\r\n    };\r\n  } catch (error) {\r\n    console.error('❌ Unexpected error testing Supabase:', error.message);\r\n    return {\r\n      success: false,\r\n      error: error.message,\r\n      usingFallback: hasPlaceholderUrl || hasPlaceholderKey\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Initialize Supabase configuration\r\n * This should be called early in the application lifecycle\r\n */\r\nexport function initializeSupabaseConfig() {\r\n  // Check for development mode\r\n  const isDev = import.meta.env.MODE === 'development' ||\r\n               window.location.hostname === 'localhost' ||\r\n               window.location.hostname === '127.0.0.1';\r\n\r\n  if (isDev) {\r\n    console.log('🔧 Development mode detected, initializing Supabase configuration...');\r\n\r\n    // Set fallback values in window object if needed\r\n    const supabaseUrl = import.meta.env.VITE_SUPABASE_URL ||\r\n                       import.meta.env.REACT_APP_SUPABASE_URL;\r\n\r\n    const supabaseKey = import.meta.env.VITE_SUPABASE_KEY ||\r\n                       import.meta.env.VITE_SUPABASE_ANON_KEY ||\r\n                       import.meta.env.REACT_APP_SUPABASE_KEY ||\r\n                       import.meta.env.REACT_APP_SUPABASE_ANON_KEY;\r\n\r\n    if (!supabaseUrl || supabaseUrl === 'your-supabase-url') {\r\n      console.warn('⚠️ Setting fallback Supabase URL in window object');\r\n      window.VITE_SUPABASE_URL = FALLBACK_SUPABASE_URL;\r\n    }\r\n\r\n    if (!supabaseKey || supabaseKey === 'your-anon-key') {\r\n      console.warn('⚠️ Setting fallback Supabase key in window object');\r\n      window.VITE_SUPABASE_KEY = FALLBACK_SUPABASE_KEY;\r\n    }\r\n\r\n    // Verify configuration\r\n    verifySupabaseConfig().then(result => {\r\n      if (result.success) {\r\n        console.log('✅ Supabase configuration verified and working');\r\n      } else {\r\n        console.error('❌ Supabase configuration verification failed:', result.error);\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nexport default {\r\n  verifySupabaseConfig,\r\n  initializeSupabaseConfig\r\n};\r\n", "import React from 'react';\r\n\r\nconst TestComponent = () => {\r\n  return (\r\n    <div style={{\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      alignItems: 'center',\r\n      justifyContent: 'center',\r\n      height: '100vh',\r\n      backgroundColor: '#f0f0f0',\r\n      color: '#333',\r\n      padding: '20px',\r\n      textAlign: 'center'\r\n    }}>\r\n      <div style={{\r\n        maxWidth: '600px',\r\n        backgroundColor: 'white',\r\n        padding: '30px',\r\n        borderRadius: '10px',\r\n        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'\r\n      }}>\r\n        <h1 style={{ color: '#4B74AA' }}>LegalScout Test Page</h1>\r\n        <p>If you can see this page, React is working correctly.</p>\r\n        <p>This is a simple React component to test if the application can render without Framer Motion.</p>\r\n        <p>Current time: {new Date().toLocaleString()}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TestComponent;\r\n", "/**\r\n * Vapi Service Manager\r\n *\r\n * This service manages the Vapi integration, automatically switching between\r\n * the real Vapi service and a mock service based on connectivity.\r\n */\r\n\r\nimport { vapiMcpService } from './vapiMcpService';\r\n\r\nclass VapiServiceManager {\r\n  constructor() {\r\n    this.useMock = false; // 💀 NO MORE MOCKS!\r\n    this.apiKey = null;\r\n    this.initialized = false;\r\n    this.mcpService = vapiMcpService;\r\n  }\r\n\r\n  /**\r\n   * Initialize the Vapi service manager\r\n   * @param {string} apiKey - Vapi API key\r\n   * @param {boolean} forceMock - Force using mock service\r\n   * @param {Object} options - Additional options\r\n   * @param {boolean} options.isAttorneyDashboard - Whether this is the attorney dashboard\r\n   * @param {boolean} options.isPreview - Whether this is the preview mode\r\n   * @param {boolean} options.isProduction - Whether this is a production environment\r\n   * @param {boolean} options.forceMcpMode - Force using MCP mode instead of direct API\r\n   * @param {boolean} options.forceDirect - Force using direct API mode\r\n   * @returns {Promise<boolean>} - Initialization success\r\n   */\r\n  async initialize(apiKey, forceMock = false, options = {}) {\r\n    const {\r\n      isAttorneyDashboard = false,\r\n      isPreview = false,\r\n      isProduction = false,\r\n      forceMcpMode = false,\r\n      forceDirect = false\r\n    } = options;\r\n\r\n    // Check if fast loading mode is enabled\r\n    const fastLoadingMode = typeof window !== 'undefined' && window.FAST_LOADING_MODE;\r\n\r\n    console.log('[VapiServiceManager] Initializing with API key:', apiKey ? '****' : 'none',\r\n      `(Attorney Dashboard: ${isAttorneyDashboard}, Preview: ${isPreview}, Production: ${isProduction}, Force MCP: ${forceMcpMode}, Force Direct: ${forceDirect}, Fast Loading: ${fastLoadingMode})`);\r\n\r\n    this.apiKey = apiKey;\r\n    this.isAttorneyDashboard = isAttorneyDashboard;\r\n    this.isPreview = isPreview;\r\n    this.isProduction = isProduction;\r\n    this.forceMcpMode = forceMcpMode;\r\n\r\n    // In attorney dashboard, preview, or production, we should prioritize real connectivity\r\n    const prioritizeReal = isAttorneyDashboard || isPreview || isProduction;\r\n\r\n    // 💀 NO MORE MOCKS! Force mock is ignored\r\n    if (forceMock) {\r\n      console.error('💀 [VapiServiceManager] Mock mode requested but MOCKS ARE DEAD! Using real service only.');\r\n    }\r\n\r\n    try {\r\n      // Try to connect to the real Vapi service\r\n      console.log('[VapiServiceManager] Trying to connect to real Vapi service');\r\n\r\n      let connected = false;\r\n\r\n      // If fast loading mode is enabled or forceDirect is true, use direct API immediately\r\n      if (fastLoadingMode || forceDirect) {\r\n        console.log('[VapiServiceManager] Fast loading mode - using direct API immediately');\r\n        const directConnected = await vapiMcpService.connect(apiKey, true);\r\n\r\n        if (directConnected) {\r\n          console.log('[VapiServiceManager] Connected to Vapi using direct API (fast loading)');\r\n          this.useMock = false;\r\n          this.mcpService = vapiMcpService;\r\n          connected = true;\r\n        }\r\n      } else if (this.forceMcpMode) {\r\n        console.log('[VapiServiceManager] Forcing MCP mode, skipping direct API');\r\n        const mcpConnected = await vapiMcpService.connect(apiKey);\r\n\r\n        if (mcpConnected) {\r\n          console.log('[VapiServiceManager] Connected to Vapi MCP server');\r\n          this.useMock = false;\r\n          this.mcpService = vapiMcpService;\r\n          connected = true;\r\n        }\r\n      } else {\r\n        // Try MCP connection first\r\n        const mcpConnected = await vapiMcpService.connect(apiKey);\r\n\r\n        if (mcpConnected) {\r\n          console.log('[VapiServiceManager] Connected to Vapi MCP server');\r\n          this.useMock = false;\r\n          this.mcpService = vapiMcpService;\r\n          connected = true;\r\n        } else {\r\n          // If MCP connection fails, try direct API\r\n          console.log('[VapiServiceManager] MCP connection failed, trying direct API');\r\n          const directConnected = await vapiMcpService.connect(apiKey, true);\r\n\r\n          if (directConnected) {\r\n            console.log('[VapiServiceManager] Connected to Vapi using direct API');\r\n            this.useMock = false;\r\n            this.mcpService = vapiMcpService;\r\n            connected = true;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If all connection attempts failed\r\n      if (!connected) {\r\n        console.error('💀 [VapiServiceManager] Failed to connect to Vapi - NO MOCKS ALLOWED!');\r\n        this.connectionError = true;\r\n        this.connectionWarning = \"Critical: Unable to connect to voice service. Please check your API key and network connection.\";\r\n        throw new Error('Failed to connect to Vapi service - no fallback available');\r\n      }\r\n\r\n      this.initialized = true;\r\n      return true;\r\n    } catch (error) {\r\n      console.error('💀 [VapiServiceManager] Error initializing Vapi service - NO MOCKS ALLOWED!', error);\r\n      this.connectionError = true;\r\n      this.connectionWarning = \"Critical error connecting to voice service. Please check your configuration.\";\r\n      throw error; // Let the error propagate instead of falling back to mocks\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get connection warning if any\r\n   * @returns {string|null} - Warning message or null if no warning\r\n   */\r\n  getConnectionWarning() {\r\n    return this.connectionWarning || null;\r\n  }\r\n\r\n  /**\r\n   * Get the MCP service (real only - no mocks!)\r\n   * @returns {Object} - MCP service\r\n   */\r\n  getMcpService() {\r\n    return this.mcpService; // 💀 NO MORE MOCKS!\r\n  }\r\n\r\n  /**\r\n   * Create a Vapi instance (real only - no mocks!)\r\n   * @param {string} apiKey - Vapi API key\r\n   * @param {Object} options - Vapi options\r\n   * @returns {Object} - Vapi instance\r\n   */\r\n  createVapiInstance(apiKey, options = {}) {\r\n    console.log('💀 [VapiServiceManager] Creating real Vapi instance - NO MOCKS!');\r\n\r\n    // Check if Vapi is available globally\r\n    if (typeof window !== 'undefined' && window.Vapi) {\r\n      return window.Vapi.create(apiKey, options);\r\n    } else if (typeof Vapi !== 'undefined') {\r\n      return Vapi.create(apiKey, options);\r\n    } else {\r\n      console.error('💀 [VapiServiceManager] Vapi not available - NO MOCKS ALLOWED!');\r\n      throw new Error('Vapi SDK not available - please ensure it is loaded');\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Check if using mock service\r\n   * @returns {boolean} - Using mock service\r\n   */\r\n  isUsingMock() {\r\n    return this.useMock;\r\n  }\r\n\r\n  /**\r\n   * Get connection status (real only - no mocks!)\r\n   * @returns {Object} - Connection status\r\n   */\r\n  getConnectionStatus() {\r\n    return {\r\n      initialized: this.initialized,\r\n      useMock: false, // 💀 NO MORE MOCKS!\r\n      connected: this.mcpService.connected,\r\n      connectionMode: this.mcpService.useDirect ? 'direct' : 'mcp'\r\n    };\r\n  }\r\n}\r\n\r\n// Export a singleton instance\r\nexport const vapiServiceManager = new VapiServiceManager();\r\n\r\nexport default vapiServiceManager;\r\n", "/**\r\n * Attorney Profile Manager\r\n *\r\n * A comprehensive service for managing attorney profiles across systems:\r\n * - Supa<PERSON> (database)\r\n * - <PERSON><PERSON><PERSON> (voice assistant)\r\n * - localStorage (client-side cache)\r\n */\r\n\r\nimport { supabase } from '../lib/supabase';\r\nimport { vapiServiceManager } from './vapiServiceManager';\r\n\r\nclass AttorneyProfileManager {\r\n  constructor() {\r\n    this.currentAttorney = null;\r\n    this.subscription = null;\r\n    this.listeners = new Set();\r\n    this.isInitialized = false;\r\n    this.lastSyncTime = null;\r\n    this.syncStatus = { consistent: false, message: 'Not synchronized yet' };\r\n    this.isRecoveryMode = false;\r\n    this.recoveryAttempts = 0;\r\n    this.MAX_RECOVERY_ATTEMPTS = 3;\r\n    this.pendingUpdates = new Map();\r\n    this.initPromise = null;\r\n\r\n    // Bind methods to ensure consistent 'this'\r\n    this.initialize = this.initialize.bind(this);\r\n    this.handleAttorneyUpdate = this.handleAttorneyUpdate.bind(this);\r\n    this.checkVapiSynchronization = this.checkVapiSynchronization.bind(this);\r\n    this.forceSynchronization = this.forceSynchronization.bind(this);\r\n\r\n    // Auto-initialize from localStorage if available\r\n    this.autoInitializeFromLocalStorage();\r\n  }\r\n\r\n  // Auto-initialize from localStorage if available\r\n  autoInitializeFromLocalStorage() {\r\n    try {\r\n      const storedAttorney = this.loadFromLocalStorage();\r\n      if (storedAttorney && storedAttorney.id) {\r\n        console.log('[AttorneyProfileManager] Auto-initializing from localStorage');\r\n        this.currentAttorney = storedAttorney;\r\n\r\n        // Set up Supabase Realtime subscription\r\n        this.setupRealtimeSubscription(storedAttorney.id);\r\n\r\n        // Schedule a sync check with refreshed data\r\n        setTimeout(async () => {\r\n          try {\r\n            // Refresh attorney data from Supabase before sync check\r\n            let attorneyToSync = storedAttorney;\r\n            if (storedAttorney.id && typeof storedAttorney.id === 'string' && !storedAttorney.id.startsWith('dev-')) {\r\n              try {\r\n                const refreshedAttorney = await this.loadAttorneyById(storedAttorney.id);\r\n                if (refreshedAttorney) {\r\n                  console.log('[AttorneyProfileManager] Refreshed attorney data for auto-sync:', {\r\n                    id: refreshedAttorney.id,\r\n                    vapi_assistant_id: refreshedAttorney.vapi_assistant_id\r\n                  });\r\n                  attorneyToSync = refreshedAttorney;\r\n                  this.currentAttorney = refreshedAttorney;\r\n                  this.saveToLocalStorage(refreshedAttorney);\r\n                }\r\n              } catch (refreshError) {\r\n                console.warn('[AttorneyProfileManager] Could not refresh attorney data for auto-sync:', refreshError);\r\n              }\r\n            }\r\n\r\n            // Skip automatic Vapi sync - following one-way sync pattern\r\n            console.log('[AttorneyProfileManager] Auto-sync: Skipping automatic Vapi sync (following one-way sync pattern)');\r\n          } catch (error) {\r\n            console.error('[AttorneyProfileManager] Auto-sync check error:', error);\r\n          }\r\n        }, 2000);\r\n      }\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Auto-initialize error:', error);\r\n    }\r\n  }\r\n\r\n  // Initialize the manager with the current user\r\n  async initialize(userId, email = null) {\r\n    // If already initializing, return the existing promise\r\n    if (this.initPromise) {\r\n      return this.initPromise;\r\n    }\r\n\r\n    // Create a new initialization promise\r\n    this.initPromise = this._initialize(userId, email);\r\n\r\n    try {\r\n      const result = await this.initPromise;\r\n      return result;\r\n    } finally {\r\n      // Clear the promise when done\r\n      this.initPromise = null;\r\n    }\r\n  }\r\n\r\n  // Internal initialization method\r\n  async _initialize(userId, email = null) {\r\n    console.log('[AttorneyProfileManager] Initializing with userId:', userId, 'email:', email);\r\n\r\n    if (this.isInitialized && this.currentAttorney) {\r\n      console.log('[AttorneyProfileManager] Already initialized with attorney:', this.currentAttorney.id);\r\n      return this.currentAttorney;\r\n    }\r\n\r\n    try {\r\n      // Try multiple methods to find the attorney profile\r\n      let attorney = null;\r\n\r\n      // Method 1: Try to load by user ID\r\n      if (userId) {\r\n        try {\r\n          attorney = await this.loadAttorneyByUserId(userId);\r\n          if (attorney) {\r\n            console.log('[AttorneyProfileManager] Found attorney by userId:', attorney.id);\r\n          }\r\n        } catch (error) {\r\n          console.warn('[AttorneyProfileManager] Error loading by userId:', error);\r\n        }\r\n      }\r\n\r\n      // Method 2: Try to load by email if available\r\n      if (!attorney && email) {\r\n        try {\r\n          attorney = await this.loadAttorneyByEmail(email);\r\n          if (attorney) {\r\n            console.log('[AttorneyProfileManager] Found attorney by email:', attorney.id);\r\n\r\n            // CRITICAL: Prevent assistant creation if attorney already has one\r\n            if (attorney.vapi_assistant_id && !attorney.vapi_assistant_id.includes('mock')) {\r\n              console.log('[AttorneyProfileManager] ✅ Attorney already has valid assistant:', attorney.vapi_assistant_id);\r\n            }\r\n\r\n            // If found by email but not linked to user ID, update the link\r\n            if (userId && (!attorney.user_id || attorney.user_id !== userId)) {\r\n              console.log('[AttorneyProfileManager] Linking attorney to userId:', userId);\r\n              attorney = await this.updateAttorneyInSupabase({\r\n                id: attorney.id,\r\n                user_id: userId\r\n              });\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.warn('[AttorneyProfileManager] Error loading by email:', error);\r\n        }\r\n      }\r\n\r\n      // Method 3: Try to load from localStorage\r\n      if (!attorney) {\r\n        const storedAttorney = this.loadFromLocalStorage();\r\n        if (storedAttorney && storedAttorney.id) {\r\n          console.log('[AttorneyProfileManager] Found attorney in localStorage:', storedAttorney.id);\r\n\r\n          // Verify the attorney exists in Supabase\r\n          try {\r\n            attorney = await this.loadAttorneyById(storedAttorney.id);\r\n            if (attorney) {\r\n              console.log('[AttorneyProfileManager] Verified attorney from localStorage exists in Supabase');\r\n\r\n              // If not linked to user ID, update the link\r\n              if (userId && (!attorney.user_id || attorney.user_id !== userId)) {\r\n                console.log('[AttorneyProfileManager] Linking attorney to userId:', userId);\r\n                attorney = await this.updateAttorneyInSupabase({\r\n                  id: attorney.id,\r\n                  user_id: userId\r\n                });\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.warn('[AttorneyProfileManager] Error verifying localStorage attorney:', error);\r\n\r\n            // Use the localStorage attorney as a fallback\r\n            attorney = storedAttorney;\r\n          }\r\n        }\r\n      }\r\n\r\n      // If still not found, create a new attorney profile\r\n      if (!attorney && (userId || email)) {\r\n        console.log('[AttorneyProfileManager] No attorney found, creating new attorney profile');\r\n\r\n        const userEmail = email || `user-${userId}@example.com`;\r\n        const name = userEmail.split('@')[0];\r\n        const domain = userEmail.split('@')[1];\r\n        const firmName = domain ? `${domain.split('.')[0]} Legal` : 'My Law Firm';\r\n\r\n        // Try to create in Supabase first\r\n        try {\r\n          const { supabase } = await import('../lib/supabase');\r\n\r\n          const attorneyData = {\r\n            subdomain: `${name}-${Date.now()}`.toLowerCase().replace(/[^a-z0-9-]/g, '-'),\r\n            firm_name: firmName,\r\n            name: name,\r\n            email: userEmail,\r\n            user_id: userId,\r\n            is_active: true,\r\n            vapi_instructions: `You are a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information for consultation.`,\r\n            welcome_message: `Hello! I'm Scout from ${firmName}. How can I help you with your legal needs today?`,\r\n            information_gathering: 'Tell me about your legal situation, and I\\'ll help connect you with the right resources and guidance.',\r\n            primary_color: '#4B74AA',\r\n            secondary_color: '#2C3E50',\r\n            background_color: '#1a1a1a',\r\n            voice_provider: 'playht',\r\n            voice_id: 'ranger',\r\n            ai_model: 'gpt-4o'\r\n          };\r\n\r\n          const { data: newAttorney, error } = await supabase\r\n            .from('attorneys')\r\n            .insert([attorneyData])\r\n            .select()\r\n            .single();\r\n\r\n          if (!error && newAttorney) {\r\n            console.log('[AttorneyProfileManager] Created new attorney in Supabase:', newAttorney.id);\r\n            attorney = newAttorney;\r\n          } else {\r\n            console.warn('[AttorneyProfileManager] Failed to create attorney in Supabase:', error);\r\n            throw error;\r\n          }\r\n        } catch (supabaseError) {\r\n          console.warn('[AttorneyProfileManager] Supabase creation failed, creating local attorney:', supabaseError);\r\n\r\n          // Fallback to local attorney\r\n          const devId = `dev-${Date.now()}`;\r\n          attorney = {\r\n            id: devId,\r\n            user_id: userId,\r\n            email: userEmail,\r\n            firm_name: firmName,\r\n            name: name,\r\n            created_at: new Date().toISOString(),\r\n            updated_at: new Date().toISOString(),\r\n            vapi_instructions: `You are a legal assistant for ${firmName}. Help potential clients understand their legal needs and collect relevant information for consultation.`,\r\n            welcome_message: `Hello! I'm Scout from ${firmName}. How can I help you with your legal needs today?`,\r\n            is_development: true\r\n          };\r\n        }\r\n\r\n        console.log('[AttorneyProfileManager] Created development attorney:', attorney.id);\r\n      }\r\n\r\n      if (attorney) {\r\n        // Store the attorney data\r\n        this.currentAttorney = attorney;\r\n\r\n        // Save to localStorage for offline access\r\n        this.saveToLocalStorage(attorney);\r\n\r\n        // Set up Supabase Realtime subscription for non-development attorneys\r\n        if (attorney.id && typeof attorney.id === 'string' && !attorney.id.startsWith('dev-')) {\r\n          this.setupRealtimeSubscription(attorney.id);\r\n        } else {\r\n          console.log('[AttorneyProfileManager] Development attorney or invalid ID, skipping Realtime subscription');\r\n        }\r\n\r\n        // Ensure attorney has latest data from Supabase before checking Vapi sync\r\n        if (attorney.id && typeof attorney.id === 'string' && !attorney.id.startsWith('dev-')) {\r\n          try {\r\n            const latestAttorney = await this.loadAttorneyById(attorney.id);\r\n            if (latestAttorney) {\r\n              console.log('[AttorneyProfileManager] Refreshed attorney data before Vapi sync:', {\r\n                id: latestAttorney.id,\r\n                vapi_assistant_id: latestAttorney.vapi_assistant_id\r\n              });\r\n              attorney = latestAttorney;\r\n              this.currentAttorney = latestAttorney;\r\n              this.saveToLocalStorage(latestAttorney);\r\n            }\r\n          } catch (refreshError) {\r\n            console.warn('[AttorneyProfileManager] Could not refresh attorney data:', refreshError);\r\n          }\r\n        }\r\n\r\n        // CRITICAL MVP FIX: Skip automatic Vapi synchronization during initialization\r\n        // Following one-way sync pattern: UI → Supabase → Vapi\r\n        // Only sync when explicitly requested by user actions\r\n        // This prevents duplicate assistant creation on every login\r\n        console.log('[AttorneyProfileManager] 🛑 MVP FIX: Skipping automatic Vapi sync to prevent duplicate assistants');\r\n\r\n        // Additional safety check: Log if attorney already has assistant\r\n        if (attorney.vapi_assistant_id && !attorney.vapi_assistant_id.includes('mock')) {\r\n          console.log('[AttorneyProfileManager] ✅ Attorney has valid assistant:', attorney.vapi_assistant_id);\r\n        }\r\n\r\n        this.isInitialized = true;\r\n        this.notifyListeners();\r\n\r\n        return attorney;\r\n      }\r\n\r\n      console.warn('[AttorneyProfileManager] No attorney profile found and could not create development attorney');\r\n      return null;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Initialization error:', error);\r\n\r\n      // Try to recover from localStorage\r\n      const storedAttorney = this.loadFromLocalStorage();\r\n      if (storedAttorney) {\r\n        console.log('[AttorneyProfileManager] Recovering from localStorage');\r\n        this.currentAttorney = storedAttorney;\r\n        this.notifyListeners();\r\n        return storedAttorney;\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Load attorney by user ID\r\n  async loadAttorneyByUserId(userId) {\r\n    try {\r\n      console.log('[AttorneyProfileManager] Loading attorney by userId:', userId);\r\n      const { data, error } = await supabase\r\n        .from('attorneys')\r\n        .select('*')\r\n        .eq('user_id', userId)\r\n        .single();\r\n\r\n      if (error) {\r\n        if (error.code === 'PGRST116') {\r\n          // No rows returned\r\n          return null;\r\n        }\r\n        throw error;\r\n      }\r\n\r\n      return data;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error loading attorney by userId:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Load attorney by email - FIXED: Prevent duplicate creation\r\n  async loadAttorneyByEmail(email) {\r\n    try {\r\n      console.log('[AttorneyProfileManager] Loading attorney by email:', email);\r\n\r\n      // CRITICAL FIX: Get the attorney with a valid vapi_assistant_id first\r\n      const { data: attorneys, error } = await supabase\r\n        .from('attorneys')\r\n        .select('*')\r\n        .eq('email', email)\r\n        .order('updated_at', { ascending: false });\r\n\r\n      if (error) {\r\n        throw error;\r\n      }\r\n\r\n      if (attorneys && attorneys.length > 0) {\r\n        if (attorneys.length > 1) {\r\n          console.warn(`[AttorneyProfileManager] Found ${attorneys.length} attorneys for email ${email}`);\r\n\r\n          // CRITICAL: Prefer attorney with valid vapi_assistant_id\r\n          const attorneyWithAssistant = attorneys.find(a =>\r\n            a.vapi_assistant_id &&\r\n            !a.vapi_assistant_id.includes('mock') &&\r\n            !a.vapi_assistant_id.includes('duplicate')\r\n          );\r\n\r\n          if (attorneyWithAssistant) {\r\n            console.log(`[AttorneyProfileManager] Using attorney with valid assistant ID: ${attorneyWithAssistant.vapi_assistant_id}`);\r\n            return attorneyWithAssistant;\r\n          }\r\n        }\r\n        return attorneys[0];\r\n      }\r\n\r\n      return null;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error loading attorney by email:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Load attorney by ID\r\n  async loadAttorneyById(id) {\r\n    try {\r\n      console.log('[AttorneyProfileManager] Loading attorney by id:', id);\r\n      const { data, error } = await supabase\r\n        .from('attorneys')\r\n        .select('*')\r\n        .eq('id', id)\r\n        .single();\r\n\r\n      if (error) {\r\n        if (error.code === 'PGRST116') {\r\n          // No rows returned\r\n          return null;\r\n        }\r\n        throw error;\r\n      }\r\n\r\n      return data;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error loading attorney by id:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Set up Supabase Realtime subscription\r\n  setupRealtimeSubscription(attorneyId) {\r\n    try {\r\n      console.log('[AttorneyProfileManager] Setting up Realtime subscription for attorney:', attorneyId);\r\n\r\n      // Check if Supabase client has channel method (v2 API)\r\n      if (typeof supabase.channel === 'function') {\r\n        // Clean up existing channel if any\r\n        if (this.channel) {\r\n          supabase.removeChannel(this.channel);\r\n          this.channel = null;\r\n        }\r\n\r\n        // Set up new channel subscription (v2 API)\r\n        this.channel = supabase\r\n          .channel(`attorneys:id=${attorneyId}`)\r\n          .on('postgres_changes',\r\n            {\r\n              event: 'UPDATE',\r\n              schema: 'public',\r\n              table: 'attorneys',\r\n              filter: `id=eq.${attorneyId}`\r\n            },\r\n            payload => this.handleAttorneyUpdate(payload)\r\n          )\r\n          .on('postgres_changes',\r\n            {\r\n              event: 'DELETE',\r\n              schema: 'public',\r\n              table: 'attorneys',\r\n              filter: `id=eq.${attorneyId}`\r\n            },\r\n            payload => this.handleAttorneyDelete(payload)\r\n          )\r\n          .subscribe();\r\n\r\n        console.log('[AttorneyProfileManager] Realtime subscription set up using channel API');\r\n      }\r\n      // Check if Supabase client has on method (v1 API)\r\n      else if (typeof supabase.from === 'function' &&\r\n               typeof supabase.from('attorneys').on === 'function') {\r\n        // Clean up existing subscription if any\r\n        if (this.subscription) {\r\n          supabase.removeSubscription(this.subscription);\r\n          this.subscription = null;\r\n        }\r\n\r\n        // Set up new subscription (v1 API)\r\n        this.subscription = supabase\r\n          .from(`attorneys:id=eq.${attorneyId}`)\r\n          .on('UPDATE', payload => this.handleAttorneyUpdate(payload))\r\n          .on('DELETE', payload => this.handleAttorneyDelete(payload))\r\n          .subscribe();\r\n\r\n        console.log('[AttorneyProfileManager] Realtime subscription set up using from().on() API');\r\n      }\r\n      else {\r\n        console.warn('[AttorneyProfileManager] Supabase Realtime API not available, using polling instead');\r\n\r\n        // Set up polling as fallback\r\n        if (this.pollingInterval) {\r\n          clearInterval(this.pollingInterval);\r\n        }\r\n\r\n        this.pollingInterval = setInterval(async () => {\r\n          try {\r\n            if (this.currentAttorney && this.currentAttorney.id) {\r\n              const { data, error } = await supabase\r\n                .from('attorneys')\r\n                .select('*')\r\n                .eq('id', this.currentAttorney.id)\r\n                .single();\r\n\r\n              if (error) throw error;\r\n\r\n              // Check if data has changed\r\n              if (data && JSON.stringify(data) !== JSON.stringify(this.currentAttorney)) {\r\n                console.log('[AttorneyProfileManager] Attorney updated via polling');\r\n                this.handleAttorneyUpdate({ new: data });\r\n              }\r\n            }\r\n          } catch (error) {\r\n            console.error('[AttorneyProfileManager] Error polling for attorney updates:', error);\r\n          }\r\n        }, 10000); // Poll every 10 seconds\r\n\r\n        console.log('[AttorneyProfileManager] Polling fallback set up');\r\n      }\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error setting up Realtime subscription:', error);\r\n\r\n      // Schedule retry\r\n      setTimeout(() => {\r\n        if (this.currentAttorney && this.currentAttorney.id) {\r\n          this.setupRealtimeSubscription(this.currentAttorney.id);\r\n        }\r\n      }, 5000);\r\n    }\r\n  }\r\n\r\n  // Handle attorney update from Supabase Realtime\r\n  async handleAttorneyUpdate(payload) {\r\n    console.log('[AttorneyProfileManager] Received attorney update from Supabase:', payload);\r\n\r\n    try {\r\n      const updatedAttorney = payload.new;\r\n\r\n      // Check if this is a pending update we initiated\r\n      const pendingUpdateId = updatedAttorney.id + '_' + updatedAttorney.updated_at;\r\n      if (this.pendingUpdates.has(pendingUpdateId)) {\r\n        console.log('[AttorneyProfileManager] This is a pending update we initiated, skipping sync check');\r\n        this.pendingUpdates.delete(pendingUpdateId);\r\n      } else {\r\n        // This is an external update - skip automatic Vapi sync\r\n        console.log('[AttorneyProfileManager] External update received, skipping automatic Vapi sync (following one-way sync pattern)');\r\n      }\r\n\r\n      // Update local state\r\n      this.currentAttorney = updatedAttorney;\r\n\r\n      // Save to localStorage\r\n      this.saveToLocalStorage(updatedAttorney);\r\n\r\n      // Notify listeners\r\n      this.notifyListeners();\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error handling attorney update:', error);\r\n    }\r\n  }\r\n\r\n  // Handle attorney delete from Supabase Realtime\r\n  handleAttorneyDelete(payload) {\r\n    console.log('[AttorneyProfileManager] Received attorney delete from Supabase:', payload);\r\n\r\n    // Clear local state\r\n    this.currentAttorney = null;\r\n\r\n    // Clear localStorage\r\n    try {\r\n      localStorage.removeItem('attorney');\r\n      localStorage.removeItem('attorney_last_updated');\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error clearing localStorage:', error);\r\n    }\r\n\r\n    // Notify listeners\r\n    this.notifyListeners();\r\n  }\r\n\r\n  // Check if we should sync with Vapi based on changed fields\r\n  shouldSyncWithVapi(updatedAttorney) {\r\n    if (!this.currentAttorney) {\r\n      // If no current attorney, only sync if the attorney has a Vapi assistant ID\r\n      // or if it's missing one and needs to be created\r\n      if (!updatedAttorney.vapi_assistant_id) {\r\n        console.log('[AttorneyProfileManager] No current attorney and no assistant ID - sync needed to create assistant');\r\n        return true;\r\n      } else {\r\n        console.log('[AttorneyProfileManager] No current attorney but has assistant ID - checking if assistant exists');\r\n        return true; // We need to verify the assistant exists\r\n      }\r\n    }\r\n\r\n    // Define fields that require Vapi sync (ONLY voice/AI related fields)\r\n    const vapiRelevantFields = [\r\n      'firm_name',\r\n      'welcome_message',\r\n      'vapi_instructions',\r\n      'voice_provider',\r\n      'voice_id',\r\n      'ai_model',\r\n      'vapi_assistant_id'\r\n    ];\r\n\r\n    // Define fields that should NEVER trigger Vapi sync\r\n    const nonVapiFields = [\r\n      'logo_url',\r\n      'profile_image',\r\n      'button_image',\r\n      'primary_color',\r\n      'secondary_color',\r\n      'button_color',\r\n      'background_color',\r\n      'address',\r\n      'phone',\r\n      'practice_areas',\r\n      'practice_description',\r\n      'scheduling_link',\r\n      'custom_fields',\r\n      'summary_prompt',\r\n      'structured_data_prompt',\r\n      'structured_data_schema'\r\n    ];\r\n\r\n    // Check if any Vapi-relevant fields have changed\r\n    for (const field of vapiRelevantFields) {\r\n      if (this.currentAttorney[field] !== updatedAttorney[field]) {\r\n        console.log(`[AttorneyProfileManager] Vapi-relevant field changed: ${field}`);\r\n        console.log(`  Old: ${this.currentAttorney[field]}`);\r\n        console.log(`  New: ${updatedAttorney[field]}`);\r\n        return true;\r\n      }\r\n    }\r\n\r\n    // Log if only non-Vapi fields changed (for debugging)\r\n    const changedFields = [];\r\n    for (const field in updatedAttorney) {\r\n      if (this.currentAttorney[field] !== updatedAttorney[field]) {\r\n        changedFields.push(field);\r\n      }\r\n    }\r\n\r\n    if (changedFields.length > 0) {\r\n      const nonVapiChanges = changedFields.filter(field => nonVapiFields.includes(field));\r\n      if (nonVapiChanges.length > 0) {\r\n        console.log(`[AttorneyProfileManager] Only non-Vapi fields changed: ${nonVapiChanges.join(', ')}`);\r\n      }\r\n    }\r\n\r\n    // No Vapi-relevant fields changed\r\n    return false;\r\n  }\r\n\r\n  // Check and fix Vapi synchronization\r\n  async checkVapiSynchronization(attorney) {\r\n    if (!attorney) {\r\n      console.warn('[AttorneyProfileManager] No attorney to check Vapi synchronization');\r\n      return;\r\n    }\r\n\r\n    // Handle case where attorney is passed as an array (fix for array issue)\r\n    if (Array.isArray(attorney)) {\r\n      console.log('[AttorneyProfileManager] Attorney passed as array, extracting first element');\r\n      if (attorney.length > 0 && attorney[0] && attorney[0].id) {\r\n        attorney = attorney[0];\r\n        console.log('[AttorneyProfileManager] Using attorney from array:', attorney.id);\r\n      } else {\r\n        console.error('[AttorneyProfileManager] Array is empty or invalid:', attorney);\r\n        this.syncStatus = {\r\n          consistent: false,\r\n          message: 'Invalid attorney array data',\r\n          lastChecked: new Date(),\r\n          error: 'Attorney array is empty or invalid'\r\n        };\r\n        return;\r\n      }\r\n    }\r\n\r\n    // Validate attorney has required properties\r\n    if (!attorney.id) {\r\n      console.error('[AttorneyProfileManager] Attorney missing ID, cannot sync with Vapi:', attorney);\r\n      this.syncStatus = {\r\n        consistent: false,\r\n        message: 'Attorney missing ID, cannot sync with voice service',\r\n        lastChecked: new Date(),\r\n        error: 'Invalid attorney data'\r\n      };\r\n      return;\r\n    }\r\n\r\n    console.log('[AttorneyProfileManager] Checking Vapi synchronization for attorney:', attorney.id);\r\n\r\n    try {\r\n      // Get the appropriate Vapi service\r\n      const vapiService = vapiServiceManager.getMcpService();\r\n\r\n      // Check if Vapi service is connected\r\n      const connected = await vapiService.ensureConnection();\r\n      if (!connected) {\r\n        console.warn('[AttorneyProfileManager] Vapi service not connected, skipping synchronization');\r\n\r\n        this.syncStatus = {\r\n          consistent: true,\r\n          message: 'Voice service not available, using local data',\r\n          lastChecked: new Date()\r\n        };\r\n\r\n        this.lastSyncTime = new Date();\r\n        return;\r\n      }\r\n\r\n      // Check if attorney has a mock assistant ID and fix it\r\n      if (attorney.vapi_assistant_id && attorney.vapi_assistant_id.startsWith('mock-')) {\r\n        console.warn('[AttorneyProfileManager] Mock assistant ID detected, attempting to fix:', attorney.vapi_assistant_id);\r\n\r\n        // Check if we're in production environment\r\n        const isProduction = typeof window !== 'undefined' &&\r\n          (window.location.hostname === 'dashboard.legalscout.net' ||\r\n           window.location.hostname.endsWith('.legalscout.net'));\r\n\r\n        try {\r\n          // First, try to find an existing real assistant for this firm\r\n          const assistants = await vapiService.listAssistants();\r\n          const firmAssistant = assistants.find(a =>\r\n            a.name && a.name.includes(attorney.firm_name) && !a.id.startsWith('mock-')\r\n          );\r\n\r\n          if (firmAssistant) {\r\n            console.log('[AttorneyProfileManager] Found existing real assistant:', firmAssistant.id);\r\n            // Update database with real assistant ID\r\n            await this.updateAttorneyInSupabase({\r\n              id: attorney.id,\r\n              vapi_assistant_id: firmAssistant.id\r\n            });\r\n\r\n            this.syncStatus = {\r\n              consistent: true,\r\n              message: 'Fixed mock assistant ID with existing real assistant',\r\n              lastChecked: new Date()\r\n            };\r\n            this.lastSyncTime = new Date();\r\n            return;\r\n          } else if (isProduction) {\r\n            // In production, create a new real assistant\r\n            console.log('[AttorneyProfileManager] No existing assistant found, creating new one');\r\n            const newAssistant = await this.createVapiAssistant(attorney);\r\n\r\n            if (newAssistant && newAssistant.id && !newAssistant.id.startsWith('mock-')) {\r\n              await this.updateAttorneyInSupabase({\r\n                id: attorney.id,\r\n                vapi_assistant_id: newAssistant.id\r\n              });\r\n\r\n              this.syncStatus = {\r\n                consistent: true,\r\n                message: 'Created new real assistant to replace mock',\r\n                lastChecked: new Date()\r\n              };\r\n              this.lastSyncTime = new Date();\r\n              return;\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error('[AttorneyProfileManager] Error fixing mock assistant ID:', error);\r\n        }\r\n\r\n        // If we can't fix it, mark as inconsistent\r\n        this.syncStatus = {\r\n          consistent: false,\r\n          message: 'Mock assistant ID detected - needs real assistant',\r\n          lastChecked: new Date(),\r\n          warning: 'Voice assistant needs to be properly configured'\r\n        };\r\n        this.lastSyncTime = new Date();\r\n        return;\r\n      }\r\n\r\n      // Check if attorney has a Vapi assistant ID\r\n      if (!attorney.vapi_assistant_id) {\r\n        console.log('[AttorneyProfileManager] Attorney has no Vapi assistant ID, creating new assistant');\r\n\r\n        // Create a new assistant\r\n        const assistant = await this.createVapiAssistant(attorney);\r\n\r\n        // Only update database if we got a real assistant ID (not mock)\r\n        if (assistant && assistant.id && !assistant.id.startsWith('mock-')) {\r\n          // Update attorney with assistant ID\r\n          await this.updateAttorneyInSupabase({\r\n            id: attorney.id,\r\n            vapi_assistant_id: assistant.id\r\n          });\r\n\r\n          this.syncStatus = {\r\n            consistent: true,\r\n            message: 'Created new Vapi assistant',\r\n            lastChecked: new Date()\r\n          };\r\n        } else {\r\n          console.warn('[AttorneyProfileManager] Mock assistant created, not saving to database');\r\n          this.syncStatus = {\r\n            consistent: false,\r\n            message: 'Mock assistant created, Vapi service may be unavailable',\r\n            lastChecked: new Date(),\r\n            warning: 'Voice assistant will be created when service is available'\r\n          };\r\n        }\r\n      } else {\r\n        // Check if the assistant exists and is up to date\r\n        let assistant;\r\n        try {\r\n          assistant = await vapiServiceManager.getMcpService().getAssistant(attorney.vapi_assistant_id);\r\n        } catch (error) {\r\n          console.error('[AttorneyProfileManager] Error getting Vapi assistant:', error);\r\n\r\n          // If MCP server is not available, use a mock assistant\r\n          if (error.message.includes('MCP server') || error.message.includes('connection')) {\r\n            console.warn('[AttorneyProfileManager] MCP server not available, using mock assistant');\r\n\r\n            assistant = {\r\n              id: attorney.vapi_assistant_id,\r\n              name: `${attorney.firm_name} Assistant`,\r\n              instructions: attorney.vapi_instructions,\r\n              firstMessage: attorney.welcome_message,\r\n              mock: true\r\n            };\r\n          } else {\r\n            assistant = null;\r\n          }\r\n        }\r\n\r\n        if (!assistant) {\r\n          console.log('[AttorneyProfileManager] Assistant not found, creating new one');\r\n\r\n          // Assistant doesn't exist, create a new one\r\n          const newAssistant = await this.createVapiAssistant(attorney);\r\n\r\n          // Update attorney with new assistant ID\r\n          await this.updateAttorneyInSupabase({\r\n            id: attorney.id,\r\n            vapi_assistant_id: newAssistant.id\r\n          });\r\n\r\n          this.syncStatus = {\r\n            consistent: true,\r\n            message: 'Created replacement Vapi assistant',\r\n            lastChecked: new Date()\r\n          };\r\n        } else {\r\n          // Check for discrepancies\r\n          const discrepancies = this.findDiscrepancies(attorney, assistant);\r\n\r\n          if (Object.keys(discrepancies).length > 0) {\r\n            console.log('[AttorneyProfileManager] Found discrepancies, updating assistant:', discrepancies);\r\n\r\n            // Update the assistant\r\n            await this.updateVapiAssistant(attorney);\r\n\r\n            this.syncStatus = {\r\n              consistent: true,\r\n              message: 'Updated Vapi assistant',\r\n              lastChecked: new Date()\r\n            };\r\n          } else {\r\n            console.log('[AttorneyProfileManager] No discrepancies found, assistant is up to date');\r\n\r\n            this.syncStatus = {\r\n              consistent: true,\r\n              message: 'Vapi assistant is up to date',\r\n              lastChecked: new Date()\r\n            };\r\n          }\r\n        }\r\n      }\r\n\r\n      this.lastSyncTime = new Date();\r\n      this.isRecoveryMode = false;\r\n      this.recoveryAttempts = 0;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error checking Vapi synchronization:', error);\r\n\r\n      // If MCP server is not available, mark as consistent but note the issue\r\n      if (error.message.includes('MCP server') || error.message.includes('connection')) {\r\n        console.warn('[AttorneyProfileManager] MCP server not available, marking as consistent but noting issue');\r\n\r\n        this.syncStatus = {\r\n          consistent: true,\r\n          message: 'Vapi MCP service not available, using local data',\r\n          lastChecked: new Date(),\r\n          warning: error.message\r\n        };\r\n\r\n        this.lastSyncTime = new Date();\r\n      } else {\r\n        this.syncStatus = {\r\n          consistent: false,\r\n          message: `Sync error: ${error.message}`,\r\n          lastChecked: new Date(),\r\n          error: error.message\r\n        };\r\n\r\n        // Enter recovery mode if not already in it\r\n        if (!this.isRecoveryMode) {\r\n          this.isRecoveryMode = true;\r\n          this.recoveryAttempts = 0;\r\n          this.attemptRecovery();\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // Attempt recovery from synchronization errors\r\n  async attemptRecovery() {\r\n    if (this.recoveryAttempts >= this.MAX_RECOVERY_ATTEMPTS) {\r\n      console.warn('[AttorneyProfileManager] Max recovery attempts reached, giving up');\r\n      this.isRecoveryMode = false;\r\n      return;\r\n    }\r\n\r\n    this.recoveryAttempts++;\r\n    console.log(`[AttorneyProfileManager] Recovery attempt ${this.recoveryAttempts}/${this.MAX_RECOVERY_ATTEMPTS}`);\r\n\r\n    try {\r\n      // Try different recovery strategies based on the attempt number\r\n      switch (this.recoveryAttempts) {\r\n        case 1:\r\n          // First attempt: Try to refresh from Supabase\r\n          if (this.currentAttorney && this.currentAttorney.id) {\r\n            const refreshedAttorney = await this.loadAttorneyById(this.currentAttorney.id);\r\n            if (refreshedAttorney) {\r\n              this.currentAttorney = refreshedAttorney;\r\n              this.saveToLocalStorage(refreshedAttorney);\r\n              await this.checkVapiSynchronization(refreshedAttorney);\r\n            }\r\n          }\r\n          break;\r\n\r\n        case 2:\r\n          // Second attempt: Try to create a new Vapi assistant\r\n          if (this.currentAttorney && this.currentAttorney.id) {\r\n            const newAssistant = await this.createVapiAssistant(this.currentAttorney);\r\n            await this.updateAttorneyInSupabase({\r\n              id: this.currentAttorney.id,\r\n              vapi_assistant_id: newAssistant.id\r\n            });\r\n          } else {\r\n            console.warn('[AttorneyProfileManager] Cannot create assistant: current attorney missing or has no ID');\r\n          }\r\n          break;\r\n\r\n        case 3:\r\n          // Third attempt: Reset Vapi assistant ID and try again\r\n          if (this.currentAttorney && this.currentAttorney.id) {\r\n            await this.updateAttorneyInSupabase({\r\n              id: this.currentAttorney.id,\r\n              vapi_assistant_id: null\r\n            });\r\n\r\n            // The Realtime update will trigger a new check\r\n          } else {\r\n            console.warn('[AttorneyProfileManager] Cannot reset assistant ID: current attorney missing or has no ID');\r\n          }\r\n          break;\r\n      }\r\n    } catch (error) {\r\n      console.error(`[AttorneyProfileManager] Recovery attempt ${this.recoveryAttempts} failed:`, error);\r\n\r\n      // Schedule next recovery attempt\r\n      setTimeout(() => {\r\n        this.attemptRecovery();\r\n      }, 5000 * this.recoveryAttempts); // Increasing backoff\r\n    }\r\n  }\r\n\r\n  // Force synchronization\r\n  async forceSynchronization() {\r\n    console.log('[AttorneyProfileManager] Forcing synchronization');\r\n\r\n    try {\r\n      if (!this.currentAttorney) {\r\n        throw new Error('No attorney profile loaded');\r\n      }\r\n\r\n      if (!this.currentAttorney.id) {\r\n        throw new Error('Current attorney missing ID, cannot sync');\r\n      }\r\n\r\n      // Get latest from Supabase\r\n      let supabaseData;\r\n      try {\r\n        console.log('[AttorneyProfileManager] Fetching latest data from Supabase for attorney:', this.currentAttorney.id);\r\n\r\n        const { data, error } = await supabase\r\n          .from('attorneys')\r\n          .select('*')\r\n          .eq('id', this.currentAttorney.id)\r\n          .single();\r\n\r\n        if (error) throw error;\r\n        supabaseData = data;\r\n\r\n        console.log('[AttorneyProfileManager] Fetched Supabase data:', {\r\n          id: data.id,\r\n          firm_name: data.firm_name,\r\n          vapi_assistant_id: data.vapi_assistant_id,\r\n          voice_provider: data.voice_provider,\r\n          voice_id: data.voice_id\r\n        });\r\n\r\n        // Update local state\r\n        this.currentAttorney = data;\r\n\r\n        // Save to localStorage\r\n        this.saveToLocalStorage(data);\r\n      } catch (supabaseError) {\r\n        console.error('[AttorneyProfileManager] Error getting attorney from Supabase:', supabaseError);\r\n\r\n        // Continue with current attorney data\r\n        console.warn('[AttorneyProfileManager] Continuing with current attorney data:', {\r\n          id: this.currentAttorney.id,\r\n          firm_name: this.currentAttorney.firm_name,\r\n          vapi_assistant_id: this.currentAttorney.vapi_assistant_id\r\n        });\r\n        supabaseData = this.currentAttorney;\r\n      }\r\n\r\n      // Try to update Vapi assistant (only if assistant ID is available)\r\n      if (supabaseData.vapi_assistant_id) {\r\n        try {\r\n          await this.updateVapiAssistant(supabaseData);\r\n        } catch (vapiError) {\r\n          console.error('[AttorneyProfileManager] Error updating Vapi assistant:', vapiError);\r\n\r\n          // If MCP server is not available, continue with warning\r\n          if (vapiError.message.includes('MCP server') || vapiError.message.includes('connection')) {\r\n            console.warn('[AttorneyProfileManager] MCP server not available, continuing with local data');\r\n\r\n            this.syncStatus = {\r\n              consistent: true,\r\n              message: 'Voice service not available, using local data',\r\n              lastChecked: new Date(),\r\n              warning: vapiError.message\r\n            };\r\n          } else {\r\n            // For other errors, mark as inconsistent\r\n            this.syncStatus = {\r\n              consistent: false,\r\n              message: `Voice service sync error: ${vapiError.message}`,\r\n              lastChecked: new Date(),\r\n              error: vapiError.message\r\n            };\r\n          }\r\n        }\r\n      } else {\r\n        console.warn('[AttorneyProfileManager] No Vapi assistant ID available, skipping Vapi sync');\r\n\r\n        // Try to create a new assistant if none exists\r\n        try {\r\n          console.log('[AttorneyProfileManager] Attempting to create new Vapi assistant');\r\n          const newAssistant = await this.createVapiAssistant(supabaseData);\r\n\r\n          if (newAssistant && newAssistant.id) {\r\n            // Update Supabase with the new assistant ID\r\n            await this.updateAttorneyInSupabase({\r\n              id: supabaseData.id,\r\n              vapi_assistant_id: newAssistant.id\r\n            });\r\n\r\n            console.log('[AttorneyProfileManager] Created and saved new Vapi assistant:', newAssistant.id);\r\n\r\n            this.syncStatus = {\r\n              consistent: true,\r\n              message: 'Created new voice assistant',\r\n              lastChecked: new Date()\r\n            };\r\n          }\r\n        } catch (createError) {\r\n          console.error('[AttorneyProfileManager] Error creating new Vapi assistant:', createError);\r\n\r\n          this.syncStatus = {\r\n            consistent: false,\r\n            message: 'No voice assistant available and could not create one',\r\n            lastChecked: new Date(),\r\n            warning: 'Voice assistant will be created on next sync attempt'\r\n          };\r\n        }\r\n      }\r\n\r\n      this.lastSyncTime = new Date();\r\n\r\n      // If syncStatus hasn't been set by error handlers\r\n      if (!this.syncStatus || !this.syncStatus.lastChecked ||\r\n          this.syncStatus.lastChecked < this.lastSyncTime) {\r\n        this.syncStatus = {\r\n          consistent: true,\r\n          message: 'Forced synchronization successful',\r\n          lastChecked: new Date()\r\n        };\r\n      }\r\n\r\n      // Notify listeners\r\n      this.notifyListeners();\r\n\r\n      return { success: true, attorney: supabaseData };\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error forcing synchronization:', error);\r\n\r\n      this.syncStatus = {\r\n        consistent: false,\r\n        message: `Sync error: ${error.message}`,\r\n        lastChecked: new Date(),\r\n        error: error.message\r\n      };\r\n\r\n      // Notify listeners even on error\r\n      this.notifyListeners();\r\n\r\n      return { success: false, error: error.message };\r\n    }\r\n  }\r\n\r\n  // Create a Vapi assistant\r\n  async createVapiAssistant(attorney) {\r\n    try {\r\n      // Ensure attorney has required properties\r\n      if (!attorney) {\r\n        throw new Error('Attorney object is required');\r\n      }\r\n\r\n      // Log attorney info (use firm_name as fallback if id is missing)\r\n      const attorneyIdentifier = attorney.id || attorney.firm_name || 'unknown';\r\n      console.log('[AttorneyProfileManager] Creating Vapi assistant for attorney:', attorneyIdentifier);\r\n\r\n      // Ensure attorney has a firm name\r\n      if (!attorney.firm_name) {\r\n        attorney.firm_name = 'Your Law Firm';\r\n      }\r\n\r\n      // Check if we're in production environment\r\n      const isProduction = typeof window !== 'undefined' &&\r\n        (window.location.hostname === 'dashboard.legalscout.net' ||\r\n         window.location.hostname.endsWith('.legalscout.net'));\r\n\r\n      // Get the appropriate Vapi service\r\n      const vapiService = vapiServiceManager.getMcpService();\r\n\r\n      // Ensure connection to Vapi service\r\n      await vapiService.ensureConnection();\r\n\r\n      const assistantConfig = {\r\n        name: `${attorney.firm_name} Assistant`,\r\n        firstMessage: attorney.welcome_message || `Hello, I'm Scout from ${attorney.firm_name}. How can I help you today?`,\r\n        firstMessageMode: \"assistant-speaks-first\",\r\n        model: {\r\n          provider: \"openai\",\r\n          model: attorney.ai_model || \"gpt-4o\",\r\n          messages: [\r\n            {\r\n              role: \"system\",\r\n              content: attorney.vapi_instructions || `You are a legal assistant for ${attorney.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`\r\n            }\r\n          ]\r\n        },\r\n        voice: {\r\n          provider: attorney.voice_provider || \"11labs\",\r\n          voiceId: attorney.voice_id || \"sarah\"\r\n        },\r\n        transcriber: {\r\n          provider: \"deepgram\",\r\n          model: \"nova-3\"\r\n        }\r\n      };\r\n\r\n      // Create the assistant\r\n      const assistant = await vapiService.createAssistant(assistantConfig);\r\n\r\n      // Validate that we got a real assistant ID, not a mock one\r\n      if (assistant && assistant.id && assistant.id.startsWith('mock-')) {\r\n        if (isProduction) {\r\n          throw new Error('Mock assistant created in production environment');\r\n        }\r\n        console.warn('[AttorneyProfileManager] Mock assistant created, this should not happen in production');\r\n      }\r\n\r\n      console.log('[AttorneyProfileManager] Created Vapi assistant:', assistant.id);\r\n      return assistant;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error creating Vapi assistant:', error);\r\n\r\n      // Check if we're in production environment\r\n      const isProduction = typeof window !== 'undefined' &&\r\n        (window.location.hostname === 'dashboard.legalscout.net' ||\r\n         window.location.hostname.endsWith('.legalscout.net'));\r\n\r\n      // In production, don't fall back to mock assistants\r\n      if (isProduction) {\r\n        throw error;\r\n      }\r\n\r\n      // If MCP server is not available in development, create a mock assistant\r\n      if (error.message.includes('MCP server') || error.message.includes('connection')) {\r\n        console.warn('[AttorneyProfileManager] MCP server not available, creating mock assistant');\r\n\r\n        return {\r\n          id: 'mock-' + Date.now(),\r\n          name: `${attorney.firm_name} Assistant`,\r\n          instructions: attorney.vapi_instructions,\r\n          firstMessage: attorney.welcome_message,\r\n          mock: true\r\n        };\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Update a Vapi assistant\r\n  async updateVapiAssistant(attorney) {\r\n    try {\r\n      // Ensure attorney has required properties\r\n      if (!attorney) {\r\n        throw new Error('Attorney object is required');\r\n      }\r\n\r\n      // Log attorney info (use firm_name as fallback if id is missing)\r\n      const attorneyIdentifier = attorney.id || attorney.firm_name || 'unknown';\r\n      console.log('[AttorneyProfileManager] Updating Vapi assistant for attorney:', attorneyIdentifier);\r\n\r\n      if (!attorney.vapi_assistant_id) {\r\n        throw new Error('No Vapi assistant ID');\r\n      }\r\n\r\n      // Get the appropriate Vapi service\r\n      const vapiService = vapiServiceManager.getMcpService();\r\n\r\n      // Check if Vapi service is connected\r\n      const connected = await vapiService.ensureConnection();\r\n      if (!connected) {\r\n        console.warn('[AttorneyProfileManager] Vapi service not connected, returning mock updated assistant');\r\n\r\n        return {\r\n          id: attorney.vapi_assistant_id,\r\n          name: `${attorney.firm_name} Assistant`,\r\n          instructions: attorney.vapi_instructions,\r\n          firstMessage: attorney.welcome_message,\r\n          mock: true\r\n        };\r\n      }\r\n\r\n      // Get current assistant\r\n      let assistant;\r\n      try {\r\n        assistant = await vapiService.getAssistant(attorney.vapi_assistant_id);\r\n      } catch (assistantError) {\r\n        console.error('[AttorneyProfileManager] Error getting assistant:', assistantError);\r\n\r\n        // If MCP server error, return a mock assistant\r\n        if (assistantError.message.includes('MCP server') || assistantError.message.includes('connection')) {\r\n          console.warn('[AttorneyProfileManager] MCP server error getting assistant, returning mock assistant');\r\n\r\n          return {\r\n            id: attorney.vapi_assistant_id,\r\n            name: `${attorney.firm_name} Assistant`,\r\n            instructions: attorney.vapi_instructions,\r\n            firstMessage: attorney.welcome_message,\r\n            mock: true\r\n          };\r\n        }\r\n\r\n        // If assistant not found, create a new one\r\n        if (assistantError.message.includes('not found')) {\r\n          console.warn('[AttorneyProfileManager] Assistant not found, creating a new one');\r\n          return await this.createVapiAssistant(attorney);\r\n        }\r\n\r\n        throw assistantError;\r\n      }\r\n\r\n      if (!assistant) {\r\n        console.warn('[AttorneyProfileManager] Assistant not found, creating a new one');\r\n        return await this.createVapiAssistant(attorney);\r\n      }\r\n\r\n      // Validate and fix voice configuration\r\n      let voiceProvider = attorney.voice_provider || (assistant.voice ? assistant.voice.provider : \"11labs\");\r\n      let voiceId = attorney.voice_id || (assistant.voice ? assistant.voice.voiceId : \"sarah\");\r\n\r\n      // Fix common voice/provider mismatches\r\n      if (voiceProvider === \"playht\" && voiceId === \"sarah\") {\r\n        console.warn('[AttorneyProfileManager] Fixing voice mismatch: sarah is not available for playht, switching to 11labs');\r\n        voiceProvider = \"11labs\";\r\n      }\r\n\r\n      // Create update configuration\r\n      const updateConfig = {\r\n        name: `${attorney.firm_name} Assistant`,\r\n        firstMessage: attorney.welcome_message || `Hello, I'm Scout from ${attorney.firm_name}. How can I help you today?`,\r\n        firstMessageMode: \"assistant-speaks-first\",\r\n        instructions: attorney.vapi_instructions || `You are a legal assistant for ${attorney.firm_name}. Help potential clients understand their legal needs and collect relevant information for consultation.`,\r\n        llm: {\r\n          ...(assistant.llm || assistant.model || { provider: \"openai\", model: \"gpt-4o\" }),\r\n          model: attorney.ai_model || (assistant.llm ? assistant.llm.model : (assistant.model ? assistant.model.model : \"gpt-4o\"))\r\n        },\r\n        voice: {\r\n          ...(assistant.voice || { provider: \"11labs\", voiceId: \"sarah\" }),\r\n          provider: voiceProvider,\r\n          voiceId: voiceId\r\n        }\r\n      };\r\n\r\n      console.log('[AttorneyProfileManager] Sending update config to Vapi:', JSON.stringify(updateConfig, null, 2));\r\n\r\n      // Update the assistant\r\n      const updatedAssistant = await vapiService.updateAssistant(attorney.vapi_assistant_id, updateConfig);\r\n\r\n      console.log('[AttorneyProfileManager] Updated Vapi assistant:', updatedAssistant.id);\r\n      return updatedAssistant;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error updating Vapi assistant:', error);\r\n\r\n      // Log additional details for 400 errors\r\n      if (error.message.includes('400')) {\r\n        console.error('[AttorneyProfileManager] 400 Error Details:');\r\n        console.error('- Assistant ID:', attorney.vapi_assistant_id);\r\n        console.error('- Attorney Data:', JSON.stringify({\r\n          firm_name: attorney.firm_name,\r\n          welcome_message: attorney.welcome_message,\r\n          vapi_instructions: attorney.vapi_instructions,\r\n          voice_id: attorney.voice_id,\r\n          voice_provider: attorney.voice_provider,\r\n          ai_model: attorney.ai_model\r\n        }, null, 2));\r\n      }\r\n\r\n      // If MCP server is not available, return a mock updated assistant\r\n      if (error.message.includes('MCP server') || error.message.includes('connection')) {\r\n        console.warn('[AttorneyProfileManager] MCP server not available, returning mock updated assistant');\r\n\r\n        return {\r\n          id: attorney.vapi_assistant_id,\r\n          name: `${attorney.firm_name} Assistant`,\r\n          instructions: attorney.vapi_instructions,\r\n          firstMessage: attorney.welcome_message,\r\n          mock: true\r\n        };\r\n      }\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Update attorney in Supabase\r\n  async updateAttorneyInSupabase(data) {\r\n    try {\r\n      // Validate input data\r\n      if (!data) {\r\n        throw new Error('No data provided for attorney update');\r\n      }\r\n\r\n      if (!data.id) {\r\n        console.error('[AttorneyProfileManager] No ID provided in update data:', data);\r\n        throw new Error('Attorney ID is required for Supabase update');\r\n      }\r\n\r\n      console.log('[AttorneyProfileManager] Updating attorney in Supabase:', data.id);\r\n\r\n      // Check if the ID is a development ID\r\n      if (data.id && data.id.startsWith('dev-')) {\r\n        console.log('[AttorneyProfileManager] Development ID detected, updating locally only');\r\n\r\n        // For development IDs, just update locally without trying Supabase\r\n        const updateData = {\r\n          ...this.currentAttorney,\r\n          ...data,\r\n          updated_at: new Date().toISOString()\r\n        };\r\n\r\n        // Update local state\r\n        this.currentAttorney = updateData;\r\n\r\n        // Save to localStorage\r\n        this.saveToLocalStorage(updateData);\r\n\r\n        // Notify listeners\r\n        this.notifyListeners();\r\n\r\n        return updateData;\r\n      }\r\n\r\n      // Add updated_at timestamp\r\n      const updateData = {\r\n        ...data,\r\n        updated_at: new Date().toISOString()\r\n      };\r\n\r\n      // Add to pending updates\r\n      const pendingUpdateId = data.id + '_' + updateData.updated_at;\r\n      this.pendingUpdates.set(pendingUpdateId, updateData);\r\n\r\n      const { data: updatedData, error } = await supabase\r\n        .from('attorneys')\r\n        .update(updateData)\r\n        .eq('id', data.id)\r\n        .select()\r\n        .single();\r\n\r\n      if (error) throw error;\r\n\r\n      console.log('[AttorneyProfileManager] Updated attorney in Supabase successfully');\r\n      return updatedData;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error updating attorney in Supabase:', error);\r\n\r\n      // Remove from pending updates\r\n      const pendingUpdateId = data.id + '_' + (data.updated_at || new Date().toISOString());\r\n      this.pendingUpdates.delete(pendingUpdateId);\r\n\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // Save attorney to localStorage\r\n  saveToLocalStorage(attorney) {\r\n    if (!attorney) {\r\n      console.warn('[AttorneyProfileManager] Cannot save undefined attorney to localStorage');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      localStorage.setItem('attorney', JSON.stringify(attorney));\r\n      localStorage.setItem('attorney_last_updated', new Date().toISOString());\r\n      console.log('[AttorneyProfileManager] Saved attorney to localStorage:', attorney.id || 'unknown');\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error saving attorney to localStorage:', error);\r\n    }\r\n  }\r\n\r\n  // Load attorney from localStorage\r\n  loadFromLocalStorage() {\r\n    try {\r\n      const attorney = localStorage.getItem('attorney');\r\n      if (!attorney) return null;\r\n\r\n      const parsed = JSON.parse(attorney);\r\n      console.log('[AttorneyProfileManager] Loaded attorney from localStorage:', parsed.id);\r\n      return parsed;\r\n    } catch (error) {\r\n      console.error('[AttorneyProfileManager] Error loading attorney from localStorage:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Find discrepancies between attorney and assistant\r\n  findDiscrepancies(attorney, assistant) {\r\n    const discrepancies = {};\r\n\r\n    if (assistant.name !== `${attorney.firm_name} Assistant`) {\r\n      discrepancies.name = {\r\n        current: assistant.name,\r\n        expected: `${attorney.firm_name} Assistant`\r\n      };\r\n    }\r\n\r\n    if (attorney.vapi_instructions && assistant.instructions !== attorney.vapi_instructions) {\r\n      discrepancies.instructions = {\r\n        current: assistant.instructions,\r\n        expected: attorney.vapi_instructions\r\n      };\r\n    }\r\n\r\n    if (attorney.welcome_message && assistant.firstMessage !== attorney.welcome_message) {\r\n      discrepancies.firstMessage = {\r\n        current: assistant.firstMessage,\r\n        expected: attorney.welcome_message\r\n      };\r\n    }\r\n\r\n    if (attorney.voice_id && assistant.voice && assistant.voice.voiceId !== attorney.voice_id) {\r\n      discrepancies.voiceId = {\r\n        current: assistant.voice ? assistant.voice.voiceId : null,\r\n        expected: attorney.voice_id\r\n      };\r\n    }\r\n\r\n    return discrepancies;\r\n  }\r\n\r\n  // Add a listener for attorney updates\r\n  addListener(listener) {\r\n    this.listeners.add(listener);\r\n\r\n    // Immediately notify with current state\r\n    if (this.currentAttorney) {\r\n      try {\r\n        listener(this.currentAttorney);\r\n      } catch (error) {\r\n        console.error('[AttorneyProfileManager] Error in initial listener notification:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Remove a listener\r\n  removeListener(listener) {\r\n    this.listeners.delete(listener);\r\n  }\r\n\r\n  // Notify all listeners of changes\r\n  notifyListeners() {\r\n    for (const listener of this.listeners) {\r\n      try {\r\n        listener(this.currentAttorney);\r\n      } catch (error) {\r\n        console.error('[AttorneyProfileManager] Error in attorney update listener:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  // Clean up resources\r\n  cleanup() {\r\n    // Clean up v1 API subscription\r\n    if (this.subscription) {\r\n      try {\r\n        supabase.removeSubscription(this.subscription);\r\n      } catch (error) {\r\n        console.warn('[AttorneyProfileManager] Error removing subscription:', error);\r\n      }\r\n      this.subscription = null;\r\n    }\r\n\r\n    // Clean up v2 API channel\r\n    if (this.channel) {\r\n      try {\r\n        supabase.removeChannel(this.channel);\r\n      } catch (error) {\r\n        console.warn('[AttorneyProfileManager] Error removing channel:', error);\r\n      }\r\n      this.channel = null;\r\n    }\r\n\r\n    // Clean up polling interval\r\n    if (this.pollingInterval) {\r\n      clearInterval(this.pollingInterval);\r\n      this.pollingInterval = null;\r\n    }\r\n\r\n    this.listeners.clear();\r\n    console.log('[AttorneyProfileManager] Resources cleaned up');\r\n  }\r\n}\r\n\r\n// Export a singleton instance\r\nexport const attorneyProfileManager = new AttorneyProfileManager();\r\n\r\n// Make it available globally for debugging and integration\r\nif (typeof window !== 'undefined') {\r\n  window.attorneyProfileManager = attorneyProfileManager;\r\n}\r\n\r\nexport default attorneyProfileManager;\r\n", "/**\r\n * useAttorneyProfile Hook\r\n * \r\n * A React hook for managing attorney profiles across systems:\r\n * - Supabase (database)\r\n * - <PERSON><PERSON><PERSON> (voice assistant)\r\n * - localStorage (client-side cache)\r\n * \r\n * This hook provides:\r\n * - Loading attorney profiles\r\n * - Updating attorney profiles\r\n * - Synchronizing with Vapi\r\n * - Error handling and recovery\r\n */\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { attorneyProfileManager } from '../services/AttorneyProfileManager';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\n/**\r\n * Hook for managing attorney profiles\r\n * @returns {Object} Attorney profile state and methods\r\n */\r\nexport const useAttorneyProfile = () => {\r\n  // Get authentication state\r\n  const { user, isAuthenticated } = useAuth();\r\n  \r\n  // State\r\n  const [attorney, setAttorney] = useState(attorneyProfileManager.currentAttorney);\r\n  const [loading, setLoading] = useState(!attorney);\r\n  const [error, setError] = useState(null);\r\n  const [syncStatus, setSyncStatus] = useState(attorneyProfileManager.syncStatus);\r\n  const [lastSyncTime, setLastSyncTime] = useState(attorneyProfileManager.lastSyncTime);\r\n\r\n  // Initialize on mount if user is authenticated\r\n  useEffect(() => {\r\n    if (isAuthenticated && user?.id) {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      attorneyProfileManager.initialize(user.id, user.email)\r\n        .then(result => {\r\n          // The listener will handle setting the attorney\r\n          setLoading(false);\r\n        })\r\n        .catch(err => {\r\n          console.error('[useAttorneyProfile] Error initializing attorney profile:', err);\r\n          setError(err.message);\r\n          setLoading(false);\r\n        });\r\n    }\r\n  }, [isAuthenticated, user?.id, user?.email]);\r\n\r\n  // Listen for attorney updates\r\n  useEffect(() => {\r\n    const handleUpdate = (updatedAttorney) => {\r\n      setAttorney(updatedAttorney);\r\n      setSyncStatus(attorneyProfileManager.syncStatus);\r\n      setLastSyncTime(attorneyProfileManager.lastSyncTime);\r\n    };\r\n\r\n    attorneyProfileManager.addListener(handleUpdate);\r\n\r\n    return () => {\r\n      attorneyProfileManager.removeListener(handleUpdate);\r\n    };\r\n  }, []);\r\n\r\n  // Force synchronization\r\n  const forceSync = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      await attorneyProfileManager.forceSynchronization();\r\n      setSyncStatus(attorneyProfileManager.syncStatus);\r\n      setLastSyncTime(attorneyProfileManager.lastSyncTime);\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('[useAttorneyProfile] Error forcing synchronization:', error);\r\n      setError(error.message);\r\n      return { success: false, error: error.message };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  // Check synchronization status\r\n  const checkSyncStatus = useCallback(async () => {\r\n    try {\r\n      if (!attorney) {\r\n        throw new Error('No attorney profile loaded');\r\n      }\r\n\r\n      await attorneyProfileManager.checkVapiSynchronization(attorney);\r\n      setSyncStatus(attorneyProfileManager.syncStatus);\r\n      setLastSyncTime(attorneyProfileManager.lastSyncTime);\r\n      return { success: true };\r\n    } catch (error) {\r\n      console.error('[useAttorneyProfile] Error checking sync status:', error);\r\n      setError(error.message);\r\n      return { success: false, error: error.message };\r\n    }\r\n  }, [attorney]);\r\n\r\n  // Update attorney profile\r\n  const updateAttorney = useCallback(async (data) => {\r\n    try {\r\n      if (!attorney) {\r\n        throw new Error('No attorney profile loaded');\r\n      }\r\n\r\n      setLoading(true);\r\n      \r\n      // Update in Supabase\r\n      const updatedAttorney = await attorneyProfileManager.updateAttorneyInSupabase({\r\n        ...data,\r\n        id: attorney.id\r\n      });\r\n      \r\n      // The Realtime subscription will handle updating the local state\r\n      \r\n      return { success: true, attorney: updatedAttorney };\r\n    } catch (error) {\r\n      console.error('[useAttorneyProfile] Error updating attorney:', error);\r\n      setError(error.message);\r\n      return { success: false, error: error.message };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [attorney]);\r\n\r\n  return {\r\n    attorney,\r\n    loading,\r\n    error,\r\n    syncStatus,\r\n    lastSyncTime,\r\n    forceSync,\r\n    checkSyncStatus,\r\n    updateAttorney\r\n  };\r\n};\r\n\r\nexport default useAttorneyProfile;\r\n", "/**\r\n * Attorney Profile Test Component\r\n *\r\n * This component is used to test the AttorneyProfileManager and useAttorneyProfile hook.\r\n * It displays the current attorney profile and provides buttons to test the synchronization.\r\n */\r\n\r\nimport React, { useState } from 'react';\r\nimport { useAttorneyProfile } from '../hooks/useAttorneyProfile';\r\nimport { attorneyProfileManager } from '../services/AttorneyProfileManager';\r\nimport { vapiServiceManager } from '../services/vapiServiceManager';\r\n\r\nconst AttorneyProfileTest = () => {\r\n  const {\r\n    attorney,\r\n    loading,\r\n    error,\r\n    syncStatus,\r\n    lastSyncTime,\r\n    forceSync,\r\n    checkSyncStatus,\r\n    updateAttorney\r\n  } = useAttorneyProfile();\r\n\r\n  const [testResults, setTestResults] = useState([]);\r\n\r\n  // Add a test result\r\n  const addTestResult = (name, success, message) => {\r\n    setTestResults(prev => [\r\n      {\r\n        name,\r\n        success,\r\n        message,\r\n        timestamp: new Date().toISOString()\r\n      },\r\n      ...prev\r\n    ]);\r\n  };\r\n\r\n  // Test Vapi MCP connection\r\n  const testVapiMcpConnection = async () => {\r\n    try {\r\n      const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||\r\n                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||\r\n                    localStorage.getItem('vapi_api_key');\r\n\r\n      if (!apiKey) {\r\n        addTestResult('Vapi MCP Connection', false, 'No API key found');\r\n        return;\r\n      }\r\n\r\n      // Initialize the service manager with MCP mode\r\n      await vapiServiceManager.initialize(apiKey);\r\n\r\n      // Get the MCP service\r\n      const mcpService = vapiServiceManager.getMcpService();\r\n\r\n      // Check if using mock\r\n      if (vapiServiceManager.isUsingMock()) {\r\n        addTestResult('Vapi MCP Connection', true, 'Connected successfully (using mock service)');\r\n        return;\r\n      }\r\n\r\n      // Check if connected\r\n      if (mcpService.connected && !mcpService.useDirect) {\r\n        addTestResult('Vapi MCP Connection', true, 'Connected successfully');\r\n      } else {\r\n        addTestResult('Vapi MCP Connection', false, 'Failed to connect');\r\n      }\r\n    } catch (error) {\r\n      addTestResult('Vapi MCP Connection', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Test Vapi Direct API connection\r\n  const testVapiDirectConnection = async () => {\r\n    try {\r\n      const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||\r\n                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||\r\n                    localStorage.getItem('vapi_api_key');\r\n\r\n      if (!apiKey) {\r\n        addTestResult('Vapi Direct API', false, 'No API key found');\r\n        return;\r\n      }\r\n\r\n      // Get the MCP service\r\n      const mcpService = vapiServiceManager.getMcpService();\r\n\r\n      // Check if using mock\r\n      if (vapiServiceManager.isUsingMock()) {\r\n        addTestResult('Vapi Direct API', true, 'Connected successfully (using mock service)');\r\n        return;\r\n      }\r\n\r\n      // Force direct API connection\r\n      const connected = await mcpService.connect(apiKey, true);\r\n\r\n      if (connected && mcpService.useDirect) {\r\n        addTestResult('Vapi Direct API', true, 'Connected successfully using direct API');\r\n      } else {\r\n        addTestResult('Vapi Direct API', false, 'Failed to connect using direct API');\r\n      }\r\n    } catch (error) {\r\n      addTestResult('Vapi Direct API', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Test Vapi Mock Mode\r\n  const testVapiMockMode = async () => {\r\n    try {\r\n      const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||\r\n                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||\r\n                    localStorage.getItem('vapi_api_key');\r\n\r\n      if (!apiKey) {\r\n        addTestResult('Vapi Mock Mode', false, 'No API key found');\r\n        return;\r\n      }\r\n\r\n      // Initialize with forced mock mode\r\n      await vapiServiceManager.initialize(apiKey, true);\r\n\r\n      // Check if using mock\r\n      if (vapiServiceManager.isUsingMock()) {\r\n        addTestResult('Vapi Mock Mode', true, 'Mock mode enabled successfully');\r\n\r\n        // List assistants to test mock functionality\r\n        const assistants = await vapiServiceManager.getMcpService().listAssistants();\r\n        console.log('Mock assistants:', assistants);\r\n\r\n        // Create a test assistant\r\n        const testAssistant = await vapiServiceManager.getMcpService().createAssistant({\r\n          name: 'Test Mock Assistant',\r\n          instructions: 'This is a test assistant created in mock mode',\r\n          firstMessage: 'Hello from mock mode!'\r\n        });\r\n\r\n        console.log('Created mock assistant:', testAssistant);\r\n      } else {\r\n        addTestResult('Vapi Mock Mode', false, 'Failed to enable mock mode');\r\n      }\r\n    } catch (error) {\r\n      addTestResult('Vapi Mock Mode', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Test Attorney Dashboard Mode\r\n  const testAttorneyDashboardMode = async () => {\r\n    try {\r\n      const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||\r\n                    (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||\r\n                    localStorage.getItem('vapi_api_key');\r\n\r\n      if (!apiKey) {\r\n        addTestResult('Attorney Dashboard Mode', false, 'No API key found');\r\n        return;\r\n      }\r\n\r\n      // Initialize with attorney dashboard mode\r\n      await vapiServiceManager.initialize(apiKey, false, {\r\n        isAttorneyDashboard: true,\r\n        isPreview: false\r\n      });\r\n\r\n      // Check connection status\r\n      const status = vapiServiceManager.getConnectionStatus();\r\n      const warning = vapiServiceManager.getConnectionWarning();\r\n\r\n      if (warning) {\r\n        addTestResult('Attorney Dashboard Mode', false, `Warning: ${warning}`);\r\n      } else if (vapiServiceManager.isUsingMock()) {\r\n        addTestResult('Attorney Dashboard Mode', true, 'Connected in mock mode (fallback)');\r\n      } else {\r\n        addTestResult('Attorney Dashboard Mode', true, `Connected using ${status.connectionMode} mode`);\r\n      }\r\n\r\n      // Try to list assistants\r\n      try {\r\n        const assistants = await vapiServiceManager.getMcpService().listAssistants();\r\n        console.log('Assistants in attorney dashboard mode:', assistants);\r\n\r\n        if (assistants && assistants.length > 0) {\r\n          addTestResult('List Assistants (Dashboard)', true,\r\n            `Found ${assistants.length} assistants${vapiServiceManager.isUsingMock() ? ' (mock)' : ''}`);\r\n        } else {\r\n          addTestResult('List Assistants (Dashboard)', true, 'No assistants found');\r\n        }\r\n      } catch (listError) {\r\n        addTestResult('List Assistants (Dashboard)', false, `Error: ${listError.message}`);\r\n      }\r\n    } catch (error) {\r\n      addTestResult('Attorney Dashboard Mode', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Test listing assistants\r\n  const testListAssistants = async () => {\r\n    try {\r\n      // Get the MCP service\r\n      const vapiService = vapiServiceManager.getMcpService();\r\n\r\n      const assistants = await vapiService.listAssistants();\r\n\r\n      if (assistants && assistants.length > 0) {\r\n        addTestResult('List Assistants', true, `Found ${assistants.length} assistants${vapiServiceManager.isUsingMock() ? ' (mock)' : ''}`);\r\n      } else {\r\n        addTestResult('List Assistants', true, 'No assistants found');\r\n      }\r\n    } catch (error) {\r\n      addTestResult('List Assistants', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Test force sync\r\n  const testForceSync = async () => {\r\n    try {\r\n      const result = await forceSync();\r\n\r\n      if (result.success) {\r\n        addTestResult('Force Sync', true, 'Sync successful');\r\n      } else {\r\n        addTestResult('Force Sync', false, `Sync failed: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      addTestResult('Force Sync', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Test check sync status\r\n  const testCheckSyncStatus = async () => {\r\n    try {\r\n      const result = await checkSyncStatus();\r\n\r\n      if (result.success) {\r\n        addTestResult('Check Sync Status', true, 'Check successful');\r\n      } else {\r\n        addTestResult('Check Sync Status', false, `Check failed: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      addTestResult('Check Sync Status', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Test update attorney\r\n  const testUpdateAttorney = async () => {\r\n    try {\r\n      if (!attorney) {\r\n        addTestResult('Update Attorney', false, 'No attorney to update');\r\n        return;\r\n      }\r\n\r\n      // Update a non-critical field\r\n      const result = await updateAttorney({\r\n        welcome_message: `Hello! I'm Scout from ${attorney.firm_name}. How can I help you today? (Updated: ${new Date().toISOString()})`\r\n      });\r\n\r\n      if (result.success) {\r\n        addTestResult('Update Attorney', true, 'Update successful');\r\n      } else {\r\n        addTestResult('Update Attorney', false, `Update failed: ${result.error}`);\r\n      }\r\n    } catch (error) {\r\n      addTestResult('Update Attorney', false, `Error: ${error.message}`);\r\n    }\r\n  };\r\n\r\n  // Format a date as a relative time string\r\n  const formatRelativeTime = (date) => {\r\n    if (!date) return 'Never';\r\n\r\n    const now = new Date();\r\n    const diffMs = now - date;\r\n    const diffSec = Math.floor(diffMs / 1000);\r\n    const diffMin = Math.floor(diffSec / 60);\r\n    const diffHour = Math.floor(diffMin / 60);\r\n\r\n    if (diffSec < 60) {\r\n      return 'Just now';\r\n    } else if (diffMin < 60) {\r\n      return `${diffMin} minute${diffMin === 1 ? '' : 's'} ago`;\r\n    } else if (diffHour < 24) {\r\n      return `${diffHour} hour${diffHour === 1 ? '' : 's'} ago`;\r\n    } else {\r\n      return date.toLocaleString();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"attorney-profile-test\">\r\n      <h2>Attorney Profile Test</h2>\r\n\r\n      <div className=\"test-section\">\r\n        <h3>Current State</h3>\r\n        <div className=\"test-info\">\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Loading:</span>\r\n            <span className=\"test-info-value\">{loading ? 'Yes' : 'No'}</span>\r\n          </div>\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Error:</span>\r\n            <span className=\"test-info-value\">{error || 'None'}</span>\r\n          </div>\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Sync Status:</span>\r\n            <span className={`test-info-value ${syncStatus?.consistent ? 'status-ok' : 'status-error'}`}>\r\n              {syncStatus?.consistent ? 'Synchronized' : 'Out of Sync'}\r\n            </span>\r\n          </div>\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Last Sync:</span>\r\n            <span className=\"test-info-value\">\r\n              {lastSyncTime ? formatRelativeTime(lastSyncTime) : 'Never'}\r\n            </span>\r\n          </div>\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Sync Message:</span>\r\n            <span className=\"test-info-value\">{syncStatus?.message || 'None'}</span>\r\n          </div>\r\n          {syncStatus?.warning && (\r\n            <div className=\"test-info-item\">\r\n              <span className=\"test-info-label\">Sync Warning:</span>\r\n              <span className=\"test-info-value warning\">{syncStatus.warning}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"test-section\">\r\n        <h3>Vapi Connection Status</h3>\r\n        <div className=\"test-info\">\r\n          {vapiServiceManager.isUsingMock() ? (\r\n            <div className=\"test-info-item\">\r\n              <span className=\"test-info-label\">Mode:</span>\r\n              <span className=\"test-info-value status-ok\">Mock Mode (Simulated Vapi)</span>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div className=\"test-info-item\">\r\n                <span className=\"test-info-label\">Connected:</span>\r\n                <span className={`test-info-value ${vapiServiceManager.getMcpService().connected ? 'status-ok' : 'status-error'}`}>\r\n                  {vapiServiceManager.getMcpService().connected ? 'Yes' : 'No'}\r\n                </span>\r\n              </div>\r\n              <div className=\"test-info-item\">\r\n                <span className=\"test-info-label\">Connection Mode:</span>\r\n                <span className=\"test-info-value\">\r\n                  {vapiServiceManager.getMcpService().useDirect ? 'Direct API' :\r\n                   vapiServiceManager.getMcpService().connected ? 'MCP Server' : 'Not Connected'}\r\n                </span>\r\n              </div>\r\n            </>\r\n          )}\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">API Key Available:</span>\r\n            <span className={`test-info-value ${vapiServiceManager.getMcpService().apiKey ? 'status-ok' : 'status-error'}`}>\r\n              {vapiServiceManager.getMcpService().apiKey ? 'Yes' : 'No'}\r\n            </span>\r\n          </div>\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Connection Attempts:</span>\r\n            <span className=\"test-info-value\">{vapiServiceManager.getMcpService().connectionAttempts || 0}</span>\r\n          </div>\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Last Connection Time:</span>\r\n            <span className=\"test-info-value\">\r\n              {vapiServiceManager.getMcpService().lastConnectionTime ?\r\n               formatRelativeTime(new Date(vapiServiceManager.getMcpService().lastConnectionTime)) : 'Never'}\r\n            </span>\r\n          </div>\r\n          {vapiServiceManager.getConnectionWarning() && (\r\n            <div className=\"test-info-item\">\r\n              <span className=\"test-info-label\">Warning:</span>\r\n              <span className=\"test-info-value warning\">{vapiServiceManager.getConnectionWarning()}</span>\r\n            </div>\r\n          )}\r\n          <div className=\"test-info-item\">\r\n            <span className=\"test-info-label\">Context:</span>\r\n            <span className=\"test-info-value\">\r\n              {vapiServiceManager.isAttorneyDashboard ? 'Attorney Dashboard' :\r\n               vapiServiceManager.isPreview ? 'Preview Mode' : 'Regular Mode'}\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"test-section\">\r\n        <h3>Attorney Profile</h3>\r\n        {attorney ? (\r\n          <div className=\"attorney-info\">\r\n            <div className=\"attorney-info-item\">\r\n              <span className=\"attorney-info-label\">ID:</span>\r\n              <span className=\"attorney-info-value\">{attorney.id}</span>\r\n            </div>\r\n            <div className=\"attorney-info-item\">\r\n              <span className=\"attorney-info-label\">Firm Name:</span>\r\n              <span className=\"attorney-info-value\">{attorney.firm_name}</span>\r\n            </div>\r\n            <div className=\"attorney-info-item\">\r\n              <span className=\"attorney-info-label\">Vapi Assistant ID:</span>\r\n              <span className=\"attorney-info-value\">{attorney.vapi_assistant_id || 'None'}</span>\r\n            </div>\r\n            <div className=\"attorney-info-item\">\r\n              <span className=\"attorney-info-label\">Welcome Message:</span>\r\n              <span className=\"attorney-info-value\">{attorney.welcome_message || 'None'}</span>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"no-attorney\">No attorney profile loaded</div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"test-section\">\r\n        <h3>Test Actions</h3>\r\n        <div className=\"test-buttons\">\r\n          <button onClick={testVapiMcpConnection} disabled={loading}>\r\n            Test Vapi MCP Connection\r\n          </button>\r\n          <button onClick={testVapiDirectConnection} disabled={loading}>\r\n            Test Vapi Direct API\r\n          </button>\r\n          <button onClick={testVapiMockMode} disabled={loading}>\r\n            Test Vapi Mock Mode\r\n          </button>\r\n          <button onClick={testAttorneyDashboardMode} disabled={loading}>\r\n            Test Attorney Dashboard Mode\r\n          </button>\r\n          <button onClick={testListAssistants} disabled={loading}>\r\n            Test List Assistants\r\n          </button>\r\n          <button onClick={testForceSync} disabled={loading}>\r\n            Test Force Sync\r\n          </button>\r\n          <button onClick={testCheckSyncStatus} disabled={loading}>\r\n            Test Check Sync Status\r\n          </button>\r\n          <button onClick={testUpdateAttorney} disabled={loading}>\r\n            Test Update Attorney\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"test-section\">\r\n        <h3>Test Results</h3>\r\n        {testResults.length > 0 ? (\r\n          <div className=\"test-results\">\r\n            {testResults.map((result, index) => (\r\n              <div key={index} className={`test-result ${result.success ? 'success' : 'failure'}`}>\r\n                <div className=\"test-result-header\">\r\n                  <span className=\"test-result-name\">{result.name}</span>\r\n                  <span className=\"test-result-status\">{result.success ? 'Success' : 'Failure'}</span>\r\n                </div>\r\n                <div className=\"test-result-message\">{result.message}</div>\r\n                <div className=\"test-result-timestamp\">{new Date(result.timestamp).toLocaleTimeString()}</div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"no-results\">No test results yet</div>\r\n        )}\r\n      </div>\r\n\r\n      <style jsx>{`\r\n        .attorney-profile-test {\r\n          padding: 20px;\r\n          max-width: 800px;\r\n          margin: 0 auto;\r\n        }\r\n\r\n        .test-section {\r\n          margin-bottom: 20px;\r\n          padding: 15px;\r\n          border: 1px solid #ddd;\r\n          border-radius: 5px;\r\n          background-color: #f9f9f9;\r\n        }\r\n\r\n        .test-section h3 {\r\n          margin-top: 0;\r\n          margin-bottom: 15px;\r\n          border-bottom: 1px solid #eee;\r\n          padding-bottom: 10px;\r\n        }\r\n\r\n        .test-info-item, .attorney-info-item {\r\n          margin-bottom: 8px;\r\n          display: flex;\r\n        }\r\n\r\n        .test-info-label, .attorney-info-label {\r\n          font-weight: bold;\r\n          width: 150px;\r\n          flex-shrink: 0;\r\n        }\r\n\r\n        .status-ok {\r\n          color: green;\r\n        }\r\n\r\n        .status-error {\r\n          color: red;\r\n        }\r\n\r\n        .warning {\r\n          color: orange;\r\n        }\r\n\r\n        .test-buttons {\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          gap: 10px;\r\n        }\r\n\r\n        .test-buttons button {\r\n          padding: 8px 16px;\r\n          background-color: #4B74AA;\r\n          color: white;\r\n          border: none;\r\n          border-radius: 4px;\r\n          cursor: pointer;\r\n        }\r\n\r\n        .test-buttons button:hover {\r\n          background-color: #3A5A8C;\r\n        }\r\n\r\n        .test-buttons button:disabled {\r\n          background-color: #ccc;\r\n          cursor: not-allowed;\r\n        }\r\n\r\n        .test-results {\r\n          display: flex;\r\n          flex-direction: column;\r\n          gap: 10px;\r\n        }\r\n\r\n        .test-result {\r\n          padding: 10px;\r\n          border-radius: 4px;\r\n        }\r\n\r\n        .test-result.success {\r\n          background-color: #d4edda;\r\n          border: 1px solid #c3e6cb;\r\n        }\r\n\r\n        .test-result.failure {\r\n          background-color: #f8d7da;\r\n          border: 1px solid #f5c6cb;\r\n        }\r\n\r\n        .test-result-header {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 5px;\r\n        }\r\n\r\n        .test-result-name {\r\n          font-weight: bold;\r\n        }\r\n\r\n        .test-result-status {\r\n          font-weight: bold;\r\n        }\r\n\r\n        .test-result-message {\r\n          margin-bottom: 5px;\r\n        }\r\n\r\n        .test-result-timestamp {\r\n          font-size: 0.8em;\r\n          color: #666;\r\n        }\r\n\r\n        .no-attorney, .no-results {\r\n          font-style: italic;\r\n          color: #666;\r\n        }\r\n      `}</style>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AttorneyProfileTest;\r\n", "import React, { useState, useEffect, Suspense, useRef, lazy } from 'react'\r\nimport { storeImage, processImageUrl } from './utils/imageStorage'\r\nimport { Routes, Route, Navigate, useLocation, Link } from 'react-router-dom'\r\nimport { ToastContainer, toast } from 'react-toastify'\r\nimport 'react-toastify/dist/ReactToastify.css'\r\nimport './App.css'\r\nimport VapiCall from './components/VapiCall.jsx'\r\nimport Button from './components/Button.jsx'\r\nimport CallTransition from './components/CallTransition.jsx'\r\nimport { withDevTools, createDebugger, trackUserJourney } from './utils/debugConfig'\r\nimport { getAttorneyConfigAsync } from './config/attorneys'\r\nimport { DEFAULT_ASSISTANT_ID } from './constants/vapiConstants'\r\nimport MapView from './components/MapView.jsx'\r\nimport { useAuth } from './contexts/AuthContext'\r\n\r\nimport AttorneyDossier from './components/AttorneyDossier.jsx'\r\nimport CallSummary from './components/CallSummary.jsx'\r\nimport { getCurrentSubdomain } from './utils/subdomainTester'\r\nimport { isAttorneySubdomain as checkIsAttorneySubdomain } from './utils/subdomainExtraction'\r\nimport Navbar from './components/Navbar.jsx'\r\nimport Dashboard from './pages/DashboardNew.jsx'\r\nimport GlobeDossierView from './components/GlobeDossierView.jsx'\r\nimport TestSubdomains from './components/TestSubdomains.jsx'\r\nimport AnimatedBackground from './components/AnimatedBackground.jsx'\r\nimport ThemeToggle from './components/ThemeToggle.jsx'\r\nimport SignInButton from './components/SignInButton.jsx'\r\nimport AboutPage from './pages/AboutPage'\r\nimport SimplifiedPreview from \"./components/SimplifiedPreview\";\r\nimport EnhancedPreviewNew from \"./components/preview/EnhancedPreviewNew\";\r\nimport PreviewFrameLoader from \"./components/preview/PreviewFrameLoader\";\r\nimport SimplePreviewPage from \"./pages/SimplePreviewPage\";\r\nimport MobileActivateAssistant from \"./components/mobile/MobileActivateAssistant\";\r\nimport AuthOverlay from './components/AuthOverlay';\r\nimport AuthCallback from './pages/AuthCallback';\r\nimport SimpleCompleteProfile from './pages/SimpleCompleteProfile';\r\nimport CrmDemo from './pages/CrmDemo';\r\nimport LoginPage from './pages/LoginPage';\r\nimport { testSupabaseConnection, logSupabaseConfig } from './testSupabase';\r\nimport { initializeSupabaseConfig, verifySupabaseConfig } from './utils/supabaseConfigVerifier';\r\nimport TestComponent from './components/TestComponent';\r\nimport SubdomainTestPage from './pages/SubdomainTestPage';\r\nimport AttorneyProfileTest from './components/AttorneyProfileTest';\r\nimport VapiTestPage from './pages/VapiTestPage';\r\nimport VapiComparisonTest from './pages/VapiComparisonTest';\r\n// Development components removed for production\r\n// import BugReportButton from './components/BugReportButton';\r\n// import VapiTestComponent from './components/VapiTestComponent';\r\n// import VapiIntegrationTest from './pages/VapiIntegrationTest';\r\n// import SubdomainEditorDemo from './pages/SubdomainEditorDemo';\r\n\r\n// Create debugger for App component\r\nconst debug = createDebugger('App');\r\n\r\n// Import fallback component directly\r\nimport SimpleDemoPageFallback from './pages/SimpleDemoPageFallback';\r\n\r\n// Lazy load components\r\nconst PreviewPage = lazy(() => import('./pages/PreviewPage'));\r\n// Use try-catch for SimpleDemoPage to handle import errors\r\nconst SimpleDemoPage = lazy(() => {\r\n  try {\r\n    return import('./pages/SimpleDemoPage');\r\n  } catch (error) {\r\n    console.error('Error loading SimpleDemoPage:', error);\r\n    return Promise.resolve({ default: SimpleDemoPageFallback });\r\n  }\r\n});\r\nconst CallControl = lazy(() => import('./pages/CallControl'));\r\n\r\n// Home component to contain the main app content\r\nconst Home = ({ isLoading, callActive, showAttorneyInfo, showCallSummary, attorneyProfile, startCall, endCall, callData, subdomain, setShowAttorneyInfo, setShowCallSummary, buttonText, isAttorneySubdomain, hideCreateAgentButton = false, isDarkTheme, vapiCallKey }) => {\r\n  const [showTransition, setShowTransition] = useState(false);\r\n  const [buttonPosition, setButtonPosition] = useState(null);\r\n  const buttonRef = useRef(null);\r\n\r\n  // Always declare hooks at the top level - never conditionally\r\n  const [configMapping, setConfigMapping] = useState(null);\r\n\r\n  // Always run useEffect - control behavior with conditions inside\r\n  useEffect(() => {\r\n    // Only load configMapping if we need it for attorney subdomain\r\n    if (isAttorneySubdomain && attorneyProfile && !isLoading && attorneyProfile.firmName && subdomain && subdomain !== 'default') {\r\n      import('./utils/configMapping').then(module => {\r\n        setConfigMapping(module);\r\n      });\r\n    }\r\n  }, [isAttorneySubdomain, attorneyProfile, isLoading, subdomain]);\r\n\r\n  // Function to handle the button click and start the transition\r\n  const handleStartCall = () => {\r\n    // Get the button position for the transition animation\r\n    if (buttonRef.current) {\r\n      const rect = buttonRef.current.getBoundingClientRect();\r\n      setButtonPosition({\r\n        top: rect.top,\r\n        left: rect.left,\r\n        width: rect.width,\r\n        height: rect.height\r\n      });\r\n    }\r\n\r\n    // Show the transition\r\n    setShowTransition(true);\r\n\r\n    // Track the call start\r\n    debug.log('Call transition started', { timestamp: new Date().toISOString() });\r\n    trackUserJourney('call_transition_started');\r\n\r\n    // Debug logging removed for production\r\n  };\r\n\r\n  // Function to handle transition completion\r\n  const handleTransitionComplete = () => {\r\n    // Actually start the call after the transition completes\r\n    // Hide the transition overlay first\r\n    setShowTransition(false);\r\n\r\n    // Then start the call after a brief delay to ensure the UI is updated\r\n    setTimeout(() => {\r\n      startCall();\r\n    }, 100);\r\n  };\r\n\r\n  // Debug the condition values\r\n  console.log('🔍 [App.jsx] Condition check:', {\r\n    isAttorneySubdomain,\r\n    hasAttorneyProfile: !!attorneyProfile,\r\n    isLoading,\r\n    firmName: attorneyProfile?.firmName,\r\n    firm_name: attorneyProfile?.firm_name,\r\n    subdomain,\r\n    subdomainNotDefault: subdomain !== 'default'\r\n  });\r\n\r\n  // If this is an attorney subdomain and we have a valid attorney profile, render the preview component directly\r\n  if (isAttorneySubdomain && attorneyProfile && !isLoading && (attorneyProfile.firmName || attorneyProfile.firm_name) && subdomain && subdomain !== 'default') {\r\n    console.log('🎯 [App.jsx] Condition met! Rendering iframe for subdomain:', subdomain);\r\n    // Import the preview component and render it directly with the attorney data\r\n    const SimplifiedPreview = React.lazy(() => import('./components/preview/SimplifiedPreview'));\r\n\r\n    if (!configMapping) {\r\n      return (\r\n        <div style={{\r\n          display: 'flex',\r\n          flexDirection: 'column',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          height: '100vh',\r\n          backgroundColor: '#f5f7fa'\r\n        }}>\r\n          <div style={{\r\n            width: '40px',\r\n            height: '40px',\r\n            border: '4px solid #f3f3f3',\r\n            borderTop: '4px solid #4B9CD3',\r\n            borderRadius: '50%',\r\n            animation: 'spin 1s linear infinite'\r\n          }}></div>\r\n          <p style={{ marginTop: '20px', color: '#666' }}>Loading attorney profile...</p>\r\n          <style>{`\r\n            @keyframes spin {\r\n              0% { transform: rotate(0deg); }\r\n              100% { transform: rotate(360deg); }\r\n            }\r\n          `}</style>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    // Use the EXACT same URL pattern and mechanism as the working dashboard preview\r\n    const previewUrl = `/simple-preview?subdomain=${encodeURIComponent(subdomain)}&theme=${isDarkTheme ? 'dark' : 'light'}&useEnhancedPreview=true`;\r\n\r\n    return (\r\n      <div className=\"attorney-subdomain-page\" style={{ width: '100%', height: '100vh', margin: 0, padding: 0 }}>\r\n        <iframe\r\n          ref={(iframe) => {\r\n            if (iframe) {\r\n              // Use the EXACT same mechanism as the working dashboard preview\r\n              // Wait for PREVIEW_READY message before sending config\r\n              const handleMessage = async (event) => {\r\n                if (event.data && event.data.type === 'PREVIEW_READY') {\r\n                  console.log('🎯 [App.jsx] Received PREVIEW_READY from subdomain iframe, sending config...');\r\n\r\n                  try {\r\n                    if (!iframe.contentWindow) return;\r\n\r\n                    // Use the EXACT same utility function as the working dashboard preview\r\n                    const { previewConfig } = await import('./utils/previewConfigHandler').then(module =>\r\n                      module.createAttorneyPreviewConfig(attorneyProfile)\r\n                    );\r\n\r\n                    // Add the theme and enhanced preview flags\r\n                    previewConfig.theme = isDarkTheme ? 'dark' : 'light';\r\n                    previewConfig.useEnhancedPreview = true;\r\n\r\n                    // Ensure the Vapi assistant ID is set (critical for functionality)\r\n                    if (attorneyProfile.vapi_assistant_id) {\r\n                      previewConfig.vapi_assistant_id = attorneyProfile.vapi_assistant_id;\r\n                      previewConfig.vapiAssistantId = attorneyProfile.vapi_assistant_id; // Include both formats for compatibility\r\n                    }\r\n\r\n                    // Send the complete config via postMessage (same as dashboard preview)\r\n                    iframe.contentWindow.postMessage({\r\n                      type: 'UPDATE_PREVIEW_CONFIG',\r\n                      config: previewConfig\r\n                    }, '*');\r\n\r\n                    console.log('🎯 [App.jsx] Sent complete config to subdomain iframe via postMessage:', previewConfig);\r\n                  } catch (error) {\r\n                    console.error('🚨 [App.jsx] Error sending config via postMessage:', error);\r\n                  }\r\n\r\n                  // Remove the event listener after sending config\r\n                  window.removeEventListener('message', handleMessage);\r\n                }\r\n              };\r\n\r\n              // Add message listener for PREVIEW_READY\r\n              window.addEventListener('message', handleMessage);\r\n\r\n              // Also set up iframe onload as fallback\r\n              iframe.onload = () => {\r\n                console.log('🎯 [App.jsx] Subdomain iframe loaded, waiting for PREVIEW_READY message...');\r\n              };\r\n            }\r\n          }}\r\n          src={previewUrl}\r\n          style={{\r\n            width: '100%',\r\n            height: '100%',\r\n            border: 'none',\r\n            margin: 0,\r\n            padding: 0\r\n          }}\r\n          title={`${attorneyProfile.firm_name || 'Attorney'} Preview`}\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Otherwise, render the regular home page\r\n  return (\r\n    <>\r\n      {isLoading ? (\r\n        <div className=\"loading-indicator\">Loading...</div>\r\n      ) : (\r\n        <>\r\n          {!callActive && !showAttorneyInfo && !showCallSummary && (\r\n            <div className=\"start-button-container\" ref={buttonRef}>\r\n              <Button\r\n                onClick={handleStartCall}\r\n                label=\"Get Started\"\r\n                mascot=\"/PRIMARY CLEAR.png\"\r\n                isLoading={false}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {/* Call Transition Animation */}\r\n          <CallTransition\r\n            isActive={showTransition}\r\n            onTransitionComplete={handleTransitionComplete}\r\n            buttonPosition={buttonPosition}\r\n            mascotUrl=\"/PRIMARY CLEAR.png\"\r\n          />\r\n\r\n          {callActive && (\r\n            <div className={`call-card-container ${callActive ? 'active' : ''}`}>\r\n              <div className=\"call-card\">\r\n                <VapiCall\r\n                  key={vapiCallKey} // Use stable key to prevent unnecessary unmounting\r\n                  onEndCall={endCall}\r\n                  subdomain={subdomain}\r\n                  assistantId={DEFAULT_ASSISTANT_ID} // Always use the default assistant ID for the home page\r\n                  forceDefaultAssistant={true} // Force using the default assistant\r\n                  initializationDelay={1200}\r\n                  showDebugPanel={false} // Disable the debug panel\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          <HomeContent\r\n            showAttorneyInfo={showAttorneyInfo}\r\n            callData={callData}\r\n            showCallSummary={showCallSummary}\r\n            setShowAttorneyInfo={setShowAttorneyInfo}\r\n            setShowCallSummary={setShowCallSummary}\r\n          />\r\n        </>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\n// Home component continued\r\nconst HomeContent = ({ showAttorneyInfo, callData, showCallSummary, setShowAttorneyInfo, setShowCallSummary }) => (\r\n  <>\r\n    {showAttorneyInfo && callData && (\r\n      <div className=\"attorney-info-container\">\r\n        <div className=\"map-dossier-container\">\r\n          <MapView attorney={callData.attorney} />\r\n          <AttorneyDossier attorney={callData.attorney} />\r\n        </div>\r\n        <button className=\"back-button\" onClick={() => setShowAttorneyInfo(false)}>\r\n          Back to Start\r\n        </button>\r\n      </div>\r\n    )}\r\n\r\n    {showCallSummary && callData && (\r\n      <div className=\"call-summary-container\">\r\n        <CallSummary data={callData.summary} />\r\n        <button className=\"back-button\" onClick={() => setShowCallSummary(false)}>\r\n          Back to Start\r\n        </button>\r\n      </div>\r\n    )}\r\n  </>\r\n);\r\n\r\nfunction App() {\r\n  const location = useLocation();\r\n  const { user } = useAuth(); // Add useAuth hook to get user\r\n  const [callActive, setCallActive] = useState(false)\r\n  const [showAttorneyInfo, setShowAttorneyInfo] = useState(false)\r\n  const [showCallSummary, setShowCallSummary] = useState(false)\r\n  const [callData, setCallData] = useState(null)\r\n  const [subdomain, setSubdomain] = useState(null)\r\n  const [isAttorneySubdomain, setIsAttorneySubdomain] = useState(false)\r\n  const [attorneyProfile, setAttorneyProfile] = useState(null)\r\n  const [isDevelopment, setIsDevelopment] = useState(false)\r\n  const [isLoading, setIsLoading] = useState(true)\r\n  const [availableSubdomains, setAvailableSubdomains] = useState(['default'])\r\n  const [isDarkTheme, setIsDarkTheme] = useState(true)\r\n  const [showSubdomains, setShowSubdomains] = useState(false)\r\n  const [selectedPracticeArea, setSelectedPracticeArea] = useState('');\r\n  const [showPreview, setShowPreview] = useState(false);\r\n  const [activeConfigTab, setActiveConfigTab] = useState('firm');\r\n  const [showAuthOverlay, setShowAuthOverlay] = useState(false);\r\n  const [configMode, setConfigMode] = useState('url');\r\n\r\n  // Add missing state variables\r\n  const [firmName, setFirmName] = useState('Smith & Associates, LLP');\r\n  const [logoUrl, setLogoUrl] = useState('');\r\n  const [state, setState] = useState('');\r\n  const [primaryColor, setPrimaryColor] = useState('#2c3e50');\r\n  const [secondaryColor, setSecondaryColor] = useState('#3498db');\r\n  const [buttonColor, setButtonColor] = useState('#3498db');\r\n\r\n  // Debug logging removed for production\r\n  const [backgroundColor, setBackgroundColor] = useState('#f0f4f8');\r\n  const [backgroundOpacity, setBackgroundOpacity] = useState(0.3);\r\n  const [buttonText, setButtonText] = useState('Start Consultation');\r\n  const [buttonOpacity, setButtonOpacity] = useState(1);\r\n  const [previewHeight, setPreviewHeight] = useState(600);\r\n  const [practiceDescription, setPracticeDescription] = useState('**Welcome to our legal practice**\\n\\nOur team of experienced attorneys is dedicated to providing you with exceptional legal representation. We combine expertise with a client-focused approach to help you navigate complex legal challenges.\\n\\n### How we can help:\\n- Personalized legal solutions\\n- Clear communication throughout your case\\n- Decades of combined experience');\r\n  const [welcomeMessage, setWelcomeMessage] = useState('Hello, I\\'m an AI assistant from Smith & Associates. How can I help you today?');\r\n  const [informationGathering, setInformationGathering] = useState('To better assist you, I\\'ll need a few details about your situation.');\r\n  const [attorneyName, setAttorneyName] = useState('John Smith');\r\n  const [practiceAreaBackgroundOpacity, setPracticeAreaBackgroundOpacity] = useState(0.2);\r\n  const [textBackgroundColor, setTextBackgroundColor] = useState('#634C38');\r\n\r\n  const [firmUrl, setFirmUrl] = useState('');\r\n  const [isUrlLoading, setIsUrlLoading] = useState(false);\r\n\r\n  const iframeRef = useRef(null);\r\n\r\n  // Practice areas configuration\r\n  const practiceAreas = {\r\n    'Personal Injury': {\r\n      questions: \"I want to know the circumstances of their injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?\",\r\n      practiceDescription: \"**Our firm specializes in personal injury law**, and we have a proven track record of success in obtaining favorable settlements and verdicts for our clients.\\n\\n### Our Services:\\n- Car accident claims\\n- Slip and fall cases\\n- Medical malpractice\\n- Workplace injuries\\n\\nWe work on a contingency basis - *you don't pay unless we win*.\",\r\n      welcomeMessage: \"Welcome to our personal injury consultation. I'm your virtual legal assistant and I'm here to help gather information about your case.\",\r\n      informationGathering: \"I want to know the circumstances of your injury, including the date, location, and how it occurred. Was it a car accident, slip and fall, or other type of incident?\"\r\n    },\r\n    'Family Law': {\r\n      questions: \"I need to understand the nature of their family law issue. Are they seeking a divorce, child custody, child support, alimony, or a modification of an existing order?\",\r\n      practiceDescription: \"**Our firm is dedicated to helping families** navigate the complexities of family law. We handle all aspects of divorce, including property division, child custody and visitation.\\n\\n### Practice Areas:\\n- Divorce proceedings\\n- Child custody & support\\n- Spousal support/alimony\\n- Prenuptial agreements\\n\\n> We approach each case with compassion and understanding during difficult times.\",\r\n      welcomeMessage: \"Welcome to our family law consultation. I'm your virtual legal assistant and I'm here to help with your family law matters.\",\r\n      informationGathering: \"I need to understand the nature of your family law issue. Are you seeking a divorce, child custody, child support, alimony, or a modification of an existing order?\"\r\n    },\r\n    'Criminal Defense': {\r\n      questions: \"I need to know the charges against the client, where they are in the criminal process, and if there are any deadlines or court dates already scheduled.\",\r\n      practiceDescription: \"## Criminal Defense Experts\\n\\nOur **experienced criminal defense attorneys** provide aggressive representation for all criminal matters, from misdemeanors to serious felony charges.\\n\\n### Our Approach:\\n1. Thorough case evaluation\\n2. Strategic defense planning\\n3. Aggressive courtroom advocacy\\n4. Pursuit of best possible outcomes\\n\\n[Contact us](#) immediately if you've been charged with a crime.\",\r\n      welcomeMessage: \"Thank you for considering our firm for your criminal defense needs. I'm here to gather some initial information about your case.\",\r\n      informationGathering: \"Please tell me about the charges you're facing and where you are in the legal process. All information is confidential and protected by attorney-client privilege.\"\r\n    }\r\n  };\r\n\r\n  // Handle practice area selection\r\n  const handlePracticeAreaChange = (e) => {\r\n    const area = e.target.value;\r\n    setSelectedPracticeArea(area);\r\n\r\n    if (area && practiceAreas[area]) {\r\n      setWelcomeMessage(practiceAreas[area].welcomeMessage);\r\n      setInformationGathering(practiceAreas[area].informationGathering);\r\n      setPracticeDescription(practiceAreas[area].practiceDescription);\r\n    }\r\n  };\r\n\r\n  // Handle file upload for logo\r\n  const handleLogoUpload = (e) => {\r\n    const file = e.target.files[0];\r\n    if (file) {\r\n      const reader = new FileReader();\r\n      reader.onloadend = () => {\r\n        // Store the image in localStorage and get the ID\r\n        const imageId = storeImage(reader.result);\r\n        console.log('Stored image with ID:', imageId);\r\n        // Set the logo URL to the image ID\r\n        setLogoUrl(imageId);\r\n      };\r\n      reader.readAsDataURL(file);\r\n    }\r\n  };\r\n\r\n  // Handle logo removal\r\n  const handleRemoveLogo = () => {\r\n    setLogoUrl('');\r\n  };\r\n\r\n  const goToPreview = () => {\r\n    setShowPreview(true);\r\n  };\r\n\r\n  // Handle URL submission for auto-configuration\r\n  const handleUrlSubmit = async (e) => {\r\n    if (e && e.preventDefault) {\r\n      e.preventDefault();\r\n    }\r\n\r\n    // Get the URL from the event, localStorage, or state\r\n    let urlToProcess = firmUrl;\r\n\r\n    // Check if there's a URL in the event\r\n    if (e && e.detail && e.detail.url) {\r\n      console.log('Using URL from event:', e.detail.url);\r\n      urlToProcess = e.detail.url;\r\n      setFirmUrl(urlToProcess); // Update the state\r\n    }\r\n\r\n    // Check localStorage as a fallback\r\n    if (!urlToProcess) {\r\n      const storedUrl = localStorage.getItem('pendingUrlAutoConfig');\r\n      if (storedUrl) {\r\n        console.log('Using URL from localStorage:', storedUrl);\r\n        urlToProcess = storedUrl;\r\n        setFirmUrl(urlToProcess); // Update the state\r\n      }\r\n    }\r\n\r\n    if (!urlToProcess) {\r\n      alert('Please enter a URL');\r\n      return;\r\n    }\r\n\r\n    console.log('Processing URL:', urlToProcess);\r\n\r\n    // Show loading state\r\n    setIsUrlLoading(true);\r\n\r\n    // Show a toast notification\r\n    toast.info('Auto-configuring from website...', {\r\n      position: 'top-center',\r\n      autoClose: false,\r\n      hideProgressBar: false,\r\n      closeOnClick: false,\r\n      pauseOnHover: true,\r\n      draggable: false,\r\n      progress: undefined,\r\n      toastId: 'auto-configure-toast'\r\n    });\r\n\r\n    try {\r\n      // Import the website scraper utility\r\n      const { scrapeWebsite } = await import('./utils/websiteScraper');\r\n\r\n      // Call the scraper to extract website data\r\n      const extractedData = await scrapeWebsite(firmUrl);\r\n      console.log('Extracted website data:', extractedData);\r\n\r\n      // Update state with extracted information\r\n      if (extractedData) {\r\n        // Basic information\r\n        setFirmName(extractedData.firmName || 'Your Law Firm');\r\n        setAttorneyName(extractedData.attorneyName || '');\r\n\r\n        // Visual elements\r\n        if (extractedData.logo && extractedData.logo.url) {\r\n          setLogoUrl(extractedData.logo.url);\r\n        }\r\n\r\n        // Colors\r\n        if (extractedData.colors) {\r\n          setPrimaryColor(extractedData.colors.primary || '#4B74AA');\r\n          setSecondaryColor(extractedData.colors.secondary || '#2C3E50');\r\n          setButtonColor(extractedData.colors.accent || extractedData.colors.secondary || '#3498db');\r\n          setBackgroundColor(extractedData.colors.background || '#1a1a1a');\r\n        }\r\n\r\n        // Content\r\n        setPracticeDescription(extractedData.contentAnalysis?.services?.join(', ') || 'Specializing in various legal matters with a client-focused approach.');\r\n\r\n        // Set welcome message and information gathering prompts\r\n        setWelcomeMessage(extractedData.welcomeMessage || ('Hello, I\\'m an AI assistant from ' + extractedData.firmName + '. How can I help you today?'));\r\n        setInformationGathering(extractedData.informationGathering || 'To better assist you, I\\'ll need a few details about your situation.');\r\n\r\n        // Set button text\r\n        setButtonText(extractedData.buttonText || 'Start Consultation');\r\n\r\n        // Set state if available\r\n        if (extractedData.address && extractedData.address.state) {\r\n          setState(extractedData.address.state);\r\n        }\r\n\r\n        // Determine practice area based on extracted data\r\n        if (extractedData.practiceAreas && extractedData.practiceAreas.length > 0) {\r\n          // Try to match extracted practice areas with our predefined ones\r\n          const practiceAreaMap = {\r\n            'personal injury': 'Personal Injury',\r\n            'accident': 'Personal Injury',\r\n            'family': 'Family Law',\r\n            'divorce': 'Family Law',\r\n            'criminal': 'Criminal Defense',\r\n            'defense': 'Criminal Defense'\r\n          };\r\n\r\n          // Look for matches in the extracted practice areas\r\n          for (const area of extractedData.practiceAreas) {\r\n            const lowerArea = area.toLowerCase();\r\n            for (const [keyword, mappedArea] of Object.entries(practiceAreaMap)) {\r\n              if (lowerArea.includes(keyword) && practiceAreas[mappedArea]) {\r\n                setSelectedPracticeArea(mappedArea);\r\n                // Don't override the extracted welcome message and information gathering\r\n                // with the predefined ones unless they're empty\r\n                if (!extractedData.welcomeMessage) {\r\n                  setWelcomeMessage(practiceAreas[mappedArea].welcomeMessage);\r\n                }\r\n                if (!extractedData.informationGathering) {\r\n                  setInformationGathering(practiceAreas[mappedArea].informationGathering);\r\n                }\r\n                break;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error scraping website:', error);\r\n\r\n      // Fallback to basic extraction if the scraper fails\r\n      const domain = firmUrl.replace(/^https?:\\/\\//, '').replace(/^www\\./, '').split('/')[0];\r\n      const generatedFirmName = domain\r\n        .split(/[.-]/)\r\n        .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n        .join(' ') + \" Law\";\r\n\r\n      setFirmName(generatedFirmName);\r\n      setWelcomeMessage('Hello, I\\'m an AI assistant from ' + generatedFirmName + '. How can I help you today?');\r\n\r\n      // Close the loading toast and show an error toast\r\n      toast.dismiss('auto-configure-toast');\r\n      toast.warning('Could not fully analyze the website. Basic information has been extracted.', {\r\n        position: 'top-center',\r\n        autoClose: 5000,\r\n        hideProgressBar: false,\r\n        closeOnClick: true,\r\n        pauseOnHover: true,\r\n        draggable: true\r\n      });\r\n\r\n      // Switch to manual config mode to show the extracted information\r\n      setConfigMode('manual');\r\n    } finally {\r\n      setIsUrlLoading(false);\r\n\r\n      // Close the loading toast if it's still open\r\n      setTimeout(() => {\r\n        toast.dismiss('auto-configure-toast');\r\n\r\n        // Show success toast if we're still in URL mode (no error occurred)\r\n        if (configMode === 'url') {\r\n          toast.success('Website auto-configuration complete!', {\r\n            position: 'top-center',\r\n            autoClose: 3000,\r\n            hideProgressBar: false,\r\n            closeOnClick: true,\r\n            pauseOnHover: true,\r\n            draggable: true\r\n          });\r\n\r\n          // Switch to manual config mode to show the extracted information\r\n          setConfigMode('manual');\r\n        }\r\n      }, 1000);\r\n    }\r\n  };\r\n\r\n  // Helper functions for color handling\r\n  const hexToRgb = (hex) => {\r\n    // Remove # if present\r\n    hex = hex.replace('#', '');\r\n\r\n    // Parse hex values\r\n    const r = parseInt(hex.substring(0, 2), 16);\r\n    const g = parseInt(hex.substring(2, 4), 16);\r\n    const b = parseInt(hex.substring(4, 6), 16);\r\n\r\n    return `${r}, ${g}, ${b}`;\r\n  };\r\n\r\n  const getContrastColor = (hexColor) => {\r\n    // Convert hex to RGB\r\n    let hex = hexColor.replace('#', '');\r\n\r\n    // Convert hex to RGB\r\n    const r = parseInt(hex.substring(0, 2), 16);\r\n    const g = parseInt(hex.substring(2, 4), 16);\r\n    const b = parseInt(hex.substring(4, 6), 16);\r\n\r\n    // Calculate luminance - standard formula\r\n    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;\r\n\r\n    // Return black for bright colors, white for dark colors\r\n    return luminance > 0.5 ? '#000000' : '#ffffff';\r\n  };\r\n\r\n  // Theme management\r\n  useEffect(() => {\r\n    document.documentElement.setAttribute('data-theme', isDarkTheme ? 'dark' : 'light');\r\n\r\n    // Add or remove dark-theme class from body\r\n    if (isDarkTheme) {\r\n      document.body.classList.add('dark-theme');\r\n    } else {\r\n      document.body.classList.remove('dark-theme');\r\n    }\r\n  }, [isDarkTheme]);\r\n\r\n  const toggleTheme = () => {\r\n    setIsDarkTheme(prev => !prev);\r\n  };\r\n\r\n  // Use useEffect to determine subdomain and initialize\r\n  useEffect(() => {\r\n    debug.log('App component mounted');\r\n\r\n    // Check for pending URL auto-configuration from localStorage\r\n    const pendingUrl = localStorage.getItem('pendingUrlAutoConfig');\r\n    const timestamp = localStorage.getItem('urlAutoConfigTimestamp');\r\n\r\n    if (pendingUrl && timestamp) {\r\n      // Only process if the timestamp is recent (within the last 5 seconds)\r\n      const now = Date.now();\r\n      const timestampNum = parseInt(timestamp, 10);\r\n\r\n      if (!isNaN(timestampNum) && now - timestampNum < 5000) {\r\n        console.log('Found pending URL auto-configuration:', pendingUrl);\r\n        setFirmUrl(pendingUrl);\r\n\r\n        // Clear the localStorage items\r\n        localStorage.removeItem('pendingUrlAutoConfig');\r\n        localStorage.removeItem('urlAutoConfigTimestamp');\r\n\r\n        // Trigger the URL submission after a short delay\r\n        setTimeout(() => {\r\n          handleUrlSubmit();\r\n        }, 500);\r\n      } else {\r\n        // Clear old items\r\n        localStorage.removeItem('pendingUrlAutoConfig');\r\n        localStorage.removeItem('urlAutoConfigTimestamp');\r\n      }\r\n    }\r\n\r\n    // Initialize and verify Supabase configuration\r\n    initializeSupabaseConfig();\r\n\r\n    // Test Supabase connection with our new test function\r\n    logSupabaseConfig();\r\n    verifySupabaseConfig().then(result => {\r\n      if (result.success) {\r\n        console.log('Supabase is properly configured and connected!', result.data);\r\n        if (result.usingFallback) {\r\n          console.warn('Using fallback Supabase configuration. Update your .env.development file for a better development experience.');\r\n        }\r\n      } else {\r\n        console.warn('Supabase connection failed:', result.error);\r\n\r\n        if (result.useMockData) {\r\n          console.info('The application will use mock data for development');\r\n          console.info('This is fine for local development, but you should fix the Supabase connection for production');\r\n\r\n          // Set a flag to indicate we're using mock data\r\n          window.USING_MOCK_DATA = true;\r\n        }\r\n      }\r\n    });\r\n\r\n    // Initialize React DevTools global hook for LegalScout\r\n    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {\r\n      window.__REACT_DEVTOOLS_GLOBAL_HOOK__.legalScout = {};\r\n    }\r\n\r\n    // Determine if we're in development environment\r\n    const isDev = import.meta.env.MODE === 'development' ||\r\n                 window.location.hostname === 'localhost' ||\r\n                 window.location.hostname === '127.0.0.1';\r\n    setIsDevelopment(isDev);\r\n\r\n    // Get subdomain using our utility\r\n    const subdomainValue = getCurrentSubdomain();\r\n    debug.log('Subdomain detected:', subdomainValue);\r\n    setSubdomain(subdomainValue);\r\n\r\n    // Check if this is an attorney subdomain using shared utility\r\n    const isAttorneySub = checkIsAttorneySubdomain(subdomainValue);\r\n    setIsAttorneySubdomain(isAttorneySub);\r\n    debug.log('Is attorney subdomain:', isAttorneySub, 'Subdomain value:', subdomainValue);\r\n\r\n    // If we're on the main domain (not a subdomain), clear any attorney profile\r\n    if (!isAttorneySub) {\r\n      setAttorneyProfile(null);\r\n    }\r\n\r\n    // Get attorney profile based on subdomain\r\n    const loadAttorneyProfile = async () => {\r\n      setIsLoading(true);\r\n      console.log('🚀 [App.jsx] Starting attorney profile load for subdomain:', subdomainValue);\r\n      debug.log('🚀 Starting attorney profile load for subdomain:', subdomainValue);\r\n\r\n      try {\r\n        console.log('📞 [App.jsx] Calling getAttorneyConfigAsync with subdomain:', subdomainValue);\r\n        debug.log('📞 Calling getAttorneyConfigAsync with subdomain:', subdomainValue);\r\n\r\n        // Add a check to make sure the function exists\r\n        if (typeof getAttorneyConfigAsync !== 'function') {\r\n          throw new Error('getAttorneyConfigAsync is not a function');\r\n        }\r\n\r\n        const profile = await getAttorneyConfigAsync(subdomainValue);\r\n        console.log('✅ [App.jsx] Attorney profile loaded successfully:', {\r\n          hasProfile: !!profile,\r\n          firmName: profile?.firmName,\r\n          id: profile?.id,\r\n          subdomain: profile?.subdomain,\r\n          isFallback: profile?.isFallback\r\n        });\r\n        debug.log('✅ Attorney profile loaded successfully:', {\r\n          hasProfile: !!profile,\r\n          firmName: profile?.firmName,\r\n          id: profile?.id,\r\n          subdomain: profile?.subdomain,\r\n          isFallback: profile?.isFallback\r\n        });\r\n        setAttorneyProfile(profile);\r\n\r\n        // If no valid attorney profile is found for this subdomain,\r\n        // don't treat it as an attorney subdomain\r\n        if (!profile || !profile.firmName) {\r\n          console.log('⚠️ [App.jsx] No valid profile found, setting isAttorneySubdomain to false');\r\n          debug.log('⚠️ No valid profile found, setting isAttorneySubdomain to false');\r\n          setIsAttorneySubdomain(false);\r\n        } else {\r\n          console.log('✅ [App.jsx] Valid profile found, keeping isAttorneySubdomain as true');\r\n          debug.log('✅ Valid profile found, keeping isAttorneySubdomain as true');\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ [App.jsx] Error loading attorney profile:', {\r\n          message: error.message,\r\n          stack: error.stack,\r\n          subdomain: subdomainValue,\r\n          functionExists: typeof getAttorneyConfigAsync === 'function'\r\n        });\r\n        // If there's an error loading the profile, don't treat it as an attorney subdomain\r\n        setIsAttorneySubdomain(false);\r\n        setAttorneyProfile(null);\r\n      } finally {\r\n        setIsLoading(false);\r\n        console.log('🏁 [App.jsx] Attorney profile loading complete');\r\n        debug.log('🏁 Attorney profile loading complete');\r\n      }\r\n    };\r\n\r\n    // Only load attorney profile if this is an attorney subdomain\r\n    if (isAttorneySub) {\r\n      console.log('🔍 [App.jsx] This is an attorney subdomain, loading profile');\r\n      loadAttorneyProfile();\r\n    } else {\r\n      console.log('🏠 [App.jsx] This is not an attorney subdomain, skipping profile load');\r\n      setIsLoading(false);\r\n    }\r\n\r\n    return () => {\r\n      debug.log('App component unmounted');\r\n    };\r\n  }, []);\r\n\r\n  // Setup subdomain testing UI in development\r\n  useEffect(() => {\r\n    if (!isDevelopment) return;\r\n\r\n    // Fetch available subdomains from Supabase\r\n    const fetchSubdomains = async () => {\r\n      try {\r\n        // Import supabase client\r\n        const { supabase } = await import('./lib/supabase');\r\n\r\n        // Fetch all attorney subdomains from the database\r\n        const { data, error } = await supabase\r\n          .from('attorneys')\r\n          .select('subdomain')\r\n          .not('subdomain', 'is', null);\r\n\r\n        if (error) {\r\n          console.warn('Error fetching attorney subdomains:', error);\r\n          setAvailableSubdomains(['default']);\r\n          return;\r\n        }\r\n\r\n        // Extract subdomain values from the data\r\n        const subdomains = data.map(item => item.subdomain).filter(Boolean);\r\n\r\n        debug.log('Available subdomains for testing:', subdomains);\r\n        setAvailableSubdomains(['default', ...subdomains]);\r\n      } catch (error) {\r\n        console.warn('Error setting up subdomain testing, using default only:', error);\r\n        setAvailableSubdomains(['default']);\r\n      }\r\n    };\r\n\r\n    fetchSubdomains();\r\n  }, [isDevelopment]);\r\n\r\n  // Track if we're in the process of starting a call to prevent race conditions\r\n  const [isStartingCall, setIsStartingCall] = useState(false);\r\n\r\n  const startCall = () => {\r\n    debug.log('Call started', { timestamp: new Date().toISOString() });\r\n    trackUserJourney('call_started');\r\n\r\n    // If we're already in the process of starting a call, don't start another one\r\n    if (isStartingCall) {\r\n      console.log('[App.jsx] Already in the process of starting a call, ignoring duplicate startCall');\r\n      return;\r\n    }\r\n\r\n    // Set the flag to indicate we're in the process of starting a call\r\n    setIsStartingCall(true);\r\n\r\n    // Clear any previous states\r\n    setShowAttorneyInfo(false);\r\n    setShowCallSummary(false);\r\n\r\n    // Clean up any lingering state from previous calls\r\n    window.vapiCallActive = false;\r\n\r\n    // Clean up any lingering Daily.co iframes before starting a new call\r\n    try {\r\n      const existingIframes = document.querySelectorAll('iframe[title*=\"daily\"]');\r\n      if (existingIframes.length > 0) {\r\n        console.log(`[App] Found ${existingIframes.length} lingering Daily.co iframes. Removing before starting new call...`);\r\n        existingIframes.forEach(iframe => {\r\n          iframe.parentNode.removeChild(iframe);\r\n        });\r\n      }\r\n    } catch (cleanupError) {\r\n      console.warn(\"[App] Error cleaning up iframes before starting call:\", cleanupError);\r\n    }\r\n\r\n    // Increment the VapiCall key to force a fresh component instance\r\n    setVapiCallKey(prev => prev + 1);\r\n\r\n    // Set call to active - this will trigger the VapiCall component to render\r\n    console.log('[App] Starting call - setting callActive to true');\r\n    setCallActive(true);\r\n\r\n    // Force the call-card-container to be visible after a short delay\r\n    setTimeout(() => {\r\n      console.log('[App] Forcing call-card-container to be visible');\r\n      const callCardContainer = document.querySelector('.call-card-container');\r\n      if (callCardContainer) {\r\n        callCardContainer.classList.add('active');\r\n        callCardContainer.style.display = 'flex';\r\n        callCardContainer.style.visibility = 'visible';\r\n        callCardContainer.style.opacity = '1';\r\n        callCardContainer.style.zIndex = '1000';\r\n      }\r\n\r\n      // Reset the starting call flag after a delay to allow for initialization\r\n      setTimeout(() => {\r\n        setIsStartingCall(false);\r\n      }, 3000); // Increased to 3 seconds to allow for VapiCall initialization\r\n    }, 500);\r\n  }\r\n\r\n  const handleGetStarted = () => {\r\n    // Show the auth overlay when Get Started is clicked\r\n    console.log('handleGetStarted called in App.jsx');\r\n    setShowAuthOverlay(true);\r\n  }\r\n\r\n  // Expose handleGetStarted, handleUrlSubmit, and setShowAuthOverlay to window object for access from other components\r\n  useEffect(() => {\r\n    window.handleGetStarted = handleGetStarted;\r\n    window.handleUrlSubmit = handleUrlSubmit;\r\n    window.setShowAuthOverlay = setShowAuthOverlay;\r\n\r\n    // Add event listener for urlAutoConfig event\r\n    const handleUrlAutoConfig = (event) => {\r\n      console.log('urlAutoConfig event received:', event.detail);\r\n      if (event.detail && event.detail.url) {\r\n        setFirmUrl(event.detail.url);\r\n        handleUrlSubmit({ detail: { url: event.detail.url } });\r\n      }\r\n    };\r\n\r\n    // Add event listener for autoConfigureClicked event\r\n    const handleAutoConfigureClicked = (event) => {\r\n      console.log('autoConfigureClicked event received:', event.detail);\r\n      if (event.detail && event.detail.url) {\r\n        setFirmUrl(event.detail.url);\r\n        handleUrlSubmit({ detail: { url: event.detail.url } });\r\n      }\r\n    };\r\n\r\n    // Add event listener for direct button clicks\r\n    const handleAutoConfigButtonClick = (event) => {\r\n      // Check if the clicked element is the Auto-Configure button\r\n      if (event.target &&\r\n          (event.target.textContent.includes('Auto-Configure') ||\r\n           (event.target.parentElement && event.target.parentElement.textContent.includes('Auto-Configure')))) {\r\n        console.log('Auto-Configure button click detected');\r\n\r\n        // Find the URL input\r\n        const urlInput = document.getElementById('firmUrl');\r\n        if (urlInput && urlInput.value) {\r\n          console.log('Found URL input with value:', urlInput.value);\r\n          setFirmUrl(urlInput.value);\r\n          handleUrlSubmit({ detail: { url: urlInput.value } });\r\n        }\r\n      }\r\n    };\r\n\r\n    document.addEventListener('urlAutoConfig', handleUrlAutoConfig);\r\n    document.addEventListener('autoConfigureClicked', handleAutoConfigureClicked);\r\n    document.addEventListener('click', handleAutoConfigButtonClick, true);\r\n\r\n    return () => {\r\n      // Clean up when component unmounts\r\n      delete window.handleGetStarted;\r\n      delete window.handleUrlSubmit;\r\n      delete window.setShowAuthOverlay;\r\n      document.removeEventListener('urlAutoConfig', handleUrlAutoConfig);\r\n      document.removeEventListener('autoConfigureClicked', handleAutoConfigureClicked);\r\n      document.removeEventListener('click', handleAutoConfigButtonClick, true);\r\n    };\r\n  }, [handleUrlSubmit]);\r\n\r\n  const handleAuthSuccess = (attorneyData) => {\r\n    // Handle successful authentication and attorney account creation\r\n    console.log('Attorney account created:', attorneyData);\r\n\r\n    // Check if this is development mode\r\n    const isDevelopmentMode = attorneyData && attorneyData.id && attorneyData.id.toString().startsWith('dev-');\r\n\r\n    if (isDevelopmentMode) {\r\n      console.log('Development mode detected. Redirecting to dashboard...');\r\n      // Close the auth overlay and redirect to dashboard\r\n      setShowAuthOverlay(false);\r\n      // Redirect to dashboard\r\n      window.location.href = `/dashboard`;\r\n    } else {\r\n      // Redirect to dashboard\r\n      window.location.href = `/dashboard`;\r\n    }\r\n  }\r\n\r\n  // Track if we're in the process of ending a call to prevent race conditions\r\n  const [isEndingCall, setIsEndingCall] = useState(false);\r\n  // Add a stable key for VapiCall to prevent unnecessary unmounting\r\n  const [vapiCallKey, setVapiCallKey] = useState(0);\r\n\r\n  const endCall = (data) => {\r\n    debug.log('Call ended', { timestamp: new Date().toISOString(), data });\r\n    trackUserJourney('call_ended');\r\n\r\n    // If we're already in the process of ending a call, don't start another cleanup\r\n    if (isEndingCall) {\r\n      console.log('[App.jsx] Already in the process of ending a call, ignoring duplicate endCall');\r\n      return;\r\n    }\r\n\r\n    // Set the flag to indicate we're in the process of ending a call\r\n    setIsEndingCall(true);\r\n\r\n    // Check if the call is still active according to the global variable\r\n    const isCallStillActive = window.vapiCallActive === true;\r\n\r\n    // Check if this is a forced end while the assistant is still speaking\r\n    const isForcedEndWhileSpeaking = data && data.forcedWhileSpeaking;\r\n\r\n    // Clean up any lingering Daily.co iframes\r\n    try {\r\n      const existingIframes = document.querySelectorAll('iframe[title*=\"daily\"]');\r\n      if (existingIframes.length > 0) {\r\n        console.log(`[App.jsx] Found ${existingIframes.length} lingering Daily.co iframes. Removing during endCall...`);\r\n        existingIframes.forEach(iframe => {\r\n          iframe.parentNode.removeChild(iframe);\r\n        });\r\n      }\r\n    } catch (cleanupError) {\r\n      console.warn(\"[App.jsx] Error cleaning up iframes during endCall:\", cleanupError);\r\n    }\r\n\r\n    // If the call is empty or undefined, it's likely a premature call during initialization\r\n    // In this case, we'll just reset the state and return to prevent the mount/unmount cycle\r\n    if (!data || (Object.keys(data).length === 0 && data.constructor === Object)) {\r\n      console.log('[App.jsx] Received empty data in endCall, likely during initialization - resetting state');\r\n\r\n      // Reset the call state\r\n      setCallActive(false);\r\n      window.vapiCallActive = false;\r\n\r\n      // Reset the ending call flag\r\n      setTimeout(() => {\r\n        setIsEndingCall(false);\r\n      }, 500);\r\n\r\n      return;\r\n    }\r\n\r\n    if (isCallStillActive || isForcedEndWhileSpeaking) {\r\n      console.log('[App.jsx] Call is still active or ended while assistant was speaking - keeping interface visible');\r\n      console.log('[App.jsx] window.vapiCallActive =', window.vapiCallActive);\r\n\r\n      // Don't hide the call UI immediately if the call is still active\r\n      // Instead, let the VapiCall component handle the cleanup when the call truly ends\r\n\r\n      // Force the call-card-container to be visible\r\n      const callCardContainer = document.querySelector('.call-card-container');\r\n      if (callCardContainer) {\r\n        callCardContainer.classList.add('active');\r\n        callCardContainer.style.display = 'flex';\r\n        callCardContainer.style.visibility = 'visible';\r\n        callCardContainer.style.opacity = '1';\r\n        callCardContainer.style.zIndex = '1000';\r\n        console.log('[App.jsx] Forced call-card-container to be visible during active call');\r\n      }\r\n\r\n      // Set up an interval to check if the call is still active\r\n      // Use a shorter interval and limit the number of checks to prevent infinite loops\r\n      let checkCount = 0;\r\n      const maxChecks = 10; // Maximum number of checks (10 seconds total)\r\n      const checkCallInterval = setInterval(() => {\r\n        checkCount++;\r\n        console.log(`[App.jsx] Check ${checkCount}/${maxChecks} for call active status: ${window.vapiCallActive}`);\r\n\r\n        if (window.vapiCallActive === false || checkCount >= maxChecks) {\r\n          console.log('[App.jsx] Call is no longer active or max checks reached, cleaning up');\r\n          clearInterval(checkCallInterval);\r\n\r\n          // Force window.vapiCallActive to false if we've reached max checks\r\n          if (checkCount >= maxChecks) {\r\n            console.log('[App.jsx] Max checks reached, forcing window.vapiCallActive to false');\r\n            window.vapiCallActive = false;\r\n          }\r\n\r\n          // Now we can safely end the call\r\n          setCallActive(false);\r\n\r\n          // Process the call data to show appropriate UI\r\n          if (data && data.attorney) {\r\n            console.log('[App.jsx] Showing attorney info');\r\n            setShowAttorneyInfo(true);\r\n            setShowCallSummary(false);\r\n            setCallData(data);\r\n          }\r\n          else if (data && data.summary) {\r\n            console.log('[App.jsx] Showing call summary');\r\n            setShowAttorneyInfo(false);\r\n            setShowCallSummary(true);\r\n            setCallData(data);\r\n          }\r\n          else {\r\n            console.log('[App.jsx] No attorney or summary data, returning to home state');\r\n            setShowAttorneyInfo(false);\r\n            setShowCallSummary(false);\r\n          }\r\n\r\n          // Clean up any lingering Daily.co iframes again\r\n          try {\r\n            const existingIframes = document.querySelectorAll('iframe[title*=\"daily\"]');\r\n            if (existingIframes.length > 0) {\r\n              console.log(`[App.jsx] Found ${existingIframes.length} lingering Daily.co iframes after interval. Removing...`);\r\n              existingIframes.forEach(iframe => {\r\n                iframe.parentNode.removeChild(iframe);\r\n              });\r\n            }\r\n          } catch (cleanupError) {\r\n            console.warn(\"[App.jsx] Error cleaning up iframes after interval:\", cleanupError);\r\n          }\r\n\r\n          // Reset the ending call flag\r\n          setIsEndingCall(false);\r\n        }\r\n      }, 1000);\r\n\r\n      return;\r\n    }\r\n\r\n    // For normal call endings, immediately set callActive to false to hide the call UI\r\n    setCallActive(false);\r\n\r\n    // Log detailed information about the call end\r\n    console.log('[App.jsx] Call ended with data:', data);\r\n\r\n    // Process the call data to show appropriate UI\r\n    if (data && data.attorney) {\r\n      console.log('[App.jsx] Showing attorney info');\r\n      setShowAttorneyInfo(true);\r\n      setShowCallSummary(false);\r\n      setCallData(data);\r\n    }\r\n    else if (data && data.summary) {\r\n      console.log('[App.jsx] Showing call summary');\r\n      setShowAttorneyInfo(false);\r\n      setShowCallSummary(true);\r\n      setCallData(data);\r\n    }\r\n    else {\r\n      console.log('[App.jsx] No attorney or summary data, returning to home state');\r\n      setShowAttorneyInfo(false);\r\n      setShowCallSummary(false);\r\n    }\r\n\r\n    // Force a re-render of the component tree to ensure the call UI is removed\r\n    setTimeout(() => {\r\n      console.log('[App.jsx] Forcing re-render after call end');\r\n      setCallActive(state => {\r\n        // Only set to false if it's not already false to avoid unnecessary re-renders\r\n        if (state === true) return false;\r\n        return state;\r\n      });\r\n\r\n      // Double-check that the global flag is set to false\r\n      window.vapiCallActive = false;\r\n      console.log('[App.jsx] Double-checked window.vapiCallActive is false at end of endCall');\r\n\r\n      // Clean up any lingering Daily.co iframes one final time\r\n      try {\r\n        const existingIframes = document.querySelectorAll('iframe[title*=\"daily\"]');\r\n        if (existingIframes.length > 0) {\r\n          console.log(`[App.jsx] Found ${existingIframes.length} lingering Daily.co iframes in final cleanup. Removing...`);\r\n          existingIframes.forEach(iframe => {\r\n            iframe.parentNode.removeChild(iframe);\r\n          });\r\n        }\r\n      } catch (cleanupError) {\r\n        console.warn(\"[App.jsx] Error cleaning up iframes in final cleanup:\", cleanupError);\r\n      }\r\n\r\n      // Reset the ending call flag\r\n      setIsEndingCall(false);\r\n    }, 100);\r\n  }\r\n\r\n  // Debug the logo URL whenever it changes\r\n  useEffect(() => {\r\n    if (logoUrl) {\r\n      console.log('[App.jsx] Current logoUrl:', logoUrl);\r\n    }\r\n  }, [logoUrl]);\r\n\r\n  return (\r\n    <div className=\"app-wrapper\">\r\n      <AnimatedBackground />\r\n      <ToastContainer\r\n        position=\"top-center\"\r\n        autoClose={5000}\r\n        hideProgressBar={false}\r\n        newestOnTop\r\n        closeOnClick\r\n        rtl={false}\r\n        pauseOnFocusLoss\r\n        draggable\r\n        pauseOnHover\r\n        theme={isDarkTheme ? 'dark' : 'light'}\r\n      />\r\n      {/* Show main app header everywhere except dashboard page and attorney subdomains */}\r\n      {location.pathname !== '/dashboard' && !isAttorneySubdomain && (\r\n        <header className=\"header\">\r\n          <div className=\"logo-container\">\r\n            <Link to=\"/\" aria-label=\"Go to home page\">\r\n              <img src=\"/nav_logo.webp\" alt=\"LegalScout Logo\" className=\"logo\" />\r\n            </Link>\r\n          </div>\r\n          <Navbar isDarkTheme={isDarkTheme} />\r\n          {/* Sign In Button - conditionally rendered based on current path */}\r\n          {location.pathname === '/demo' && (\r\n            <SignInButton onClick={() => setShowAuthOverlay(true)} />\r\n          )}\r\n          <ThemeToggle isDark={isDarkTheme} onToggle={toggleTheme} />\r\n        </header>\r\n      )}\r\n\r\n      <main className=\"main-content-layer\">\r\n        <Routes>\r\n          {/* Smart routing: attorney subdomains show homepage at root, others redirect based on auth */}\r\n          <Route path=\"/\" element={\r\n            // Wait for subdomain detection to complete before routing\r\n            subdomain === null || isLoading ? (\r\n              <div className=\"loading-container\" style={{\r\n                display: 'flex',\r\n                flexDirection: 'column',\r\n                alignItems: 'center',\r\n                justifyContent: 'center',\r\n                height: '100vh',\r\n                color: '#fff'\r\n              }}>\r\n                <div className=\"loading-spinner\" style={{\r\n                  border: '4px solid #f3f3f3',\r\n                  borderTop: '4px solid #3498db',\r\n                  borderRadius: '50%',\r\n                  width: '40px',\r\n                  height: '40px',\r\n                  animation: 'spin 2s linear infinite',\r\n                  marginBottom: '20px'\r\n                }}></div>\r\n                <p>Loading...</p>\r\n              </div>\r\n            ) : isAttorneySubdomain ? (\r\n              <Home\r\n                isLoading={isLoading}\r\n                callActive={callActive}\r\n                showAttorneyInfo={showAttorneyInfo}\r\n                showCallSummary={showCallSummary}\r\n                attorneyProfile={attorneyProfile}\r\n                startCall={startCall}\r\n                endCall={endCall}\r\n                callData={callData}\r\n                subdomain={subdomain}\r\n                setShowAttorneyInfo={setShowAttorneyInfo}\r\n                setShowCallSummary={setShowCallSummary}\r\n                buttonText={buttonText}\r\n                isAttorneySubdomain={isAttorneySubdomain}\r\n                hideCreateAgentButton={true}\r\n                isDarkTheme={isDarkTheme}\r\n                vapiCallKey={vapiCallKey}\r\n              />\r\n            ) : (\r\n              // FIXED: Simplified routing - authenticated users go to dashboard\r\n              user ? <Navigate to=\"/dashboard\" replace /> : <Navigate to=\"/home\" replace />\r\n            )\r\n          } />\r\n\r\n          {/* Primary home route - for main domain when not on attorney subdomain */}\r\n          <Route\r\n            path=\"/home\"\r\n            element={\r\n              <Home\r\n                isLoading={isLoading}\r\n                callActive={callActive}\r\n                showAttorneyInfo={showAttorneyInfo}\r\n                showCallSummary={showCallSummary}\r\n                attorneyProfile={attorneyProfile}\r\n                startCall={startCall}\r\n                endCall={endCall}\r\n                callData={callData}\r\n                subdomain={subdomain}\r\n                setShowAttorneyInfo={setShowAttorneyInfo}\r\n                setShowCallSummary={setShowCallSummary}\r\n                buttonText={buttonText}\r\n                isAttorneySubdomain={isAttorneySubdomain}\r\n                hideCreateAgentButton={true}\r\n                isDarkTheme={isDarkTheme}\r\n                vapiCallKey={vapiCallKey}\r\n              />\r\n            }\r\n          />\r\n          <Route path=\"/about\" element={<AboutPage />} />\r\n          <Route path=\"/contact\" element={<div>Contact Page Coming Soon</div>} />\r\n          <Route path=\"/auth/callback\" element={<AuthCallback />} />\r\n          <Route path=\"/login\" element={<LoginPage />} />\r\n          <Route path=\"/test\" element={<TestComponent />} />\r\n          <Route path=\"/subdomain-test\" element={<SubdomainTestPage />} />\r\n          <Route path=\"/attorney-profile-test\" element={<AttorneyProfileTest />} />\r\n          <Route path=\"/vapi-test\" element={<VapiTestPage />} />\r\n          <Route path=\"/vapi-comparison\" element={<VapiComparisonTest />} />\r\n          <Route path=\"/complete-profile\" element={<SimpleCompleteProfile />} />\r\n          <Route path=\"/dashboard\" element={<Dashboard />} />\r\n          <Route path=\"/crm-demo\" element={<CrmDemo />} />\r\n          <Route path=\"/call-control\" element={\r\n            <Suspense fallback={\r\n              <div className=\"loading-container\">\r\n                <div className=\"loading-spinner\"></div>\r\n                <p>Loading call control interface...</p>\r\n              </div>\r\n            }>\r\n              <CallControl />\r\n            </Suspense>\r\n          } />\r\n          <Route path=\"/demo\" element={\r\n            <Suspense fallback={\r\n              <div className=\"loading-container\">\r\n                <div className=\"loading-spinner\"></div>\r\n                <p>Loading demo interface...</p>\r\n              </div>\r\n            }>\r\n              <SimpleDemoPage\r\n                firmName={firmName}\r\n                logoUrl={logoUrl}\r\n                state={state}\r\n                primaryColor={primaryColor}\r\n                secondaryColor={secondaryColor}\r\n                buttonColor={buttonColor}\r\n                setButtonColor={setButtonColor}\r\n                backgroundColor={backgroundColor}\r\n                backgroundOpacity={backgroundOpacity}\r\n                welcomeMessage={welcomeMessage}\r\n                informationGathering={informationGathering}\r\n                practiceDescription={practiceDescription}\r\n                previewHeight={previewHeight}\r\n                setPreviewHeight={setPreviewHeight}\r\n                attorneyName={attorneyName}\r\n                selectedPracticeArea={selectedPracticeArea}\r\n                handlePracticeAreaChange={handlePracticeAreaChange}\r\n                showPreview={showPreview}\r\n                setShowPreview={setShowPreview}\r\n                handleLogoUpload={handleLogoUpload}\r\n                handleRemoveLogo={handleRemoveLogo}\r\n                practiceAreas={practiceAreas}\r\n                activeConfigTab={activeConfigTab}\r\n                setActiveConfigTab={setActiveConfigTab}\r\n                buttonText={buttonText}\r\n                setButtonText={setButtonText}\r\n                buttonOpacity={buttonOpacity}\r\n                setButtonOpacity={setButtonOpacity}\r\n                practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}\r\n                setPracticeAreaBackgroundOpacity={setPracticeAreaBackgroundOpacity}\r\n                textBackgroundColor={textBackgroundColor}\r\n                setTextBackgroundColor={setTextBackgroundColor}\r\n                goToPreview={goToPreview}\r\n                setFirmName={setFirmName}\r\n                setAttorneyName={setAttorneyName}\r\n                setPracticeDescription={setPracticeDescription}\r\n                setState={setState}\r\n                setWelcomeMessage={setWelcomeMessage}\r\n                setInformationGathering={setInformationGathering}\r\n                setPrimaryColor={setPrimaryColor}\r\n                setSecondaryColor={setSecondaryColor}\r\n                setBackgroundColor={setBackgroundColor}\r\n                setBackgroundOpacity={setBackgroundOpacity}\r\n                iframeRef={iframeRef}\r\n                firmUrl={firmUrl}\r\n                setFirmUrl={setFirmUrl}\r\n                isLoading={isUrlLoading}\r\n                handleUrlSubmit={handleUrlSubmit}\r\n                isDarkTheme={isDarkTheme}\r\n                handleGetStarted={() => setShowAuthOverlay(true)}\r\n              />\r\n            </Suspense>\r\n          } />\r\n          <Route path=\"/demo/preview\" element={\r\n            <Suspense fallback={\r\n              <div className=\"loading-container\">\r\n                <div className=\"loading-spinner\"></div>\r\n                <p>Loading preview...</p>\r\n              </div>\r\n            }>\r\n              <PreviewPage\r\n                firmName={firmName}\r\n                attorneyName={attorneyName}\r\n                darkMode={isDarkTheme}\r\n                onToggleDarkMode={() => setIsDarkTheme(!isDarkTheme)}\r\n              />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/preview\" element={\r\n            <Suspense fallback={\r\n              <div className=\"loading-container\">\r\n                <div className=\"loading-spinner\"></div>\r\n                <p>Loading preview...</p>\r\n              </div>\r\n            }>\r\n              <SimplifiedPreview\r\n                firmName={firmName}\r\n                primaryColor={primaryColor}\r\n                secondaryColor={secondaryColor}\r\n                buttonColor={buttonColor}\r\n                backgroundColor={backgroundColor}\r\n                backgroundOpacity={backgroundOpacity}\r\n                practiceDescription={practiceDescription}\r\n                welcomeMessage={welcomeMessage}\r\n                informationGathering={informationGathering}\r\n                theme={isDarkTheme ? 'dark' : 'light'}\r\n                logoUrl={logoUrl || \"/PRIMARY CLEAR.png\"}\r\n                buttonText={buttonText || \"Start Consultation\"}\r\n                buttonOpacity={buttonOpacity}\r\n                practiceAreaBackgroundOpacity={practiceAreaBackgroundOpacity}\r\n                textBackgroundColor={textBackgroundColor}\r\n                mascot=\"/PRIMARY CLEAR.png\"\r\n                vapiInstructions=\"You are a general legal assistant. Collect basic information about the client's legal needs and help them find an appropriate attorney.\"\r\n              />\r\n            </Suspense>\r\n          } />\r\n\r\n          <Route path=\"/simple-preview\" element={<SimplePreviewPage />} />\r\n\r\n          {/* Preview frame for dashboard */}\r\n          <Route path=\"/preview-frame\" element={\r\n            <PreviewFrameLoader />\r\n          } />\r\n\r\n          {/* Test route for preview frame debugging */}\r\n          <Route path=\"/preview-frame-test\" element={\r\n            <div style={{\r\n              padding: '20px',\r\n              display: 'flex',\r\n              flexDirection: 'column',\r\n              alignItems: 'center',\r\n              gap: '20px'\r\n            }}>\r\n              <h1>Simple Preview Test</h1>\r\n              <p>This page is for testing the simple preview route directly.</p>\r\n              <div style={{\r\n                width: '100%',\r\n                maxWidth: '800px',\r\n                height: '600px',\r\n                border: '2px solid #4B74AA',\r\n                borderRadius: '8px',\r\n                overflow: 'hidden'\r\n              }}>\r\n                <iframe\r\n                  src=\"/simple-preview?subdomain=default&theme=dark&loadFromSupabase=true&useEnhancedPreview=true\"\r\n                  title=\"Simple Preview Test\"\r\n                  style={{\r\n                    width: '100%',\r\n                    height: '100%',\r\n                    border: 'none'\r\n                  }}\r\n                />\r\n              </div>\r\n              <div>\r\n                <h2>Troubleshooting</h2>\r\n                <ul>\r\n                  <li>If the preview appears here but not in the dashboard, there's likely a CSS or layout issue in the dashboard.</li>\r\n                  <li>If the preview doesn't appear here either, there might be an issue with the preview component itself.</li>\r\n                  <li>This test uses the same simple-preview route that all other previews now use.</li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          } />\r\n\r\n          {/* Simple test route */}\r\n          <Route path=\"/test-route\" element={\r\n            <div style={{ padding: '20px', textAlign: 'center' }}>\r\n              <h1>Test Route Works!</h1>\r\n              <p>If you can see this, routing is working correctly.</p>\r\n            </div>\r\n          } />\r\n\r\n          {/* Development test routes removed for production */}\r\n        </Routes>\r\n      </main>\r\n\r\n      {/* Development subdomain tools removed for production */}\r\n\r\n      {/* Auth Overlay */}\r\n      <AuthOverlay\r\n        isOpen={showAuthOverlay}\r\n        onClose={() => setShowAuthOverlay(false)}\r\n        onSuccess={handleAuthSuccess}\r\n      />\r\n\r\n      {/* Mobile Activate Assistant - Global component for mobile devices */}\r\n      <MobileActivateAssistant\r\n        onActivated={(config) => {\r\n          console.log('[App] Assistant activated:', config);\r\n          // Optionally update attorney profile if needed\r\n          if (config && config.id) {\r\n            setAttorneyProfile(config);\r\n          }\r\n        }}\r\n      />\r\n\r\n      {/* Bug Report Button removed for production */}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default withDevTools(App, {\r\n  displayName: 'LegalScoutApp',\r\n  type: 'container',\r\n  description: 'Main application container that manages call state',\r\n  responsibleFor: ['call initiation', 'layout management', 'state control']\r\n});", "import React from 'react';\r\n\r\nclass ErrorBoundary extends React.Component {\r\n  constructor(props) {\r\n    super(props);\r\n    this.state = { \r\n      hasError: false,\r\n      error: null, \r\n      errorInfo: null \r\n    };\r\n  }\r\n\r\n  static getDerivedStateFromError(error) {\r\n    // Update state so the next render will show the fallback UI\r\n    return { hasError: true };\r\n  }\r\n\r\n  componentDidCatch(error, errorInfo) {\r\n    // Log the error to the console\r\n    console.error(\"Error caught by ErrorBoundary:\", error, errorInfo);\r\n    this.setState({\r\n      error,\r\n      errorInfo\r\n    });\r\n\r\n    // You can also log the error to an error reporting service\r\n    // Example: logErrorToService(error, errorInfo);\r\n  }\r\n\r\n  render() {\r\n    if (this.state.hasError) {\r\n      // You can render any custom fallback UI\r\n      return (\r\n        <div className=\"error-boundary-fallback\">\r\n          <h2>Something went wrong.</h2>\r\n          {this.props.showDetails && (\r\n            <details style={{ whiteSpace: 'pre-wrap' }}>\r\n              <summary>Error Details</summary>\r\n              <p>{this.state.error && this.state.error.toString()}</p>\r\n              <p>Component Stack: {this.state.errorInfo && this.state.errorInfo.componentStack}</p>\r\n            </details>\r\n          )}\r\n          {this.props.onReset && (\r\n            <button onClick={() => {\r\n              this.setState({ hasError: false });\r\n              this.props.onReset();\r\n            }}>\r\n              Try Again\r\n            </button>\r\n          )}\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return this.props.children;\r\n  }\r\n}\r\n\r\nexport default ErrorBoundary; ", "/**\r\n * Sync Auth Provider\r\n * \r\n * This component integrates the SyncProvider and AuthProvider to ensure\r\n * that authentication state is properly synchronized across systems.\r\n */\r\n\r\nimport React from 'react';\r\nimport { SyncProvider, useSync } from '../contexts/SyncContext';\r\nimport { AuthProvider } from '../contexts/AuthContext';\r\n\r\n/**\r\n * Inner Auth Provider\r\n * \r\n * This component receives the sync tools from the SyncProvider\r\n * and passes them to the AuthProvider.\r\n * \r\n * @param {Object} props - The component props\r\n * @param {React.ReactNode} props.children - The child components\r\n * @returns {JSX.Element} The component\r\n */\r\nconst InnerAuthProvider = ({ children }) => {\r\n  // Get the sync tools\r\n  const syncTools = useSync();\r\n  \r\n  return (\r\n    <AuthProvider syncTools={syncTools}>\r\n      {children}\r\n    </AuthProvider>\r\n  );\r\n};\r\n\r\n/**\r\n * Sync Auth Provider\r\n * \r\n * This component provides both synchronization and authentication\r\n * contexts to the application.\r\n * \r\n * @param {Object} props - The component props\r\n * @param {React.ReactNode} props.children - The child components\r\n * @returns {JSX.Element} The component\r\n */\r\nconst SyncAuthProvider = ({ children }) => {\r\n  return (\r\n    <SyncProvider>\r\n      <InnerAuthProvider>\r\n        {children}\r\n      </InnerAuthProvider>\r\n    </SyncProvider>\r\n  );\r\n};\r\n\r\nexport default SyncAuthProvider;\r\n", "/**\r\n * Environment Variable Verifier\r\n *\r\n * This utility verifies that all required environment variables are properly set\r\n * and logs any issues to the console. It's designed to help diagnose deployment issues.\r\n */\r\n\r\n// List of required environment variables\r\nconst REQUIRED_VARIABLES = [\r\n  'VITE_SUPABASE_URL',\r\n  'VITE_SUPABASE_KEY',\r\n  'VITE_VAPI_PUBLIC_KEY'\r\n];\r\n\r\n/**\r\n * Verifies that all required environment variables are set\r\n * @returns {Object} Object containing verification results\r\n */\r\nexport const verifyEnvironment = () => {\r\n  const results = {\r\n    allVariablesPresent: true,\r\n    missingVariables: [],\r\n    variables: {}\r\n  };\r\n\r\n  // Check each required variable - safely access import.meta.env\r\n  REQUIRED_VARIABLES.forEach(varName => {\r\n    let value = null;\r\n    try {\r\n      value = import.meta.env[varName];\r\n    } catch (e) {\r\n      // import.meta.env may not be available in all contexts\r\n    }\r\n\r\n    const isPresent = Boolean(value);\r\n    const isMasked = varName.includes('KEY') || varName.includes('TOKEN');\r\n\r\n    results.variables[varName] = {\r\n      isPresent,\r\n      value: isMasked ? (isPresent ? '****' : undefined) : value\r\n    };\r\n\r\n    if (!isPresent) {\r\n      results.allVariablesPresent = false;\r\n      results.missingVariables.push(varName);\r\n    }\r\n  });\r\n\r\n  return results;\r\n};\r\n\r\n/**\r\n * Logs environment verification results to the console\r\n */\r\nexport const logEnvironmentStatus = () => {\r\n  const results = verifyEnvironment();\r\n\r\n  console.group('Environment Variable Verification');\r\n  console.log('All required variables present:', results.allVariablesPresent ? '✅ Yes' : '❌ No');\r\n\r\n  if (!results.allVariablesPresent) {\r\n    console.warn('Missing variables:', results.missingVariables.join(', '));\r\n  }\r\n\r\n  console.log('Variables status:');\r\n  Object.entries(results.variables).forEach(([name, info]) => {\r\n    console.log(`- ${name}: ${info.isPresent ? '✅ Present' : '❌ Missing'} ${info.value ? `(${info.value})` : ''}`);\r\n  });\r\n\r\n  console.groupEnd();\r\n\r\n  return results;\r\n};\r\n\r\n/**\r\n * Initializes environment verification\r\n * This should be called early in the application startup\r\n */\r\nexport const initEnvironmentVerification = () => {\r\n  const results = logEnvironmentStatus();\r\n\r\n  // Add fallback values to window object if needed\r\n  if (!results.allVariablesPresent) {\r\n    console.warn('Setting fallback values for missing environment variables');\r\n\r\n    // Fallback values for development\r\n    const FALLBACK_SUPABASE_URL = 'https://utopqxsvudgrtiwenlzl.supabase.co';\r\n    const FALLBACK_SUPABASE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';\r\n    const FALLBACK_VAPI_KEY = '310f0d43-27c2-47a5-a76d-e55171d024f7';\r\n\r\n    // Set fallback values in window object - safely check environment\r\n    let hasSupabaseUrl = false;\r\n    try {\r\n      hasSupabaseUrl = Boolean(import.meta.env.VITE_SUPABASE_URL);\r\n    } catch (e) {\r\n      // import.meta.env may not be available\r\n    }\r\n\r\n    if (!hasSupabaseUrl) {\r\n      window.VITE_SUPABASE_URL = FALLBACK_SUPABASE_URL;\r\n    }\r\n\r\n    let hasSupabaseKey = false;\r\n    try {\r\n      hasSupabaseKey = Boolean(import.meta.env.VITE_SUPABASE_KEY);\r\n    } catch (e) {\r\n      // import.meta.env may not be available\r\n    }\r\n\r\n    if (!hasSupabaseKey) {\r\n      window.VITE_SUPABASE_KEY = FALLBACK_SUPABASE_KEY;\r\n    }\r\n\r\n    let hasVapiKey = false;\r\n    try {\r\n      hasVapiKey = Boolean(import.meta.env.VITE_VAPI_PUBLIC_KEY);\r\n    } catch (e) {\r\n      // import.meta.env may not be available\r\n    }\r\n\r\n    if (!hasVapiKey) {\r\n      window.VITE_VAPI_PUBLIC_KEY = FALLBACK_VAPI_KEY;\r\n    }\r\n  }\r\n\r\n  return results;\r\n};\r\n\r\nexport default {\r\n  verifyEnvironment,\r\n  logEnvironmentStatus,\r\n  initEnvironmentVerification\r\n};\r\n", "/**\r\n * Attorney Profile Manager Initialization\r\n *\r\n * This script initializes the AttorneyProfileManager when the application loads.\r\n * It ensures that the attorney profile is properly loaded and synchronized across systems.\r\n */\r\n\r\nimport { attorneyProfileManager } from '../services/AttorneyProfileManager';\r\nimport { vapiServiceManager } from '../services/vapiServiceManager';\r\n\r\n/**\r\n * Initialize the attorney profile manager\r\n * @returns {Promise<void>}\r\n */\r\nexport const initAttorneyProfileManager = async () => {\r\n  console.log('[initAttorneyProfileManager] Initializing attorney profile manager');\r\n\r\n  try {\r\n    // Try to connect to Vapi service\r\n    const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||\r\n                  (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||\r\n                  localStorage.getItem('vapi_api_key');\r\n\r\n    if (apiKey) {\r\n      console.log('[initAttorneyProfileManager] Initializing Vapi service manager');\r\n\r\n      try {\r\n        // Determine if we're in attorney dashboard or preview mode\r\n        const isAttorneyDashboard = window.location.pathname.includes('/dashboard') ||\r\n                                   window.location.pathname.includes('/admin');\r\n        const isPreview = window.location.pathname.includes('/preview');\r\n\r\n        // Determine if we're in production\r\n        const isProduction = typeof window !== 'undefined' &&\r\n                            (window.location.hostname.includes('.com') ||\r\n                             window.location.hostname.includes('.org') ||\r\n                             window.location.hostname.includes('.net') ||\r\n                             window.location.hostname.includes('.io') ||\r\n                             !window.location.hostname.includes('localhost'));\r\n\r\n        // Prioritize direct API connection instead of MCP\r\n        const forceMcpMode = false;\r\n\r\n        // Initialize the Vapi service manager with appropriate options\r\n        await vapiServiceManager.initialize(apiKey, false, {\r\n          isAttorneyDashboard,\r\n          isPreview,\r\n          isProduction,\r\n          forceMcpMode\r\n        });\r\n\r\n        // Log connection status\r\n        const status = vapiServiceManager.getConnectionStatus();\r\n        console.log('[initAttorneyProfileManager] Vapi service manager initialized:', status);\r\n\r\n        // Check for connection warnings\r\n        const connectionWarning = vapiServiceManager.getConnectionWarning();\r\n        if (connectionWarning) {\r\n          console.warn('[initAttorneyProfileManager] Vapi connection warning:', connectionWarning);\r\n\r\n          // Only show warning in the dashboard, not in preview or demo pages\r\n          if (isAttorneyDashboard && !window.location.pathname.includes('/demo')) {\r\n            // Use setTimeout to ensure DOM is ready\r\n            setTimeout(() => {\r\n              try {\r\n                // Create or update warning banner\r\n                let warningBanner = document.getElementById('voice-connection-warning');\r\n                if (!warningBanner) {\r\n                  warningBanner = document.createElement('div');\r\n                  warningBanner.id = 'voice-connection-warning';\r\n                  warningBanner.style.backgroundColor = '#FFF3CD';\r\n                  warningBanner.style.color = '#856404';\r\n                  warningBanner.style.padding = '10px';\r\n                  warningBanner.style.margin = '10px 0';\r\n                  warningBanner.style.borderRadius = '4px';\r\n                  warningBanner.style.border = '1px solid #FFEEBA';\r\n                  warningBanner.style.position = 'fixed';\r\n                  warningBanner.style.top = '10px';\r\n                  warningBanner.style.right = '10px';\r\n                  warningBanner.style.zIndex = '9999';\r\n                  warningBanner.style.maxWidth = '400px';\r\n                  document.body.appendChild(warningBanner);\r\n                }\r\n                warningBanner.textContent = connectionWarning;\r\n              } catch (domError) {\r\n                console.error('[initAttorneyProfileManager] Error showing warning banner:', domError);\r\n              }\r\n            }, 1000);\r\n          } else {\r\n            // For demo and preview pages, just log the warning without showing a banner\r\n            console.log('[initAttorneyProfileManager] Voice service warning (hidden from UI):', connectionWarning);\r\n          }\r\n        }\r\n\r\n        if (status.useMock) {\r\n          if (isAttorneyDashboard || isPreview) {\r\n            console.warn('[initAttorneyProfileManager] Using mock Vapi service in attorney dashboard/preview mode');\r\n          } else {\r\n            console.log('[initAttorneyProfileManager] Using mock Vapi service');\r\n          }\r\n        } else {\r\n          console.log(`[initAttorneyProfileManager] Connected to Vapi using ${status.connectionMode} mode`);\r\n        }\r\n      } catch (error) {\r\n        console.warn('[initAttorneyProfileManager] Failed to initialize Vapi service manager:', error);\r\n        console.log('[initAttorneyProfileManager] Continuing with initialization despite Vapi connection failure');\r\n      }\r\n    } else {\r\n      console.warn('[initAttorneyProfileManager] No Vapi API key found');\r\n    }\r\n\r\n    // Auto-initialize from localStorage if available\r\n    const storedAttorney = localStorage.getItem('attorney');\r\n    if (storedAttorney) {\r\n      try {\r\n        const attorney = JSON.parse(storedAttorney);\r\n        if (attorney && attorney.id) {\r\n          console.log('[initAttorneyProfileManager] Found attorney in localStorage:', attorney.id);\r\n\r\n          // The AttorneyProfileManager will handle the rest of the initialization\r\n          // in its constructor via autoInitializeFromLocalStorage()\r\n        }\r\n      } catch (error) {\r\n        console.error('[initAttorneyProfileManager] Error parsing stored attorney:', error);\r\n      }\r\n    }\r\n\r\n    console.log('[initAttorneyProfileManager] Initialization complete');\r\n  } catch (error) {\r\n    console.error('[initAttorneyProfileManager] Initialization error:', error);\r\n  }\r\n};\r\n\r\n// Auto-initialize when this module is imported\r\ninitAttorneyProfileManager();\r\n\r\nexport default initAttorneyProfileManager;\r\n", "/**\r\n * Vapi MCP Debugger\r\n * \r\n * A focused debugging utility for Vapi MCP server integration.\r\n * This module provides tools for:\r\n * - Structured logging for MCP connection issues\r\n * - Network request/response logging for API calls\r\n * - Environment variable verification\r\n * - Connection status tracking\r\n */\r\n\r\n// Default configuration\r\nconst DEFAULT_CONFIG = {\r\n  enabled: true,\r\n  logToConsole: true,\r\n  logToMemory: true,\r\n  maxLogEntries: 100,\r\n  networkLogging: {\r\n    enabled: true,\r\n    includeHeaders: true,\r\n    includeBody: true,\r\n    maxBodyLength: 500\r\n  }\r\n};\r\n\r\n// In-memory log storage\r\nconst connectionLogs = [];\r\nconst networkLogs = [];\r\nconst errors = [];\r\n\r\n// Current configuration\r\nlet config = { ...DEFAULT_CONFIG };\r\n\r\n/**\r\n * Configure the Vapi MCP debugger\r\n * @param {Object} newConfig - Configuration options\r\n */\r\nexport const configureDebugger = (newConfig = {}) => {\r\n  config = { ...DEFAULT_CONFIG, ...newConfig };\r\n  \r\n  // Make configuration available globally for debugging\r\n  if (typeof window !== 'undefined') {\r\n    window.VapiMcpDebugger = {\r\n      config,\r\n      connectionLogs,\r\n      networkLogs,\r\n      errors,\r\n      getLogs: () => [...connectionLogs],\r\n      getNetworkLogs: () => [...networkLogs],\r\n      getErrors: () => [...errors],\r\n      clearLogs: () => {\r\n        connectionLogs.length = 0;\r\n        console.log('Vapi MCP connection logs cleared');\r\n      },\r\n      clearNetworkLogs: () => {\r\n        networkLogs.length = 0;\r\n        console.log('Vapi MCP network logs cleared');\r\n      },\r\n      clearErrors: () => {\r\n        errors.length = 0;\r\n        console.log('Vapi MCP errors cleared');\r\n      },\r\n      clearAll: () => {\r\n        connectionLogs.length = 0;\r\n        networkLogs.length = 0;\r\n        errors.length = 0;\r\n        console.log('All Vapi MCP logs cleared');\r\n      },\r\n      // Utility to check environment variables\r\n      checkEnvironment: () => checkVapiEnvironment(),\r\n      // Utility to test connection\r\n      testConnection: async () => {\r\n        try {\r\n          const result = await testVapiMcpConnection();\r\n          console.log('Vapi MCP connection test result:', result);\r\n          return result;\r\n        } catch (error) {\r\n          console.error('Vapi MCP connection test failed:', error);\r\n          return { success: false, error: error.message };\r\n        }\r\n      }\r\n    };\r\n  }\r\n};\r\n\r\n/**\r\n * Log a connection event\r\n * @param {string} status - Connection status\r\n * @param {string} message - Log message\r\n * @param {Object} details - Additional details\r\n */\r\nexport const logConnection = (status, message, details = {}) => {\r\n  if (!config.enabled) return;\r\n  \r\n  const entry = {\r\n    timestamp: new Date().toISOString(),\r\n    status,\r\n    message,\r\n    details: { ...details }\r\n  };\r\n  \r\n  // Add to logs\r\n  connectionLogs.push(entry);\r\n  \r\n  // Trim logs if they exceed max entries\r\n  if (connectionLogs.length > config.maxLogEntries) {\r\n    connectionLogs.splice(0, connectionLogs.length - config.maxLogEntries);\r\n  }\r\n  \r\n  // Log to console\r\n  if (config.logToConsole) {\r\n    const statusColor = \r\n      status === 'success' ? 'color: green; font-weight: bold' :\r\n      status === 'error' ? 'color: red; font-weight: bold' :\r\n      status === 'warning' ? 'color: orange; font-weight: bold' :\r\n      'color: blue; font-weight: bold';\r\n    \r\n    console.groupCollapsed(`%c[Vapi MCP] ${status.toUpperCase()}: ${message}`, statusColor);\r\n    console.log('Details:', details);\r\n    console.log('Timestamp:', entry.timestamp);\r\n    console.groupEnd();\r\n  }\r\n  \r\n  // Add to errors if it's an error\r\n  if (status === 'error') {\r\n    errors.push(entry);\r\n  }\r\n};\r\n\r\n/**\r\n * Log a network request or response\r\n * @param {string} type - 'request' or 'response'\r\n * @param {string} url - Request URL\r\n * @param {string} method - HTTP method\r\n * @param {number} status - HTTP status code (for responses)\r\n * @param {Object} headers - HTTP headers\r\n * @param {Object|string} body - Request or response body\r\n */\r\nexport const logNetwork = (type, url, method, status, headers, body) => {\r\n  if (!config.enabled || !config.networkLogging.enabled) return;\r\n  \r\n  const entry = {\r\n    timestamp: new Date().toISOString(),\r\n    type,\r\n    url,\r\n    method,\r\n    status,\r\n    headers: config.networkLogging.includeHeaders ? headers : undefined,\r\n    body: config.networkLogging.includeBody ? (\r\n      typeof body === 'string' \r\n        ? (body.length > config.networkLogging.maxBodyLength \r\n            ? body.substring(0, config.networkLogging.maxBodyLength) + '...' \r\n            : body)\r\n        : body\r\n    ) : undefined\r\n  };\r\n  \r\n  // Add to logs\r\n  networkLogs.push(entry);\r\n  \r\n  // Trim logs if they exceed max entries\r\n  if (networkLogs.length > config.maxLogEntries) {\r\n    networkLogs.splice(0, networkLogs.length - config.maxLogEntries);\r\n  }\r\n  \r\n  // Log to console\r\n  if (config.logToConsole) {\r\n    const typeColor = type === 'request' ? 'color: blue' : 'color: green';\r\n    const statusColor = \r\n      (status >= 200 && status < 300) ? 'color: green' :\r\n      (status >= 400) ? 'color: red' :\r\n      'color: orange';\r\n    \r\n    if (type === 'request') {\r\n      console.groupCollapsed(`%c[Vapi MCP] ${method} ${url}`, typeColor);\r\n      console.log('Headers:', headers);\r\n      console.log('Body:', body);\r\n      console.groupEnd();\r\n    } else {\r\n      console.groupCollapsed(`%c[Vapi MCP] Response: %c${status}%c ${url}`, typeColor, statusColor, 'color: black');\r\n      console.log('Headers:', headers);\r\n      console.log('Body:', body);\r\n      console.groupEnd();\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Check Vapi environment variables\r\n * @returns {Object} Environment status\r\n */\r\nexport const checkVapiEnvironment = () => {\r\n  const environment = {\r\n    VITE_VAPI_PUBLIC_KEY: typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY ? 'Set' : 'Not set',\r\n    VITE_VAPI_SECRET_KEY: typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_SECRET_KEY ? 'Set' : 'Not set',\r\n    VAPI_TOKEN: typeof process !== 'undefined' && process.env?.VAPI_TOKEN ? 'Set' : 'Not set',\r\n    localStorage_vapi_api_key: typeof localStorage !== 'undefined' && localStorage.getItem('vapi_api_key') ? 'Set' : 'Not set',\r\n    isDevelopment: typeof import.meta !== 'undefined' && import.meta.env?.MODE === 'development',\r\n    origin: typeof window !== 'undefined' ? window.location.origin : 'Unknown'\r\n  };\r\n  \r\n  // Log to console\r\n  if (config.logToConsole) {\r\n    console.group('[Vapi MCP] Environment Check');\r\n    console.table(environment);\r\n    console.groupEnd();\r\n  }\r\n  \r\n  return environment;\r\n};\r\n\r\n/**\r\n * Test Vapi MCP connection\r\n * @returns {Promise<Object>} Connection test result\r\n */\r\nexport const testVapiMcpConnection = async () => {\r\n  const apiKey = (typeof import.meta !== 'undefined' && import.meta.env?.VITE_VAPI_PUBLIC_KEY) ||\r\n                (typeof window !== 'undefined' && window.VITE_VAPI_PUBLIC_KEY) ||\r\n                (typeof localStorage !== 'undefined' && localStorage.getItem('vapi_api_key'));\r\n  \r\n  if (!apiKey) {\r\n    logConnection('error', 'No API key available for connection test');\r\n    return { success: false, error: 'No API key available' };\r\n  }\r\n  \r\n  // Test direct connection to Vapi API\r\n  try {\r\n    logConnection('info', 'Testing direct connection to Vapi API', { apiKey: `${apiKey.substring(0, 5)}...` });\r\n    \r\n    const endpoints = [\r\n      'https://api.vapi.ai/v1/assistants?limit=1',\r\n      'https://api.vapi.ai/assistants?limit=1',\r\n      'https://api.vapi.ai/api/v1/assistants?limit=1',\r\n      'https://api.vapi.ai/api/assistants?limit=1'\r\n    ];\r\n    \r\n    for (const endpoint of endpoints) {\r\n      try {\r\n        logNetwork('request', endpoint, 'GET', null, { 'Authorization': `Bearer ${apiKey}` }, null);\r\n        \r\n        const response = await fetch(endpoint, {\r\n          method: 'GET',\r\n          headers: {\r\n            'Authorization': `Bearer ${apiKey}`,\r\n            'Content-Type': 'application/json',\r\n            'Accept': 'application/json'\r\n          }\r\n        });\r\n        \r\n        const responseBody = await response.json();\r\n        \r\n        logNetwork('response', endpoint, 'GET', response.status, response.headers, responseBody);\r\n        \r\n        if (response.ok) {\r\n          logConnection('success', `Direct connection successful with endpoint: ${endpoint}`);\r\n          return { \r\n            success: true, \r\n            endpoint, \r\n            mode: 'direct',\r\n            status: response.status,\r\n            data: responseBody\r\n          };\r\n        } else {\r\n          logConnection('warning', `Endpoint ${endpoint} returned status: ${response.status}`);\r\n        }\r\n      } catch (error) {\r\n        logConnection('warning', `Error with endpoint ${endpoint}`, { error: error.message });\r\n      }\r\n    }\r\n    \r\n    logConnection('error', 'All direct API endpoints failed');\r\n    return { success: false, error: 'All direct API endpoints failed', mode: 'direct' };\r\n  } catch (error) {\r\n    logConnection('error', 'Error testing Vapi connection', { error: error.message });\r\n    return { success: false, error: error.message };\r\n  }\r\n};\r\n\r\n// Initialize the debugger\r\nconfigureDebugger();\r\n\r\n// Export default instance\r\nexport default {\r\n  configureDebugger,\r\n  logConnection,\r\n  logNetwork,\r\n  checkVapiEnvironment,\r\n  testVapiMcpConnection\r\n};\r\n", "/**\r\n * Vapi Network Interceptor\r\n * \r\n * This module intercepts network requests to Vapi API endpoints\r\n * and logs them for debugging purposes.\r\n */\r\n\r\nimport { logNetwork } from './vapiMcpDebugger';\r\n\r\n// Original fetch function\r\nconst originalFetch = window.fetch;\r\n\r\n/**\r\n * Install the network interceptor\r\n */\r\nexport const installVapiNetworkInterceptor = () => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  // Check if disabled by clean auth solution\r\n  if (window.__VAPI_NETWORK_INTERCEPTOR_DISABLED) {\r\n    console.log('[Vapi Network Interceptor] 🚫 Disabled by clean auth solution');\r\n    return;\r\n  }\r\n\r\n  // Check if already installed\r\n  if (window.vapiNetworkInterceptorInstalled) return;\r\n  \r\n  // Override fetch\r\n  window.fetch = async function(url, options = {}) {\r\n    // Check if this is a Vapi API request\r\n    const isVapiRequest = typeof url === 'string' && (\r\n      url.includes('api.vapi.ai') || \r\n      url.includes('public.vapi.ai') ||\r\n      url.includes('vapi-mcp-server') ||\r\n      url.includes('vapi-mcp')\r\n    );\r\n    \r\n    if (!isVapiRequest) {\r\n      // Not a Vapi request, use original fetch\r\n      return originalFetch.apply(this, arguments);\r\n    }\r\n    \r\n    // Log request\r\n    const method = options.method || 'GET';\r\n    const headers = options.headers || {};\r\n    const body = options.body;\r\n    \r\n    logNetwork('request', url, method, null, headers, body);\r\n    \r\n    try {\r\n      // Make the request\r\n      const response = await originalFetch.apply(this, arguments);\r\n      \r\n      // Clone the response to avoid consuming it\r\n      const clonedResponse = response.clone();\r\n      \r\n      // Try to parse the response body\r\n      let responseBody;\r\n      try {\r\n        responseBody = await clonedResponse.text();\r\n        \r\n        // Try to parse as JSON\r\n        try {\r\n          responseBody = JSON.parse(responseBody);\r\n        } catch (e) {\r\n          // Not JSON, keep as text\r\n        }\r\n      } catch (e) {\r\n        responseBody = 'Unable to read response body';\r\n      }\r\n      \r\n      // Log response\r\n      logNetwork(\r\n        'response',\r\n        url,\r\n        method,\r\n        response.status,\r\n        Object.fromEntries([...response.headers.entries()]),\r\n        responseBody\r\n      );\r\n      \r\n      return response;\r\n    } catch (error) {\r\n      // Log error\r\n      logNetwork(\r\n        'response',\r\n        url,\r\n        method,\r\n        0,\r\n        {},\r\n        { error: error.message }\r\n      );\r\n      \r\n      throw error;\r\n    }\r\n  };\r\n  \r\n  // Mark as installed\r\n  window.vapiNetworkInterceptorInstalled = true;\r\n  \r\n  console.log('[Vapi Network Interceptor] Installed');\r\n};\r\n\r\n/**\r\n * Uninstall the network interceptor\r\n */\r\nexport const uninstallVapiNetworkInterceptor = () => {\r\n  if (typeof window === 'undefined') return;\r\n  \r\n  // Check if installed\r\n  if (!window.vapiNetworkInterceptorInstalled) return;\r\n  \r\n  // Restore original fetch\r\n  window.fetch = originalFetch;\r\n  \r\n  // Mark as uninstalled\r\n  window.vapiNetworkInterceptorInstalled = false;\r\n  \r\n  console.log('[Vapi Network Interceptor] Uninstalled');\r\n};\r\n\r\n// Export default\r\nexport default {\r\n  installVapiNetworkInterceptor,\r\n  uninstallVapiNetworkInterceptor\r\n};\r\n", "// Import React polyfill first to ensure it's available for all dependencies\r\nimport './utils/reactPolyfill.js';\r\n\r\n// 💀 REMOVED: headers-fix causing duplicate Content-Type headers\r\n// import './utils/headers-fix.js';\r\n\r\nimport React from 'react'\r\nimport ReactDOM from 'react-dom/client'\r\nimport { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom'\r\nimport App from './App.jsx'\r\nimport './index.css'\r\nimport 'leaflet/dist/leaflet.css'\r\nimport './fixes/interactionFix.css' // Import the interaction fix CSS\r\nimport ErrorBoundary from './utils/ErrorBoundary.jsx'\r\nimport ProductionErrorBoundary from './components/ProductionErrorBoundary.jsx'\r\nimport SyncAuthProvider from './components/SyncAuthProvider.jsx'\r\nimport { ThemeProvider } from './contexts/ThemeContext.jsx'\r\nimport { AttorneyStateProvider } from './contexts/AttorneyStateContext.jsx'\r\n\r\n// Import schema generator to make it available globally\r\nimport './utils/schemaGenerator.js'\r\n\r\n// Import and initialize environment verification\r\nimport { initEnvironmentVerification } from './utils/environmentVerifier.js'\r\n\r\n// Import and initialize attorney profile manager\r\nimport './utils/initAttorneyProfileManager.js'\r\n\r\n// Import and initialize Vapi debugger\r\nimport { initVapiDebugger } from './utils/initVapiDebugger.js'\r\n\r\n// Verify environment variables early\r\ninitEnvironmentVerification();\r\n\r\n// Initialize Vapi debugger in development mode - safely check environment\r\ntry {\r\n  if (import.meta.env.DEV || import.meta.env.MODE === 'development') {\r\n    initVapiDebugger();\r\n  }\r\n} catch (e) {\r\n  // Fallback to checking hostname for development\r\n  if (typeof window !== 'undefined' &&\r\n      (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {\r\n    initVapiDebugger();\r\n  }\r\n}\r\n\r\nReactDOM.createRoot(document.getElementById('root')).render(\r\n  <React.StrictMode>\r\n    <ProductionErrorBoundary>\r\n      <ErrorBoundary showDetails={true} onReset={() => window.location.reload()}>\r\n        <BrowserRouter>\r\n          <ThemeProvider>\r\n            <AttorneyStateProvider>\r\n              <SyncAuthProvider>\r\n                <App />\r\n              </SyncAuthProvider>\r\n            </AttorneyStateProvider>\r\n          </ThemeProvider>\r\n        </BrowserRouter>\r\n      </ErrorBoundary>\r\n    </ProductionErrorBoundary>\r\n  </React.StrictMode>\r\n)"], "file": "assets/index-43b09a69.js"}