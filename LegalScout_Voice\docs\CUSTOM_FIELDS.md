# Custom Fields Documentation

## Overview

The Custom Fields feature allows attorneys to define custom data collection fields for their AI assistant. These fields enable attorneys to gather specific information from potential clients during consultations, tailored to their practice areas and requirements.

## Key Features

1. **Field Creation and Management**
   - Create, edit, and delete custom fields
   - Reorder fields to control the flow of data collection
   - Group fields into logical sections

2. **Field Types**
   - Text input (short and long form)
   - Multiple choice (single and multiple selection)
   - Date picker
   - Numeric input
   - File upload

3. **Field Validation**
   - Required fields
   - Format validation (email, phone, etc.)
   - Custom validation rules

4. **Integration with AI Assistant**
   - Dynamic prompting based on field definitions
   - Structured data collection
   - Conditional field display

## Component Architecture

The Custom Fields feature is implemented through several components:

```
CustomFieldsTab
├── FieldList
│   ├── FieldItem
│   └── ReorderControls
├── FieldEditor
│   ├── FieldTypeSelector
│   ├── ValidationOptions
│   └── ConditionalLogic
└── FieldPreview
```

## Database Schema

Custom fields are stored in the Supabase database:

```sql
CREATE TABLE IF NOT EXISTS public.custom_fields (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Relationship
  attorney_id UUID REFERENCES public.attorneys(id) NOT NULL,
  
  -- Field Configuration
  field_name TEXT NOT NULL,
  field_type TEXT NOT NULL,
  field_label TEXT NOT NULL,
  field_description TEXT,
  placeholder TEXT,
  
  -- Validation
  is_required BOOLEAN DEFAULT false,
  validation_rules JSONB,
  
  -- Options (for multiple choice fields)
  options JSONB,
  
  -- Display
  display_order INTEGER NOT NULL,
  section TEXT,
  
  -- Conditional Logic
  conditional_logic JSONB,
  
  -- Metadata
  metadata JSONB
);

-- Index for faster queries
CREATE INDEX IF NOT EXISTS custom_fields_attorney_id_idx ON public.custom_fields (attorney_id);
```

## Implementation Details

### CustomFieldsTab Component

The `CustomFieldsTab` component is the main interface for managing custom fields:

```jsx
// src/components/dashboard/CustomFieldsTab.jsx
const CustomFieldsTab = ({ attorney, onUpdate }) => {
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingField, setEditingField] = useState(null);
  
  // Load fields from database
  useEffect(() => {
    const loadFields = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('custom_fields')
          .select('*')
          .eq('attorney_id', attorney.id)
          .order('display_order', { ascending: true });
        
        if (error) throw error;
        setFields(data || []);
      } catch (error) {
        console.error('Error loading custom fields:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadFields();
  }, [attorney.id]);
  
  // Save field to database
  const saveField = async (field) => {
    try {
      if (field.id) {
        // Update existing field
        const { error } = await supabase
          .from('custom_fields')
          .update({
            field_name: field.field_name,
            field_type: field.field_type,
            field_label: field.field_label,
            field_description: field.field_description,
            placeholder: field.placeholder,
            is_required: field.is_required,
            validation_rules: field.validation_rules,
            options: field.options,
            section: field.section,
            conditional_logic: field.conditional_logic,
            updated_at: new Date()
          })
          .eq('id', field.id);
        
        if (error) throw error;
      } else {
        // Create new field
        const { error } = await supabase
          .from('custom_fields')
          .insert([{
            attorney_id: attorney.id,
            field_name: field.field_name,
            field_type: field.field_type,
            field_label: field.field_label,
            field_description: field.field_description,
            placeholder: field.placeholder,
            is_required: field.is_required,
            validation_rules: field.validation_rules,
            options: field.options,
            display_order: fields.length,
            section: field.section,
            conditional_logic: field.conditional_logic
          }]);
        
        if (error) throw error;
      }
      
      // Reload fields
      loadFields();
      
      // Notify parent component of update
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error saving custom field:', error);
    }
  };
  
  // Delete field from database
  const deleteField = async (fieldId) => {
    try {
      const { error } = await supabase
        .from('custom_fields')
        .delete()
        .eq('id', fieldId);
      
      if (error) throw error;
      
      // Reload fields
      loadFields();
      
      // Notify parent component of update
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error deleting custom field:', error);
    }
  };
  
  // Reorder fields
  const reorderFields = async (newOrder) => {
    try {
      // Update display_order for each field
      const updates = newOrder.map((field, index) => ({
        id: field.id,
        display_order: index
      }));
      
      // Update each field in the database
      for (const update of updates) {
        const { error } = await supabase
          .from('custom_fields')
          .update({ display_order: update.display_order })
          .eq('id', update.id);
        
        if (error) throw error;
      }
      
      // Reload fields
      loadFields();
      
      // Notify parent component of update
      onUpdate && onUpdate();
    } catch (error) {
      console.error('Error reordering custom fields:', error);
    }
  };
  
  // Render component
  return (
    <div className="custom-fields-tab">
      <h2>Custom Fields</h2>
      <p>Define custom fields to collect specific information from potential clients.</p>
      
      {loading ? (
        <div className="loading-spinner">Loading...</div>
      ) : (
        <>
          <FieldList
            fields={fields}
            onEdit={setEditingField}
            onDelete={deleteField}
            onReorder={reorderFields}
          />
          
          <button
            className="add-field-button"
            onClick={() => setEditingField({
              field_name: '',
              field_type: 'text',
              field_label: '',
              field_description: '',
              placeholder: '',
              is_required: false,
              validation_rules: {},
              options: {},
              section: 'General',
              conditional_logic: {}
            })}
          >
            Add New Field
          </button>
          
          {editingField && (
            <FieldEditor
              field={editingField}
              onSave={saveField}
              onCancel={() => setEditingField(null)}
              existingFields={fields}
            />
          )}
        </>
      )}
    </div>
  );
};
```

### Field Types and Configuration

Each field type has specific configuration options:

```javascript
const FIELD_TYPES = {
  TEXT: {
    id: 'text',
    label: 'Text Input',
    icon: 'text-field',
    options: {
      multiline: false,
      maxLength: null
    }
  },
  TEXTAREA: {
    id: 'textarea',
    label: 'Long Text',
    icon: 'text-box',
    options: {
      rows: 4,
      maxLength: null
    }
  },
  SELECT: {
    id: 'select',
    label: 'Dropdown',
    icon: 'menu-down',
    options: {
      choices: [],
      allowMultiple: false
    }
  },
  RADIO: {
    id: 'radio',
    label: 'Radio Buttons',
    icon: 'radiobox-marked',
    options: {
      choices: []
    }
  },
  CHECKBOX: {
    id: 'checkbox',
    label: 'Checkboxes',
    icon: 'checkbox-marked',
    options: {
      choices: []
    }
  },
  DATE: {
    id: 'date',
    label: 'Date Picker',
    icon: 'calendar',
    options: {
      format: 'MM/DD/YYYY',
      minDate: null,
      maxDate: null
    }
  },
  NUMBER: {
    id: 'number',
    label: 'Number',
    icon: 'numeric',
    options: {
      min: null,
      max: null,
      step: 1
    }
  },
  FILE: {
    id: 'file',
    label: 'File Upload',
    icon: 'file-upload',
    options: {
      acceptedTypes: '.pdf,.doc,.docx',
      maxSize: 5 // MB
    }
  }
};
```

### Field Editor Component

The `FieldEditor` component allows attorneys to configure fields:

```jsx
const FieldEditor = ({ field, onSave, onCancel, existingFields }) => {
  const [formData, setFormData] = useState(field);
  
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  
  const handleTypeChange = (type) => {
    setFormData({
      ...formData,
      field_type: type,
      options: FIELD_TYPES[type].options
    });
  };
  
  const handleOptionsChange = (options) => {
    setFormData({
      ...formData,
      options
    });
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };
  
  return (
    <div className="field-editor">
      <h3>{field.id ? 'Edit Field' : 'Add New Field'}</h3>
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="field_label">Field Label</label>
          <input
            type="text"
            id="field_label"
            name="field_label"
            value={formData.field_label}
            onChange={handleChange}
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="field_name">Field Name</label>
          <input
            type="text"
            id="field_name"
            name="field_name"
            value={formData.field_name}
            onChange={handleChange}
            required
          />
          <small>Used as the identifier for this field. No spaces or special characters.</small>
        </div>
        
        <div className="form-group">
          <label>Field Type</label>
          <FieldTypeSelector
            selectedType={formData.field_type}
            onChange={handleTypeChange}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="field_description">Description</label>
          <textarea
            id="field_description"
            name="field_description"
            value={formData.field_description}
            onChange={handleChange}
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="placeholder">Placeholder</label>
          <input
            type="text"
            id="placeholder"
            name="placeholder"
            value={formData.placeholder}
            onChange={handleChange}
          />
        </div>
        
        <div className="form-group">
          <label>
            <input
              type="checkbox"
              name="is_required"
              checked={formData.is_required}
              onChange={handleChange}
            />
            Required Field
          </label>
        </div>
        
        {/* Field-specific options */}
        <FieldOptions
          type={formData.field_type}
          options={formData.options}
          onChange={handleOptionsChange}
        />
        
        <div className="form-group">
          <label htmlFor="section">Section</label>
          <input
            type="text"
            id="section"
            name="section"
            value={formData.section}
            onChange={handleChange}
          />
        </div>
        
        {/* Conditional logic */}
        <ConditionalLogic
          logic={formData.conditional_logic}
          onChange={(logic) => setFormData({ ...formData, conditional_logic: logic })}
          fields={existingFields}
        />
        
        <div className="button-group">
          <button type="submit" className="save-button">Save Field</button>
          <button type="button" className="cancel-button" onClick={onCancel}>Cancel</button>
        </div>
      </form>
    </div>
  );
};
```

## Integration with AI Assistant

### Schema Generation

Custom fields are converted to a schema for the AI assistant:

```javascript
// src/utils/schemaGenerator.js
export const generateSchemaFromCustomFields = (fields) => {
  const schema = {
    type: 'object',
    properties: {},
    required: []
  };
  
  // Group fields by section
  const sections = {};
  fields.forEach(field => {
    const section = field.section || 'General';
    if (!sections[section]) {
      sections[section] = [];
    }
    sections[section].push(field);
  });
  
  // Create schema properties for each section
  Object.entries(sections).forEach(([sectionName, sectionFields]) => {
    schema.properties[sectionName] = {
      type: 'object',
      properties: {},
      required: []
    };
    
    // Add fields to section
    sectionFields.forEach(field => {
      const property = {
        title: field.field_label,
        description: field.field_description
      };
      
      // Set property type based on field type
      switch (field.field_type) {
        case 'text':
        case 'textarea':
          property.type = 'string';
          break;
        case 'number':
          property.type = 'number';
          break;
        case 'date':
          property.type = 'string';
          property.format = 'date';
          break;
        case 'select':
        case 'radio':
          property.type = 'string';
          property.enum = field.options.choices.map(choice => choice.value);
          break;
        case 'checkbox':
          property.type = 'array';
          property.items = {
            type: 'string',
            enum: field.options.choices.map(choice => choice.value)
          };
          break;
        case 'file':
          property.type = 'string';
          property.format = 'uri';
          break;
        default:
          property.type = 'string';
      }
      
      // Add validation rules
      if (field.validation_rules) {
        Object.entries(field.validation_rules).forEach(([rule, value]) => {
          property[rule] = value;
        });
      }
      
      // Add property to section
      schema.properties[sectionName].properties[field.field_name] = property;
      
      // Add to required fields if necessary
      if (field.is_required) {
        schema.properties[sectionName].required.push(field.field_name);
      }
    });
    
    // If section has required fields, add section to required
    if (schema.properties[sectionName].required.length > 0) {
      schema.required.push(sectionName);
    }
  });
  
  return schema;
};
```

### AI Assistant Configuration

The schema is included in the Vapi assistant configuration:

```javascript
// src/services/vapiAssistantService.js
const createAssistantForAttorney = async (attorneyData) => {
  try {
    // Get custom fields for attorney
    const { data: customFields, error } = await supabase
      .from('custom_fields')
      .select('*')
      .eq('attorney_id', attorneyData.id)
      .order('display_order', { ascending: true });
    
    if (error) {
      console.error('Error fetching custom fields:', error);
      customFields = [];
    }
    
    // Generate schema from custom fields
    const structuredDataSchema = generateSchemaFromCustomFields(customFields);
    
    // Create assistant configuration
    const assistantConfig = {
      name: `${attorneyData.firm_name} Assistant`,
      instructions: attorneyData.vapi_instructions || DEFAULT_INSTRUCTIONS,
      firstMessage: attorneyData.welcome_message || DEFAULT_WELCOME_MESSAGE,
      firstMessageMode: "assistant-speaks-first",
      llm: {
        provider: "openai",
        model: attorneyData.ai_model || "gpt-4o"
      },
      voice: {
        provider: attorneyData.voice_provider || "playht",
        voiceId: attorneyData.voice_id || "sarah"
      },
      transcriber: {
        provider: "deepgram",
        model: "nova-3"
      },
      analysis: {
        summary: {
          prompt: attorneyData.summary_prompt || DEFAULT_SUMMARY_PROMPT
        },
        structuredData: {
          prompt: attorneyData.structured_data_prompt || DEFAULT_STRUCTURED_DATA_PROMPT,
          schema: structuredDataSchema
        }
      }
    };
    
    // Create the assistant
    const assistant = await vapiMcpService.createAssistant(assistantConfig);
    
    return assistant;
  } catch (error) {
    console.error('Error creating assistant for attorney:', error);
    throw error;
  }
};
```

## Data Collection Flow

The AI assistant collects data based on the custom fields:

1. **Initial Greeting**: Assistant introduces itself and explains the purpose of the consultation
2. **Information Gathering**: Assistant asks questions based on custom fields
3. **Structured Data Collection**: Assistant collects and validates data according to field definitions
4. **Conditional Logic**: Assistant shows or hides fields based on previous answers
5. **Data Submission**: Collected data is stored in structured format

## Practice Area Templates

The system includes predefined templates for common practice areas:

```javascript
// src/data/practiceAreaTemplates.js
export const PRACTICE_AREA_TEMPLATES = {
  'Personal Injury': [
    {
      field_name: 'injury_type',
      field_type: 'select',
      field_label: 'Type of Injury',
      field_description: 'What type of injury did you sustain?',
      is_required: true,
      options: {
        choices: [
          { label: 'Car Accident', value: 'car_accident' },
          { label: 'Slip and Fall', value: 'slip_and_fall' },
          { label: 'Medical Malpractice', value: 'medical_malpractice' },
          { label: 'Workplace Injury', value: 'workplace_injury' },
          { label: 'Product Liability', value: 'product_liability' },
          { label: 'Other', value: 'other' }
        ]
      },
      section: 'Injury Information'
    },
    {
      field_name: 'injury_date',
      field_type: 'date',
      field_label: 'Date of Injury',
      field_description: 'When did the injury occur?',
      is_required: true,
      options: {
        format: 'MM/DD/YYYY',
        maxDate: 'today'
      },
      section: 'Injury Information'
    },
    // More fields...
  ],
  'Family Law': [
    // Family law fields...
  ],
  'Criminal Defense': [
    // Criminal defense fields...
  ],
  // More practice areas...
};
```

## User Interface

### Field List

The `FieldList` component displays the list of custom fields:

```jsx
const FieldList = ({ fields, onEdit, onDelete, onReorder }) => {
  // Drag and drop functionality
  const [items, setItems] = useState(fields);
  
  useEffect(() => {
    setItems(fields);
  }, [fields]);
  
  const handleDragEnd = (result) => {
    if (!result.destination) return;
    
    const reorderedItems = Array.from(items);
    const [removed] = reorderedItems.splice(result.source.index, 1);
    reorderedItems.splice(result.destination.index, 0, removed);
    
    setItems(reorderedItems);
    onReorder(reorderedItems);
  };
  
  // Group fields by section
  const sections = {};
  items.forEach(field => {
    const section = field.section || 'General';
    if (!sections[section]) {
      sections[section] = [];
    }
    sections[section].push(field);
  });
  
  return (
    <div className="field-list">
      <DragDropContext onDragEnd={handleDragEnd}>
        {Object.entries(sections).map(([sectionName, sectionFields]) => (
          <div key={sectionName} className="field-section">
            <h3>{sectionName}</h3>
            <Droppable droppableId={sectionName}>
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="field-items"
                >
                  {sectionFields.map((field, index) => (
                    <Draggable
                      key={field.id}
                      draggableId={field.id}
                      index={index}
                    >
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className="field-item"
                        >
                          <div className="field-info">
                            <span className="field-label">{field.field_label}</span>
                            <span className="field-type">{FIELD_TYPES[field.field_type].label}</span>
                          </div>
                          <div className="field-actions">
                            <button
                              className="edit-button"
                              onClick={() => onEdit(field)}
                            >
                              Edit
                            </button>
                            <button
                              className="delete-button"
                              onClick={() => onDelete(field.id)}
                            >
                              Delete
                            </button>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </div>
        ))}
      </DragDropContext>
    </div>
  );
};
```

### Field Preview

The `FieldPreview` component shows a preview of how the field will appear:

```jsx
const FieldPreview = ({ field }) => {
  const renderField = () => {
    switch (field.field_type) {
      case 'text':
        return (
          <input
            type="text"
            placeholder={field.placeholder}
            className="preview-input"
            disabled
          />
        );
      case 'textarea':
        return (
          <textarea
            placeholder={field.placeholder}
            className="preview-textarea"
            rows={field.options.rows || 4}
            disabled
          />
        );
      case 'select':
        return (
          <select className="preview-select" disabled>
            <option value="">{field.placeholder || 'Select an option'}</option>
            {field.options.choices.map((choice) => (
              <option key={choice.value} value={choice.value}>
                {choice.label}
              </option>
            ))}
          </select>
        );
      case 'radio':
        return (
          <div className="preview-radio-group">
            {field.options.choices.map((choice) => (
              <label key={choice.value} className="preview-radio-label">
                <input
                  type="radio"
                  name={`preview-${field.field_name}`}
                  value={choice.value}
                  disabled
                />
                {choice.label}
              </label>
            ))}
          </div>
        );
      case 'checkbox':
        return (
          <div className="preview-checkbox-group">
            {field.options.choices.map((choice) => (
              <label key={choice.value} className="preview-checkbox-label">
                <input
                  type="checkbox"
                  name={`preview-${field.field_name}`}
                  value={choice.value}
                  disabled
                />
                {choice.label}
              </label>
            ))}
          </div>
        );
      case 'date':
        return (
          <input
            type="date"
            placeholder={field.placeholder}
            className="preview-date"
            disabled
          />
        );
      case 'number':
        return (
          <input
            type="number"
            placeholder={field.placeholder}
            className="preview-number"
            min={field.options.min}
            max={field.options.max}
            step={field.options.step}
            disabled
          />
        );
      case 'file':
        return (
          <div className="preview-file">
            <input
              type="file"
              className="preview-file-input"
              disabled
            />
            <small>Accepted formats: {field.options.acceptedTypes}</small>
          </div>
        );
      default:
        return <div>Unknown field type</div>;
    }
  };
  
  return (
    <div className="field-preview">
      <h4>Preview</h4>
      <div className="preview-field">
        <label className="preview-label">
          {field.field_label}
          {field.is_required && <span className="required-indicator">*</span>}
        </label>
        {field.field_description && (
          <p className="preview-description">{field.field_description}</p>
        )}
        {renderField()}
      </div>
    </div>
  );
};
```

## Best Practices

### Field Naming
- Use clear, descriptive names for fields
- Follow a consistent naming convention
- Avoid special characters and spaces in field names

### Field Organization
- Group related fields into sections
- Order fields logically
- Use conditional logic to show/hide fields as needed

### Validation
- Use appropriate validation for each field type
- Provide clear error messages
- Don't over-validate - only enforce necessary rules

### User Experience
- Provide clear labels and descriptions
- Use appropriate field types for different data
- Keep forms as short as possible

## Future Enhancements

Planned enhancements for the Custom Fields feature:

1. **Advanced Conditional Logic**
   - Complex conditions with AND/OR operators
   - Multi-field dependencies
   - Calculated fields

2. **Field Templates**
   - More practice area templates
   - Custom template creation
   - Template sharing between attorneys

3. **Enhanced Validation**
   - Custom validation rules
   - Real-time validation
   - Cross-field validation

4. **Data Analysis**
   - Field usage statistics
   - Completion rate analysis
   - Data visualization

5. **Integration Expansion**
   - CRM integration
   - Document generation
   - Calendar scheduling
