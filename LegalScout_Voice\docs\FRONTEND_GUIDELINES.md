# LegalScout Frontend Development Guidelines

## Component Architecture

### Component Structure
```jsx
// Example component structure
import React from 'react';
import PropTypes from 'prop-types';

const ComponentName = ({ prop1, prop2 }) => {
  // Hooks at the top
  const [state, setState] = useState(initialState);
  
  // Event handlers and business logic
  const handleEvent = () => {
    // Logic here
  };
  
  // Render helpers
  const renderHelper = () => {
    return (/* JSX */);
  };
  
  // Main render
  return (
    <div className="component-name">
      {/* JSX content */}
    </div>
  );
};

ComponentName.propTypes = {
  prop1: PropTypes.string.required,
  prop2: PropTypes.func
};

export default ComponentName;
```

### Directory Structure
```
src/
├── api/              # API integration and service calls
├── components/       # React components
│   ├── __tests__/   # Component tests
│   ├── base/        # Base/common components
│   ├── call/        # Voice call related components
│   └── [feature].jsx # Feature-specific components
├── config/          # Configuration files
├── constants/       # Constants and enums
├── data/           # Static data and mock data
├── hooks/          # Custom React hooks
├── lib/            # Third-party library integrations
├── pages/          # Page components
├── scripts/        # Utility scripts
├── services/       # Business logic services
├── types/          # TypeScript type definitions
└── utils/          # Utility functions
```

### Key Components

#### Voice Interface
- `VapiCall.jsx`: Main voice interface component
- `CallController.jsx`: Call management and control
- `CallSummary.jsx`: Call result display
- `ActiveCallDetail.jsx`: Active call information

#### Map Visualization
- `MapView.jsx`: Base map component
- `MapDossierView.jsx`: Attorney location visualization
- `GlobeDossierView.jsx`: Global view of attorneys

#### Attorney Management
- `AttorneyDossier.jsx`: Attorney profile display
- `LoginButton.jsx`: Authentication handling

#### UI Components
- `Button.jsx`: Reusable button component
- `ThemeToggle.jsx`: Dark/light mode toggle
- `Navbar.jsx`: Navigation component
- `ErrorBoundary.jsx`: Error handling wrapper

## Coding Standards

### Naming Conventions
- **Components**: PascalCase (e.g., `AttorneyCard.jsx`)
- **Files**: Same as component name
- **Functions**: camelCase (e.g., `handleSubmit`)
- **Variables**: camelCase (e.g., `userData`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINT`)
- **CSS Classes**: kebab-case (e.g., `attorney-card`)

### Component Guidelines
1. Keep components focused and single-responsibility
2. Extract reusable logic into custom hooks
3. Use TypeScript for type safety
4. Implement proper error boundaries
5. Document props with PropTypes or TypeScript

### State Management
1. Use React Context for global state
2. Keep state as close to usage as possible
3. Implement proper state initialization
4. Handle loading and error states
5. Use reducers for complex state logic

## Styling Guidelines

### TailwindCSS Usage
```jsx
// Preferred
<div className="flex items-center justify-between p-4 bg-white shadow-md">

// Avoid
<div className="custom-container">
```

### Custom CSS Rules
1. Use Tailwind utilities when possible
2. Create custom utilities for repeated patterns
3. Use CSS modules for component-specific styles
4. Follow BEM naming for custom classes
5. Maintain consistent spacing units

## Performance Optimization

### Code Splitting
```jsx
// Lazy loading components
const HeavyComponent = React.lazy(() => import('./HeavyComponent'));

// Usage
<Suspense fallback={<Loading />}>
  <HeavyComponent />
</Suspense>
```

### Rendering Optimization
1. Use React.memo for pure components
2. Implement useMemo for expensive calculations
3. Use useCallback for event handlers
4. Avoid inline styles and functions
5. Optimize images and assets

## Error Handling

### Error Boundaries
```jsx
class ErrorBoundary extends React.Component {
  state = { hasError: false };
  
  static getDerivedStateFromError(error) {
    return { hasError: true };
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback />;
    }
    return this.props.children;
  }
}
```

### Error States
1. Implement proper loading states
2. Show user-friendly error messages
3. Provide recovery options
4. Log errors for debugging
5. Handle network failures gracefully

## Testing Guidelines

### Component Testing
```jsx
describe('ComponentName', () => {
  it('renders correctly', () => {
    render(<ComponentName />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
  
  it('handles user interaction', () => {
    const handleClick = jest.fn();
    render(<ComponentName onClick={handleClick} />);
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalled();
  });
});
```

### Testing Best Practices
1. Test component rendering
2. Test user interactions
3. Test error states
4. Mock external dependencies
5. Maintain high test coverage

## Accessibility Guidelines

### ARIA Implementation
```jsx
// Good example
<button
  aria-label="Close menu"
  aria-expanded={isOpen}
  onClick={handleClose}
>
  <Icon name="close" />
</button>
```

### Accessibility Checklist
1. Proper heading hierarchy
2. Keyboard navigation support
3. Screen reader compatibility
4. Sufficient color contrast
5. Focus management

## Documentation

### Component Documentation
```jsx
/**
 * AttorneyCard component displays attorney information in a card format
 * @component
 * @param {Object} attorney - Attorney information object
 * @param {string} attorney.name - Attorney's full name
 * @param {string} attorney.specialty - Attorney's area of practice
 * @param {Function} onSelect - Callback when attorney is selected
 */
```

### Documentation Standards
1. Document component props
2. Include usage examples
3. Document complex logic
4. Maintain README files
5. Include setup instructions

## Image Handling

When working with images in the LegalScout interface, follow these guidelines:

### Image Display Best Practices

1. **Always set explicit dimensions**
   - Use height and width attributes or CSS properties
   - Use max-width/max-height to maintain aspect ratio
   - For responsive images, consider using percentages with max constraints

2. **Z-index considerations**
   - Set appropriate z-index values to ensure images appear above/below other elements as intended
   - Add `position: relative` or `position: absolute` when using z-index
   - Use z-index values consistently across components (e.g., 5 for foreground, 1 for background)

3. **Background vs. foreground images**
   - For decorative images, use CSS background-image
   - For content images, use <img> tags with proper alt text
   - When placing images on colored backgrounds, consider adding a semi-transparent container for better visibility

4. **SVG fallbacks**
   - Provide SVG fallbacks for cases where images fail to load
   - Use inline SVG for critical UI elements rather than external files
   - Apply consistent styling to SVGs (stroke width, colors, etc.)

### Image Filters and Effects

1. **Use filters carefully**
   - Test brightness/contrast filters on various backgrounds
   - Verify that invert() filters work as expected on different image types
   - Consider using CSS class toggles for applying filters conditionally

2. **Debugging image display issues**
   - Add temporary borders or background colors to container elements
   - Use CSS `!important` flags only during debugging, avoid in production code
   - Explicitly set display, visibility, and opacity properties when troubleshooting

### Responsive Images

1. **Mobile-first approach**
   - Design for small screens first, then enhance for larger displays
   - Use percentage-based dimensions with max constraints
   - Test on various device sizes and orientations

2. **Object-fit property**
   - Use `object-fit: contain` to ensure the entire image is visible
   - Use `object-fit: cover` for images that should fill their container
   - Specify `object-position` when alignment is important

### File Upload Components

1. **Preview implementation**
   - Show preview images at a reasonable size
   - Include loading states during preview generation
   - Provide clear feedback on upload success/failure

2. **Error handling**
   - Validate file types before upload
   - Check file sizes and limit appropriately
   - Provide user-friendly error messages for invalid uploads
``` 