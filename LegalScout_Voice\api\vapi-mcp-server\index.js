/**
 * Vapi MCP Server Serverless Function
 *
 * This serverless function provides an API endpoint for the Vapi MCP Server.
 * It uses the official Vercel MCP adapter to run the Vapi MCP Server as a Vercel serverless function.
 */

import { createMcpHandler } from '@vercel/mcp-adapter';
import { createMcpServer } from '@vapi-ai/mcp-server';

// Get Vapi API key from environment variable
// Use VAPI_TOKEN (private key) for server-side operations
const VAPI_TOKEN = process.env.VAPI_TOKEN || process.env.VAPI_SECRET_KEY || '6734febc-fc65-4669-93b0-929b31ff6564';

// Create a Vapi MCP server instance
const createVapiMcpServer = () => {
  // Check if Vapi token is configured
  if (!VAPI_TOKEN) {
    throw new Error('Vapi API key not configured. Please set the VAPI_TOKEN environment variable.');
  }

  // Create and return the Vapi MCP server with proper configuration
  return createMcpServer({
    vapiToken: VAPI_TOKEN
  });
};

// Handle OPTIONS requests for CORS preflight
const handleOptions = (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  // Return 200 for OPTIONS requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return true;
  }

  return false;
};

// Create the MCP handler using the Vercel MCP adapter
const handler = createMcpHandler({
  createServer: createVapiMcpServer,
  // Optional configuration
  config: {
    // Enable debug logging in development
    debug: process.env.NODE_ENV === 'development',
    // Set a custom path for the SSE endpoint (default is '/sse')
    ssePath: '/sse',
    // Set CORS headers with expanded configuration
    cors: {
      origin: '*',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'apikey', 'X-Client-Info', 'Prefer', 'Accept'],
      credentials: true,
      maxAge: 86400 // 24 hours
    }
  }
});

// Wrap the handler to handle OPTIONS requests
export default (req, res) => {
  // Handle OPTIONS requests
  if (handleOptions(req, res)) {
    return;
  }

  // Otherwise, pass to the MCP handler
  return handler(req, res);
};
