{"version": 3, "file": "vapiFieldMapping-185dba29.js", "sources": ["../../src/utils/vapiFieldMapping.js"], "sourcesContent": ["/**\r\n * Vapi Field Mapping Utility\r\n *\r\n * This utility provides standardized mapping between Supabase fields and Vapi API fields.\r\n * It ensures consistent data transformation between the two systems.\r\n */\r\n\r\nimport { createLogger } from './loggerUtils';\r\n\r\nconst logger = createLogger('vapiFieldMapping');\r\n\r\n/**\r\n * Field mapping between Supabase and Vapi\r\n * \r\n * This mapping defines how fields in the Supabase attorneys table\r\n * map to fields in the Vapi API.\r\n */\r\nexport const FIELD_MAPPING = {\r\n  // Basic fields\r\n  welcome_message: 'firstMessage',\r\n  vapi_instructions: 'instructions',\r\n  \r\n  // Voice configuration\r\n  voice_id: 'voice.voiceId',\r\n  voice_provider: 'voice.provider',\r\n  \r\n  // LLM configuration\r\n  ai_model: 'llm.model',\r\n  \r\n  // Analysis configuration\r\n  summary_prompt: 'analysis.summary.prompt',\r\n  structured_data_prompt: 'analysis.structuredData.prompt',\r\n  structured_data_schema: 'analysis.structuredData.schema',\r\n};\r\n\r\n/**\r\n * Default values for Vapi fields\r\n * \r\n * These values are used when the corresponding Supabase field is not provided.\r\n */\r\nexport const DEFAULT_VALUES = {\r\n  firstMessage: \"Hello, I'm Scout from your law firm. How can I help you today?\",\r\n  firstMessageMode: \"assistant-speaks-first\",\r\n  instructions: \"You are a legal assistant. Help potential clients understand their legal needs and collect relevant information for consultation.\",\r\n  voice: {\r\n    provider: \"11labs\",\r\n    voiceId: \"sarah\",\r\n    model: \"eleven_turbo_v2_5\"\r\n  },\r\n  llm: {\r\n    provider: \"openai\",\r\n    model: \"gpt-4o\"\r\n  },\r\n  transcriber: {\r\n    provider: \"deepgram\",\r\n    model: \"nova-3\"\r\n  }\r\n};\r\n\r\n/**\r\n * Convert attorney data from Supabase to Vapi format\r\n * \r\n * @param {Object} attorneyData - Attorney data from Supabase\r\n * @returns {Object} - Data in Vapi format\r\n */\r\nexport const attorneyToVapi = (attorneyData) => {\r\n  if (!attorneyData) {\r\n    logger.error('No attorney data provided');\r\n    return { ...DEFAULT_VALUES };\r\n  }\r\n\r\n  // Start with default values\r\n  const vapiData = { ...DEFAULT_VALUES };\r\n\r\n  // Set name based on firm name\r\n  vapiData.name = `${attorneyData.firm_name || 'Law Firm'} Legal Assistant`;\r\n\r\n  // Map simple fields\r\n  if (attorneyData.welcome_message) {\r\n    vapiData.firstMessage = attorneyData.welcome_message;\r\n  }\r\n\r\n  if (attorneyData.vapi_instructions) {\r\n    vapiData.instructions = attorneyData.vapi_instructions;\r\n  }\r\n\r\n  // Map voice configuration\r\n  if (attorneyData.voice_id || attorneyData.voice_provider) {\r\n    vapiData.voice = {\r\n      ...vapiData.voice,\r\n      voiceId: attorneyData.voice_id || vapiData.voice.voiceId,\r\n      provider: attorneyData.voice_provider || vapiData.voice.provider\r\n    };\r\n  }\r\n\r\n  // Map LLM configuration\r\n  if (attorneyData.ai_model) {\r\n    vapiData.llm = {\r\n      ...vapiData.llm,\r\n      model: attorneyData.ai_model\r\n    };\r\n  }\r\n\r\n  // Map analysis configuration if available\r\n  if (attorneyData.summary_prompt || attorneyData.structured_data_prompt || attorneyData.structured_data_schema) {\r\n    vapiData.analysis = {\r\n      summary: {\r\n        enabled: !!attorneyData.summary_prompt,\r\n        prompt: attorneyData.summary_prompt || ''\r\n      },\r\n      structuredData: {\r\n        enabled: !!(attorneyData.structured_data_prompt && attorneyData.structured_data_schema),\r\n        prompt: attorneyData.structured_data_prompt || '',\r\n        schema: attorneyData.structured_data_schema || {}\r\n      }\r\n    };\r\n  }\r\n\r\n  logger.info('Converted attorney data to Vapi format');\r\n  return vapiData;\r\n};\r\n\r\n/**\r\n * Extract fields from Vapi assistant data to update attorney record\r\n * \r\n * @param {Object} vapiData - Assistant data from Vapi\r\n * @returns {Object} - Fields to update in Supabase\r\n */\r\nexport const vapiToAttorney = (vapiData) => {\r\n  if (!vapiData) {\r\n    logger.error('No Vapi data provided');\r\n    return {};\r\n  }\r\n\r\n  const attorneyData = {};\r\n\r\n  // Map simple fields\r\n  if (vapiData.firstMessage) {\r\n    attorneyData.welcome_message = vapiData.firstMessage;\r\n  }\r\n\r\n  if (vapiData.instructions) {\r\n    attorneyData.vapi_instructions = vapiData.instructions;\r\n  }\r\n\r\n  // Map voice configuration\r\n  if (vapiData.voice) {\r\n    if (vapiData.voice.voiceId) {\r\n      attorneyData.voice_id = vapiData.voice.voiceId;\r\n    }\r\n    \r\n    if (vapiData.voice.provider) {\r\n      attorneyData.voice_provider = vapiData.voice.provider;\r\n    }\r\n  }\r\n\r\n  // Map LLM configuration\r\n  if (vapiData.llm && vapiData.llm.model) {\r\n    attorneyData.ai_model = vapiData.llm.model;\r\n  }\r\n\r\n  // Map analysis configuration if available\r\n  if (vapiData.analysis) {\r\n    if (vapiData.analysis.summary && vapiData.analysis.summary.prompt) {\r\n      attorneyData.summary_prompt = vapiData.analysis.summary.prompt;\r\n    }\r\n\r\n    if (vapiData.analysis.structuredData) {\r\n      if (vapiData.analysis.structuredData.prompt) {\r\n        attorneyData.structured_data_prompt = vapiData.analysis.structuredData.prompt;\r\n      }\r\n\r\n      if (vapiData.analysis.structuredData.schema) {\r\n        attorneyData.structured_data_schema = vapiData.analysis.structuredData.schema;\r\n      }\r\n    }\r\n  }\r\n\r\n  logger.info('Extracted attorney fields from Vapi data');\r\n  return attorneyData;\r\n};\r\n\r\n/**\r\n * Create update data for Vapi assistant based on changed fields\r\n * \r\n * @param {Object} changedFields - Fields that have been changed\r\n * @param {Object} currentAssistant - Current assistant data from Vapi\r\n * @returns {Object} - Update data for Vapi API\r\n */\r\nexport const createVapiUpdateData = (changedFields, currentAssistant = {}) => {\r\n  if (!changedFields || Object.keys(changedFields).length === 0) {\r\n    logger.warn('No changed fields provided');\r\n    return {};\r\n  }\r\n\r\n  const updateData = {};\r\n\r\n  // Map simple fields\r\n  if (changedFields.welcome_message !== undefined) {\r\n    updateData.firstMessage = changedFields.welcome_message;\r\n  }\r\n\r\n  if (changedFields.vapi_instructions !== undefined) {\r\n    updateData.instructions = changedFields.vapi_instructions;\r\n  }\r\n\r\n  // Map voice configuration\r\n  if (changedFields.voice_id !== undefined || changedFields.voice_provider !== undefined) {\r\n    updateData.voice = {\r\n      ...(currentAssistant.voice || {}),\r\n      voiceId: changedFields.voice_id !== undefined ? changedFields.voice_id : (currentAssistant.voice?.voiceId || 'sarah'),\r\n      provider: changedFields.voice_provider !== undefined ? changedFields.voice_provider : (currentAssistant.voice?.provider || '11labs')\r\n    };\r\n  }\r\n\r\n  // Map LLM configuration\r\n  if (changedFields.ai_model !== undefined) {\r\n    updateData.llm = {\r\n      ...(currentAssistant.llm || {}),\r\n      model: changedFields.ai_model\r\n    };\r\n  }\r\n\r\n  logger.info('Created Vapi update data from changed fields');\r\n  return updateData;\r\n};\r\n\r\nexport default {\r\n  FIELD_MAPPING,\r\n  DEFAULT_VALUES,\r\n  attorneyToVapi,\r\n  vapiToAttorney,\r\n  createVapiUpdateData\r\n};\r\n"], "names": ["logger", "createLogger", "FIELD_MAPPING", "DEFAULT_VALUES", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "attorneyData", "vapiData", "vapiToAttorney", "createVapiUpdateData", "changed<PERSON>ields", "currentAssistant", "updateData", "vapiFieldMapping"], "mappings": "wGASA,MAAMA,EAASC,EAAa,kBAAkB,EAQjCC,EAAgB,CAE3B,gBAAiB,eACjB,kBAAmB,eAGnB,SAAU,gBACV,eAAgB,iBAGhB,SAAU,YAGV,eAAgB,0BAChB,uBAAwB,iCACxB,uBAAwB,gCAC1B,EAOaC,EAAiB,CAC5B,aAAc,iEACd,iBAAkB,yBAClB,aAAc,oIACd,MAAO,CACL,SAAU,SACV,QAAS,QACT,MAAO,mBACR,EACD,IAAK,CACH,SAAU,SACV,MAAO,QACR,EACD,YAAa,CACX,SAAU,WACV,MAAO,QACR,CACH,EAQaC,EAAkBC,GAAiB,CAC9C,GAAI,CAACA,EACH,OAAAL,EAAO,MAAM,2BAA2B,EACjC,CAAE,GAAGG,GAId,MAAMG,EAAW,CAAE,GAAGH,GAGtB,OAAAG,EAAS,KAAO,GAAGD,EAAa,WAAa,UAAU,mBAGnDA,EAAa,kBACfC,EAAS,aAAeD,EAAa,iBAGnCA,EAAa,oBACfC,EAAS,aAAeD,EAAa,oBAInCA,EAAa,UAAYA,EAAa,kBACxCC,EAAS,MAAQ,CACf,GAAGA,EAAS,MACZ,QAASD,EAAa,UAAYC,EAAS,MAAM,QACjD,SAAUD,EAAa,gBAAkBC,EAAS,MAAM,QAC9D,GAIMD,EAAa,WACfC,EAAS,IAAM,CACb,GAAGA,EAAS,IACZ,MAAOD,EAAa,QAC1B,IAIMA,EAAa,gBAAkBA,EAAa,wBAA0BA,EAAa,0BACrFC,EAAS,SAAW,CAClB,QAAS,CACP,QAAS,CAAC,CAACD,EAAa,eACxB,OAAQA,EAAa,gBAAkB,EACxC,EACD,eAAgB,CACd,QAAS,CAAC,EAAEA,EAAa,wBAA0BA,EAAa,wBAChE,OAAQA,EAAa,wBAA0B,GAC/C,OAAQA,EAAa,wBAA0B,CAAE,CAClD,CACP,GAGEL,EAAO,KAAK,wCAAwC,EAC7CM,CACT,EAQaC,EAAkBD,GAAa,CAC1C,GAAI,CAACA,EACH,OAAAN,EAAO,MAAM,uBAAuB,EAC7B,GAGT,MAAMK,EAAe,CAAA,EAGrB,OAAIC,EAAS,eACXD,EAAa,gBAAkBC,EAAS,cAGtCA,EAAS,eACXD,EAAa,kBAAoBC,EAAS,cAIxCA,EAAS,QACPA,EAAS,MAAM,UACjBD,EAAa,SAAWC,EAAS,MAAM,SAGrCA,EAAS,MAAM,WACjBD,EAAa,eAAiBC,EAAS,MAAM,WAK7CA,EAAS,KAAOA,EAAS,IAAI,QAC/BD,EAAa,SAAWC,EAAS,IAAI,OAInCA,EAAS,WACPA,EAAS,SAAS,SAAWA,EAAS,SAAS,QAAQ,SACzDD,EAAa,eAAiBC,EAAS,SAAS,QAAQ,QAGtDA,EAAS,SAAS,iBAChBA,EAAS,SAAS,eAAe,SACnCD,EAAa,uBAAyBC,EAAS,SAAS,eAAe,QAGrEA,EAAS,SAAS,eAAe,SACnCD,EAAa,uBAAyBC,EAAS,SAAS,eAAe,UAK7EN,EAAO,KAAK,0CAA0C,EAC/CK,CACT,EASaG,EAAuB,CAACC,EAAeC,EAAmB,KAAO,CAC5E,GAAI,CAACD,GAAiB,OAAO,KAAKA,CAAa,EAAE,SAAW,EAC1D,OAAAT,EAAO,KAAK,4BAA4B,EACjC,GAGT,MAAMW,EAAa,CAAA,EAGnB,OAAIF,EAAc,kBAAoB,SACpCE,EAAW,aAAeF,EAAc,iBAGtCA,EAAc,oBAAsB,SACtCE,EAAW,aAAeF,EAAc,oBAItCA,EAAc,WAAa,QAAaA,EAAc,iBAAmB,UAC3EE,EAAW,MAAQ,CACjB,GAAID,EAAiB,OAAS,GAC9B,QAASD,EAAc,WAAa,OAAYA,EAAc,SAAYC,EAAiB,OAAO,SAAW,QAC7G,SAAUD,EAAc,iBAAmB,OAAYA,EAAc,eAAkBC,EAAiB,OAAO,UAAY,QACjI,GAIMD,EAAc,WAAa,SAC7BE,EAAW,IAAM,CACf,GAAID,EAAiB,KAAO,GAC5B,MAAOD,EAAc,QAC3B,GAGET,EAAO,KAAK,8CAA8C,EACnDW,CACT,EAEeC,EAAA,CACb,cAAAV,EACA,eAAAC,EACA,eAAAC,EACA,eAAAG,EACA,qBAAAC,CACF"}