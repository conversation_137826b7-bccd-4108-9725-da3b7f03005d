/**
 * Fix Attorney Validation
 * 
 * This script fixes issues with attorney validation and ensures
 * that a valid attorney is always available in local storage.
 */

(function() {
  console.log('[FixAttorneyValidation] Starting fix...');
  
  // UUID validation regex
  const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  
  // Storage keys
  const STORAGE_KEYS = {
    ATTORNEY: 'attorney',
    ATTORNEY_ID: 'attorney_id',
    ATTORNEY_VERSION: 'attorney_version',
    LAST_SYNC: 'attorney_last_sync'
  };
  
  /**
   * Generate a UUID v4
   * @returns {string} A UUID v4 string
   */
  function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
  
  /**
   * Validate a UUID
   * @param {string} uuid - The UUID to validate
   * @returns {boolean} Whether the UUID is valid
   */
  function isValidUUID(uuid) {
    return UUID_REGEX.test(uuid);
  }
  
  /**
   * Create a default attorney object
   * @param {Object} overrides - Properties to override in the default attorney
   * @returns {Object} A default attorney object
   */
  function createDefaultAttorney(overrides = {}) {
    const defaultAttorney = {
      id: generateUUID(),
      subdomain: 'default',
      firm_name: 'Your Law Firm',
      name: 'Your Name',
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      voice_provider: '11labs',
      voice_id: 'sarah',
      welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
      information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
      vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
      vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
    };
    
    return { ...defaultAttorney, ...overrides };
  }
  
  /**
   * Save attorney to localStorage
   * @param {Object} attorney - The attorney to save
   */
  function saveToLocalStorage(attorney) {
    if (!attorney || !isValidUUID(attorney.id)) {
      console.warn('[FixAttorneyValidation] Cannot save invalid attorney to localStorage');
      return;
    }
    
    try {
      // Save full attorney object
      localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(attorney));
      
      // Save ID separately for redundancy
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, attorney.id);
      
      // Save version and timestamp
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());
      
      console.log('[FixAttorneyValidation] Saved attorney to localStorage:', attorney.id);
    } catch (error) {
      console.error('[FixAttorneyValidation] Error saving to localStorage:', error);
    }
  }
  
  /**
   * Load attorney from localStorage
   * @returns {Object|null} The attorney object or null if not found
   */
  function loadFromLocalStorage() {
    try {
      // Try to get from localStorage
      const storedAttorney = localStorage.getItem(STORAGE_KEYS.ATTORNEY);
      if (storedAttorney) {
        try {
          const parsedAttorney = JSON.parse(storedAttorney);
          
          // Validate the attorney data
          if (parsedAttorney && isValidUUID(parsedAttorney.id)) {
            console.log('[FixAttorneyValidation] Loaded attorney from localStorage:', parsedAttorney.id);
            return parsedAttorney;
          }
        } catch (parseError) {
          console.error('[FixAttorneyValidation] Error parsing attorney from localStorage:', parseError);
        }
      }
      
      // If not found or invalid, try to get just the ID
      const storedAttorneyId = localStorage.getItem(STORAGE_KEYS.ATTORNEY_ID);
      if (storedAttorneyId && isValidUUID(storedAttorneyId)) {
        console.log('[FixAttorneyValidation] Found attorney ID in localStorage:', storedAttorneyId);
        
        // Create a minimal attorney object with the ID
        const minimalAttorney = createDefaultAttorney({ id: storedAttorneyId });
        saveToLocalStorage(minimalAttorney);
        return minimalAttorney;
      }
      
      return null;
    } catch (error) {
      console.error('[FixAttorneyValidation] Error loading from localStorage:', error);
      return null;
    }
  }
  
  /**
   * Fix attorney validation issues
   */
  function fixAttorneyValidation() {
    // Load attorney from localStorage
    const attorney = loadFromLocalStorage();
    
    // If no attorney was loaded, create a default one
    if (!attorney) {
      console.log('[FixAttorneyValidation] No attorney found, creating default');
      const defaultAttorney = createDefaultAttorney();
      saveToLocalStorage(defaultAttorney);
      return defaultAttorney;
    }
    
    // Ensure the attorney has all required fields
    const enhancedAttorney = {
      ...createDefaultAttorney(),
      ...attorney,
      updated_at: new Date().toISOString()
    };
    
    // Ensure ID doesn't change
    enhancedAttorney.id = attorney.id;
    
    // Save the enhanced attorney back to localStorage
    saveToLocalStorage(enhancedAttorney);
    
    console.log('[FixAttorneyValidation] Fixed attorney validation issues:', enhancedAttorney.id);
    
    return enhancedAttorney;
  }
  
  // Run the fix
  const fixedAttorney = fixAttorneyValidation();
  
  // Make the fixed attorney available globally
  window.fixedAttorney = fixedAttorney;
  
  console.log('[FixAttorneyValidation] Fix completed');
})();
