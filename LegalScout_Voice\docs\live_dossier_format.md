# Live Dossier Tool Format Documentation

The live_dossier tool is a core component of the LegalScout platform, providing real-time case information updates during user consultations. This document outlines the expected data structure and integration details.

## Tool ID
```
LIVE_DOSSIER: "4a0d63cf-0b84-4eec-bddf-9c5869439d7e"
```

## Make.com Webhook Integration
The live_dossier tool is integrated with Make.com via webhook:
```
Webhook URL: https://hook.us1.make.com/2c6ez4oq26ka5kv7h7zjhiy4v18l20t1
```

When the tool is triggered, data is sent to this webhook for additional processing and integrations.

## Data Structure

The live_dossier tool uses a structured format with emoji-prefixed fields for visual clarity. Below is the complete structure:

```json
{
  "🚨Urgency": "High/Medium/Low",
  "Case Dossier": {
    "📁 Client Background": "Client name and background information",
    "🔄 STATUS": "Current case status",
    "📍 Jurisdiction": "Location information (may include coordinates)",
    "📝 Statement of Facts": "Detailed factual information about the case",
    "⚖️ Legal Issues": "Description of legal issues faced by the client",
    "🎯 Objectives": "Client's goals and objectives for the consultation"
  }
}
```

## Field Descriptions

### Top-Level Fields

- **🚨Urgency**: Indicates case urgency level, which determines whether to proceed to attorney locator or end the call.

### Case Dossier Fields

- **📁 Client Background**: Client's personal details and context.
- **🔄 STATUS**: Current status of the case and consultation.
- **📍 Jurisdiction**: Geographic location information relevant to the case, may include coordinates.
- **📝 Statement of Facts**: Detailed description of case facts and timeline.
- **⚖️ Legal Issues**: Legal challenges, questions, or problems the client is facing.
- **🎯 Objectives**: What the client hopes to achieve from the consultation.

## Processing Rules

1. **Default Values**: For unknown or unclear items, provide reasonable default values based on context.
2. **Conciseness**: Keep information concise and structured for real-time visual assistance.
3. **Incremental Updates**: The dossier is updated incrementally during the call; not all fields may be filled at once.
4. **Practice Area Extraction**: When legal issues are identified, the system attempts to extract a relevant practice area.

## Application Integration

The VapiCall component processes live_dossier updates through the following steps:

1. Receive the tool message from the Vapi agent.
2. Parse the JSON structure into an object.
3. Extract relevant fields using the `updateDossierFromToolData` function.
4. Convert the structured data into application state via `setDossierData`.
5. Synchronize the data with Make.com using the webhook URL.

## Make.com Integration

The live_dossier data is sent to Make.com for:

1. External data processing and enrichment
2. Integration with CRM systems
3. Lead tracking and routing
4. Analytics and reporting
5. Notification systems

## Example Data

```json
{
  "🚨Urgency": "High",
  "Case Dossier": {
    "📁 Client Background": "John Doe, 35, small business owner",
    "🔄 STATUS": "Needs immediate consultation",
    "📍 Jurisdiction": "New York, NY",
    "📝 Statement of Facts": "Client's business partner withdrew $50,000 without authorization last week",
    "⚖️ Legal Issues": "Business partnership dispute, potential fraud",
    "🎯 Objectives": "Recover funds and determine legal options against partner"
  }
}
```

## Troubleshooting

If the live_dossier tool data is not being processed correctly:

1. Check the console for parsing errors
2. Verify the tool ID matches the expected value in VapiCall.jsx
3. Confirm the webhook URL is correctly configured
4. Ensure the data follows the expected structure with emoji-prefixed fields 