# Logo Management in LegalScout

This document describes the logo management functionality in the LegalScout platform, including uploading, displaying, and removing logos.

## Features

### Logo Upload

The platform provides two ways to add a logo:
1. **File Upload**: Users can upload an image file directly through the file input.
2. **URL Input**: Users can enter a direct URL to an image (not yet implemented).

### Logo Preview

After uploading a logo:
- A preview of the logo is displayed below the upload control
- The logo is shown at a reasonable size (max-width: 200px, max-height: 80px)
- A helpful description explains how the logo will be used in the interface

### Logo Removal

The "Remove" button appears only when a logo is present, allowing users to:
- Clear the current logo
- Remove the file from the form
- Reset to the default LegalScout SVG logo in the interface

## Implementation Details

### State Management

The logo URL is stored in the React state:
- `logoUrl` state in App.jsx is the primary source of truth
- `setLogoUrl('')` is called by the `handleRemoveLogo` function to clear the logo
- The state is passed to the SimpleDemoPage component and eventually to the PreviewInterface

### Display in Interface

The logo appears in two places:
1. **Header**: When a chat session begins
2. **Consultation Button**: As part of the main call-to-action button

### Technical Considerations

#### Button Logo Display

The logo in the consultation button required special treatment:
- A semi-transparent background container improves visibility
- Z-index settings ensure the logo appears correctly above the button background
- CSS fixes were applied to ensure the image is visible and properly sized

#### SVG Fallback

When no custom logo is provided, the interface displays a default LegalScout logo using inline SVG.

### Technical Considerations

#### Logo Display

The logo appears in two key locations with consistent styling:
- **Button Logo**: Displayed within the circular consultation button
  - Semi-transparent white background container (rgba(255,255,255,0.2))
  - Rounded corners (8px border radius)
  - Padding (8px) to provide space around the logo
  - Z-index settings (z-index: 5) to ensure proper layering
  - Object-fit: contain to maintain aspect ratio

- **Header Logo**: Displayed in the header after conversation starts
  - Matching semi-transparent white background container (rgba(255,255,255,0.2))
  - Similar rounded corners (6px border radius)
  - Consistent padding (4px) for visual harmony
  - Z-index settings (z-index: 100) to ensure visibility

#### SVG Fallback

When no custom logo is provided:
- The interface displays a default LegalScout logo using inline SVG
- SVG uses a white stroke for visibility against colored backgrounds
- SVG dimensions are responsive based on container size

## Testing

When testing the logo functionality, verify:
1. Upload works correctly for common image formats (PNG, JPG, SVG)
2. The preview displays properly
3. The logo appears correctly in the interface header and button
4. The Remove button successfully clears the logo
5. The interface properly reverts to the default logo when removed

## Future Enhancements

Potential improvements for the logo management include:
- Image format validation
- Image cropping/resizing tools
- Logo size and position customization
- Lazy loading for performance optimization 