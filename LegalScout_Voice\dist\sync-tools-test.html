<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synchronization Tools Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        textarea {
            height: 100px;
        }
    </style>
</head>
<body>
    <h1>Synchronization Tools Test</h1>
    
    <div class="section">
        <h2>Sync Attorney Profile</h2>
        <div>
            <label for="attorneyId">Attorney ID:</label>
            <input type="text" id="attorneyId" value="test-attorney-id">
        </div>
        <div>
            <label for="forceUpdate">Force Update:</label>
            <input type="checkbox" id="forceUpdate">
        </div>
        <button id="syncAttorneyProfile">Sync Attorney Profile</button>
    </div>
    
    <div class="section">
        <h2>Manage Auth State</h2>
        <div>
            <label for="authAction">Action:</label>
            <select id="authAction">
                <option value="login">Login</option>
                <option value="logout">Logout</option>
                <option value="refresh">Refresh</option>
            </select>
        </div>
        <div>
            <label for="authData">Auth Data:</label>
            <textarea id="authData">{
  "user": {
    "id": "test-user-id",
    "email": "<EMAIL>",
    "user_metadata": {
      "name": "Test User"
    }
  },
  "session": {
    "access_token": "test-access-token"
  }
}</textarea>
        </div>
        <button id="manageAuthState">Manage Auth State</button>
    </div>
    
    <div class="section">
        <h2>Validate Configuration</h2>
        <div>
            <label for="configAttorneyId">Attorney ID:</label>
            <input type="text" id="configAttorneyId" value="test-attorney-id">
        </div>
        <div>
            <label for="configData">Config Data:</label>
            <textarea id="configData">{
  "name": "Test Attorney",
  "email": "<EMAIL>",
  "firm_name": "Test Law Firm",
  "welcome_message": "Welcome to Test Law Firm",
  "vapi_instructions": "You are a legal assistant for Test Law Firm",
  "voice_provider": "playht",
  "voice_id": "ranger"
}</textarea>
        </div>
        <button id="validateConfiguration">Validate Configuration</button>
    </div>
    
    <div class="section">
        <h2>Check Preview Consistency</h2>
        <div>
            <label for="previewAttorneyId">Attorney ID:</label>
            <input type="text" id="previewAttorneyId" value="test-attorney-id">
        </div>
        <button id="checkPreviewConsistency">Check Preview Consistency</button>
    </div>
    
    <div class="section">
        <h2>Log</h2>
        <div id="log" class="log"></div>
    </div>
    
    <script>
        // Log function
        function log(message) {
            const logElement = document.getElementById('log');
            logElement.innerHTML += message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Sync Attorney Profile
        document.getElementById('syncAttorneyProfile').addEventListener('click', async () => {
            try {
                const attorneyId = document.getElementById('attorneyId').value.trim();
                const forceUpdate = document.getElementById('forceUpdate').checked;
                
                log(`Syncing attorney profile for ${attorneyId}...`);
                
                // Call the API
                const response = await fetch('/api/sync-tools/sync-attorney-profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        attorneyId,
                        forceUpdate
                    })
                });
                
                const result = await response.json();
                log(`Sync result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error syncing attorney profile: ${error.message}`);
            }
        });
        
        // Manage Auth State
        document.getElementById('manageAuthState').addEventListener('click', async () => {
            try {
                const action = document.getElementById('authAction').value;
                const authData = JSON.parse(document.getElementById('authData').value.trim());
                
                log(`Managing auth state for action: ${action}...`);
                
                // Call the API
                const response = await fetch('/api/sync-tools/manage-auth-state', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        authData,
                        action
                    })
                });
                
                const result = await response.json();
                log(`Auth state result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error managing auth state: ${error.message}`);
            }
        });
        
        // Validate Configuration
        document.getElementById('validateConfiguration').addEventListener('click', async () => {
            try {
                const attorneyId = document.getElementById('configAttorneyId').value.trim();
                const configData = JSON.parse(document.getElementById('configData').value.trim());
                
                log(`Validating configuration for ${attorneyId}...`);
                
                // Call the API
                const response = await fetch('/api/sync-tools/validate-configuration', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        attorneyId,
                        configData
                    })
                });
                
                const result = await response.json();
                log(`Validation result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error validating configuration: ${error.message}`);
            }
        });
        
        // Check Preview Consistency
        document.getElementById('checkPreviewConsistency').addEventListener('click', async () => {
            try {
                const attorneyId = document.getElementById('previewAttorneyId').value.trim();
                
                log(`Checking preview consistency for ${attorneyId}...`);
                
                // Call the API
                const response = await fetch('/api/sync-tools/check-preview-consistency', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        attorneyId
                    })
                });
                
                const result = await response.json();
                log(`Consistency check result: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`Error checking preview consistency: ${error.message}`);
            }
        });
        
        // Initial log
        log('Synchronization Tools Test Page Loaded');
    </script>
</body>
</html>
