# Vapi MCP Server Integration

This document describes the integration of Vapi's Model Context Protocol (MCP) server with LegalScout Voice.

## Overview

The Vapi MCP server provides a standardized way to interact with Vapi's voice AI services. It exposes all Vapi endpoints as tools that can be called by any LLM or agent framework that supports the Model Context Protocol.

## Key Benefits

- **Streamable HTTP**: More reliable connections compared to long-lived SSE connections
- **Tool-Based Access**: Exposes all Vapi endpoints as tools for any LLM or agent framework
- **Standardized Interface**: Consistent way to interact with Vapi services
- **Simplified Implementation**: Replaces complex fallback mechanisms with standardized tool calls

## Implementation Details

### MCP Server Setup

The MCP server is set up in `src/server/mcpServerSetup.js`. It creates an MCP server with tools for interacting with Vapi's API, including:

- **Assistant Tools**: List assistants, get assistant by ID
- **Call Tools**: List calls, create call, get call by ID
- **Phone Number Tools**: List phone numbers, get phone number by ID
- **Tool Tools**: List tools, get tool by ID

### API Route

The API route for the MCP server is defined in `src/api/vapi-mcp-server/route.js`. It handles SSE connections for the MCP server.

### Configuration

The MCP server configuration is defined in `src/config/mcp.config.js`. It includes settings for:

- **Vapi API Keys**: Public and secret API keys
- **MCP Server URL**: URL for the MCP server
- **MCP Server Proxy Path**: Proxy path for the MCP server
- **Default Voice**: Default voice provider and voice ID
- **Default Assistant ID**: Default assistant ID for fallback
- **API Endpoints**: API endpoints for different environments
- **Fallback Options**: Options for fallback behavior

## Usage

### Initialization

To initialize the MCP server, use the `initializeEnhancedVapi` function from `src/utils/enhancedIntegration.js`:

```javascript
import { initializeEnhancedVapi } from '../utils/enhancedIntegration';

// Initialize the MCP server
await initializeEnhancedVapi();
```

### Making Tool Calls

To make tool calls to the MCP server, use the `enhancedVapiMcpService` from `src/services/EnhancedVapiMcpService.js`:

```javascript
import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';

// Connect to the MCP server
await enhancedVapiMcpService.connect(apiKey);

// Get an assistant by ID
const assistant = await enhancedVapiMcpService.getAssistant(assistantId);

// List all assistants
const assistants = await enhancedVapiMcpService.listAssistants();

// Create a new assistant
const newAssistant = await enhancedVapiMcpService.createAssistant(assistantConfig);
```

### Enhanced Components

The following enhanced components use the MCP server:

- **EnhancedVapiCall**: A full-featured call component that uses the MCP server for voice calls
- **EnhancedAgentPreview**: A preview component that uses the MCP server to preview the attorney's AI assistant
- **EnhancedPreviewTab**: A tab component that uses the MCP server to preview the attorney's AI assistant

## One-Way Sync Pattern

The implementation follows the one-way sync pattern (UI → Supabase → Vapi) with Supabase as the primary source of truth:

1. **Save to Supabase**: All configuration data is saved to Supabase first
2. **Sync to Vapi**: After saving to Supabase, the data is synced to Vapi
3. **Validation**: Data is validated before saving to either system
4. **Error Handling**: Errors are logged and user feedback is provided for sync failures

## Field Mapping

The following fields are mapped between Supabase and Vapi:

- `welcome_message` (Supabase) → `firstMessage` (Vapi)
- `vapi_instructions` (Supabase) → `instructions` (Vapi)
- `voice_id` (Supabase) → `voice.voiceId` (Vapi)
- `voice_provider` (Supabase) → `voice.provider` (Vapi)
- `ai_model` (Supabase) → `llm.model` (Vapi)

## Fallback Mechanisms

The implementation includes the following fallback mechanisms:

- **Direct API**: If the MCP server fails, the system can fall back to using the direct Vapi API
- **Assistant Creation**: If an attorney doesn't have a valid Vapi assistant ID, a new one is created
- **Retry Logic**: Failed API calls are retried with exponential backoff

## Error Handling

Errors are handled at multiple levels:

- **Service Level**: Errors are logged and propagated up the call stack
- **Component Level**: Errors are displayed to the user with appropriate messages
- **Application Level**: Errors are logged to the console and reported to error tracking systems

## Future Directions

As the MCP server matures, we may consider using it more extensively in production, particularly for:

- **Complex Workflows**: Building multi-step workflows to navigate assistants through logical steps
- **Call Analysis**: Analyzing call data with Claude or other LLMs
- **Integration with Custom Frameworks**: Integrating Vapi with custom agent frameworks
