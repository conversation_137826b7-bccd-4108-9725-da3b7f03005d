# Vapi MCP Server Debugging Guide

This guide provides instructions on how to debug Vapi MCP server integration issues in the LegalScout application.

## Overview

The Vapi MCP server integration has been enhanced with comprehensive debugging tools to help identify and resolve connection issues. These tools include:

1. **Structured Logging**: Detailed logs for connection attempts, successes, and failures
2. **Network Request/Response Logging**: Logs for all API calls to Vapi endpoints
3. **Environment Variable Verification**: Tools to check if all required environment variables are set
4. **Connection Status Tracking**: Tracking of connection status and attempts
5. **Diagnostic Tools**: Tools to test different connection methods and endpoints

## Using the Debugging Tools

### Console Commands

The debugging tools are available in the browser console in development mode. Here are the available commands:

```javascript
// Run all diagnostics
VapiDebug.diagnose()

// Check environment variables
VapiDebug.checkEnv()

// Show help
VapiDebug.help()

// Clear all logs
VapiDebug.clearLogs()

// Get connection logs
VapiMcpDebugger.getLogs()

// Get network logs
VapiMcpDebugger.getNetworkLogs()

// Get error logs
VapiMcpDebugger.getErrors()
```

### Diagnosing Connection Issues

To diagnose connection issues, follow these steps:

1. Open the browser console (F12 or Ctrl+Shift+I)
2. Run `VapiDebug.diagnose()` to run all diagnostics
3. Check the results to see which connection methods work and which don't
4. If no connection method works, check environment variables with `VapiDebug.checkEnv()`
5. Check network logs with `VapiMcpDebugger.getNetworkLogs()` to see if there are any API errors

### Common Issues and Solutions

#### 1. Missing API Key

**Symptoms**: All connection attempts fail with "No API key available" error

**Solution**: 
- Check if the `VITE_VAPI_PUBLIC_KEY` environment variable is set
- Check if the API key is valid by testing it directly with the Vapi API

#### 2. Proxy Path Issues

**Symptoms**: MCP connection attempts fail but direct API works

**Solution**:
- Check if the proxy path is correct in `src/config/mcp.config.js`
- Try different proxy paths by adding them to `fallbackProxyPaths` in the config
- Check if the Vapi MCP server is running and accessible

#### 3. CORS Issues

**Symptoms**: Network errors with CORS-related messages

**Solution**:
- Check if the Vapi MCP server has CORS headers configured correctly
- Try using a different proxy path that handles CORS correctly

#### 4. API Endpoint Issues

**Symptoms**: Direct API connection attempts fail with 404 errors

**Solution**:
- Check if the API endpoints are correct in `src/services/vapiMcpService.js`
- Try different API endpoints by adding them to `possibleApiEndpoints` in the service

## Debugging Workflow

1. **Check Environment**: Run `VapiDebug.checkEnv()` to verify environment variables
2. **Run Diagnostics**: Run `VapiDebug.diagnose()` to test all connection methods
3. **Check Logs**: Examine connection logs with `VapiMcpDebugger.getLogs()`
4. **Check Network**: Examine network logs with `VapiMcpDebugger.getNetworkLogs()`
5. **Fix Issues**: Based on the logs, fix any identified issues
6. **Retest**: Run diagnostics again to verify the fixes

## Advanced Debugging

### Network Interceptor

The network interceptor logs all requests to Vapi API endpoints. You can see these logs in the console or by calling `VapiMcpDebugger.getNetworkLogs()`.

### Connection Logging

Connection attempts, successes, and failures are logged with detailed information. You can see these logs in the console or by calling `VapiMcpDebugger.getLogs()`.

### Error Tracking

Errors are tracked separately for easier debugging. You can see these logs by calling `VapiMcpDebugger.getErrors()`.

## Troubleshooting Specific Issues

### Issue: Cannot connect to Vapi MCP server

1. Check if the Vapi MCP server is running and accessible
2. Check if the proxy path is correct in `src/config/mcp.config.js`
3. Check if the API key is valid
4. Try different proxy paths
5. Check network logs for specific errors

### Issue: Cannot connect to Vapi API directly

1. Check if the API key is valid
2. Check if the API endpoints are correct
3. Try different API endpoints
4. Check network logs for specific errors

### Issue: Connection works but API calls fail

1. Check if the API key has the necessary permissions
2. Check if the API endpoints are correct
3. Check network logs for specific errors in the API calls

## Conclusion

The Vapi MCP server debugging tools provide comprehensive visibility into the connection process and API calls. By using these tools, you can quickly identify and resolve integration issues.

If you encounter any issues that cannot be resolved with these tools, please contact the development team for further assistance.
