/**
 * API Route: /api/sync-tools/check-preview-consistency
 *
 * This endpoint handles checking the consistency between preview and deployment.
 */

import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client - use server environment variables only
const supabaseUrl = process.env.SUPABASE_URL || 'https://utopqxsvudgrtiwenlzl.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY;

let supabase = null;
if (supabaseUrl && supabaseServiceKey) {
  // Use clean, simple Supabase client configuration
  supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

// Server-side implementation
const checkPreviewConsistency = async ({ attorneyId }) => {
  console.log('Server-side checkPreviewConsistency called with:', { attorneyId });

  if (!supabase) {
    return {
      success: false,
      error: 'Supabase not configured'
    };
  }

  try {
    // Fetch attorney data
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();

    if (error) {
      throw error;
    }

    // Check for consistency issues
    const differences = [];

    if (!attorney.firm_name) {
      differences.push('Missing firm name');
    }

    if (!attorney.email) {
      differences.push('Missing email');
    }

    return {
      success: true,
      message: 'Preview consistency check completed',
      attorneyId,
      isConsistent: differences.length === 0,
      differences
    };
  } catch (error) {
    console.error('Error checking preview consistency:', error);
    return {
      success: false,
      error: error.message
    };
  }
};



export default async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed'
    });
  }

  try {
    // Parse request body if it's a string
    let body = req.body;
    if (typeof body === 'string') {
      try {
        body = JSON.parse(body);
      } catch (parseError) {
        console.error('Error parsing request body:', parseError);
        return res.status(400).json({
          success: false,
          error: 'Invalid JSON in request body'
        });
      }
    }

    const { attorneyId } = body || {};

    // Validate required parameters
    if (!attorneyId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required parameter: attorneyId is required'
      });
    }

    // Call the checkPreviewConsistency function
    const result = await checkPreviewConsistency({ attorneyId });

    // Return the result
    return res.status(200).json({
      success: true,
      result
    });
  } catch (error) {
    console.error('Error checking preview consistency:', error);

    // Return a proper error response
    return res.status(500).json({
      success: false,
      error: error.message || 'An unknown error occurred'
    });
  }
}
