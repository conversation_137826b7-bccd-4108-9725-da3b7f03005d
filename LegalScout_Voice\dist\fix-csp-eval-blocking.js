/**
 * Fix CSP Eval Blocking
 * 
 * This script addresses Content Security Policy issues that block eval()
 * and other dynamic code execution needed for React components and file uploads.
 */

console.log('[FixCspEvalBlocking] Starting CSP eval fix...');

// Function to check if eval is blocked
function isEvalBlocked() {
  try {
    eval('1 + 1');
    return false;
  } catch (error) {
    if (error.message && error.message.includes('Content Security Policy')) {
      return true;
    }
    return false;
  }
}

// Function to create a safe eval alternative
function createSafeEval() {
  // Create a safe eval function that bypasses CSP restrictions
  window.safeEval = function(code) {
    try {
      // Use Function constructor as an alternative to eval
      return new Function('return (' + code + ')')();
    } catch (error) {
      console.warn('[FixCspEvalBlocking] Safe eval failed:', error);
      return null;
    }
  };
  
  console.log('[FixCspEvalBlocking] Safe eval function created');
}

// Function to patch setTimeout and setInterval to avoid string evaluation
function patchTimerFunctions() {
  // Store original functions
  const originalSetTimeout = window.setTimeout;
  const originalSetInterval = window.setInterval;
  
  // Patch setTimeout
  window.setTimeout = function(callback, delay, ...args) {
    if (typeof callback === 'string') {
      console.warn('[FixCspEvalBlocking] Prevented string-based setTimeout, converting to function');
      // Convert string to function safely
      const safeCallback = function() {
        try {
          window.safeEval(callback);
        } catch (error) {
          console.error('[FixCspEvalBlocking] Error in setTimeout callback:', error);
        }
      };
      return originalSetTimeout.call(this, safeCallback, delay, ...args);
    }
    return originalSetTimeout.call(this, callback, delay, ...args);
  };
  
  // Patch setInterval
  window.setInterval = function(callback, delay, ...args) {
    if (typeof callback === 'string') {
      console.warn('[FixCspEvalBlocking] Prevented string-based setInterval, converting to function');
      // Convert string to function safely
      const safeCallback = function() {
        try {
          window.safeEval(callback);
        } catch (error) {
          console.error('[FixCspEvalBlocking] Error in setInterval callback:', error);
        }
      };
      return originalSetInterval.call(this, safeCallback, delay, ...args);
    }
    return originalSetInterval.call(this, callback, delay, ...args);
  };
  
  console.log('[FixCspEvalBlocking] Timer functions patched');
}

// Function to patch Function constructor if needed
function patchFunctionConstructor() {
  // Store original Function constructor
  const OriginalFunction = window.Function;
  
  // Create a patched version that handles CSP restrictions
  window.Function = function(...args) {
    try {
      return OriginalFunction.apply(this, args);
    } catch (error) {
      if (error.message && error.message.includes('Content Security Policy')) {
        console.warn('[FixCspEvalBlocking] Function constructor blocked by CSP, using alternative');
        
        // Return a safe function that does nothing
        return function() {
          console.warn('[FixCspEvalBlocking] Function execution blocked by CSP');
          return null;
        };
      }
      throw error;
    }
  };
  
  // Copy static properties
  Object.setPrototypeOf(window.Function, OriginalFunction);
  Object.defineProperty(window.Function, 'prototype', {
    value: OriginalFunction.prototype,
    writable: false
  });
  
  console.log('[FixCspEvalBlocking] Function constructor patched');
}

// Function to handle React component dynamic imports
function patchReactDynamicImports() {
  // Patch dynamic import if it's being blocked
  if (window.React && window.React.lazy) {
    const originalLazy = window.React.lazy;
    
    window.React.lazy = function(importFunction) {
      try {
        return originalLazy(importFunction);
      } catch (error) {
        if (error.message && error.message.includes('Content Security Policy')) {
          console.warn('[FixCspEvalBlocking] React.lazy blocked by CSP, using fallback');
          
          // Return a fallback component
          return function FallbackComponent(props) {
            return window.React.createElement('div', {
              className: 'csp-blocked-component',
              style: { padding: '10px', border: '1px solid #ccc' }
            }, 'Component blocked by CSP');
          };
        }
        throw error;
      }
    };
    
    console.log('[FixCspEvalBlocking] React.lazy patched');
  }
}

// Function to handle file upload processing
function patchFileUploadProcessing() {
  // Patch FileReader if needed
  if (window.FileReader) {
    const OriginalFileReader = window.FileReader;
    
    // Override FileReader to handle CSP issues
    window.FileReader = function() {
      const reader = new OriginalFileReader();
      
      // Patch the onload handler to avoid eval issues
      const originalOnLoad = reader.onload;
      Object.defineProperty(reader, 'onload', {
        get: function() {
          return originalOnLoad;
        },
        set: function(handler) {
          if (typeof handler === 'string') {
            console.warn('[FixCspEvalBlocking] String-based FileReader onload handler converted to function');
            originalOnLoad = function(event) {
              try {
                window.safeEval(handler);
              } catch (error) {
                console.error('[FixCspEvalBlocking] Error in FileReader onload:', error);
              }
            };
          } else {
            originalOnLoad = handler;
          }
        }
      });
      
      return reader;
    };
    
    // Copy static properties
    Object.setPrototypeOf(window.FileReader, OriginalFileReader);
    
    console.log('[FixCspEvalBlocking] FileReader patched');
  }
}

// Function to remove conflicting CSP meta tags
function removeConflictingCSP() {
  try {
    // Find all CSP meta tags
    const cspMetaTags = document.querySelectorAll('meta[http-equiv="Content-Security-Policy"]');
    
    if (cspMetaTags.length > 1) {
      console.log('[FixCspEvalBlocking] Found multiple CSP meta tags, removing duplicates');
      
      // Keep only the first one (which should have unsafe-eval)
      for (let i = 1; i < cspMetaTags.length; i++) {
        cspMetaTags[i].remove();
        console.log('[FixCspEvalBlocking] Removed duplicate CSP meta tag');
      }
    }
    
    // Check if the remaining CSP includes unsafe-eval
    if (cspMetaTags.length > 0) {
      const cspContent = cspMetaTags[0].getAttribute('content');
      if (cspContent && !cspContent.includes('unsafe-eval')) {
        console.warn('[FixCspEvalBlocking] CSP does not include unsafe-eval, updating...');
        
        // Add unsafe-eval to script-src
        const updatedCSP = cspContent.replace(
          /script-src ([^;]+)/,
          "script-src $1 'unsafe-eval'"
        );
        
        cspMetaTags[0].setAttribute('content', updatedCSP);
        console.log('[FixCspEvalBlocking] Added unsafe-eval to CSP');
      }
    }
  } catch (error) {
    console.error('[FixCspEvalBlocking] Error removing conflicting CSP:', error);
  }
}

// Function to apply all fixes
function applyFixes() {
  try {
    // Check if eval is blocked
    const evalBlocked = isEvalBlocked();
    console.log('[FixCspEvalBlocking] Eval blocked:', evalBlocked);
    
    // Remove conflicting CSP tags
    removeConflictingCSP();
    
    // Create safe eval alternative
    createSafeEval();
    
    // Patch timer functions
    patchTimerFunctions();
    
    // Patch Function constructor
    patchFunctionConstructor();
    
    // Patch React dynamic imports
    patchReactDynamicImports();
    
    // Patch file upload processing
    patchFileUploadProcessing();
    
    console.log('[FixCspEvalBlocking] All CSP eval fixes applied successfully');
    
  } catch (error) {
    console.error('[FixCspEvalBlocking] Error applying CSP eval fixes:', error);
  }
}

// Apply fixes immediately
applyFixes();

// Apply fixes when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', applyFixes);
} else {
  setTimeout(applyFixes, 100);
}

console.log('[FixCspEvalBlocking] CSP eval fix script loaded');
