# Vapi Mock Mode for Development

This document explains how to use Vapi mock mode for development without needing a valid Vapi API key.

## Overview

The application has been configured to always use mock mode in development environments. This allows you to develop and test the application without needing a valid Vapi API key.

## How It Works

The Vapi Service Manager has been updated to automatically use mock mode in development environments. This is done by:

1. Detecting if the application is running in a development environment
2. Forcing mock mode if in development
3. Using the mock Vapi service instead of the real one

## Benefits of Mock Mode

- **No API Key Required**: You don't need a valid Vapi API key to develop and test the application
- **Consistent Behavior**: The mock service provides consistent responses for testing
- **Offline Development**: You can develop and test the application without an internet connection
- **No API Rate Limits**: You don't have to worry about hitting API rate limits during development

## Mock Mode Implementation

The mock mode implementation is in the following files:

- `src/services/vapiServiceManager.js`: The main service manager that decides whether to use the real or mock service
- `src/services/vapiMockService.js`: The mock implementation of the Vapi service
- `src/utils/initAttorneyProfileManager.js`: The initialization code that sets up the Vapi service manager

## Using Mock Mode

Mock mode is automatically enabled in development environments. You don't need to do anything special to use it.

### Verifying Mock Mode

To verify that mock mode is being used:

1. Open the browser console
2. Look for log messages like:
   ```
   [VapiServiceManager] Development environment detected, using mock mode by default
   [VapiServiceManager] Forcing mock mode
   [VapiServiceManager] Creating mock Vapi instance
   ```

3. Or check the connection status:
   ```javascript
   vapiServiceManager.getConnectionStatus()
   ```
   This should show `useMock: true` and `connectionMode: 'mock'`

## Disabling Mock Mode

If you want to test with the real Vapi service in development:

1. Get a valid Vapi API key from [https://dashboard.vapi.ai/settings/api-keys](https://dashboard.vapi.ai/settings/api-keys)
2. Update your `.env.development` file with the API key:
   ```
   VITE_VAPI_PUBLIC_KEY=your_actual_api_key_here
   ```
3. Modify `src/utils/initAttorneyProfileManager.js` to disable forced mock mode:
   ```javascript
   // Change this line
   const forceMock = !isProduction;
   
   // To this
   const forceMock = false;
   ```
4. Restart the development server

## Mock Service Behavior

The mock Vapi service simulates the behavior of the real Vapi service, including:

- Creating and managing assistants
- Making voice calls
- Handling user input and generating responses
- Simulating speech recognition and synthesis

The mock service uses predefined responses and simulated delays to mimic the real service.

## Limitations of Mock Mode

While mock mode is useful for development, it has some limitations:

- **Limited Functionality**: The mock service only implements the most commonly used features
- **Simplified Responses**: Responses are simplified and may not match the complexity of the real service
- **No Real Voice**: The mock service doesn't actually process audio or generate speech
- **No Learning**: The mock service doesn't learn from interactions like the real service

## Production Deployment

When deploying to production:

1. Make sure you have a valid Vapi API key
2. Update your production environment variables with the API key
3. The application will automatically use the real Vapi service in production

## Troubleshooting

If you encounter issues with mock mode:

1. Check the browser console for error messages
2. Verify that the mock service is being used by checking the connection status
3. Try restarting the development server
4. Check if there are any conflicts with other services or components

If you need to debug the mock service implementation, look at the code in `src/services/vapiMockService.js`.

## Conclusion

Using mock mode for development makes it easier to work on the application without needing a valid Vapi API key. It provides a consistent development experience and allows you to test the application's functionality without relying on external services.
