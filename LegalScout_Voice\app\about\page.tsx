import { motion } from 'framer-motion';
import { FaRocket, FaBrain, FaHandshake, FaClock } from 'react-icons/fa';

export default function AboutPage() {
  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white">
      {/* Hero Section */}
      <motion.section 
        className="px-4 py-20 md:py-32 max-w-7xl mx-auto text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
          Revolutionizing Legal Help
        </h1>
        <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto">
          LegalScout changes the way you find legal assistance. Instead of you searching for an attorney, 
          we have attorneys search for you.
        </p>
      </motion.section>

      {/* Key Features */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <FaBrain className="w-8 h-8" />,
                title: "AI-Powered Matching",
                description: "Our AI updates you as qualified attorneys review your anonymous brief."
              },
              {
                icon: <FaRocket className="w-8 h-8" />,
                title: "Quality-First Approach",
                description: "Attorneys pay to contact you, ensuring genuine interest and expertise."
              },
              {
                icon: <FaHandshake className="w-8 h-8" />,
                title: "Free For You",
                description: "Our service is completely free and respects your privacy."
              },
              {
                icon: <FaClock className="w-8 h-8" />,
                title: "Time-Saving",
                description: "Share your story once and let us handle the rest."
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                className="p-6 rounded-xl bg-gray-50 hover:bg-gray-100 transition-all duration-300"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="text-blue-600 mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">How It Works</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                step: "1",
                title: "Introduce Your Legal Matter",
                description: "Start the conversation with Scout, our intelligent assistant."
              },
              {
                step: "2",
                title: "Detail Your Needs",
                description: "Scout guides you through a detailed exploration of your case."
              },
              {
                step: "3",
                title: "Match and Meet Your Attorney",
                description: "Get connected with an ideal attorney knowledgeable about your issue."
              }
            ].map((step, index) => (
              <motion.div
                key={index}
                className="relative p-6 rounded-xl bg-white shadow-sm"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.2 }}
              >
                <div className="absolute -top-4 left-6 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold mt-4 mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-12">Frequently Asked Questions</h2>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                question: "If LegalScout is free to use, how do you make money?",
                answer: "Registered attorneys may view case summaries within their expertise, but must pay to contact you - ensuring genuine interest and expressing confidence."
              },
              {
                question: "How can I shape the platform?",
                answer: "Your insights directly influence LegalScout's development, ensuring it meets real-world needs."
              },
              {
                question: "What does sharing LegalScout accomplish?",
                answer: "By sharing with your network, you're helping us grow a supportive legal community that benefits everyone."
              },
              {
                question: "How does expanding the community benefit me?",
                answer: "As our community grows, enjoy broader coverage and quicker service, making legal assistance more accessible."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                className="p-6 rounded-xl bg-gray-50"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                <p className="text-gray-600">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-700 text-white">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl mb-8 opacity-90">Join LegalScout today and revolutionize your legal journey.</p>
          <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
            Get Started Now
          </button>
        </div>
      </section>
    </div>
  );
} 