import React, { useState, useEffect, useRef } from 'react';
import Vapi from '@vapi-ai/web';
import { BsFillMicFill, BsFillMicMuteFill } from 'react-icons/bs';
import './SimpleVapiCall.css';
import { formatSeconds } from '../utils/formatTime';
import SendButton from './SendButton';
import CallCard from './CallCard';
import VolumeLevel from './call/VolumeLevel';
import AssistantSpeechIndicator from './call/AssistantSpeechIndicator';

// Import the constants from vapiConstants
import { DEFAULT_ASSISTANT_ID, CALL_STATUS } from '../constants/vapiConstants';

/**
 * A simplified Vapi integration component following Vapi documentation best practices
 */
const SimpleVapiCall = ({
  apiKey,
  assistantId = 'legal-consultation',
  customInstructions = {},
  onCallStarted,
  onCallEnded,
  onMessageReceived,
  onError,
  isDarkTheme = false,
  theme,
  setMetadata,
  transcript,
  setIsCallStarted,
  onClose,
  presetPrompt,
  onMessage
}) => {
  // State
  const [status, setStatus] = useState('idle');
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState(presetPrompt || '');
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [volume, setVolume] = useState(0);
  const [dossierData, setDossierData] = useState({
    practiceArea: 'Personal Injury',
    location: {
      city: 'New York',
      state: 'NY'
    },
    caseType: 'Vehicle Accident',
    urgency: 'Medium',
    clientName: 'Sample Client',
    notes: 'Initial consultation for personal injury case'
  });
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [sendDisabled, setSendDisabled] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [micActive, setMicActive] = useState(true);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [showDebug, setShowDebug] = useState(false);
  const [transcriptStatus, setTranscriptStatus] = useState('');
  const [isInputEnabled, setIsInputEnabled] = useState(true);

  // Refs
  const vapiRef = useRef(null);
  const callIdRef = useRef(null);
  const isMountedRef = useRef(true);
  const messagesEndRef = useRef(null);
  const containerRef = useRef(null);
  const listenersRef = useRef(new Map()); // Add a ref to store listeners
  const inputRef = useRef(null);

  // Initialize Vapi on component mount
  useEffect(() => {
    console.log('▶️ SimpleVapiCall: Initializing component...');
    console.log('   Props Received:', { apiKey: !!apiKey, assistantId, customInstructions: !!customInstructions }); // Log prop presence

    isMountedRef.current = true;

    // Initialize Vapi instance
    if (!vapiRef.current && apiKey) {
      try {
        console.log('SimpleVapiCall: Creating Vapi instance with apiKey');
        vapiRef.current = new Vapi(apiKey);

        // Set up event listeners
        setupEventListeners();
      } catch (error) {
        console.error('SimpleVapiCall: Error initializing Vapi:', error);
        handleError(error);
      }
    }

    // Force visibility of our component and its children
    if (containerRef.current) {
      applyVisibilityStyles(containerRef.current);
    }

    // Expose test functions for triggering transcripts from console
    if (typeof window !== 'undefined') {
      window.testTranscript = (text = 'This is a test transcript', isFinal = false) => {
        console.log(`SimpleVapiCall: Test transcript triggered: "${text}" (final: ${isFinal})`);
        const event = new MessageEvent('message', {
          data: {
            type: 'transcript',
            transcript: text,
            isFinal: isFinal
          }
        });
        window.dispatchEvent(event);
        return 'Test transcript event dispatched. Check UI for transcript bubble.';
      };

      window.testFinalTranscript = (text = 'This is a final test transcript') => {
        return window.testTranscript(text, true);
      };

      console.log('SimpleVapiCall: Test functions added to window. Use window.testTranscript() or window.testFinalTranscript() to test.');
    }

    // Set up global message listener for transcript and speaking events
    const handleWindowMessage = (event) => {
      if (!event.data) return;

      console.log('SimpleVapiCall: Window message received:', event.data);

      // Enhanced transcript detection - check multiple possible sources
      const isTranscript =
        event.data.type === 'transcript' ||
        event.data.type === 'transcription' ||
        event.data.action === 'transcript' ||
        event.data.event === 'transcript' ||
        (event.data.data && event.data.data.transcript) ||
        event.data.transcript;

      if (isTranscript) {
        // Extract transcript text from various possible structures
        let transcriptText = '';
        let isFinal = false;

        // Check all possible places where transcript text might be
        if (typeof event.data.transcript === 'string') {
          transcriptText = event.data.transcript;
          isFinal = !!event.data.isFinal;
        } else if (event.data.text) {
          transcriptText = event.data.text;
          isFinal = !!event.data.isFinal;
        } else if (event.data.data && event.data.data.transcript) {
          transcriptText = event.data.data.transcript;
          isFinal = !!event.data.data.isFinal;
        } else if (event.data.payload && event.data.payload.transcript) {
          transcriptText = event.data.payload.transcript;
          isFinal = !!event.data.payload.isFinal;
        }

        console.log(`SimpleVapiCall: Window transcript event detected - text: "${transcriptText}", isFinal: ${isFinal}`);

        if (transcriptText && transcriptText.trim()) {
          // Always display the current transcript
          setCurrentTranscript(transcriptText);

          // If final and from user, add to messages
          if (isFinal) {
            const role = event.data.role || event.data.data?.role || 'user';
            if (role === 'user' && transcriptText.trim()) {
              console.log('SimpleVapiCall: Adding final window transcript to messages');

              // Add to messages array with timestamp
              setMessages(prev => [...prev, {
                role: 'user',
                content: transcriptText,
                timestamp: Date.now()
              }]);

              // Clear the transcript bubble after adding as a message
              setTimeout(() => {
                setCurrentTranscript('');
              }, 300);
            }
          }
        }
      }

      // Check for speaking status updates
      const isSpeakingEvent =
        event.data.type === 'speaking-status-change' ||
        (event.data.type === 'message' && event.data.speaking !== undefined) ||
        (event.data.speaking !== undefined) ||
        (event.data.data && event.data.data.speaking !== undefined) ||
        (event.data.event === 'speaking');

      if (isSpeakingEvent) {
        // Extract speaking status from different message formats
        let isSpeakingNow = false;

        if (typeof event.data.speaking === 'boolean') {
          isSpeakingNow = event.data.speaking;
        } else if (event.data.data && typeof event.data.data.speaking === 'boolean') {
          isSpeakingNow = event.data.data.speaking;
        } else if (event.data.payload && typeof event.data.payload.speaking === 'boolean') {
          isSpeakingNow = event.data.payload.speaking;
        }

        console.log('SimpleVapiCall: Window speaking event detected:', isSpeakingNow);
        setIsSpeaking(isSpeakingNow);
      }
    };

    // Add window message event listener
    window.addEventListener('message', handleWindowMessage);

    return () => {
      console.log('SimpleVapiCall: Cleaning up');
      isMountedRef.current = false;

      // Remove window message listener
      window.removeEventListener('message', handleWindowMessage);

      // End call if active
      if (callIdRef.current) {
        try {
          endCall();
        } catch (err) {
          console.error('SimpleVapiCall: Error ending call during cleanup:', err);
        }
      }

      // Remove event listeners
      if (vapiRef.current) {
        try {
          removeEventListeners();
        } catch (err) {
          console.error('SimpleVapiCall: Error removing event listeners:', err);
        }
      }
    };
  }, [apiKey, customInstructions]);

  // Auto-scroll to bottom when messages or transcript change - improved to prevent global scroll interference
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      const conversationArea = messagesEndRef.current.closest('.conversation-area');
      if (conversationArea) {
        // Check if user is near the bottom (within 100px)
        const isNearBottom = conversationArea.scrollHeight - conversationArea.scrollTop - conversationArea.clientHeight < 100;

        // Only auto-scroll if user is near the bottom (not reading old messages)
        if (isNearBottom) {
          try {
            // Directly set scrollTop without any smooth scrolling or events
            conversationArea.scrollTop = conversationArea.scrollHeight;

            // Ensure the scroll stays within the conversation area
            conversationArea.style.scrollBehavior = 'auto';

            // Force a layout recalculation to ensure scroll position is applied
            conversationArea.offsetHeight;

            console.log('SimpleVapiCall: Scrolled conversation area to bottom');
          } catch (error) {
            console.warn('Error scrolling conversation area:', error);
          }
        }
      }
    }
  };

  useEffect(() => {
    // Only scroll if we have messages, with debouncing to prevent excessive scrolling
    if (messages.length > 0) {
      // Use a timeout to debounce scroll calls
      const scrollTimeout = setTimeout(() => {
        scrollToBottom();
      }, 50); // Small delay to batch multiple message updates

      return () => clearTimeout(scrollTimeout);
    }
  }, [messages, currentTranscript]);

  // Auto-force visibility of component
  useEffect(() => {
    // Force visibility every 2 seconds to ensure it's always visible
    const visibilityInterval = setInterval(() => {
      if (containerRef.current) {
        applyVisibilityStyles(containerRef.current);
      }
    }, 2000);

    return () => clearInterval(visibilityInterval);
  }, []);

  // Add effect for dossier animation style
  useEffect(() => {
    if (!document.querySelector('#dossier-animation-style')) {
      const style = document.createElement('style');
      style.id = 'dossier-animation-style';
      style.textContent = `
        .dossier-items.updating {
          animation: dossier-update 0.3s ease-in-out;
        }
        @keyframes dossier-update {
          0% { opacity: 0.7; transform: scale(0.98); }
          100% { opacity: 1; transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }

    return () => {
      const styleElement = document.querySelector('#dossier-animation-style');
      if (styleElement) {
        styleElement.remove();
      }
    };
  }, []);

  // Apply visibility styles to elements to ensure they're displayed properly
  const applyVisibilityStyles = (element) => {
    try {
      // Force the element to be visible
      element.style.visibility = 'visible';
      element.style.display = 'flex';
      element.style.opacity = '1';
      element.style.position = 'relative';
      element.style.zIndex = '100'; // High z-index to ensure visibility

      // Apply to all children recursively
      Array.from(element.children).forEach(child => {
        child.style.visibility = 'visible';
        child.style.display = child.tagName === 'DIV' ? 'flex' : '';
        child.style.opacity = '1';

        // Apply higher z-index to transcript areas
        if (child.classList.contains('transcript-area')) {
          child.style.zIndex = '200';
        }

        // Apply higher z-index to message displays
        if (child.classList.contains('messages-display')) {
          child.style.zIndex = '150';
        }

        // Apply to grandchildren
        if (child.children && child.children.length > 0) {
          applyVisibilityStyles(child);
        }
      });
    } catch (error) {
      console.error('❌ [SimpleVapiCall] Error forcing visibility:', error);
    }
  };

  // Periodically filter out duplicate welcome messages
  useEffect(() => {
    // Wait until we have multiple messages
    if (messages.length > 1) {
      // Apply filtering and update if changes were made
      const filteredMessages = filterDuplicateWelcomeMessages(messages);

      // Only update if filtering actually changed the messages
      if (filteredMessages.length !== messages.length) {
        console.log('SimpleVapiCall: Filtered out duplicate welcome messages');
        setMessages(filteredMessages);
      }
    }
  }, [messages.length]); // Only run when message count changes

  // Auto-start call when component mounts
  useEffect(() => {
    if (vapiRef.current && status === 'idle') {
      console.log('SimpleVapiCall: Auto-starting call...');
      // Add a small delay to ensure everything is properly initialized
      const timer = setTimeout(() => {
        startCall();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [vapiRef.current, status]);

  // Helper to filter out duplicate welcome messages
  const filterDuplicateWelcomeMessages = (messagesList) => {
    if (!messagesList || messagesList.length < 2) return messagesList;

    // Get only assistant messages
    const assistantMessages = messagesList.filter(m => m.role === 'assistant');

    // If we have multiple assistant messages, detect potential welcome messages
    if (assistantMessages.length > 1) {
      console.log('SimpleVapiCall: Checking for duplicate welcome messages in list of', assistantMessages.length, 'assistant messages');

      // Words that commonly appear in welcome messages
      const welcomePatterns = [
        'hello', 'hi', 'greetings', 'welcome', 'assist', 'help you',
        'how can i help', 'legal', 'scout', 'smith & associates',
        'my name is', "i'm", 'today'
      ];

      // First, identify which messages look like welcome messages
      const welcomeMessageIndexes = [];

      messagesList.forEach((msg, index) => {
        if (msg.role === 'assistant') {
          const content = msg.content.toLowerCase();

          // Check if this looks like a welcome message
          const matchesWelcomePattern = welcomePatterns.some(pattern =>
            content.includes(pattern)
          );

          if (matchesWelcomePattern) {
            welcomeMessageIndexes.push(index);
          }
        }
      });

      // If we found multiple welcome messages
      if (welcomeMessageIndexes.length > 1) {
        console.log('SimpleVapiCall: Found', welcomeMessageIndexes.length, 'potential welcome messages');

        // Keep only the most recent welcome message
        const filteredMessages = [...messagesList];

        // Sort indexes in descending order to remove from end to beginning
        const sortedIndexes = [...welcomeMessageIndexes].sort((a, b) => b - a);

        // Keep the first (most recent) welcome message, remove others
        for (let i = 1; i < sortedIndexes.length; i++) {
          filteredMessages.splice(sortedIndexes[i], 1);
        }

        console.log('SimpleVapiCall: After filtering, messages reduced from',
          messagesList.length, 'to', filteredMessages.length);

        return filteredMessages;
      }
    }

    return messagesList;
  };

  // Add this effect to update the transcript bubble whenever currentTranscript changes
  useEffect(() => {
    console.log('SimpleVapiCall: currentTranscript changed:', currentTranscript);
    // No DOM manipulation needed - the component will re-render with the updated state
  }, [currentTranscript]);

  // Effect to auto-scroll messages into view when messages change - improved
  useEffect(() => {
    if (messagesEndRef.current) {
      console.log('SimpleVapiCall: Scrolling to messages end');
      // Use the improved scroll function instead of scrollIntoView
      scrollToBottom();
    }
  }, [messages]);

  // Effect to monitor and handle transcript updates
  useEffect(() => {
    if (currentTranscript) {
      console.log('SimpleVapiCall: Current transcript updated:', currentTranscript);

      // Auto-scroll to the bottom when transcript updates
      if (messagesEndRef.current) {
        setTimeout(() => {
          messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }, 100); // Small delay to ensure render completes
      }
    }
  }, [currentTranscript]);

  // Add a handler function for the first message from the assistant
  useEffect(() => {
    // When we receive a message and it's from the assistant, turn off loading state
    if (messages.length > 0 && messages[messages.length - 1].role === 'assistant') {
      setIsLoading(false);
    }
  }, [messages]);

  // Handle sending user messages
  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    try {
      console.log('SimpleVapiCall: Sending user message:', inputValue);

      // Create user message object
      const userMessage = {
        role: 'user',
        content: inputValue,
        timestamp: Date.now()
      };

      // Add to messages array - functional update to preserve all previous messages
      setMessages(prevMessages => [...prevMessages, userMessage]);

      // Disable send button while processing
      setSendDisabled(true);

      // Send message to Vapi if available
      if (vapiRef.current && callIdRef.current) {
        vapiRef.current.sendText(callIdRef.current, inputValue);

        // Clear input field after sending
        setInputValue('');

        // Scroll to bottom with new message
        setTimeout(() => {
          if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
          }
          setSendDisabled(false);
        }, 300);
      } else {
        console.warn('SimpleVapiCall: Cannot send message - Vapi or callId not available');
        setInputValue('');
        setSendDisabled(false);
      }
    } catch (error) {
      console.error('SimpleVapiCall: Error sending message:', error);
      setSendDisabled(false);
    }
  };

  // Handle input key events (like pressing Enter to send)
  const handleInputKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey && !sendDisabled) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Set up event listeners for the Vapi instance
  const setupEventListeners = () => {
    if (!vapiRef.current) return;

    console.log('SimpleVapiCall: Setting up event listeners');

    // Call started events
    const callStartedListener = (event) => {
      console.log('SimpleVapiCall: Call started event received:', event);
      if (isMountedRef.current) {
        setStatus('connected');
        setIsLoading(false);

        // Store the callId for later use
        if (event.callId) {
          callIdRef.current = event.callId;
        } else if (event.id) {
          callIdRef.current = event.id;
        }

        console.log(`SimpleVapiCall: Call ID stored: ${callIdRef.current}`);

        // Add welcome message when call is connected if specified in customInstructions
        if (customInstructions && customInstructions.initialMessage && messages.length === 0) {
          setMessages([{
            role: 'assistant',
            content: customInstructions.initialMessage,
            timestamp: Date.now()
          }]);
        }

        // Notify parent of call started
        if (typeof onCallStarted === 'function') {
          onCallStarted(event);
        }

        // Set isCallStarted in parent component if the function is provided
        if (typeof setIsCallStarted === 'function') {
          setIsCallStarted(true);
        }

        // Ensure the connecting banner is gone
        document.querySelector('.connection-status-banner')?.classList.add('hidden');
      }
    };

    // Call ended events
    const callEndedListener = (event) => {
      console.log('SimpleVapiCall: Call ended event received:', event);
      if (isMountedRef.current) {
        setStatus('idle');

        // Clear the callId
        callIdRef.current = null;

        // Notify parent of call ended
        if (typeof onCallEnded === 'function') {
          onCallEnded(event);
        }

        // Set isCallStarted in parent component if the function is provided
        if (typeof setIsCallStarted === 'function') {
          setIsCallStarted(false);
        }
      }
    };

    // Error events
    const errorListener = (error) => {
      console.error('SimpleVapiCall: Error event received:', error);
      handleError(error);
    };

    // Message events from the assistant
    const messageListener = (msg) => {
      console.log('SimpleVapiCall: Message event received:', msg);
      if (isMountedRef.current) {
        try {
          // Process the message
          let processedMessage = {};

          // Handle different message formats
          if (typeof msg === 'string') {
            try {
              // Try to parse as JSON first
              processedMessage = JSON.parse(msg);
            } catch (e) {
              // If not JSON, treat as plain content
              processedMessage = { role: 'assistant', content: msg, type: 'message' };
            }
          } else if (typeof msg === 'object') {
            processedMessage = msg;
          }

          // Process different message types
          if (processedMessage) {
            const messageType = processedMessage.type || 'message';

            switch (messageType) {
              case 'message':
                // Handle regular messages
                if (processedMessage.role && processedMessage.content) {
                  console.log(`SimpleVapiCall: Message received - role: ${processedMessage.role}, content: "${processedMessage.content.substring(0, 50)}..."`);

                  // If we get a message, turn off loading state
                  if (isLoading) {
                    setIsLoading(false);
                  }

                  // Add message to history if it has content
                  if (typeof processedMessage.content === 'string' && processedMessage.content.trim()) {
                    // Create new message with timestamp
                    const newMessage = {
                      role: processedMessage.role,
                      content: processedMessage.content,
                      timestamp: Date.now()
                    };

                    // Add to messages - important to use functional update to preserve all previous messages
                    setMessages(prevMessages => [...prevMessages, newMessage]);

                    // Notify parent of message received if it's from assistant
                    if (processedMessage.role === 'assistant' && onMessageReceived) {
                      onMessageReceived(processedMessage);
                    }

                    // Send onMessage for parent component integration
                    if (typeof onMessage === 'function') {
                      onMessage(processedMessage);
                    }

                    // Ensure we scroll to the latest message
                    setTimeout(() => {
                      if (messagesEndRef.current) {
                        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
                      }
                    }, 100);
                  }
                }
                break;

              case 'speaking':
                // Handle speaking status updates
                if (typeof processedMessage.speaking !== 'undefined') {
                  setIsSpeaking(!!processedMessage.speaking);
                }
                break;

              case 'dossier-data':
                // Handle dossier data updates
                try {
                  const parsedData = typeof processedMessage.data === 'string'
                    ? JSON.parse(processedMessage.data)
                    : processedMessage.data;

                  if (parsedData) {
                    console.log('SimpleVapiCall: Dossier data received:', parsedData);

                    // Update dossier data without overwriting existing data
                    setDossierData(prevData => {
                      const newData = { ...prevData, ...parsedData };
                      console.log('SimpleVapiCall: Updated dossier data:', newData);
                      return newData;
                    });

                    // Also call the setMetadata function if available
                    if (typeof setMetadata === 'function') {
                      setMetadata(parsedData);
                    }
                  }
                } catch (err) {
                  console.error('SimpleVapiCall: Error parsing dossier data', err);
                }
                break;

              case 'transcript':
                // Handle transcript messages directly from Vapi
                try {
                  const transcriptText = processedMessage.text || processedMessage.transcript || '';
                  const isFinal = processedMessage.isFinal || processedMessage.is_final || false;
                  const role = processedMessage.role || 'user';

                  console.log(`SimpleVapiCall: Transcript received from Vapi - text: "${transcriptText}", isFinal: ${isFinal}`);

                  if (transcriptText && transcriptText.trim()) {
                    // Always display transcript in real-time
                    setCurrentTranscript(transcriptText);

                    // If final transcript, add to message history
                    if (isFinal && role === 'user') {
                      setMessages(prevMessages => [...prevMessages, {
                        role: 'user',
                        content: transcriptText,
                        timestamp: Date.now()
                      }]);

                      // Clear current transcript after a delay
                      setTimeout(() => {
                        setCurrentTranscript('');
                      }, 300);
                    }
                  }
                } catch (err) {
                  console.error('SimpleVapiCall: Error processing transcript message', err);
                }
                break;

              default:
                console.log('SimpleVapiCall: Unhandled message type', processedMessage.type);
                break;
            }
          }
        } catch (error) {
          console.error('SimpleVapiCall: Error processing message', error);
        }
      }
    };

    // Speaking events
    const speakingListener = (isSpeaking) => {
      console.log('SimpleVapiCall: Speaking event received:', isSpeaking);
      if (isMountedRef.current) {
        setIsSpeaking(!!isSpeaking);
      }
    };

    // Volume level events
    const volumeLevelListener = (level) => {
      if (isMountedRef.current) {
        setVolume(typeof level === 'number' ? level : 0);
      }
    };

    // Transcription events
    const transcriptionListener = (transcriptionData) => {
      console.log('SimpleVapiCall: Transcription event received', transcriptionData);
      if (isMountedRef.current) {
        try {
          // Process the transcription data
          const text = transcriptionData?.transcript || transcriptionData?.text || '';
          const isFinal = transcriptionData?.is_final || transcriptionData?.isFinal || false;
          const role = transcriptionData?.role || 'user';

          console.log(`SimpleVapiCall: Processing transcription - text: "${text}", isFinal: ${isFinal}, role: ${role}`);

          // Always update the transcript in real-time if we have text
          if (text && text.trim()) {
            console.log('SimpleVapiCall: Updating current transcript:', text);
            setCurrentTranscript(text);
          }

          // If this is a final transcript (complete utterance), add it to the message history
          if (isFinal && role === 'user' && text && text.trim()) {
            console.log('SimpleVapiCall: Adding final transcript to message history');

            // Add to messages array
            setMessages(prevMessages => [...prevMessages, {
              role: 'user',
              content: text,
              timestamp: Date.now()
            }]);

            // Clear the current transcript
            setCurrentTranscript('');
          }
        } catch (error) {
          console.error('SimpleVapiCall: Error processing transcription:', error);
        }
      }
    };

    // Store listeners in the ref for later removal
    listenersRef.current.set('call-started', callStartedListener);
    listenersRef.current.set('call-ended', callEndedListener);
    listenersRef.current.set('error', errorListener);
    listenersRef.current.set('message', messageListener);
    listenersRef.current.set('speaking', speakingListener);
    listenersRef.current.set('volume-level', volumeLevelListener);
    listenersRef.current.set('transcription', transcriptionListener);

    // Attach listeners to Vapi instance
    vapiRef.current.on('call-started', callStartedListener);
    vapiRef.current.on('call-ended', callEndedListener);
    vapiRef.current.on('error', errorListener);
    vapiRef.current.on('message', messageListener);
    vapiRef.current.on('speaking', speakingListener);
    vapiRef.current.on('volume-level', volumeLevelListener);
    vapiRef.current.on('transcription', transcriptionListener);
  };

  // Remove event listeners
  const removeEventListeners = () => {
    if (!vapiRef.current) return;

    console.log('SimpleVapiCall: Removing event listeners');

    for (const [event, listener] of listenersRef.current.entries()) {
      if (vapiRef.current && typeof vapiRef.current.off === 'function') {
        vapiRef.current.off(event, listener);
      }
    }

    listenersRef.current.clear();
  };

  // Handle errors
  const handleError = (error) => {
    console.error('SimpleVapiCall: Error occurred:', error);
    setStatus('error');
    if (typeof onError === 'function') {
      onError(error);
    }
  };

  // Map assistantId to a valid UUID if needed
  const getAssistantUUID = (id) => {
    // Use a mapping object to convert known identifiers to UUIDs
    const idMapping = {
      'legal-consultation': DEFAULT_ASSISTANT_ID || 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865'
    };

    // If the ID is already a UUID format, return it as is
    if (id && id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      return id;
    }

    // Look up the ID in the mapping
    return idMapping[id] || DEFAULT_ASSISTANT_ID || 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865';
  };

  // Start a call using Vapi
  const startCall = () => {
    if (!vapiRef.current) {
      console.error('SimpleVapiCall: Cannot start call, Vapi not initialized');
      return;
    }

    // Don't start if already in progress or connecting
    if (status === 'connected' || status === 'connecting') {
      console.warn('SimpleVapiCall: Call already in progress or connecting');
      return;
    }

    console.log('SimpleVapiCall: Starting call...');
    setStatus('connecting');

    try {
      // Get assistant UUID or ID
      const assistantUUID = getAssistantUUID(assistantId);
      console.log(`SimpleVapiCall: Using assistant ID: ${assistantUUID}`);

      // Start call with Vapi
      vapiRef.current.start(assistantUUID);

      // Set state to connecting
      console.log('SimpleVapiCall: Call started successfully');
    } catch (error) {
      console.error('SimpleVapiCall: Error starting call:', error);
      handleError(error);
    }
  };

  // End a call using Vapi
  const endCall = () => {
    if (!vapiRef.current || !callIdRef.current) {
      console.warn('SimpleVapiCall: Cannot end call, Vapi or callId not available');
      return;
    }

    console.log('SimpleVapiCall: Ending call...');

    try {
      vapiRef.current.stop(callIdRef.current);
      callIdRef.current = null;

      // Set state to idle
      setStatus('idle');

      // Notify parent of call ended if provided
      if (typeof onCallEnded === 'function') {
        onCallEnded({ status: 'ended' });
      }

      // Set isCallStarted in parent component if the function is provided
      if (typeof setIsCallStarted === 'function') {
        setIsCallStarted(false);
      }

      // Call onClose if provided
      if (typeof onClose === 'function') {
        onClose();
      }
    } catch (error) {
      console.error('SimpleVapiCall: Error ending call:', error);
      handleError(error);
    }
  };

  // Calculate volume level for UI display
  const getVolumeBarLevel = () => {
    // Map volume (typically 0-1) to 0-100% for visual representation
    return `${Math.min(100, Math.max(0, volume * 100))}%`;
  };

  // Render status text based on current status
  const getStatusText = () => {
    switch (status) {
      case 'idle': return 'Ready';
      case 'connecting': return 'Connecting...';
      case 'connected': return 'Connected';
      case 'error': return 'Error';
      default: return 'Unknown';
    }
  };

  // Updated getDossierDisplayItems for better data display
  const getDossierDisplayItems = () => {
    const items = [];

    if (dossierData.practiceArea) {
      items.push({
        label: 'Practice Area',
        value: dossierData.practiceArea
      });
    }

    if (dossierData.location) {
      let locationStr = '';
      const loc = dossierData.location;

      if (loc.city && loc.state) {
        locationStr = `${loc.city}, ${loc.state}`;
      } else if (loc.city) {
        locationStr = loc.city;
      } else if (loc.state) {
        locationStr = loc.state;
      } else if (typeof loc === 'string') {
        locationStr = loc;
      }

      if (locationStr) {
        items.push({
          label: 'Location',
          value: locationStr
        });
      }
    }

    if (dossierData.caseType) {
      items.push({
        label: 'Case Type',
        value: dossierData.caseType
      });
    }

    if (dossierData.urgency) {
      items.push({
        label: 'Urgency',
        value: dossierData.urgency
      });
    }

    if (dossierData.clientName) {
      items.push({
        label: 'Client',
        value: dossierData.clientName
      });
    }

    if (dossierData.notes) {
      items.push({
        label: 'Notes',
        value: dossierData.notes
      });
    }

    return items;
  };

  // Determine if dossier has enough data to display
  const hasDossierData = () => {
    const hasData = Object.keys(dossierData).length > 0;
    console.log('SimpleVapiCall: Has dossier data:', hasData, dossierData);
    return hasData;
  };

  // Render messages with proper conversation history
  const renderMessages = () => {
    if (messages.length === 0 && !currentTranscript) {
      return null;
    }

    return (
      <div className="message-list">
        {messages.map((message, index) => (
          <div key={`msg-${index}-${message.timestamp || Date.now()}`} className={`message-bubble ${message.role}`}>
            <div className="message-header">
              <span className="message-role">{message.role === 'user' ? 'You' : 'Assistant'}</span>
              <span className="message-timestamp">
                {message.timestamp ? new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : ''}
              </span>
            </div>
            <div className="message-content">{message.content}</div>
          </div>
        ))}

        {/* Display current transcript if available */}
        {currentTranscript && (
          <div className="message-bubble transcript">
            <div className="message-header">
              <span className="message-role">You (typing...)</span>
            </div>
            <div className="message-content">{currentTranscript}</div>
          </div>
        )}
      </div>
    );
  };

  // Main render
  return (
    <div
      className={`simple-vapi-call ${isDarkTheme ? 'dark-theme' : 'light-theme'}`}
      ref={containerRef}
    >
      <div className="call-header">
        <div className="call-title">Legal Scout Assistant</div>
        <button onClick={endCall} className="end-call-button">
          <img src="/PRIMARY CLEAR.png" alt="Legal Scout Mascot" className="end-call-mascot" />
          <span>End Call</span>
        </button>
      </div>

      <div className="call-ui-container">
        <div className="assistant-card">
          <div className="assistant-image">
            <img src="/PRIMARY CLEAR.png" alt="Legal Scout Assistant" />
            {isSpeaking && <div className="speaking-indicator"></div>}
          </div>
          <div className="assistant-status">
            {isSpeaking && <div className="speaking-status">Speaking...</div>}
          </div>
        </div>

        {/* Connection status shown separately */}
        {status === 'connecting' && (
          <div className="connection-status-banner">
            Initializing connection...
          </div>
        )}
      </div>

      <div className="call-content">
        {/* Dossier section - Always visible */}
        <div className="dossier-section" style={{ zIndex: 150 }}>
          <h3 className="dossier-title">Case Information</h3>
          <div className="dossier-items">
            {getDossierDisplayItems().map((item, index) => (
              <div key={index} className="dossier-item">
                <div className="dossier-label">{item.label}</div>
                <div className="dossier-value">{item.value}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Conversation area */}
        <div className="conversation-area">
          <div className="messages-display">
            {renderMessages()}
            <div ref={messagesEndRef} style={{ height: '1px', clear: 'both' }} />
          </div>

          <div className="input-container">
            <input
              ref={inputRef}
              type="text"
              className="message-input"
              placeholder="Type your message..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleInputKeyDown}
              disabled={!isInputEnabled || status !== 'connected'}
            />
            <SendButton
              onClick={handleSendMessage}
              disabled={sendDisabled || !inputValue.trim() || status !== 'connected'}
            />
          </div>
        </div>
      </div>

      {/* Status indicator for debugging */}
      {showDebug && (
        <div className="debug-status" style={{
          position: 'absolute',
          bottom: '10px',
          right: '10px',
          background: 'rgba(0,0,0,0.7)',
          padding: '5px',
          borderRadius: '5px',
          zIndex: 1000
        }}>
          Status: {status} | Speaking: {isSpeaking ? 'Yes' : 'No'} | Transcript: {transcriptStatus}
        </div>
      )}
    </div>
  );
};

export default SimpleVapiCall;

