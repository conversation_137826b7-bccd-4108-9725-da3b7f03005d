<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Import Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto; }
        input[type="url"] { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        .status { font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Website Import Test Suite</h1>
        <p>Test each component of the website import system to identify issues.</p>

        <!-- Test Configuration -->
        <div class="test-section info">
            <h3>📋 Test Configuration</h3>
            <label>Test URL:</label>
            <input type="url" id="testUrl" value="https://www.generalcounsel.online" placeholder="Enter a law firm website URL">
            <button onclick="runAllTests()">🚀 Run All Tests</button>
            <button onclick="clearLogs()">🧹 Clear Logs</button>
            <button onclick="testDebugAPI()">🔧 Test Debug API</button>
        </div>

        <!-- Environment Check -->
        <div class="test-section" id="envSection">
            <h3>🌍 Environment Check</h3>
            <div class="status" id="envStatus">Click "Run All Tests" to check environment</div>
            <div class="log" id="envLog"></div>
        </div>

        <!-- Jina AI Test -->
        <div class="test-section" id="jinaSection">
            <h3>📖 Jina AI Reader Test</h3>
            <div class="status" id="jinaStatus">Waiting...</div>
            <button onclick="testJinaReader()" id="jinaBtn">Test Jina AI</button>
            <div class="log" id="jinaLog"></div>
        </div>

        <!-- Local API Test -->
        <div class="test-section" id="apiSection">
            <h3>🔧 Local API Test</h3>
            <div class="status" id="apiStatus">Waiting...</div>
            <button onclick="testLocalAPI()" id="apiBtn">Test Local API</button>
            <div class="log" id="apiLog"></div>
        </div>

        <!-- MCP Blocker Test -->
        <div class="test-section" id="mcpSection">
            <h3>🚫 MCP Blocker Test</h3>
            <div class="status" id="mcpStatus">Waiting...</div>
            <button onclick="testMCPBlocker()" id="mcpBtn">Test MCP Blocker</button>
            <div class="log" id="mcpLog"></div>
        </div>

        <!-- Full Integration Test -->
        <div class="test-section" id="integrationSection">
            <h3>🔄 Full Integration Test</h3>
            <div class="status" id="integrationStatus">Waiting...</div>
            <button onclick="testFullIntegration()" id="integrationBtn">Test Full Flow</button>
            <div class="log" id="integrationLog"></div>
        </div>

        <!-- Test Summary -->
        <div class="test-section" id="summarySection">
            <h3>📊 Test Summary</h3>
            <div id="testSummary">Run tests to see summary</div>
        </div>
    </div>

    <script>
        let testResults = {
            environment: null,
            jina: null,
            api: null,
            mcp: null,
            integration: null
        };

        function log(sectionId, message, type = 'info') {
            const logElement = document.getElementById(sectionId + 'Log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function setStatus(sectionId, message, type = 'info') {
            const statusElement = document.getElementById(sectionId + 'Status');
            const sectionElement = document.getElementById(sectionId + 'Section');
            
            statusElement.textContent = message;
            sectionElement.className = `test-section ${type}`;
        }

        function clearLogs() {
            ['env', 'jina', 'api', 'mcp', 'integration'].forEach(section => {
                document.getElementById(section + 'Log').textContent = '';
                setStatus(section, 'Cleared', 'info');
            });
            document.getElementById('testSummary').textContent = 'Run tests to see summary';
        }

        async function checkEnvironment() {
            log('env', 'Checking environment...');
            setStatus('env', 'Checking...', 'warning');

            try {
                // Check if we're on the right port
                const currentUrl = window.location.href;
                log('env', `Current URL: ${currentUrl}`);
                
                // Check if API endpoint is accessible
                const apiUrl = '/api/website-import';
                log('env', `Testing API endpoint: ${apiUrl}`);
                
                // Test CORS and basic connectivity
                const corsTest = await fetch('/api/website-import', {
                    method: 'OPTIONS'
                });
                
                log('env', `CORS preflight: ${corsTest.status} ${corsTest.statusText}`);
                
                setStatus('env', '✅ Environment OK', 'success');
                testResults.environment = { success: true };
                return true;
            } catch (error) {
                log('env', `Environment error: ${error.message}`);
                setStatus('env', '❌ Environment Error', 'error');
                testResults.environment = { success: false, error: error.message };
                return false;
            }
        }

        function validateUrl(url) {
            try {
                // Clean up the URL
                let cleanUrl = url.trim();

                // Remove any duplicate protocols
                cleanUrl = cleanUrl.replace(/^https?:\/\/https?:\/\//, 'https://');

                // Ensure it starts with https://
                if (!cleanUrl.startsWith('http://') && !cleanUrl.startsWith('https://')) {
                    cleanUrl = 'https://' + cleanUrl;
                }

                // Validate URL format
                new URL(cleanUrl);
                return cleanUrl;
            } catch (error) {
                throw new Error(`Invalid URL format: ${url}`);
            }
        }

        async function testJinaReader() {
            const testUrl = document.getElementById('testUrl').value;
            log('jina', `Testing Jina AI with URL: ${testUrl}`);
            setStatus('jina', 'Testing...', 'warning');

            try {
                const cleanUrl = validateUrl(testUrl);
                log('jina', `Cleaned URL: ${cleanUrl}`);

                const jinaUrl = `https://r.jina.ai/${cleanUrl}`;
                log('jina', `Fetching: ${jinaUrl}`);

                const response = await fetch(jinaUrl, {
                    headers: {
                        'Accept': 'application/json',
                        'X-With-Generated-Alt': 'true'
                    }
                });

                if (!response.ok) {
                    throw new Error(`Jina API error: ${response.status} ${response.statusText}`);
                }

                const content = await response.text();
                log('jina', `Content length: ${content.length} characters`);
                log('jina', `Sample: ${content.substring(0, 200)}...`);

                setStatus('jina', '✅ Jina AI Success', 'success');
                testResults.jina = { success: true, contentLength: content.length };
                return content;
            } catch (error) {
                log('jina', `Jina error: ${error.message}`);
                setStatus('jina', '❌ Jina AI Failed', 'error');
                testResults.jina = { success: false, error: error.message };
                return null;
            }
        }

        async function testLocalAPI() {
            const testUrl = document.getElementById('testUrl').value;
            log('api', `Testing local API with URL: ${testUrl}`);
            setStatus('api', 'Testing...', 'warning');

            try {
                const cleanUrl = validateUrl(testUrl);
                log('api', `Using cleaned URL: ${cleanUrl}`);

                const response = await fetch('/api/website-import', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: cleanUrl })
                });

                log('api', `Response status: ${response.status} ${response.statusText}`);

                const responseText = await response.text();
                log('api', `Response length: ${responseText.length} characters`);

                if (responseText.length > 1000) {
                    log('api', `Response sample: ${responseText.substring(0, 500)}...`);
                } else {
                    log('api', `Full response: ${responseText}`);
                }

                if (!response.ok) {
                    throw new Error(`API error: ${response.status} - ${responseText}`);
                }

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    throw new Error(`JSON parse error: ${parseError.message}`);
                }

                if (result.success) {
                    log('api', `Extracted firm: ${result.data.firmName || 'Not found'}`);
                    log('api', `Confidence: ${result.data.extractionConfidence || 'N/A'}`);
                    setStatus('api', '✅ Local API Success', 'success');
                    testResults.api = { success: true, data: result.data };
                    return result.data;
                } else {
                    throw new Error(result.error || 'API returned failure');
                }
            } catch (error) {
                log('api', `API error: ${error.message}`);
                setStatus('api', '❌ Local API Failed', 'error');
                testResults.api = { success: false, error: error.message };
                return null;
            }
        }

        async function testDebugAPI() {
            const testUrl = document.getElementById('testUrl').value;
            log('api', `Testing DEBUG API with URL: ${testUrl}`);
            setStatus('api', 'Testing Debug API...', 'warning');

            try {
                const cleanUrl = validateUrl(testUrl);
                log('api', `Using cleaned URL: ${cleanUrl}`);

                const response = await fetch('/api/debug-website-import', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: cleanUrl })
                });

                const responseText = await response.text();
                log('api', `Debug response status: ${response.status}`);
                log('api', `Debug response: ${responseText}`);

                if (response.ok) {
                    const result = JSON.parse(responseText);
                    if (result.debug) {
                        log('api', `Debug steps: ${JSON.stringify(result.debug.steps, null, 2)}`);
                        log('api', `Environment: ${JSON.stringify(result.debug.environment, null, 2)}`);
                    }
                    setStatus('api', '✅ Debug API Success', 'success');
                } else {
                    setStatus('api', '❌ Debug API Failed', 'error');
                }
            } catch (error) {
                log('api', `Debug API error: ${error.message}`);
                setStatus('api', '❌ Debug API Failed', 'error');
            }
        }

        async function testMCPBlocker() {
            log('mcp', 'Testing MCP blocker...');
            setStatus('mcp', 'Testing...', 'warning');

            try {
                // Test if MCP blocker utility exists
                log('mcp', 'Checking if MCP blocker is available...');
                
                // Simulate MCP blocking test
                const originalFetch = window.fetch;
                let mcpBlocked = false;
                
                // Mock MCP blocker behavior
                window.fetch = function(url, options) {
                    if (typeof url === 'string' && url.includes('mcp')) {
                        mcpBlocked = true;
                        return Promise.reject(new Error('MCP_BLOCKED_DURING_IMPORT'));
                    }
                    return originalFetch.call(this, url, options);
                };
                
                // Test blocking
                try {
                    await fetch('https://mcp.vapi.ai/test');
                } catch (error) {
                    if (error.message.includes('MCP_BLOCKED_DURING_IMPORT')) {
                        log('mcp', 'MCP blocking works correctly');
                    }
                }
                
                // Restore original fetch
                window.fetch = originalFetch;
                
                if (mcpBlocked) {
                    setStatus('mcp', '✅ MCP Blocker Works', 'success');
                    testResults.mcp = { success: true };
                    return true;
                } else {
                    throw new Error('MCP request was not blocked');
                }
            } catch (error) {
                log('mcp', `MCP blocker error: ${error.message}`);
                setStatus('mcp', '❌ MCP Blocker Failed', 'error');
                testResults.mcp = { success: false, error: error.message };
                return false;
            }
        }

        async function testFullIntegration() {
            log('integration', 'Testing full integration flow...');
            setStatus('integration', 'Testing...', 'warning');

            try {
                // Test the complete flow
                const testUrl = document.getElementById('testUrl').value;
                
                log('integration', '1. Starting MCP blocking...');
                // Simulate MCP blocking
                
                log('integration', '2. Testing website import...');
                const result = await testLocalAPI();
                
                if (!result) {
                    throw new Error('Website import failed');
                }
                
                log('integration', '3. Testing data extraction...');
                if (!result.firmName && !result.attorneyName) {
                    log('integration', 'Warning: No firm or attorney name extracted');
                }
                
                log('integration', '4. Stopping MCP blocking...');
                // Simulate stopping MCP blocking
                
                log('integration', 'Full integration test completed successfully');
                setStatus('integration', '✅ Integration Success', 'success');
                testResults.integration = { success: true };
                return true;
            } catch (error) {
                log('integration', `Integration error: ${error.message}`);
                setStatus('integration', '❌ Integration Failed', 'error');
                testResults.integration = { success: false, error: error.message };
                return false;
            }
        }

        async function runAllTests() {
            clearLogs();
            
            log('env', 'Starting comprehensive test suite...');
            
            // Run tests in sequence
            await checkEnvironment();
            await testJinaReader();
            await testLocalAPI();
            await testMCPBlocker();
            await testFullIntegration();
            
            // Generate summary
            updateTestSummary();
        }

        function updateTestSummary() {
            const results = Object.values(testResults);
            const passed = results.filter(r => r && r.success).length;
            const failed = results.filter(r => r && !r.success).length;
            const total = results.filter(r => r !== null).length;
            
            const summaryHtml = `
                <h4>📊 Results Summary</h4>
                <p><strong>Total Tests:</strong> ${total}</p>
                <p><strong>✅ Passed:</strong> ${passed}</p>
                <p><strong>❌ Failed:</strong> ${failed}</p>
                <p><strong>📈 Success Rate:</strong> ${total > 0 ? Math.round((passed / total) * 100) : 0}%</p>
                
                <h4>💡 Recommendations</h4>
                ${generateRecommendations()}
            `;
            
            document.getElementById('testSummary').innerHTML = summaryHtml;
        }

        function generateRecommendations() {
            const recommendations = [];
            
            if (testResults.jina && !testResults.jina.success) {
                recommendations.push('🌐 Jina AI failed - check internet connection and CORS settings');
            }
            
            if (testResults.api && !testResults.api.success) {
                recommendations.push('🔧 Local API failed - check server is running and OpenAI API key is configured');
            }
            
            if (testResults.mcp && !testResults.mcp.success) {
                recommendations.push('🚫 MCP blocker failed - check implementation');
            }
            
            if (recommendations.length === 0) {
                recommendations.push('🎉 All tests passed! Website import should work correctly.');
            }
            
            return recommendations.map(r => `<p>${r}</p>`).join('');
        }

        // Auto-run environment check on page load
        window.addEventListener('load', checkEnvironment);
    </script>
</body>
</html>
