# Enhanced Vapi Components

This document explains how to use the enhanced Vapi components in LegalScout. These components are inspired by Vapi Blocks but styled to match LegalScout's UI design.

## Overview

The enhanced Vapi components provide a more modern and consistent UI for voice interactions with Vapi assistants. They use the `use-vapi` hook for consistent functionality and are designed to be drop-in replacements for the existing components.

## Components

### 1. use-vapi Hook

The `use-vapi` hook provides a consistent way to interact with the Vapi SDK throughout the application.

**Location:** `src/hooks/use-vapi.ts`

**Usage:**
```jsx
import useVapi from '../hooks/use-vapi';

const MyComponent = () => {
  const { 
    volumeLevel, 
    isSessionActive, 
    conversation, 
    currentSpeaker,
    toggleCall,
    startCall,
    stopCall
  } = useVapi();
  
  // Use these values and functions to control Vapi
};
```

### 2. EnhancedSpeechParticles

This component provides a visualization of speech audio levels, with different colors for user and assistant speech.

**Location:** `src/components/EnhancedSpeechParticles.jsx`

**Usage:**
```jsx
import EnhancedSpeechParticles from '../components/EnhancedSpeechParticles';

const MyComponent = () => {
  return (
    <EnhancedSpeechParticles className="custom-class" />
  );
};
```

### 3. EnhancedCallController

This component provides a UI for controlling calls with Vapi, including a call button, status indicators, and speech visualization.

**Location:** `src/components/call/EnhancedCallController.jsx`

**Usage:**
```jsx
import EnhancedCallController from '../components/call/EnhancedCallController';

const MyComponent = () => {
  return (
    <EnhancedCallController 
      assistantId="your-assistant-id"
      showTranscript={true}
      showVisualization={true}
    />
  );
};
```

### 4. EnhancedVapiCall

This component provides a complete interface for voice calls with Vapi, including speech visualization, transcript display, and dossier data collection.

**Location:** `src/components/EnhancedVapiCall.jsx`

**Usage:**
```jsx
import EnhancedVapiCall from '../components/EnhancedVapiCall';

const MyComponent = () => {
  return (
    <EnhancedVapiCall 
      assistantId="your-assistant-id"
      customInstructions={{
        firmName: "Your Firm Name",
        welcomeMessage: "Welcome to our firm!",
        voiceId: "sarah",
        voiceProvider: "11labs"
      }}
      onEndCall={() => console.log('Call ended')}
      showTranscript={true}
      showDossier={true}
    />
  );
};
```

### 5. EnhancedAgentPreview

This component provides a preview of the attorney's AI assistant using the enhanced Vapi components.

**Location:** `src/components/EnhancedAgentPreview.jsx`

**Usage:**
```jsx
import EnhancedAgentPreview from '../components/EnhancedAgentPreview';

const MyComponent = () => {
  return (
    <EnhancedAgentPreview 
      attorney={attorneyObject}
      isDarkTheme={false}
      onError={(error) => console.error(error)}
    />
  );
};
```

### 6. EnhancedPreviewTab

This component provides a tab for the dashboard that contains the enhanced agent preview.

**Location:** `src/components/dashboard/EnhancedPreviewTab.jsx`

**Usage:**
```jsx
import EnhancedPreviewTab from '../components/dashboard/EnhancedPreviewTab';

const MyComponent = () => {
  return (
    <EnhancedPreviewTab 
      attorney={attorneyObject}
      isDarkTheme={false}
      onToggleTheme={() => setIsDarkTheme(!isDarkTheme)}
    />
  );
};
```

## Integration with Dashboard

To integrate the enhanced components into the dashboard, you can replace the existing preview tab with the `EnhancedPreviewTab` component:

```jsx
// In Dashboard.jsx
import EnhancedPreviewTab from '../components/dashboard/EnhancedPreviewTab';

// Replace the existing preview tab
const renderTabContent = () => {
  switch (activeTab) {
    case 'preview':
      return (
        <EnhancedPreviewTab 
          attorney={attorney}
          isDarkTheme={isDarkTheme}
          onToggleTheme={toggleTheme}
        />
      );
    // Other tabs...
  }
};
```

## Integration with Agent Page

To integrate the enhanced components into the agent page, you can replace the existing call button with the `EnhancedCallController` component:

```jsx
// In AgentPage.jsx
import EnhancedCallController from '../components/call/EnhancedCallController';

// Replace the existing call button
return (
  <div className="agent-page">
    {/* Other content */}
    <EnhancedCallController 
      assistantId={attorney.vapi_assistant_id}
      showTranscript={true}
      showVisualization={true}
    />
    {/* Other content */}
  </div>
);
```

## Styling

All components use CSS variables to match LegalScout's design system. You can customize these variables in your global CSS to match your design system:

```css
:root {
  --primary-color: #3b82f6;
  --primary-color-dark: #2563eb;
  --secondary-color: #6b7280;
  --secondary-color-dark: #4b5563;
  --success-color: #10b981;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;
  --card-background: #ffffff;
  --border-color: rgba(0, 0, 0, 0.1);
  /* ... other variables ... */
}
```

## Demo Page

A demo page is available to showcase these components:

**Location:** `src/pages/VapiDemo.jsx`

You can add this page to your routes to see all the enhanced components in action:

```jsx
// In your routes file
import VapiDemo from '../pages/VapiDemo';

// Add the route
<Route path="/vapi-demo" element={<VapiDemo />} />
```

## Next Steps

1. **Test the Components**: Try out the enhanced components in your application to ensure they work as expected.

2. **Customize the Styling**: Adjust the CSS variables to match your design system.

3. **Integrate with Existing Pages**: Replace the existing components with the enhanced ones in your dashboard and agent pages.

4. **Add Analytics**: Track usage of the enhanced components to measure their effectiveness.

## Resources

- [Vapi Blocks Documentation](https://www.vapiblocks.com/docs)
- [Vapi API Reference](https://docs.vapi.ai/api-reference)
- [Framer Motion Documentation](https://www.framer.com/motion/)
