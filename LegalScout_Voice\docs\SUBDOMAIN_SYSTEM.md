# Subdomain System

## Overview
The Subdomain System allows LegalScout to provide customized experiences for different law firms and attorneys. Each attorney/firm gets their own subdomain with customized branding, voice interactions, and configuration.

## Architecture

### Database Schema
```sql
-- From the attorneys table
CREATE TABLE IF NOT EXISTS public.attorneys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subdomain TEXT UNIQUE NOT NULL,
  firm_name TEXT NOT NULL,
  vapi_instructions TEXT,
  vapi_context TEXT,
  practice_areas TEXT[] DEFAULT '{}',
  interaction_deposit_url TEXT,
  is_active BOOLEAN DEFAULT true
);
```

## Configuration Structure

### Subdomain Config
```typescript
interface SubdomainConfig {
  // Basic Information
  subdomain: string;
  firmName: string;
  
  // Branding
  logoUrl?: string;
  profileImage?: string;
  
  // Voice AI Configuration
  vapiInstructions: string;
  vapiContext: string;
  
  // Business Configuration
  practiceAreas: string[];
  interactionDepositUrl?: string;
  
  // Status
  isActive: boolean;
}
```

## Features

### 1. Custom Voice Interactions
```typescript
// Example voice configuration
const voiceConfig = {
  instructions: `
    You are representing ${firmName}, a law firm specializing in ${practiceAreas.join(', ')}.
    Gather information about potential cases while maintaining a professional and empathetic tone.
  `,
  context: `
    ${firmName} has extensive experience in ${practiceAreas.join(', ')}.
    Initial consultations require a $50 deposit through our secure payment system.
  `
};
```

### 2. Branding Customization
```typescript
interface BrandingConfig {
  logo: {
    url: string;
    width: number;
    height: number;
  };
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  fonts: {
    heading: string;
    body: string;
  };
}
```

### 3. Practice Area Specialization
```typescript
interface PracticeAreaConfig {
  areas: string[];
  expertise: {
    [area: string]: {
      description: string;
      experience: string;
      caseTypes: string[];
    };
  };
}
```

## Implementation

### 1. Subdomain Resolution
```typescript
async function resolveSubdomain(hostname: string): Promise<SubdomainConfig> {
  const subdomain = extractSubdomain(hostname);
  const config = await fetchSubdomainConfig(subdomain);
  return validateAndEnrichConfig(config);
}
```

### 2. Configuration Loading
```typescript
async function loadSubdomainConfig(subdomain: string) {
  // Fetch from database
  const attorney = await supabase
    .from('attorneys')
    .select('*')
    .eq('subdomain', subdomain)
    .single();

  // Transform to config
  return {
    subdomain: attorney.subdomain,
    firmName: attorney.firm_name,
    vapiInstructions: attorney.vapi_instructions,
    vapiContext: attorney.vapi_context,
    practiceAreas: attorney.practice_areas,
    // ... other fields
  };
}
```

### 3. Route Handling
```typescript
function setupSubdomainRoutes(app) {
  app.use(async (req, res, next) => {
    const subdomain = extractSubdomain(req.hostname);
    if (subdomain) {
      req.subdomainConfig = await loadSubdomainConfig(subdomain);
    }
    next();
  });
}
```

## Security

### 1. Access Control
```sql
-- Row Level Security for attorneys table
CREATE POLICY "Public attorneys are viewable by everyone"
  ON public.attorneys FOR SELECT
  USING (true);

CREATE POLICY "Only admins can modify attorneys"
  ON public.attorneys FOR ALL
  USING (auth.role() = 'authenticated');
```

### 2. Validation
```typescript
function validateSubdomainConfig(config: SubdomainConfig): ValidationResult {
  return {
    isValid: true,
    errors: [],
    warnings: []
  };
}
```

### 3. Rate Limiting
```typescript
const subdomainRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each subdomain to 100 requests per windowMs
});
```

## Integration Example

### React Component
```jsx
function SubdomainProvider({ children }) {
  const [config, setConfig] = useState(null);
  const subdomain = useSubdomain();

  useEffect(() => {
    async function loadConfig() {
      const config = await loadSubdomainConfig(subdomain);
      setConfig(config);
    }
    loadConfig();
  }, [subdomain]);

  if (!config) return <Loading />;

  return (
    <SubdomainContext.Provider value={config}>
      {children}
    </SubdomainContext.Provider>
  );
}
```

### Usage Example
```jsx
function AttorneyProfile() {
  const config = useSubdomainConfig();
  
  return (
    <div className="attorney-profile">
      <header>
        <img src={config.logoUrl} alt={config.firmName} />
        <h1>{config.firmName}</h1>
      </header>
      
      <section className="practice-areas">
        {config.practiceAreas.map(area => (
          <PracticeAreaCard key={area} area={area} />
        ))}
      </section>
      
      <VapiCall subdomain={config.subdomain} />
    </div>
  );
}
```

## Testing

### 1. Configuration Testing
```typescript
describe('Subdomain Configuration', () => {
  it('loads valid configuration', async () => {
    const config = await loadSubdomainConfig('test-firm');
    expect(config).toMatchSnapshot();
  });

  it('handles missing configuration', async () => {
    const config = await loadSubdomainConfig('non-existent');
    expect(config).toBeNull();
  });
});
```

### 2. Integration Testing
```typescript
describe('Subdomain Integration', () => {
  it('applies correct branding', async () => {
    render(<SubdomainProvider subdomain="test-firm" />);
    expect(screen.getByAltText('Test Firm Logo')).toBeInTheDocument();
  });

  it('loads correct voice configuration', async () => {
    const { vapi } = render(<VapiCall subdomain="test-firm" />);
    expect(vapi.instructions).toContain('Test Firm');
  });
});
```

## Best Practices

1. **Configuration Management**
   - Store sensitive data securely
   - Version control configurations
   - Implement validation
   - Provide defaults

2. **Performance**
   - Cache configurations
   - Implement rate limiting
   - Optimize database queries
   - Use CDN for assets

3. **Security**
   - Validate subdomains
   - Implement access control
   - Sanitize user input
   - Monitor usage

4. **Maintenance**
   - Regular configuration audits
   - Update documentation
   - Monitor error rates
   - Backup configurations
``` 