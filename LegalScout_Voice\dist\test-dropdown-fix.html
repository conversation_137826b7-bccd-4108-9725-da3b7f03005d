<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant Dropdown Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #1a4a1a; border: 1px solid #4a8a4a; }
        .error { background: #4a1a1a; border: 1px solid #8a4a4a; }
        .info { background: #1a1a4a; border: 1px solid #4a4a8a; }
        button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0052a3; }
        select {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border: 1px solid #666;
            background: #333;
            color: #fff;
        }
        pre {
            background: #000;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Assistant Dropdown Fix Test</h1>
    
    <div class="test-section">
        <h2>Test the Fixed Assistant Dropdown</h2>
        <p>This test verifies that the assistant dropdown fix is working correctly.</p>
        
        <button onclick="testAssistantDropdown()">🚀 Test Assistant Dropdown</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
        
        <div id="dropdown-container" style="margin: 20px 0;">
            <label for="test-dropdown">Assistant Dropdown (Test):</label>
            <select id="test-dropdown">
                <option value="">Loading...</option>
            </select>
        </div>
    </div>

    <div id="results"></div>

    <script>
        const VAPI_PRIVATE_KEY = '6734febc-fc65-4669-93b0-929b31ff6564';
        let testResults = [];

        function addResult(type, title, message, data = null) {
            const result = { type, title, message, data, timestamp: new Date().toISOString() };
            testResults.push(result);
            renderResults();
        }

        function renderResults() {
            const container = document.getElementById('results');
            container.innerHTML = testResults.map(result => `
                <div class="result ${result.type}">
                    <h3>${result.title}</h3>
                    <p>${result.message}</p>
                    ${result.data ? `<pre>${JSON.stringify(result.data, null, 2)}</pre>` : ''}
                    <small>⏰ ${result.timestamp}</small>
                </div>
            `).join('');
        }

        function clearResults() {
            testResults = [];
            renderResults();
        }

        // Mock VapiAssistantService with the FIXED getAllAssistants method
        class FixedVapiAssistantService {
            constructor() {
                this.connected = false;
            }

            async ensureConnection() {
                if (!this.connected) {
                    addResult('info', '🔌 Connection', 'Ensuring connection to Vapi...');
                    // Simulate connection
                    this.connected = true;
                }
                return this.connected;
            }

            // This is the FIXED version of getAllAssistants
            async getAllAssistants() {
                try {
                    // Ensure connection to Vapi MCP server
                    const connected = await this.ensureConnection();
                    
                    if (!connected) {
                        addResult('error', '❌ Connection Failed', 'Not connected to Vapi MCP server');
                        return [];
                    }

                    addResult('info', '📡 API Call', 'Making direct API call to Vapi...');
                    
                    // Make direct API call (simulating the fixed MCP service)
                    const response = await fetch('https://api.vapi.ai/assistant', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${VAPI_PRIVATE_KEY}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const assistants = await response.json();
                    addResult('success', '✅ API Success', `Retrieved ${assistants.length} assistants`);
                    return assistants || [];
                } catch (error) {
                    addResult('error', '❌ API Error', `Error getting assistants: ${error.message}`);
                    return [];
                }
            }
        }

        // Simulate EnhancedAssistantDropdown logic
        async function testAssistantDropdown() {
            clearResults();
            addResult('info', '🧪 Test Started', 'Testing the fixed assistant dropdown logic...');

            const attorney = {
                id: '87756a2c-a398-43f2-889a-b8815684df71',
                email: '<EMAIL>',
                firm_name: 'LegalScout',
                subdomain: 'damon',
                vapi_assistant_id: 'cd0b44b7-397e-410d-8835-ce9c3ba584b2'
            };

            const vapiService = new FixedVapiAssistantService();

            try {
                // Step 1: Load assistants
                addResult('info', '📋 Step 1', 'Loading assistants...');
                const allAssistants = await vapiService.getAllAssistants();

                if (allAssistants.length === 0) {
                    addResult('error', '❌ No Assistants', 'No assistants were loaded');
                    return;
                }

                // Step 2: Filter relevant assistants
                addResult('info', '🔍 Step 2', 'Filtering relevant assistants...');
                const relevantAssistants = allAssistants.filter(assistant => {
                    const name = assistant.name?.toLowerCase() || '';
                    
                    // Include assistants with attorney's subdomain
                    if (name.includes(attorney.subdomain.toLowerCase())) return true;
                    
                    // Include assistants with attorney's name variations
                    if (name.includes('kost')) return true;
                    if (name.includes('damon')) return true;
                    
                    // Include generic LegalScout assistants
                    if (name.includes('legalscout')) return true;
                    
                    return false;
                });

                addResult('success', '✅ Filtering Success', 
                    `Filtered ${allAssistants.length} assistants down to ${relevantAssistants.length} relevant ones`);

                // Step 3: Populate dropdown
                addResult('info', '📝 Step 3', 'Populating dropdown...');
                const dropdown = document.getElementById('test-dropdown');
                dropdown.innerHTML = '';

                // Add default option
                dropdown.innerHTML += '<option value="">Select Assistant</option>';

                // Add relevant assistants
                relevantAssistants.forEach(assistant => {
                    const option = document.createElement('option');
                    option.value = assistant.id;
                    option.textContent = assistant.name || `Assistant ${assistant.id.slice(0, 8)}`;
                    dropdown.appendChild(option);
                });

                // Add create new option
                dropdown.innerHTML += '<option value="create_new">Create New Assistant</option>';

                // Set current assistant
                dropdown.value = attorney.vapi_assistant_id;

                addResult('success', '✅ Dropdown Populated', 
                    `Dropdown now shows ${dropdown.options.length} options`);

                // Verify current assistant is selected
                if (dropdown.value === attorney.vapi_assistant_id) {
                    addResult('success', '✅ Current Assistant Selected', 
                        `Current assistant "${dropdown.options[dropdown.selectedIndex].textContent}" is properly selected`);
                } else {
                    addResult('error', '❌ Selection Failed', 
                        `Could not select current assistant ${attorney.vapi_assistant_id}`);
                }

                addResult('success', '🎉 Test Complete', 
                    'Assistant dropdown fix is working correctly!', 
                    {
                        totalAssistants: allAssistants.length,
                        relevantAssistants: relevantAssistants.length,
                        dropdownOptions: dropdown.options.length,
                        currentAssistantSelected: dropdown.value === attorney.vapi_assistant_id
                    });

            } catch (error) {
                addResult('error', '❌ Test Failed', `Test execution failed: ${error.message}`);
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            addResult('info', '🔧 Test Ready', 'Click "Test Assistant Dropdown" to verify the fix is working.');
        });
    </script>
</body>
</html>
