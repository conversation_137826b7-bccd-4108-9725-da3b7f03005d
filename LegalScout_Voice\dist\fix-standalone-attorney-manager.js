/**
 * Fix Standalone Attorney Manager
 * 
 * This script ensures that the standalone attorney manager is properly initialized
 * and available globally.
 */

(function() {
  console.log('[FixStandaloneAttorneyManager] Starting fix...');
  
  // Check if the standalone attorney manager is already available
  if (window.standaloneAttorneyManager) {
    console.log('[FixStandaloneAttorneyManager] Standalone attorney manager already available');
    return;
  }
  
  // Load the fixed standalone attorney manager
  function loadFixedManager() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = '/standalone-attorney-manager-fixed.js';
      script.onload = () => {
        console.log('[FixStandaloneAttorneyManager] Fixed standalone attorney manager loaded');
        resolve();
      };
      script.onerror = (error) => {
        console.error('[FixStandaloneAttorneyManager] Error loading fixed standalone attorney manager:', error);
        reject(error);
      };
      document.head.appendChild(script);
    });
  }
  
  // Load the attorney validation fix
  function loadValidationFix() {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = '/fix-attorney-validation.js';
      script.onload = () => {
        console.log('[FixStandaloneAttorneyManager] Attorney validation fix loaded');
        resolve();
      };
      script.onerror = (error) => {
        console.error('[FixStandaloneAttorneyManager] Error loading attorney validation fix:', error);
        reject(error);
      };
      document.head.appendChild(script);
    });
  }
  
  // Create a fallback standalone attorney manager
  function createFallbackManager() {
    console.log('[FixStandaloneAttorneyManager] Creating fallback standalone attorney manager');
    
    // UUID validation regex
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    // Storage keys
    const STORAGE_KEYS = {
      ATTORNEY: 'attorney',
      ATTORNEY_ID: 'attorney_id',
      ATTORNEY_VERSION: 'attorney_version',
      LAST_SYNC: 'attorney_last_sync'
    };
    
    // Create a minimal standalone attorney manager
    const manager = {
      attorney: null,
      isLoading: false,
      isSaving: false,
      isSyncing: false,
      lastError: null,
      subscribers: [],
      initialized: false,
      
      initialize: function() {
        console.log('[FixStandaloneAttorneyManager] Initializing fallback manager');
        
        if (this.initialized) {
          console.log('[FixStandaloneAttorneyManager] Fallback manager already initialized');
          return;
        }
        
        try {
          // Load attorney from localStorage
          this.loadFromLocalStorage();
          
          // If no attorney was loaded, create a default one
          if (!this.attorney) {
            console.log('[FixStandaloneAttorneyManager] No attorney found, creating default');
            this.attorney = this.createDefaultAttorney();
            this.saveToLocalStorage(this.attorney);
          }
          
          // Mark as initialized
          this.initialized = true;
          console.log('[FixStandaloneAttorneyManager] Fallback manager initialization complete');
          
          // Notify subscribers
          this.notifySubscribers();
        } catch (error) {
          console.error('[FixStandaloneAttorneyManager] Fallback manager initialization failed:', error);
          this.lastError = error;
        }
      },
      
      generateUUID: function() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          const r = Math.random() * 16 | 0;
          const v = c === 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      },
      
      isValidUUID: function(uuid) {
        return UUID_REGEX.test(uuid);
      },
      
      createDefaultAttorney: function(overrides = {}) {
        const defaultAttorney = {
          id: this.generateUUID(),
          subdomain: 'default',
          firm_name: 'Your Law Firm',
          name: 'Your Name',
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          voice_provider: '11labs',
          voice_id: 'sarah',
          welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
          information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
          vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
          vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
        };
        
        return { ...defaultAttorney, ...overrides };
      },
      
      loadFromLocalStorage: function() {
        try {
          // Try to get from localStorage
          const storedAttorney = localStorage.getItem(STORAGE_KEYS.ATTORNEY);
          if (storedAttorney) {
            try {
              const parsedAttorney = JSON.parse(storedAttorney);
              
              // Validate the attorney data
              if (parsedAttorney && this.isValidUUID(parsedAttorney.id)) {
                console.log('[FixStandaloneAttorneyManager] Loaded attorney from localStorage:', parsedAttorney.id);
                this.attorney = parsedAttorney;
                this.notifySubscribers();
                return parsedAttorney;
              }
            } catch (parseError) {
              console.error('[FixStandaloneAttorneyManager] Error parsing attorney from localStorage:', parseError);
            }
          }
          
          // If not found or invalid, try to get just the ID
          const storedAttorneyId = localStorage.getItem(STORAGE_KEYS.ATTORNEY_ID);
          if (storedAttorneyId && this.isValidUUID(storedAttorneyId)) {
            console.log('[FixStandaloneAttorneyManager] Found attorney ID in localStorage:', storedAttorneyId);
            
            // Create a minimal attorney object with the ID
            const minimalAttorney = this.createDefaultAttorney({ id: storedAttorneyId });
            this.attorney = minimalAttorney;
            this.saveToLocalStorage(minimalAttorney);
            this.notifySubscribers();
            return minimalAttorney;
          }
          
          return null;
        } catch (error) {
          console.error('[FixStandaloneAttorneyManager] Error loading from localStorage:', error);
          return null;
        }
      },
      
      saveToLocalStorage: function(attorney) {
        if (!attorney || !this.isValidUUID(attorney.id)) {
          console.warn('[FixStandaloneAttorneyManager] Cannot save invalid attorney to localStorage');
          return;
        }
        
        try {
          // Save full attorney object
          localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(attorney));
          
          // Save ID separately for redundancy
          localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, attorney.id);
          
          // Save version and timestamp
          localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());
          
          console.log('[FixStandaloneAttorneyManager] Saved attorney to localStorage:', attorney.id);
        } catch (error) {
          console.error('[FixStandaloneAttorneyManager] Error saving to localStorage:', error);
        }
      },
      
      updateAttorney: function(updates) {
        try {
          if (!this.attorney) {
            console.warn('[FixStandaloneAttorneyManager] No attorney to update');
            return null;
          }
          
          // Create updated attorney
          const updatedAttorney = {
            ...this.attorney,
            ...updates,
            updated_at: new Date().toISOString()
          };
          
          // Ensure ID doesn't change
          updatedAttorney.id = this.attorney.id;
          
          // Save to localStorage
          this.saveToLocalStorage(updatedAttorney);
          
          // Update local state
          this.attorney = updatedAttorney;
          
          // Notify subscribers
          this.notifySubscribers();
          
          return updatedAttorney;
        } catch (error) {
          console.error('[FixStandaloneAttorneyManager] Error updating attorney:', error);
          this.lastError = error;
          return null;
        }
      },
      
      syncWithVapi: function() {
        console.log('[FixStandaloneAttorneyManager] Fallback syncWithVapi called');
        return Promise.resolve({ action: 'none' });
      },
      
      subscribe: function(callback) {
        if (typeof callback !== 'function') {
          console.warn('[FixStandaloneAttorneyManager] Cannot subscribe with non-function callback');
          return () => {};
        }
        
        this.subscribers.push(callback);
        
        // Call immediately with current state
        if (this.attorney) {
          callback(this.attorney);
        }
        
        // Return unsubscribe function
        return () => this.unsubscribe(callback);
      },
      
      unsubscribe: function(callback) {
        this.subscribers = this.subscribers.filter(cb => cb !== callback);
      },
      
      notifySubscribers: function() {
        if (!this.attorney) return;
        
        this.subscribers.forEach(callback => {
          try {
            callback(this.attorney);
          } catch (error) {
            console.error('[FixStandaloneAttorneyManager] Error in subscriber callback:', error);
          }
        });
      }
    };
    
    // Initialize the manager
    manager.initialize();
    
    // Make it available globally
    window.standaloneAttorneyManager = manager;
    
    console.log('[FixStandaloneAttorneyManager] Fallback manager created and initialized');
    
    return manager;
  }
  
  // Try to load the fixed manager, fall back to creating one if that fails
  async function applyFix() {
    try {
      // First try to load the fixed manager
      await loadFixedManager();
      
      // If that fails, create a fallback manager
      if (!window.standaloneAttorneyManager) {
        createFallbackManager();
      }
      
      // Load the validation fix
      await loadValidationFix();
      
      // Load other fixes
      // NOTE: Commenting out dynamic loading of potentially interfering script
      /*
      if (document.readyState === 'complete') {
        loadOtherFixes();
      } else {
        window.addEventListener('load', loadOtherFixes);
      }
      */
      
      console.log('[FixStandaloneAttorneyManager] Fix completed');
    } catch (error) {
      console.error('[FixStandaloneAttorneyManager] Error applying fix:', error);
      
      // Create a fallback manager if loading the fixed one failed
      if (!window.standaloneAttorneyManager) {
        createFallbackManager();
      }
    }
  }
  
  // Apply the fix
  applyFix();
})();
