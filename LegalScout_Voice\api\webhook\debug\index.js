/**
 * Debug Webhook Handler
 * 
 * Simple webhook to debug environment and database connectivity
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle OPTIONS request for CORS
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    const debug = {
      timestamp: new Date().toISOString(),
      method: req.method,
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL: process.env.VERCEL,
        VERCEL_ENV: process.env.VERCEL_ENV,
        VERCEL_URL: process.env.VERCEL_URL
      },
      supabase: {
        url: process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing',
        key: process.env.VITE_SUPABASE_KEY ? '✅ Set' : '❌ Missing',
        serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing',
        urlValue: process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL,
        keyLength: (process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_ANON_KEY || '').length
      },
      vapi: {
        token: process.env.VAPI_TOKEN ? '✅ Set' : '❌ Missing',
        secret: process.env.VAPI_WEBHOOK_SECRET ? '✅ Set' : '❌ Missing',
        tokenLength: (process.env.VAPI_TOKEN || '').length
      }
    };

    // Test Supabase connection if available
    if (process.env.VITE_SUPABASE_URL && (process.env.VITE_SUPABASE_KEY || process.env.SUPABASE_SERVICE_ROLE_KEY)) {
      try {
        const { createClient } = await import('@supabase/supabase-js');
        
        const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_KEY;
        
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        // Test basic query
        const { data, error } = await supabase
          .from('attorneys')
          .select('id')
          .limit(1);
        
        if (error) {
          debug.supabase.testResult = `❌ Error: ${error.message}`;
        } else {
          debug.supabase.testResult = `✅ Success: Found ${data?.length || 0} records`;
        }
      } catch (error) {
        debug.supabase.testResult = `❌ Connection Error: ${error.message}`;
      }
    } else {
      debug.supabase.testResult = '❌ Missing credentials';
    }

    return res.status(200).json({
      success: true,
      message: 'Debug webhook working',
      debug
    });

  } catch (error) {
    return res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack
    });
  }
}
