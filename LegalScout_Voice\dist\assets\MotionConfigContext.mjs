// Self-contained mock implementation of MotionConfigContext
// This version doesn't rely on React at all

// Create a completely standalone mock context
const MotionConfigContext = {
  Provider: function(props) { 
    return typeof props.children !== 'undefined' ? props.children : null; 
  },
  Consumer: function(props) { 
    return props.children && typeof props.children === 'function' 
      ? props.children({}) 
      : null; 
  },
  displayName: 'MotionConfigContext',
  _currentValue: {},
  _currentValue2: {},
  _threadCount: 0,
  _defaultValue: {}
};

// Expose on window for other modules
if (typeof window !== 'undefined') {
  window.MotionConfigContext = MotionConfigContext;
}

// Export both as default and named export
export { MotionConfigContext };
export default MotionConfigContext; 