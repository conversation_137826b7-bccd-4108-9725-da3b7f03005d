<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Case Dossier</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css">
    <style>
        /* Dark mode base styles */
        body {
            font-family: 'Courier New', monospace;
            background: #0a1929;
            color: #e0e0e0;
            margin: 0;
            min-height: 100vh;
            overflow: hidden;
        }

        .container {
            position: relative;
            height: 100vh;
        }

        /* Initial header styles */
        .init-header {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #66b2ff;
            font-size: 1.5rem;
            z-index: 1000;
            transition: opacity 0.5s;
        }

        /* Map container styles */
        .map-container {
            height: 100vh;
            width: 100%;
            filter: grayscale(80%) brightness(0.4);
            transition: filter 0.5s;
        }

        .map-container.active {
            filter: grayscale(0) brightness(0.7);
        }

        /* Dossier table styles */
        .dossier-table {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(16, 25, 40, 0.9);
            backdrop-filter: blur(5px);
            border-radius: 8px;
            padding: 1.5rem;
            width: 400px;
            box-shadow: 0 0 20px rgba(102, 178, 255, 0.1);
            opacity: 0;
            transition: opacity 1s;
        }

        .dossier-table.visible {
            opacity: 1;
        }

        /* Table row styles */
        .case-table {
            width: 100%;
            border-collapse: collapse;
        }

        .case-table tr {
            transition: all 0.3s;
            border-left: 2px solid transparent;
        }

        .case-table tr:hover {
            background: rgba(102, 178, 255, 0.05);
            border-left: 2px solid #66b2ff;
        }

        .case-table td {
            padding: 1rem;
            vertical-align: top;
        }

        .emoji {
            font-size: 1.2rem;
            width: 40px;
            text-align: center;
        }

        .label {
            color: #99ccff;
            width: 120px;
        }

        .value {
            position: relative;
            color: #e0e0e0;
            max-width: 200px;
        }

        /* Animations */
        @keyframes typing {
            from { width: 0 }
            to { width: 100% }
        }

        .typing-effect {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            animation: typing 0.5s steps(40, end);
        }

        .stamp {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border: 2px solid #66b2ff;
            border-radius: 4px;
            color: #66b2ff;
            animation: stamp 0.3s ease-out;
        }

        @keyframes stamp {
            from { transform: scale(3); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="init-header">
            🕵️♂️ Your Case Profile will populate as you discuss your issue with Scout
        </div>
        
        <div id="map" class="map-container"></div>
        
        <div class="dossier-table">
            <table class="case-table">
                <!-- Rows will be populated dynamically -->
            </table>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script>
        let map;
        let currentMarker = null;

        // Example API response structure
        const apiResponse = {
            "tool_calls": [{
                "function": {
                    "arguments": {
                        "🔄_s_t_a_t_u_s": "Client information collected for case file",
                        "📍_jurisdiction": {
                            "lat_long": "40.712776,-74.005974",
                            "simple_address": "New York, NY"
                        },
                        "📁_client_background": "Individual case",
                        "⚖️_legal_issues": "Injury by construction vehicle",
                        "📝_statement_of_facts": "Injured while walking down the street when a construction vehicle was involved, obtained a picture of the license plate.",
                        "🎯_objectives": "Seek medical compensation and lost wages."
                    }
                }
            }]
        };

        // Process API response
        function processApiResponse(response) {
            const dossierData = response.tool_calls[0].function.arguments;
            const fieldMap = {
                "🔄_s_t_a_t_u_s": "STATUS",
                "📍_jurisdiction": "Jurisdiction",
                "📁_client_background": "Client Background",
                "⚖️_legal_issues": "Legal Issues",
                "📝_statement_of_facts": "Statement of Facts",
                "🎯_objectives": "Objectives"
            };

            // Update or create table rows
            Object.entries(fieldMap).forEach(([key, label]) => {
                const value = key === '📍_jurisdiction' 
                    ? dossierData[key].simple_address 
                    : dossierData[key];
                
                updateDossierRow(label, value, key);
            });

            // Update map if jurisdiction exists
            if (dossierData['📍_jurisdiction']?.lat_long) {
                const [lat, lng] = dossierData['📍_jurisdiction'].lat_long.split(',').map(Number);
                updateMap(lat, lng, dossierData['📍_jurisdiction'].simple_address);
            }
        }

        function updateDossierRow(label, value, emojiKey) {
            const emoji = emojiKey.split('_')[0];
            const row = document.querySelector(`tr[data-label="${label}"]`) || createNewRow(label, emoji);
            
            const valueCell = row.querySelector('.value');
            valueCell.innerHTML = '';
            
            const newValue = document.createElement('span');
            newValue.textContent = value;
            
            if (label === 'STATUS') {
                newValue.classList.add('stamp');
            } else {
                newValue.classList.add('typing-effect');
            }
            
            valueCell.appendChild(newValue);
        }

        function createNewRow(label, emoji) {
            const row = document.createElement('tr');
            row.setAttribute('data-label', label);
            
            row.innerHTML = `
                <td class="emoji">${emoji}</td>
                <td class="label">${label}</td>
                <td class="value"><span class="placeholder">Loading...</span></td>
            `;
            
            document.querySelector('.case-table').appendChild(row);
            return row;
        }

        function updateMap(lat, lng, address) {
            if (!map) {
                map = L.map('map').setView([lat, lng], 12);
                L.tileLayer('https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png').addTo(map);
                document.querySelector('.dossier-table').classList.add('visible');
            }

            if (currentMarker) {
                map.removeLayer(currentMarker);
            }

            currentMarker = L.marker([lat, lng])
                .addTo(map)
                .bindPopup(address);
            
            document.querySelector('.map-container').classList.add('active');
        }

        // Initial setup
        setTimeout(() => {
            document.querySelector('.init-header').style.opacity = '0';
            processApiResponse(apiResponse);
        }, 2000);
    </script>
</body>
</html>