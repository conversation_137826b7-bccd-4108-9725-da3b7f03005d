/**
 * Force New Assistant
 * 
 * Forces the system to use the new assistant ID and clears all caches
 */

(function() {
  'use strict';

  console.log('🔄 [ForceNewAssistant] Forcing system to use new assistant...');

  const NEW_ASSISTANT_ID = '4e899c0a-b435-45c4-abcd-8abd3ff13ec3';
  const OLD_ASSISTANT_ID = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

  // 1. Clear all localStorage
  function clearAllStorage() {
    console.log('🧹 [ForceNewAssistant] Clearing all localStorage...');
    
    try {
      // Clear all localStorage
      localStorage.clear();
      
      // Clear sessionStorage
      sessionStorage.clear();
      
      // Clear any specific keys that might be cached
      const keysToRemove = [
        'vapi_assistant_id',
        'attorney_data',
        'assistant_config',
        'vapi_config',
        'cached_assistant',
        'attorney_profile'
      ];
      
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        sessionStorage.removeItem(key);
      });
      
      console.log('✅ [ForceNewAssistant] Storage cleared');
      
    } catch (error) {
      console.log(`❌ [ForceNewAssistant] Error clearing storage: ${error.message}`);
    }
  }

  // 2. Set new assistant ID globally
  function setNewAssistantGlobally() {
    console.log('🎯 [ForceNewAssistant] Setting new assistant ID globally...');
    
    try {
      // Set in localStorage
      localStorage.setItem('vapi_assistant_id', NEW_ASSISTANT_ID);
      localStorage.setItem('force_assistant_id', NEW_ASSISTANT_ID);
      
      // Set in window globals
      window.FORCE_ASSISTANT_ID = NEW_ASSISTANT_ID;
      window.VAPI_ASSISTANT_ID = NEW_ASSISTANT_ID;
      
      // Set in any existing attorney data
      const attorneyData = {
        vapi_assistant_id: NEW_ASSISTANT_ID,
        id: '695b5caf-4884-456d-a3b1-7765427b6095',
        email: '<EMAIL>'
      };
      
      localStorage.setItem('attorney_data', JSON.stringify(attorneyData));
      window.ATTORNEY_DATA = attorneyData;
      
      console.log(`✅ [ForceNewAssistant] New assistant ID set: ${NEW_ASSISTANT_ID}`);
      
    } catch (error) {
      console.log(`❌ [ForceNewAssistant] Error setting new assistant: ${error.message}`);
    }
  }

  // 3. Override any functions that might return the old assistant ID
  function overrideAssistantFunctions() {
    console.log('🔧 [ForceNewAssistant] Overriding assistant functions...');
    
    try {
      // Override any global functions that might return assistant ID
      window.getAssistantId = function() {
        console.log('[ForceNewAssistant] getAssistantId called, returning new ID');
        return NEW_ASSISTANT_ID;
      };
      
      window.getVapiAssistantId = function() {
        console.log('[ForceNewAssistant] getVapiAssistantId called, returning new ID');
        return NEW_ASSISTANT_ID;
      };
      
      // Override StandaloneAttorneyManager if it exists
      if (window.StandaloneAttorneyManager) {
        const originalGetAssistantId = window.StandaloneAttorneyManager.getAssistantId;
        window.StandaloneAttorneyManager.getAssistantId = function() {
          console.log('[ForceNewAssistant] StandaloneAttorneyManager.getAssistantId overridden');
          return NEW_ASSISTANT_ID;
        };
      }
      
      console.log('✅ [ForceNewAssistant] Assistant functions overridden');
      
    } catch (error) {
      console.log(`❌ [ForceNewAssistant] Error overriding functions: ${error.message}`);
    }
  }

  // 4. Intercept and replace old assistant ID in API calls
  function interceptAssistantIdInCalls() {
    console.log('🌐 [ForceNewAssistant] Intercepting API calls to replace assistant ID...');
    
    try {
      // Store original fetch
      const originalFetch = window.fetch;
      
      window.fetch = function(url, options) {
        // Check if this is a Vapi call with the old assistant ID
        if (typeof url === 'string' && url.includes('api.vapi.ai')) {
          console.log('[ForceNewAssistant] Intercepting Vapi API call:', url);
          
          // Replace old assistant ID in URL
          if (url.includes(OLD_ASSISTANT_ID)) {
            url = url.replace(OLD_ASSISTANT_ID, NEW_ASSISTANT_ID);
            console.log('[ForceNewAssistant] Replaced assistant ID in URL:', url);
          }
          
          // Replace old assistant ID in request body
          if (options && options.body) {
            let body = options.body;
            if (typeof body === 'string') {
              if (body.includes(OLD_ASSISTANT_ID)) {
                body = body.replace(new RegExp(OLD_ASSISTANT_ID, 'g'), NEW_ASSISTANT_ID);
                options.body = body;
                console.log('[ForceNewAssistant] Replaced assistant ID in request body');
              }
            }
          }
        }
        
        return originalFetch(url, options);
      };
      
      console.log('✅ [ForceNewAssistant] API call interception set up');
      
    } catch (error) {
      console.log(`❌ [ForceNewAssistant] Error setting up interception: ${error.message}`);
    }
  }

  // 5. Force reload of any cached components
  function forceComponentReload() {
    console.log('🔄 [ForceNewAssistant] Forcing component reload...');
    
    try {
      // Dispatch custom event to force reload
      window.dispatchEvent(new CustomEvent('forceAssistantReload', {
        detail: {
          newAssistantId: NEW_ASSISTANT_ID,
          oldAssistantId: OLD_ASSISTANT_ID
        }
      }));
      
      // Force reload of any React components that might be cached
      if (window.React && window.React.version) {
        console.log('[ForceNewAssistant] React detected, dispatching state update event');
        
        // Trigger a global state update
        window.dispatchEvent(new CustomEvent('assistantIdChanged', {
          detail: { assistantId: NEW_ASSISTANT_ID }
        }));
      }
      
      console.log('✅ [ForceNewAssistant] Component reload triggered');
      
    } catch (error) {
      console.log(`❌ [ForceNewAssistant] Error forcing reload: ${error.message}`);
    }
  }

  // 6. Initialize all fixes
  function initializeForceNewAssistant() {
    console.log('🚀 [ForceNewAssistant] Initializing force new assistant...');
    
    try {
      clearAllStorage();
      setNewAssistantGlobally();
      overrideAssistantFunctions();
      interceptAssistantIdInCalls();
      forceComponentReload();
      
      // Set completion flag
      window.FORCE_NEW_ASSISTANT_COMPLETE = true;
      
      console.log('🎉 [ForceNewAssistant] Force new assistant complete!');
      console.log(`✅ New assistant ID: ${NEW_ASSISTANT_ID}`);
      console.log('🔄 Please refresh the page to ensure all changes take effect');
      
      // DISABLED: Auto-refresh to prevent loops
      // setTimeout(() => {
      //   console.log('🔄 [ForceNewAssistant] Auto-refreshing page...');
      //   window.location.reload();
      // }, 2000);

      console.log('🔄 [ForceNewAssistant] Auto-refresh disabled to prevent loops');
      
    } catch (error) {
      console.log(`❌ [ForceNewAssistant] Error in initialization: ${error.message}`);
    }
  }

  // Execute immediately
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeForceNewAssistant);
  } else {
    initializeForceNewAssistant();
  }

})();
