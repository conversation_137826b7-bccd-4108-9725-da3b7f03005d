/**
 * Fix Call Start Issues
 * 
 * Based on the actual logs, this fixes the real issues preventing calls from starting.
 */

console.log('[FixCallStart] Applying call start fixes...');

// 1. Fix MCP CORS issue (already handled by production-signin-fix.js)
// The MCP fallback is working correctly

// 2. Ensure audio permissions are requested immediately
async function ensureAudioPermissions() {
  try {
    console.log('[FixCallStart] Requesting audio permissions...');
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    console.log('[FixCallStart] ✅ Audio permissions granted');
    stream.getTracks().forEach(track => track.stop());
    return true;
  } catch (error) {
    console.error('[FixCallStart] ❌ Audio permissions denied:', error);
    
    // Show user-friendly error
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #ff4444;
      color: white;
      padding: 15px;
      border-radius: 8px;
      z-index: 10000;
      max-width: 300px;
    `;
    errorDiv.innerHTML = `
      <strong>Microphone Access Required</strong><br>
      Please allow microphone access to start voice calls.
      <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; cursor: pointer;">×</button>
    `;
    document.body.appendChild(errorDiv);
    
    return false;
  }
}

// 3. Fix WebRTC connection issues
function fixWebRTCConnection() {
  console.log('[FixCallStart] Configuring WebRTC...');
  
  // Override RTCPeerConnection to add better STUN servers
  const originalRTCPeerConnection = window.RTCPeerConnection;
  window.RTCPeerConnection = function(config = {}) {
    // Ensure we have good STUN servers
    config.iceServers = config.iceServers || [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' }
    ];
    
    console.log('[FixCallStart] RTCPeerConnection created with config:', config);
    return new originalRTCPeerConnection(config);
  };
  
  // Copy static properties
  Object.setPrototypeOf(window.RTCPeerConnection, originalRTCPeerConnection);
  Object.assign(window.RTCPeerConnection, originalRTCPeerConnection);
}

// 4. Monitor actual call state
function monitorCallState() {
  console.log('[FixCallStart] Setting up call state monitoring...');
  
  // Monitor for WebRTC events
  const originalAddEventListener = EventTarget.prototype.addEventListener;
  EventTarget.prototype.addEventListener = function(type, listener, options) {
    if (this instanceof RTCPeerConnection && type.includes('ice')) {
      console.log(`[FixCallStart] WebRTC event: ${type}`);
    }
    return originalAddEventListener.call(this, type, listener, options);
  };
  
  // Monitor audio elements
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.tagName === 'AUDIO') {
          console.log('[FixCallStart] 🔊 Audio element created:', node);
          
          node.addEventListener('loadstart', () => {
            console.log('[FixCallStart] 🎵 Audio loading started');
          });
          
          node.addEventListener('canplay', () => {
            console.log('[FixCallStart] ✅ Audio can play');
          });
          
          node.addEventListener('error', (e) => {
            console.error('[FixCallStart] ❌ Audio error:', e);
          });
        }
      });
    });
  });
  
  observer.observe(document.body, { childList: true, subtree: true });
}

// 5. Fix assistant configuration
function fixAssistantConfig() {
  console.log('[FixCallStart] Checking assistant configuration...');
  
  // Monitor Vapi calls to ensure correct assistant ID
  const originalFetch = window.fetch;
  window.fetch = function(url, options = {}) {
    if (typeof url === 'string' && url.includes('api.vapi.ai/call/web')) {
      console.log('[FixCallStart] 📞 Vapi call request:', {
        url,
        method: options.method,
        hasBody: !!options.body
      });
      
      if (options.body) {
        try {
          const body = JSON.parse(options.body);
          console.log('[FixCallStart] 📋 Call payload:', {
            assistantId: body.assistantId,
            hasAssistant: !!body.assistant,
            assistantName: body.assistant?.name
          });
          
          // Ensure we're using the correct assistant ID
          if (!body.assistantId && !body.assistant) {
            console.error('[FixCallStart] ❌ No assistant specified in call');
          }
        } catch (e) {
          console.log('[FixCallStart] Could not parse call body');
        }
      }
    }
    
    return originalFetch.call(this, url, options);
  };
}

// 6. Add call success detection
function detectCallSuccess() {
  console.log('[FixCallStart] Setting up call success detection...');
  
  // Listen for successful WebRTC connection
  let callSuccessDetected = false;
  
  const checkForCallSuccess = () => {
    // Look for audio elements with src
    const audioElements = document.querySelectorAll('audio[src]');
    if (audioElements.length > 0 && !callSuccessDetected) {
      console.log('[FixCallStart] ✅ Call appears to be active - audio element found');
      callSuccessDetected = true;
      
      // Show success indicator
      const successDiv = document.createElement('div');
      successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: #44ff44;
        color: black;
        padding: 15px;
        border-radius: 8px;
        z-index: 10000;
      `;
      successDiv.innerHTML = '✅ Call Active';
      document.body.appendChild(successDiv);
      
      setTimeout(() => successDiv.remove(), 3000);
    }
    
    // Check for call UI indicators
    const callIndicators = document.querySelectorAll('[class*="call"], [class*="active"], [class*="connecting"]');
    callIndicators.forEach(el => {
      if (el.textContent?.includes('Active') || el.textContent?.includes('Connected')) {
        if (!callSuccessDetected) {
          console.log('[FixCallStart] ✅ Call UI indicates active call');
          callSuccessDetected = true;
        }
      }
    });
  };
  
  // Check periodically
  setInterval(checkForCallSuccess, 1000);
}

// 7. Main initialization
async function initializeCallFixes() {
  console.log('[FixCallStart] Initializing call fixes...');
  
  // Request audio permissions immediately
  await ensureAudioPermissions();
  
  // Apply fixes
  fixWebRTCConnection();
  monitorCallState();
  fixAssistantConfig();
  detectCallSuccess();
  
  console.log('[FixCallStart] ✅ All call fixes applied');
  
  // Add manual test function
  window.testCallStart = async function() {
    console.log('[FixCallStart] 🧪 Running manual call test...');
    
    const hasAudio = await ensureAudioPermissions();
    console.log('Audio permissions:', hasAudio ? '✅' : '❌');
    
    const hasVapi = !!window.Vapi;
    console.log('Vapi SDK loaded:', hasVapi ? '✅' : '❌');
    
    const audioElements = document.querySelectorAll('audio');
    console.log('Audio elements:', audioElements.length);
    
    const callButtons = document.querySelectorAll('button[class*="call"], button[onclick*="call"]');
    console.log('Call buttons found:', callButtons.length);
    
    return {
      audioPermissions: hasAudio,
      vapiLoaded: hasVapi,
      audioElements: audioElements.length,
      callButtons: callButtons.length
    };
  };
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeCallFixes);
} else {
  initializeCallFixes();
}

console.log('[FixCallStart] Call start fix script loaded');
console.log('[FixCallStart] 💡 Run window.testCallStart() to test call functionality');
