<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- No framer-motion patches needed anymore - using Vite plugin instead -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LegalScout</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
    
    <!-- Load Three.js from CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Load Supabase from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Create default attorney -->
    <script>
      // Function to generate a valid UUID v4
      function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
          var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
          return v.toString(16);
        });
      }
      
      // Create a default attorney
      const defaultAttorney = {
        id: generateUUID(),
        subdomain: 'default',
        firm_name: 'Your Law Firm',
        name: 'Your Name',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        voice_provider: '11labs',
        voice_id: 'sarah',
        welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
        information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
        vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
        vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
      };
      
      // Save to localStorage
      localStorage.setItem('attorney', JSON.stringify(defaultAttorney));
      localStorage.setItem('attorney_id', defaultAttorney.id);
      localStorage.setItem('attorney_version', Date.now().toString());
      
      console.log('Default attorney created and saved to localStorage:', defaultAttorney.id);
      
      // Create a fake Supabase client
      window.supabase = {
        from: function(table) {
          console.log(`Fake Supabase client: from('${table}')`);
          return {
            select: function() {
              return {
                eq: function(column, value) {
                  return {
                    single: function() {
                      return Promise.resolve({
                        data: defaultAttorney,
                        error: null
                      });
                    }
                  };
                }
              };
            },
            update: function(data) {
              return {
                eq: function(column, value) {
                  return Promise.resolve({
                    data: { ...data, id: value },
                    error: null
                  });
                }
              };
            },
            insert: function(data) {
              return {
                select: function() {
                  return {
                    single: function() {
                      return Promise.resolve({
                        data: { ...data[0], id: generateUUID() },
                        error: null
                      });
                    }
                  };
                }
              };
            }
          };
        },
        storage: {
          from: function(bucket) {
            return {
              upload: function(path, file, options) {
                return Promise.resolve({
                  data: {
                    path: path,
                    fullPath: `${bucket}/${path}`,
                    id: 'dev-file-' + Date.now()
                  },
                  error: null
                });
              },
              getPublicUrl: function(path) {
                return {
                  data: {
                    publicUrl: '/PRIMARY CLEAR.png'
                  }
                };
              }
            };
          }
        }
      };
      
      console.log('Fake Supabase client created');
    </script>
    
    <!-- Fix for API errors - Must be the very first script -->
    <script src="/fix-api-errors.js"></script>
    <!-- Fix for React context timeout - Must load before other React-related scripts -->
    <script src="/fix-react-context-timeout.js"></script>
    <!-- Fix for standalone attorney manager - Must load before attorney scripts -->
    <script src="/fix-standalone-attorney-manager.js"></script>
    <!-- Attorney validation fix - Must load before other attorney scripts -->
    <script src="/fix-attorney-validation.js"></script>
    <!-- Standalone Attorney Manager - Original script kept for compatibility -->
    <script src="/standalone-attorney-manager-fixed.js"></script>
    <!-- Attorney State Initializer - Legacy script, kept for compatibility -->
    <script src="/attorney-state-initializer.js"></script>
    <!-- Simplified Vapi Integration - Improved Vapi integration -->
    <script src="/simplified-vapi-integration.js"></script>
    <!-- Supabase Realtime Sync - Keep attorney data in sync -->
    <script src="/supabase-realtime-sync.js"></script>
    <!-- Attorney Bridge - Unified API for attorney management -->
    <script src="/attorney-bridge.js"></script>
    <!-- React Context Fix - Aggressive fix for React context issues -->
    <script src="/react-context-fix.js"></script>
    <!-- Auth Context Fix - Fix for auth context issues -->
    <script src="/auth-context-fix.js"></script>
    <!-- Attorney State Context Fix - Fix for attorney state context issues -->
    <script src="/attorney-state-context-fix.js"></script>
    <!-- Auto Configure Direct Fix - Fix for auto configuration issues -->
    <script src="/auto-configure-direct-fix.js"></script>
    <!-- Headers Fix - Fix for headers issues -->
    <script src="/headers-fix.js"></script>
    <!-- Auth State Fix - Fix for auth state issues -->
    <script src="/fix-auth-state.js"></script>
    <!-- Attorney Creation Fix - Fix for attorney creation issues -->
    <script src="/fix-attorney-creation.js"></script>
    <!-- Sync Tools Fix - Fix for sync tools issues -->
    <script src="/sync-tools-fix.js"></script>
    <!-- Preview Controls Fix - Fix for preview controls issues -->
    <script src="/preview-controls-fix.js"></script>
    <!-- Fix for attorney state manager -->
    <script src="/attorney-state-manager-fix.js"></script>
    <!-- Enhanced fix for attorney state manager -->
    <script src="/fix-attorney-state-manager.js"></script>
    <!-- Fix for Supabase connection -->
    <script>
      // Ensure Supabase is properly initialized
      (function() {
        console.log('[SupabaseConnectionFix] Starting fix...');
        
        // Check if Supabase is already initialized
        if (window.supabase) {
          console.log('[SupabaseConnectionFix] Supabase already initialized');
          return;
        }
        
        // Wait for Supabase to be initialized
        const checkInterval = setInterval(() => {
          if (window.supabase) {
            clearInterval(checkInterval);
            console.log('[SupabaseConnectionFix] Supabase initialized');
            return;
          }
          
          // Try to initialize Supabase manually
          try {
            // Supabase URL and key
            const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
            const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
            
            // Check if Supabase client is available
            if (typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
              console.log('[SupabaseConnectionFix] Creating Supabase client manually');
              
              // Create Supabase client
              window.supabase = supabase.createClient(supabaseUrl, supabaseKey);
              
              console.log('[SupabaseConnectionFix] Supabase client created manually');
              clearInterval(checkInterval);
            }
          } catch (error) {
            console.error('[SupabaseConnectionFix] Error initializing Supabase:', error);
          }
        }, 100);
        
        // Clear interval after 10 seconds
        setTimeout(() => {
          clearInterval(checkInterval);
          console.warn('[SupabaseConnectionFix] Timed out waiting for Supabase');
        }, 10000);
      })();
    </script>
    <!-- Additional attorney validation fix - Must be the last script -->
    <script>
      // Ensure we have a valid attorney in localStorage
      (function() {
        console.log('[AdditionalFix] Checking attorney validity...');
        
        // Check if we have a valid attorney in localStorage
        try {
          const storedAttorney = localStorage.getItem('attorney');
          if (storedAttorney) {
            try {
              const parsedAttorney = JSON.parse(storedAttorney);
              
              // Validate the attorney data
              if (parsedAttorney && parsedAttorney.id) {
                console.log('[AdditionalFix] Found attorney in localStorage:', parsedAttorney.id);
                return;
              }
            } catch (parseError) {
              console.error('[AdditionalFix] Error parsing attorney from localStorage:', parseError);
            }
          }
          
          // If we don't have a valid attorney, create one
          if (window.standaloneAttorneyManager && window.standaloneAttorneyManager.attorney) {
            console.log('[AdditionalFix] Using standalone attorney manager');
            return;
          }
          
          // If we don't have a standalone attorney manager, create a default attorney
          console.log('[AdditionalFix] Creating default attorney');
          
          // Generate a UUID
          const generateUUID = function() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
              const r = Math.random() * 16 | 0;
              const v = c === 'x' ? r : (r & 0x3 | 0x8);
              return v.toString(16);
            });
          };
          
          // Create a default attorney
          const defaultAttorney = {
            id: generateUUID(),
            subdomain: 'default',
            firm_name: 'Your Law Firm',
            name: 'Your Name',
            is_active: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            voice_provider: '11labs',
            voice_id: 'sarah',
            welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
            information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
            vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
            vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
          };
          
          // Save to localStorage
          localStorage.setItem('attorney', JSON.stringify(defaultAttorney));
          localStorage.setItem('attorney_id', defaultAttorney.id);
          localStorage.setItem('attorney_version', Date.now().toString());
          
          console.log('[AdditionalFix] Saved default attorney to localStorage:', defaultAttorney.id);
        } catch (error) {
          console.error('[AdditionalFix] Error checking attorney validity:', error);
        }
      })();
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
