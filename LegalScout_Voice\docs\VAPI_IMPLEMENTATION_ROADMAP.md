# Vapi Implementation Roadmap

This document outlines the roadmap for implementing Vapi voice AI features in the LegalScout platform, with a focus on reaching <PERSON> efficiently.

## MVP Priorities

### 1. Fix Assistant Creation and Management
- Implement consistent assistant creation for all attorneys
- Ensure proper data synchronization between Supabase and Vapi
- Validate assistant configuration before saving

### 2. Improve Call Experience
- Add call status indicators and feedback
- Implement basic call logging
- Ensure preview consistently uses the attorney's assistant

### 3. Streamline Configuration
- Create clear UI for saving Vapi field changes
- Add validation before saving to either system
- Provide clear feedback on save/sync operations

## Implementation Approach

### One-Way Sync Pattern
- **Direction**: UI → Supabase → Vapi (never in reverse)
- **Primary Source**: Supabase as the source of truth
- **Validation**: Validate data before saving to either system
- **Error Handling**: Log errors and provide user feedback

### Field Mapping
- `welcome_message` (Supabase) → `firstMessage` (Vapi)
- `vapi_instructions` (Supabase) → `instructions` (Vapi)
- `voice_id` (Supabase) → `voice.voiceId` (Vapi)
- `voice_provider` (Supabase) → `voice.provider` (Vapi)
- `ai_model` (Supabase) → `llm.model` (Vapi)

## MVP-Accelerating Implementations

### 1. Webhook Integration for Call Logging
- **Why it helps MVP**: Provides essential call tracking without complex custom code
- **Implementation difficulty**: Low (single API endpoint)
- **Value**: High (gives attorneys visibility into their assistant's activity)
- **Example**: [Vercel Webhook](https://github.com/vapi-ai/vapi-examples/tree/main/webhooks/vercel)
- **Implementation steps**:
  1. Create webhook endpoint in Vercel
  2. Store call data in Supabase
  3. Display call history in attorney dashboard

### 2. Supabase Assistant Management
- **Why it helps MVP**: Solves current assistant creation inconsistency issues
- **Implementation difficulty**: Medium
- **Value**: Critical (ensures every attorney has a working assistant)
- **Example**: [Supabase Functions](https://github.com/vapi-ai/vapi-examples/tree/main/server/supabase)
- **Implementation steps**:
  1. Create Supabase function for assistant management
  2. Implement retry logic and error handling
  3. Add validation for assistant configuration

### 3. React Call Status Components
- **Why it helps MVP**: Provides essential user feedback during calls
- **Implementation difficulty**: Low (drop-in components)
- **Value**: High (improves user experience significantly)
- **Example**: [React Components](https://github.com/vapi-ai/react-components)
- **Implementation steps**:
  1. Install Vapi React components
  2. Integrate call status indicators
  3. Style components to match LegalScout design

### 4. MCP Server Integration
- **Why it helps MVP**: Simplifies programmatic control of Vapi
- **Implementation difficulty**: Medium (already implemented)
- **Value**: High (provides consistent interface for Vapi operations)
- **Example**: [MCP Server](https://github.com/vapi-ai/vapi-examples/tree/main/mcp)
- **Implementation steps**:
  1. Deploy MCP server on Vercel
  2. Update client code to use MCP server
  3. Add error handling and fallbacks

## Post-MVP Enhancements

### 1. Call Recording & Transcription Storage
- Store call transcripts in Supabase
- Implement searchable call archives
- Add automatic summarization of consultations

### 2. Real-Time Call Analytics
- Create dashboard for call metrics
- Track common legal questions
- Optimize assistant performance based on data

### 3. Multi-Assistant Workflows
- Implement call transfers between assistants
- Create specialized legal assistants for different practice areas
- Add handoff from AI to human attorney when needed

### 4. Mobile Support
- Extend platform to mobile with Flutter or React Native
- Enable on-the-go consultations
- Create companion app for attorneys

## Resources

### Vapi AI Ecosystem
- **Real-time SDKs**: Web · Flutter · React Native · iOS · Python · Vanilla
- **Client Examples**: Next.js · React · Flutter · React Native
- **Server Examples**: Vercel · Cloudflare · Supabase · Node · Bun · Deno · Flask · Laravel · Go · Rust
- **Resources**: [Official Docs](https://docs.vapi.ai/) · [API Reference](https://docs.vapi.ai/api-reference) · [MCP Server](https://docs.vapi.ai/mcp-server)
- **Community**: Videos · UI Library

### Key Examples for Implementation
- [Vercel Webhook Example](https://github.com/vapi-ai/vapi-examples/tree/main/webhooks/vercel)
- [Supabase Functions Example](https://github.com/vapi-ai/vapi-examples/tree/main/server/supabase)
- [React Components](https://github.com/vapi-ai/react-components)
- [MCP Server Example](https://github.com/vapi-ai/vapi-examples/tree/main/mcp)
- [Next.js Example](https://github.com/vapi-ai/vapi-examples/tree/main/client/nextjs)
