# LegalScout Development Workflow

## Development Environment Setup

### Prerequisites
- Node.js (version specified in package.json)
- npm or yarn
- Git
- VSCode (recommended)

### Initial Setup
1. Clone the repository:
   ```bash
   git clone https://github.com/damonkost/LegalScout_Voice.git
   cd LegalScout_Voice
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```

4. Configure environment variables in `.env`:
   ```
   VITE_SUPABASE_URL=your-supabase-url
   VITE_SUPABASE_KEY=your-supabase-anon-key
   VITE_VAPI_PUBLIC_KEY=your-vapi-public-key
   VAPI_TOKEN=your-vapi-api-key
   ```

5. Start the development server:
   ```bash
   npm run dev
   ```

## Development Workflow

### Branch Strategy
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/feature-name` - Feature branches
- `fix/bug-name` - Bug fix branches

### Feature Development Process
1. Create a new feature branch from `develop`:
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/your-feature-name
   ```

2. Implement your feature, following the coding standards and guidelines.

3. Test your changes locally:
   ```bash
   npm run dev
   ```

4. Commit your changes with descriptive messages:
   ```bash
   git add .
   git commit -m "[Feature] Implement feature description"
   ```

5. Push your branch to GitHub:
   ```bash
   git push origin feature/your-feature-name
   ```

6. Create a pull request to merge into `develop`.

7. After review and approval, merge the pull request.

### Bug Fix Process
1. Create a new bug fix branch from `develop`:
   ```bash
   git checkout develop
   git pull
   git checkout -b fix/bug-name
   ```

2. Fix the bug and test thoroughly.

3. Commit your changes:
   ```bash
   git add .
   git commit -m "[Fix] Fix bug description"
   ```

4. Push your branch and create a pull request.

### Code Review Guidelines
- Check for adherence to coding standards
- Verify functionality works as expected
- Ensure proper error handling
- Look for potential performance issues
- Validate accessibility compliance
- Review documentation updates

## Testing

### Local Testing
- Run the development server: `npm run dev`
- Test in different browsers
- Test responsive design using browser dev tools
- Verify voice functionality with Vapi

### Automated Testing
- Run unit tests: `npm test`
- Ensure all tests pass before submitting PR

## Deployment

### Staging Deployment
1. Merge changes into `develop` branch
2. Vercel will automatically deploy to staging environment
3. Test thoroughly in staging environment

### Production Deployment
1. Create a pull request from `develop` to `main`
2. After review and approval, merge to `main`
3. Vercel will automatically deploy to production
4. Verify deployment in production environment

## Supabase Database Changes

### Schema Changes
1. Create a new SQL migration file in `supabase/migrations/`
2. Test the migration locally
3. Apply the migration to the development database
4. Document the changes in `docs/BACKEND_STRUCTURE.md`

### Data Migrations
1. Create a migration script in `scripts/`
2. Test the script locally
3. Run the script against the development database
4. Document the migration process

## Vapi Integration Changes

### Assistant Configuration
1. Update the assistant configuration in `src/services/vapiAssistantService.js`
2. Test the changes locally
3. Document the changes in `docs/VAPI_INTEGRATION.md`

### Voice Configuration
1. Update voice settings in `src/components/dashboard/VoiceTab.jsx`
2. Test with different voice providers
3. Document available voices and configuration options

## Documentation

### Code Documentation
- Use JSDoc comments for functions and components
- Document complex logic with inline comments
- Update component props documentation

### Project Documentation
- Update `README.md` with new features or changes
- Keep `memory.md` updated with architectural decisions
- Update `todo.md` with completed tasks and new items
- Create or update feature-specific documentation in `docs/`

## Troubleshooting

### Common Issues

#### Supabase Connection Issues
- Verify Supabase URL and key in `.env`
- Check Supabase service status
- Verify RLS policies are correctly configured

#### Vapi Integration Issues
- Confirm Vapi API key is valid
- Check Vapi service status
- Verify assistant configuration

#### React Component Issues
- Check browser console for errors
- Verify component props
- Check state management logic

### Debugging Tools
- Browser DevTools for frontend debugging
- Vapi dashboard for voice assistant debugging
- Supabase dashboard for database debugging
- VSCode debugger for JavaScript debugging

## Performance Optimization

### Frontend Optimization
- Use React.memo for expensive components
- Implement lazy loading for routes
- Optimize images and assets
- Use proper key props in lists

### API Optimization
- Implement caching for frequent API calls
- Use pagination for large data sets
- Optimize database queries

## Accessibility Guidelines

- Ensure proper contrast ratios
- Add aria attributes to interactive elements
- Test with keyboard navigation
- Support screen readers

## Security Best Practices

- Never commit sensitive information
- Use environment variables for secrets
- Implement proper authentication checks
- Validate user inputs
- Follow Supabase RLS best practices

## Continuous Integration

- GitHub Actions for automated testing
- Vercel for automated deployments
- Automated dependency updates with Dependabot

## Version Control Best Practices

- Make atomic commits (one logical change per commit)
- Write descriptive commit messages
- Reference issue numbers in commits
- Keep branches up to date with `develop`
- Delete branches after merging

## Project Management

- Track tasks in `todo.md`
- Update project status in `project_status.md`
- Document architectural decisions in `memory.md`
- Use GitHub Issues for bug tracking
