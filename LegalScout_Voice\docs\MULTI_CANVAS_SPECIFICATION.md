# Multi-Canvas Interface Specification

## Overview

The Multi-Canvas Interface System transforms LegalScout's call interface from a two-column layout (dossier + conversation) to a three-column layout that includes a dynamic results and tools panel. This enhancement provides real-time visibility into AI agent activities, tool executions, and collaborative features.

## Architecture

### Current Layout (Two-Column)
```
┌─────────────────┬─────────────────────────────────┐
│                 │                                 │
│   Dossier       │        Map/Globe View           │
│   Panel         │                                 │
│   (Left)        │                                 │
│                 │                                 │
├─────────────────┼─────────────────────────────────┤
│                 │    Conversation Input           │
│                 │                                 │
└─────────────────┴─────────────────────────────────┘
```

### Proposed Layout (Three-Column)
```
┌─────────────┬─────────────────┬─────────────────┐
│             │                 │                 │
│  Dossier    │   Map/Globe     │   Multi-Canvas  │
│  Panel      │   View          │   Results       │
│  (Left)     │   (Center)      │   (Right)       │
│             │                 │                 │
├─────────────┼─────────────────┼─────────────────┤
│             │  Conversation Input               │
│             │                                   │
└─────────────┴───────────────────────────────────┘
```

## Multi-Canvas Panel Components

### 1. Tool Execution Results Display
**Purpose**: Show real-time output from MCP tool executions
**Features**:
- Streaming tool output with syntax highlighting
- Execution status indicators (running, completed, error)
- Collapsible sections for different tool types
- Export/copy functionality for results

### 2. Agent Reasoning Visualization
**Purpose**: Display AI agent's thought process and decision-making
**Features**:
- Step-by-step reasoning breakdown
- Confidence scores for decisions
- Alternative paths considered
- Visual decision trees

### 3. Task Lists and Workflow Progress
**Purpose**: Track multi-step processes and task completion
**Features**:
- Interactive task checkboxes
- Progress bars for complex workflows
- Dependency visualization
- Time estimates and actual completion times

### 4. Citations and Source References
**Purpose**: Display sources and references used by the AI
**Features**:
- Clickable citation links
- Source credibility indicators
- Reference categorization (legal, factual, procedural)
- Bookmark functionality for important sources

### 5. Web Agent Screenshots
**Purpose**: Show visual results from browser automation
**Features**:
- Real-time screenshot capture
- Annotated screenshots with highlights
- Screenshot history and comparison
- Click-to-navigate functionality

### 6. Plan Approval Interface
**Purpose**: Allow attorney oversight and approval of AI actions
**Features**:
- Action preview before execution
- Approve/reject buttons with comments
- Batch approval for similar actions
- Audit trail of all approvals

### 7. Geographic Visualization
**Purpose**: Enhanced maps, globes, and location-based data
**Features**:
- 3D globe integration
- Multi-layer map overlays
- Location-based legal jurisdiction information
- Interactive geographic search

### 8. Video Integration Window
**Purpose**: Enable attorney participation in calls
**Features**:
- Picture-in-picture video window
- Screen sharing capabilities
- Video recording for consultation records
- Multi-party video support

### 9. Collaborative Document Editor
**Purpose**: Real-time document collaboration during calls
**Features**:
- Live document editing
- Version control and change tracking
- Comment and annotation system
- Template library integration

## Technical Implementation

### Core Technologies

#### Vapi MCP Integration with Streamable HTTP
```javascript
// Enhanced Vapi service with MCP support using Vercel's streamable HTTP
class EnhancedVapiMcpService {
  constructor() {
    this.mcpClient = new VapiMcpClient();
    this.streamableHttp = new StreamableHttpClient();
    this.mcpAgent = null; // Last Mile mcp-agent framework integration
  }

  async initializeMcpAgent() {
    // Initialize Last Mile mcp-agent framework for agent-as-server architecture
    this.mcpAgent = new MCPAgent({
      name: "legalscout_multi_canvas",
      servers: ["fetch", "filesystem", "e2b", "ultimate-mcp"],
      workflows: ["parallel", "router", "orchestrator"]
    });
    await this.mcpAgent.start();
  }

  async executeToolWithResults(toolName, params) {
    const stream = await this.streamableHttp.execute(toolName, params);
    return this.handleStreamingResults(stream);
  }

  handleStreamingResults(stream) {
    // Real-time result processing for multi-canvas display
    return {
      toolOutput: stream.output,
      agentReasoning: stream.reasoning,
      citations: stream.sources,
      screenshots: stream.screenshots,
      taskProgress: stream.progress
    };
  }
}
```

#### E2B Sandbox Integration with MCP
```javascript
// Secure code execution environment with MCP server integration
class E2BSandboxManager {
  constructor() {
    this.mcpServer = new E2BMcpServer();
  }

  async executeCode(code, language) {
    // Use E2B MCP server for secure execution
    const result = await this.mcpServer.callTool("execute_code", {
      code,
      language,
      environment: "secure_sandbox"
    });

    return this.formatResults(result);
  }

  formatResults(results) {
    return {
      output: results.stdout,
      errors: results.stderr,
      screenshots: results.screenshots,
      files: results.generatedFiles,
      executionTime: results.duration,
      memoryUsage: results.memory
    };
  }
}
```

#### Multi-Canvas Layout Component with Dynamic Panels
```jsx
const MultiCanvasLayout = ({ dossierData, conversationData, toolResults }) => {
  const [activePanels, setActivePanels] = useState([
    'toolResults', 'agentReasoning', 'taskProgress'
  ]);

  return (
    <div className="multi-canvas-container">
      <DossierPanel data={dossierData} className="canvas-column-left" />
      <CentralMapView className="canvas-column-center" />
      <MultiCanvasPanel className="canvas-column-right">
        <PanelTabs activePanels={activePanels} onToggle={setActivePanels} />

        {activePanels.includes('toolResults') && (
          <ToolResultsDisplay results={toolResults} />
        )}

        {activePanels.includes('agentReasoning') && (
          <AgentReasoningView reasoning={toolResults.agentReasoning} />
        )}

        {activePanels.includes('taskProgress') && (
          <TaskProgressTracker progress={toolResults.taskProgress} />
        )}

        {activePanels.includes('citations') && (
          <CitationsPanel citations={toolResults.citations} />
        )}

        {activePanels.includes('video') && (
          <VideoWindow />
        )}

        {activePanels.includes('documents') && (
          <DocumentEditor />
        )}
      </MultiCanvasPanel>
    </div>
  );
};
```

### Integration Points

#### 1. Last Mile mcp-agent Framework
```javascript
// Agent-as-server architecture for sophisticated workflows
class LegalScoutMcpAgent {
  constructor() {
    this.app = new MCPApp({ name: "legalscout_voice" });
    this.agents = new Map();
    this.workflows = new Map();
  }

  async initializeAgents() {
    // Legal research agent with specialized tools
    const legalResearchAgent = new Agent({
      name: "legal_researcher",
      instruction: "Research legal precedents and statutes",
      serverNames: ["fetch", "ultimate-mcp", "court-listener"]
    });

    // Document analysis agent
    const documentAgent = new Agent({
      name: "document_analyzer",
      instruction: "Analyze legal documents and contracts",
      serverNames: ["filesystem", "e2b", "pdf-parser"]
    });

    this.agents.set("legal_research", legalResearchAgent);
    this.agents.set("document_analysis", documentAgent);
  }

  async createWorkflow(type, agents) {
    switch(type) {
      case "parallel":
        return new ParallelLLM({
          fanInAgent: agents.coordinator,
          fanOutAgents: agents.workers,
          llmFactory: OpenAIAugmentedLLM
        });

      case "orchestrator":
        return new Orchestrator({
          llmFactory: AnthropicAugmentedLLM,
          availableAgents: agents.all
        });
    }
  }
}
```

#### 2. FastAPI-MCP Auto-Generation
```python
# Automatic API-to-MCP server conversion
from fastapi_mcp import FastAPIMCPConverter

class LegalScoutAPIConverter:
    def __init__(self):
        self.converter = FastAPIMCPConverter()

    async def convert_existing_apis(self):
        # Convert LegalScout's existing APIs to MCP servers
        apis = [
            "attorney_search_api",
            "case_management_api",
            "document_storage_api",
            "billing_api"
        ]

        for api in apis:
            mcp_server = await self.converter.convert(api)
            await self.register_server(mcp_server)
```

#### 3. Ultimate MCP Server Integration
```javascript
// Access to 50+ pre-built tools through unified interface
class UltimateMcpIntegration {
  constructor() {
    this.ultimateServer = new UltimateMCPServer();
    this.availableTools = new Map();
  }

  async initializeTools() {
    const tools = await this.ultimateServer.listTools();

    // Categorize tools for legal use cases
    const legalTools = tools.filter(tool =>
      tool.categories.includes('legal') ||
      tool.categories.includes('research') ||
      tool.categories.includes('document')
    );

    legalTools.forEach(tool => {
      this.availableTools.set(tool.name, tool);
    });
  }
}
```

#### 4. E2B Secure Sandbox Environment
```javascript
// Secure code execution for AI-generated tools
class E2BLegalSandbox {
  async executeAIGeneratedCode(code, context) {
    const sandbox = await E2B.createSandbox({
      template: "legal_research_env",
      environment: {
        LEGAL_DB_ACCESS: context.attorneyId,
        CASE_ID: context.caseId
      }
    });

    const result = await sandbox.execute(code);

    return {
      output: result.stdout,
      visualizations: result.screenshots,
      generatedDocuments: result.files,
      securityLog: result.auditLog
    };
  }
}
```

## Implementation Timeline

### Phase 1: Foundation (Weeks 1-4)
- [ ] Implement basic three-column layout
- [ ] Integrate Vapi MCP server
- [ ] Set up E2B sandbox environment
- [ ] Create tool results display panel

### Phase 2: Core Features (Weeks 5-8)
- [ ] Agent reasoning visualization
- [ ] Task progress tracking
- [ ] Citations and references panel
- [ ] Web agent screenshots integration

### Phase 3: Advanced Features (Weeks 9-12)
- [ ] Video window integration
- [ ] Collaborative document editor
- [ ] Geographic visualization enhancements
- [ ] Plan approval interface

### Phase 4: Polish and Optimization (Weeks 13-16)
- [ ] Performance optimization
- [ ] Mobile responsiveness
- [ ] User experience refinements
- [ ] Comprehensive testing

## Benefits for Attorneys

### Immediate Value
- **Transparency**: Complete visibility into AI agent activities
- **Control**: Real-time oversight and intervention capabilities
- **Efficiency**: Parallel processing of multiple tasks
- **Quality**: Enhanced accuracy through human oversight

### Long-term Impact
- **Client Trust**: Demonstrable AI transparency builds confidence
- **Competitive Advantage**: Advanced tooling differentiates services
- **Scalability**: Handle more complex cases with AI assistance
- **Innovation**: Platform for developing new legal service models

## Success Metrics

### Technical Metrics
- Tool execution response time < 2 seconds
- Multi-canvas layout load time < 1 second
- 99.9% uptime for MCP integrations
- Zero security incidents with E2B sandbox

### User Experience Metrics
- Attorney adoption rate > 80%
- Client satisfaction increase > 15%
- Case resolution time reduction > 25%
- Tool usage frequency > 10 tools per session

## Risk Mitigation

### Technical Risks
- **MCP Server Reliability**: Implement fallback mechanisms
- **Sandbox Security**: Regular security audits and updates
- **Performance Impact**: Lazy loading and optimization
- **Browser Compatibility**: Progressive enhancement approach

### User Experience Risks
- **Complexity Overload**: Configurable interface with presets
- **Learning Curve**: Comprehensive training and documentation
- **Mobile Limitations**: Responsive design with priority features
- **Attorney Resistance**: Gradual rollout with feedback loops

## Future Enhancements

### Advanced AI Integration
- Predictive tool suggestions based on case type
- Automated workflow optimization
- Machine learning for interface personalization
- Natural language tool invocation

### Platform Expansion
- Third-party tool marketplace
- Custom tool development framework
- API for external integrations
- White-label solutions for law firms

This specification provides the foundation for implementing a comprehensive multi-canvas interface that significantly enhances LegalScout's capabilities while maintaining usability and performance.
