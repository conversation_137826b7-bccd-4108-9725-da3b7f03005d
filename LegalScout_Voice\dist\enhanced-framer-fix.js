/**
 * Enhanced Framer Motion Fix
 *
 * This script combines the functionality of both patch-layout-group-context.js and disable-framer-motion.js
 * with additional measures to prevent the "Cannot read properties of undefined (reading 'createContext')" error.
 */

(function() {
  console.log('[EnhancedFix] Setting up enhanced Framer Motion fix');

  // STEP 1: Immediately define React and React.createContext globally
  // This must happen before any other scripts execute
  if (typeof window !== 'undefined') {
    // Create a global React object if it doesn't exist
    if (!window.React) {
      window.React = {};
    }

    // Force override createContext even if it exists
    window.React.createContext = function(defaultValue) {
      console.log('[EnhancedFix] Using mock createContext');
      return {
        Provider: function(props) { return props.children || null; },
        Consumer: function(props) { return props.children ? props.children({}) : null; },
        displayName: 'MockContext',
        _currentValue: defaultValue,
        _currentValue2: defaultValue,
        _threadCount: 0,
        _defaultValue: defaultValue
      };
    };

    // Add other essential React methods
    const reactMethods = [
      'useState', 'useEffect', 'useLayoutEffect', 'useRef',
      'useCallback', 'useMemo', 'useContext', 'forwardRef',
      'createElement', 'cloneElement', 'createRef', 'Component',
      'PureComponent', 'Fragment', 'Children', 'isValidElement'
    ];

    reactMethods.forEach(method => {
      if (typeof window.React[method] === 'undefined') {
        console.log(`[EnhancedFix] Adding ${method} placeholder`);
        window.React[method] = function() {
          return arguments[0] instanceof Function ? arguments[0]() : null;
        };
      }
    });
  }

  // STEP 2: Create a mock implementation of LayoutGroupContext
  // This will be used to replace any imports of LayoutGroupContext.mjs
  const mockLayoutGroupContext = {
    LayoutGroupContext: {
      Provider: function(props) { return props.children || null; },
      Consumer: function(props) { return props.children ? props.children({}) : null; },
      displayName: 'LayoutGroupContext'
    }
  };

  // STEP 3: Create a mock implementation of Framer Motion
  const mockFramerMotion = {
    motion: new Proxy({}, {
      get: function(target, prop) {
        // Return the HTML element name for any motion component
        return prop;
      }
    }),
    AnimatePresence: function(props) {
      return props.children || null;
    },
    useAnimation: function() {
      return {
        start: function() {},
        stop: function() {}
      };
    },
    useMotionValue: function(initial) {
      return {
        get: function() { return initial; },
        set: function() {},
        onChange: function() {}
      };
    },
    useTransform: function() {
      return {
        get: function() { return 0; }
      };
    },
    useSpring: function() {
      return {
        get: function() { return 0; }
      };
    },
    useViewportScroll: function() {
      return {
        scrollY: {
          get: function() { return 0; },
          onChange: function() {}
        },
        scrollYProgress: {
          get: function() { return 0; },
          onChange: function() {}
        }
      };
    },
    useScroll: function() {
      return {
        scrollY: {
          get: function() { return 0; },
          onChange: function() {}
        },
        scrollYProgress: {
          get: function() { return 0; },
          onChange: function() {}
        }
      };
    },
    useCycle: function() {
      return [0, function() {}];
    },
    LayoutGroup: function(props) {
      return props.children || null;
    }
  };

  // STEP 4: Override the import function to intercept Framer Motion and LayoutGroupContext imports
  if (window.import) {
    const originalImport = window.import;
    window.import = function(specifier) {
      if (specifier === 'framer-motion' || specifier.includes('framer-motion/')) {
        console.log('[EnhancedFix] Intercepted Framer Motion import:', specifier);
        return Promise.resolve(mockFramerMotion);
      }
      if (specifier.includes('LayoutGroupContext.mjs') || specifier.includes('LayoutGroupContext')) {
        console.log('[EnhancedFix] Intercepted LayoutGroupContext import:', specifier);
        return Promise.resolve(mockLayoutGroupContext);
      }
      return originalImport.apply(this, arguments);
    };
  }

  // STEP 5: Override the fetch function to intercept requests for LayoutGroupContext.mjs
  const originalFetch = window.fetch;
  window.fetch = function(url, options) {
    if (url && typeof url === 'string' && url.includes('LayoutGroupContext.mjs')) {
      console.log('[EnhancedFix] Intercepted fetch for LayoutGroupContext.mjs');

      // Return a mock response
      return Promise.resolve({
        ok: true,
        status: 200,
        text: function() {
          return Promise.resolve(`
            // Mock implementation of LayoutGroupContext.mjs
            const LayoutGroupContext = {
              Provider: function(props) { return props.children || null; },
              Consumer: function(props) { return props.children ? props.children({}) : null; }
            };

            export { LayoutGroupContext };
            export default LayoutGroupContext;
          `);
        },
        json: function() {
          return Promise.resolve(mockLayoutGroupContext);
        }
      });
    }

    // Otherwise, use the original fetch
    return originalFetch.apply(this, arguments);
  };

  // STEP 6: Override the XMLHttpRequest.prototype.open method to intercept requests
  const originalOpen = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
    if (url && typeof url === 'string' && url.includes('LayoutGroupContext.mjs')) {
      console.log('[EnhancedFix] Intercepted XMLHttpRequest for LayoutGroupContext.mjs');

      // Modify the URL to point to a non-existent resource
      url = '/empty.js';
    }

    // Call the original open method
    return originalOpen.call(this, method, url, async, user, password);
  };

  // STEP 7: Create an empty.js file in memory
  const blob = new Blob([`
    // Empty file
    console.log('[EnhancedFix] Using empty.js instead of LayoutGroupContext.mjs');
  `], { type: 'application/javascript' });

  // Create a URL for the blob
  const emptyJsUrl = URL.createObjectURL(blob);

  // STEP 8: Override the createElement method to intercept script tags
  const originalCreateElement = document.createElement;
  document.createElement = function(tagName) {
    const element = originalCreateElement.call(document, tagName);

    if (tagName.toLowerCase() === 'script') {
      const originalSetAttribute = element.setAttribute;
      element.setAttribute = function(name, value) {
        if (name === 'src' && value && (value.includes('framer-motion') || value.includes('LayoutGroupContext'))) {
          console.log('[EnhancedFix] Blocking script:', value);
          return;
        }

        // Otherwise, use the original setAttribute
        return originalSetAttribute.apply(this, arguments);
      };
    }

    return element;
  };

  // STEP 9: Define a global LayoutGroupContext
  window.LayoutGroupContext = {
    Provider: function(props) { return props.children || null; },
    Consumer: function(props) { return props.children ? props.children({}) : null; }
  };

  // STEP 10: Override the require function to intercept Framer Motion requires
  if (typeof window.require === 'function') {
    const originalRequire = window.require;
    window.require = function(module) {
      if (module === 'framer-motion' || module.includes('framer-motion/')) {
        console.log('[EnhancedFix] Intercepted Framer Motion require:', module);
        return mockFramerMotion;
      }
      if (module.includes('LayoutGroupContext')) {
        console.log('[EnhancedFix] Intercepted LayoutGroupContext require:', module);
        return mockLayoutGroupContext;
      }
      return originalRequire.apply(this, arguments);
    };
  }

  // STEP 11: Patch dynamic imports
  const originalHead = document.head;
  Object.defineProperty(document, 'head', {
    get: function() {
      return originalHead;
    },
    set: function(value) {
      console.log('[EnhancedFix] Attempted to modify document.head');
      // Don't allow modification of document.head
      return originalHead;
    }
  });

  // STEP 12: Instead of using Object.defineProperty which can cause conflicts,
  // we'll create a backup of React.createContext that we can restore if needed
  const originalCreateContext = window.React && window.React.createContext;

  // Set up a MutationObserver to monitor changes to window.React
  if (typeof MutationObserver !== 'undefined' && window.React) {
    // Create a function to check and restore React.createContext
    const checkAndRestoreReact = function() {
      if (window.React && !window.React.createContext && originalCreateContext) {
        console.log('[EnhancedFix] Restoring React.createContext');
        window.React.createContext = originalCreateContext;
      }
    };

    // Set up an interval to periodically check React.createContext
    setInterval(checkAndRestoreReact, 100);

    // Also set up event listeners for various events that might trigger React loading
    window.addEventListener('DOMContentLoaded', checkAndRestoreReact);
    window.addEventListener('load', checkAndRestoreReact);
  }

  console.log('[EnhancedFix] Enhanced Framer Motion fix setup complete');
})();
