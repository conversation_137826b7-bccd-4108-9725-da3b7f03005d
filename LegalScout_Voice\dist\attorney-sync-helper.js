/**
 * Attorney Sync Helper
 * 
 * A simplified helper for synchronizing attorney profiles between
 * localStorage, Supabase, and Vapi.
 */

(function() {
  // Default Vapi assistant configuration
  const DEFAULT_VAPI_CONFIG = {
    llm: {
      provider: 'openai',
      model: 'gpt-4o-mini'
    },
    transcriber: {
      provider: 'deepgram',
      model: 'nova-3'
    }
  };

  /**
   * Create a Vapi assistant configuration from attorney data
   * @param {Object} attorney - The attorney data
   * @returns {Object} Vapi assistant configuration
   */
  function createVapiConfig(attorney) {
    return {
      name: `${attorney.firm_name || 'Law Firm'} Legal Assistant`,
      instructions: attorney.vapi_instructions || `You are a legal assistant for ${attorney.firm_name || 'a law firm'}. Help potential clients understand their legal needs and collect relevant information for consultation.`,
      firstMessage: attorney.welcome_message || 'Hello! I\'m your legal assistant. How can I help you today?',
      voice: {
        provider: attorney.voice_provider || '11labs',
        voiceId: attorney.voice_id || 'sarah'
      },
      ...DEFAULT_VAPI_CONFIG
    };
  }

  /**
   * Synchronize attorney with Vapi
   * @param {Object} attorney - The attorney to synchronize
   * @param {boolean} force - Whether to force synchronization
   * @returns {Promise<Object>} The synchronization result
   */
  async function syncWithVapi(attorney, force = false) {
    if (!attorney || !attorney.id) {
      console.warn('[AttorneySyncHelper] Cannot sync invalid attorney with Vapi');
      return { success: false, error: 'Invalid attorney data' };
    }

    console.log('[AttorneySyncHelper] Syncing attorney with Vapi:', attorney.id);

    try {
      // Check if MCP is available
      if (!window.mcp) {
        console.warn('[AttorneySyncHelper] MCP not available, skipping Vapi sync');
        return { 
          success: true, 
          action: 'skipped',
          message: 'MCP not available'
        };
      }

      // Create Vapi configuration
      const vapiConfig = createVapiConfig(attorney);

      // If attorney has a Vapi assistant ID, update it
      if (attorney.vapi_assistant_id && !force) {
        try {
          // Get the current assistant
          const currentAssistant = await window.mcp.invoke('get_assistant_vapi-mcp-server', {
            assistantId: attorney.vapi_assistant_id
          });

          // Check if update is needed
          const needsUpdate = 
            currentAssistant.name !== vapiConfig.name ||
            currentAssistant.instructions !== vapiConfig.instructions ||
            currentAssistant.firstMessage !== vapiConfig.firstMessage ||
            currentAssistant.voice.provider !== vapiConfig.voice.provider ||
            currentAssistant.voice.voiceId !== vapiConfig.voice.voiceId;

          if (needsUpdate || force) {
            // Update the assistant
            const updatedAssistant = await window.mcp.invoke('update_assistant_vapi-mcp-server', {
              assistantId: attorney.vapi_assistant_id,
              ...vapiConfig
            });

            return {
              success: true,
              action: 'updated',
              assistantId: updatedAssistant.id,
              message: 'Vapi assistant updated successfully'
            };
          }

          return {
            success: true,
            action: 'none',
            assistantId: attorney.vapi_assistant_id,
            message: 'Vapi assistant is up to date'
          };
        } catch (error) {
          console.error('[AttorneySyncHelper] Error updating Vapi assistant:', error);

          // If the assistant doesn't exist, create a new one
          if (error.message && (error.message.includes('not found') || error.message.includes('404'))) {
            console.log('[AttorneySyncHelper] Assistant not found, creating new one');
            return await createVapiAssistant(attorney);
          }

          return {
            success: false,
            action: 'error',
            error: error.message,
            message: 'Error updating Vapi assistant'
          };
        }
      } else {
        // Create a new assistant
        return await createVapiAssistant(attorney);
      }
    } catch (error) {
      console.error('[AttorneySyncHelper] Error syncing with Vapi:', error);
      return {
        success: false,
        action: 'error',
        error: error.message,
        message: 'Error syncing with Vapi'
      };
    }
  }

  /**
   * Create a new Vapi assistant
   * @param {Object} attorney - The attorney data
   * @returns {Promise<Object>} The creation result
   */
  async function createVapiAssistant(attorney) {
    try {
      // Create Vapi configuration
      const vapiConfig = createVapiConfig(attorney);

      // Create the assistant
      const newAssistant = await window.mcp.invoke('create_assistant_vapi-mcp-server', vapiConfig);

      return {
        success: true,
        action: 'created',
        assistantId: newAssistant.id,
        message: 'Vapi assistant created successfully'
      };
    } catch (error) {
      console.error('[AttorneySyncHelper] Error creating Vapi assistant:', error);
      return {
        success: false,
        action: 'error',
        error: error.message,
        message: 'Error creating Vapi assistant'
      };
    }
  }

  /**
   * Ensure attorney profile persistence across systems
   * @param {Object} params - Parameters
   * @param {Object} params.attorney - The attorney data
   * @param {boolean} params.syncWithVapi - Whether to sync with Vapi
   * @param {boolean} params.force - Whether to force synchronization
   * @returns {Promise<Object>} The persistence result
   */
  async function ensureAttorneyPersistence(params) {
    const { attorney, syncWithVapi: shouldSyncVapi = false, force = false } = params;

    if (!attorney || !attorney.id) {
      console.warn('[AttorneySyncHelper] Cannot ensure persistence for invalid attorney');
      return { success: false, error: 'Invalid attorney data' };
    }

    console.log('[AttorneySyncHelper] Ensuring attorney persistence:', attorney.id);

    try {
      // Step 1: Save to localStorage
      const localStorageResult = window.simplifiedAttorneyManager.saveToLocalStorage(attorney);
      
      // Step 2: Sync with Vapi if requested
      let vapiResult = null;
      if (shouldSyncVapi) {
        vapiResult = await syncWithVapi(attorney, force);
        
        // If Vapi sync was successful and created/updated an assistant,
        // update the attorney with the assistant ID
        if (vapiResult.success && vapiResult.assistantId && 
            (vapiResult.action === 'created' || vapiResult.action === 'updated')) {
          // Update attorney with assistant ID
          const updatedAttorney = {
            ...attorney,
            vapi_assistant_id: vapiResult.assistantId,
            updated_at: new Date().toISOString()
          };
          
          // Save updated attorney to localStorage
          window.simplifiedAttorneyManager.saveToLocalStorage(updatedAttorney);
        }
      }

      return {
        success: true,
        attorney: localStorageResult,
        localStorage: { success: true },
        vapi: vapiResult,
        message: 'Attorney persistence ensured'
      };
    } catch (error) {
      console.error('[AttorneySyncHelper] Error ensuring attorney persistence:', error);
      return {
        success: false,
        error: error.message,
        message: 'Error ensuring attorney persistence'
      };
    }
  }

  /**
   * Get current attorney with fallback
   * @returns {Object} The current attorney
   */
  function getCurrentAttorney() {
    // Try to get from simplified manager
    if (window.simplifiedAttorneyManager) {
      return window.simplifiedAttorneyManager.getCurrentAttorney();
    }
    
    // Try to get from standalone manager
    if (window.standaloneAttorneyManager) {
      const attorney = window.standaloneAttorneyManager.getAttorney();
      if (attorney) return attorney;
      
      // Try to load from localStorage
      return window.standaloneAttorneyManager.loadFromLocalStorage();
    }
    
    // Last resort: try to get directly from localStorage
    try {
      const storedAttorney = localStorage.getItem('attorney');
      if (storedAttorney) {
        return JSON.parse(storedAttorney);
      }
    } catch (error) {
      console.error('[AttorneySyncHelper] Error getting attorney from localStorage:', error);
    }
    
    // Create a default attorney if all else fails
    return {
      id: 'fallback-' + Date.now(),
      name: 'Attorney',
      firm_name: 'Law Firm',
      welcome_message: 'Hello, I\'m your legal assistant. How can I help you today?',
      vapi_instructions: 'You are a legal assistant. Help potential clients understand their legal needs and collect relevant information for consultation.',
      voice_provider: '11labs',
      voice_id: 'sarah',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }

  // Create the attorney sync helper
  const attorneySyncHelper = {
    createVapiConfig,
    syncWithVapi,
    createVapiAssistant,
    ensureAttorneyPersistence,
    getCurrentAttorney
  };

  // Make it available globally
  window.attorneySyncHelper = attorneySyncHelper;
  
  console.log('[AttorneySyncHelper] Initialized');
})();
