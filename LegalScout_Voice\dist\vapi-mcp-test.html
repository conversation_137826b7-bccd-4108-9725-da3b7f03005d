<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vapi MCP Server Test</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="text"] {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    button:hover {
      background-color: #45a049;
    }
    button:disabled {
      background-color: #cccccc;
      cursor: not-allowed;
    }
    .result {
      background-color: #f9f9f9;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-top: 20px;
      white-space: pre-wrap;
      font-family: monospace;
      max-height: 300px;
      overflow-y: auto;
    }
    .error {
      color: #d32f2f;
      background-color: #ffebee;
      border: 1px solid #ffcdd2;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }
    .success {
      color: #388e3c;
      background-color: #e8f5e9;
      border: 1px solid #c8e6c9;
      padding: 10px;
      border-radius: 4px;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <h1>Vapi MCP Server Test</h1>
  
  <div class="card">
    <h2>Test Vapi API Connection</h2>
    <div class="form-group">
      <label for="apiKey">Vapi API Key</label>
      <input type="text" id="apiKey" placeholder="Enter your Vapi API key">
    </div>
    <button id="testApiButton">Test API Connection</button>
    <div id="apiResult" class="result" style="display: none;"></div>
  </div>
  
  <div class="card">
    <h2>List Assistants</h2>
    <button id="listAssistantsButton" disabled>List Assistants</button>
    <div id="assistantsResult" class="result" style="display: none;"></div>
  </div>
  
  <div class="card">
    <h2>List Phone Numbers</h2>
    <button id="listPhoneNumbersButton" disabled>List Phone Numbers</button>
    <div id="phoneNumbersResult" class="result" style="display: none;"></div>
  </div>
  
  <script>
    // Elements
    const apiKeyInput = document.getElementById('apiKey');
    const testApiButton = document.getElementById('testApiButton');
    const apiResult = document.getElementById('apiResult');
    const listAssistantsButton = document.getElementById('listAssistantsButton');
    const assistantsResult = document.getElementById('assistantsResult');
    const listPhoneNumbersButton = document.getElementById('listPhoneNumbersButton');
    const phoneNumbersResult = document.getElementById('phoneNumbersResult');
    
    // State
    let apiKey = '';
    
    // Test API Connection
    testApiButton.addEventListener('click', async () => {
      apiKey = apiKeyInput.value.trim();
      
      if (!apiKey) {
        showError(apiResult, 'Please enter a valid API key');
        return;
      }
      
      try {
        apiResult.style.display = 'block';
        apiResult.innerHTML = 'Testing API connection...';
        
        const response = await fetch('https://api.vapi.ai/api/v1/assistants', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        apiResult.innerHTML = `✅ API connection successful!\n\nFound ${data.length} assistants.`;
        apiResult.classList.add('success');
        
        // Enable other buttons
        listAssistantsButton.disabled = false;
        listPhoneNumbersButton.disabled = false;
      } catch (error) {
        showError(apiResult, `Error: ${error.message}`);
      }
    });
    
    // List Assistants
    listAssistantsButton.addEventListener('click', async () => {
      try {
        assistantsResult.style.display = 'block';
        assistantsResult.innerHTML = 'Loading assistants...';
        
        const response = await fetch('https://api.vapi.ai/api/v1/assistants', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.length === 0) {
          assistantsResult.innerHTML = 'No assistants found.';
          return;
        }
        
        let result = `Found ${data.length} assistants:\n\n`;
        
        data.forEach((assistant, index) => {
          result += `${index + 1}. ${assistant.name} (${assistant.id})\n`;
        });
        
        assistantsResult.innerHTML = result;
      } catch (error) {
        showError(assistantsResult, `Error: ${error.message}`);
      }
    });
    
    // List Phone Numbers
    listPhoneNumbersButton.addEventListener('click', async () => {
      try {
        phoneNumbersResult.style.display = 'block';
        phoneNumbersResult.innerHTML = 'Loading phone numbers...';
        
        const response = await fetch('https://api.vapi.ai/api/v1/phone-numbers', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.length === 0) {
          phoneNumbersResult.innerHTML = 'No phone numbers found.';
          return;
        }
        
        let result = `Found ${data.length} phone numbers:\n\n`;
        
        data.forEach((phoneNumber, index) => {
          result += `${index + 1}. ${phoneNumber.phoneNumber} (${phoneNumber.id})\n`;
        });
        
        phoneNumbersResult.innerHTML = result;
      } catch (error) {
        showError(phoneNumbersResult, `Error: ${error.message}`);
      }
    });
    
    // Helper function to show error
    function showError(element, message) {
      element.style.display = 'block';
      element.innerHTML = message;
      element.classList.add('error');
      element.classList.remove('success');
    }
  </script>
</body>
</html>
