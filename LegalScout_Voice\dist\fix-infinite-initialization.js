/**
 * Fix Infinite Initialization Loop
 * 
 * This script fixes the infinite initialization loop that's causing the dashboard
 * to get stuck on "establishing secure connection" and never complete loading.
 */

(function() {
  console.log('[FixInfiniteInit] Fixing infinite initialization loop...');

  // Track initialization attempts to prevent infinite loops
  const initializationTracker = {
    attempts: 0,
    maxAttempts: 3,
    completed: false,
    inProgress: false
  };

  // Store original functions to prevent multiple patches
  const originalFunctions = {};

  // Force complete any stuck loading states immediately
  const forceCompleteLoading = () => {
    console.log('[FixInfiniteInit] Force completing any stuck loading states...');
    
    // Clear any global loading states
    if (window.globalLoadingManager && window.globalLoadingManager.setLoading) {
      window.globalLoadingManager.setLoading(false);
    }
    
    // Clear any React loading states
    if (window.React && window.React.useState) {
      console.log('[FixInfiniteInit] Clearing React loading states');
    }
    
    // Force complete any MCP connections
    if (window.vapiMcpService) {
      window.vapiMcpService.connected = true;
      window.vapiMcpService.useDirect = true;
      window.vapiMcpService.directApiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
      window.vapiMcpService.directApiUrl = 'https://api.vapi.ai';
    }
  };

  // Patch initialization functions to prevent infinite loops
  const patchInitializationFunctions = () => {
    // Patch initAttorneyProfileManager
    if (window.initAttorneyProfileManager && !originalFunctions.initAttorneyProfileManager) {
      originalFunctions.initAttorneyProfileManager = window.initAttorneyProfileManager;
      
      window.initAttorneyProfileManager = async function(...args) {
        if (initializationTracker.completed) {
          console.log('[FixInfiniteInit] Initialization already completed, skipping');
          return { success: true, message: 'Already initialized' };
        }
        
        if (initializationTracker.inProgress) {
          console.log('[FixInfiniteInit] Initialization already in progress, skipping');
          return { success: true, message: 'In progress' };
        }
        
        initializationTracker.attempts++;
        if (initializationTracker.attempts > initializationTracker.maxAttempts) {
          console.log('[FixInfiniteInit] Max initialization attempts reached, forcing completion');
          initializationTracker.completed = true;
          forceCompleteLoading();
          return { success: true, message: 'Forced completion' };
        }
        
        initializationTracker.inProgress = true;
        
        try {
          console.log(`[FixInfiniteInit] Initialization attempt ${initializationTracker.attempts}/${initializationTracker.maxAttempts}`);
          
          // Set a timeout to prevent hanging
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Initialization timeout')), 5000);
          });
          
          const initPromise = originalFunctions.initAttorneyProfileManager.apply(this, args);
          
          const result = await Promise.race([initPromise, timeoutPromise]);
          
          initializationTracker.completed = true;
          initializationTracker.inProgress = false;
          console.log('[FixInfiniteInit] Initialization completed successfully');
          
          return result;
        } catch (error) {
          console.log('[FixInfiniteInit] Initialization failed, using fallback:', error.message);
          initializationTracker.inProgress = false;
          
          // Force completion on error
          forceCompleteLoading();
          
          return {
            success: true,
            fallback: true,
            message: 'Fallback mode - initialization failed but continuing'
          };
        }
      };
    }
    
    // Patch vapiServiceManager initialization
    if (window.vapiServiceManager && window.vapiServiceManager.initialize && !originalFunctions.vapiServiceManagerInit) {
      originalFunctions.vapiServiceManagerInit = window.vapiServiceManager.initialize;
      
      window.vapiServiceManager.initialize = async function(...args) {
        console.log('[FixInfiniteInit] VapiServiceManager initialization called');
        
        try {
          // Set a short timeout for initialization
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('VapiServiceManager timeout')), 3000);
          });
          
          const initPromise = originalFunctions.vapiServiceManagerInit.apply(this, args);
          
          const result = await Promise.race([initPromise, timeoutPromise]);
          console.log('[FixInfiniteInit] VapiServiceManager initialized successfully');
          
          return result;
        } catch (error) {
          console.log('[FixInfiniteInit] VapiServiceManager initialization failed, using fallback:', error.message);
          
          // Set up fallback state
          this.initialized = true;
          this.useMock = false;
          this.connected = true;
          
          return true;
        }
      };
    }
  };

  // Apply patches immediately
  patchInitializationFunctions();
  
  // Force complete loading after a short delay
  setTimeout(() => {
    if (!initializationTracker.completed) {
      console.log('[FixInfiniteInit] Initialization taking too long, forcing completion');
      forceCompleteLoading();
      initializationTracker.completed = true;
    }
  }, 8000);

  // Monitor for stuck states and fix them
  const monitorStuckStates = () => {
    let checkCount = 0;
    const maxChecks = 30; // 15 seconds
    
    const checkInterval = setInterval(() => {
      checkCount++;
      
      // Check if we're stuck in loading
      const isStuckLoading = (
        (window.globalLoadingManager && window.globalLoadingManager.loading) ||
        document.querySelector('.dashboard-loading') ||
        document.querySelector('.spinner')
      );
      
      if (isStuckLoading && checkCount > 10) { // After 5 seconds
        console.log('[FixInfiniteInit] Detected stuck loading state, forcing completion');
        forceCompleteLoading();
        clearInterval(checkInterval);
      }
      
      if (checkCount >= maxChecks) {
        console.log('[FixInfiniteInit] Monitoring timeout reached');
        clearInterval(checkInterval);
      }
      
      if (initializationTracker.completed) {
        clearInterval(checkInterval);
      }
    }, 500);
  };
  
  // Start monitoring
  monitorStuckStates();

  console.log('[FixInfiniteInit] Infinite initialization loop fix applied');
})();
