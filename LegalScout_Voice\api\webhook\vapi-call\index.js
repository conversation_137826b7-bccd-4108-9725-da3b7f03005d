/**
 * Vapi Call Webhook Handler
 *
 * This serverless function handles webhooks from Vapi for call events.
 * It stores call data in Supabase for later retrieval and analysis,
 * and sends SMS notifications to attorneys about ongoing calls.
 */

// Use dynamic imports to avoid module loading issues in serverless
let supabaseClient = null;

/**
 * Initialize Supabase client lazily
 */
async function getSupabaseClient() {
  if (supabaseClient) {
    return supabaseClient;
  }

  try {
    const { createClient } = await import('@supabase/supabase-js');

    const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      console.warn('Supabase not configured - webhook will run in mock mode');
      return null;
    }

    supabaseClient = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    return supabaseClient;
  } catch (error) {
    console.error('Error initializing Supabase client:', error);
    return null;
  }
}

/**
 * Get attorney ID from assistant ID
 * @param {string} assistantId - The Vapi assistant ID
 * @returns {Promise<string|null>} - The attorney ID or null if not found
 */
async function getAttorneyIdFromAssistantId(assistantId) {
  try {
    const supabase = await getSupabaseClient();
    if (!supabase) {
      console.warn('Supabase not configured, returning mock attorney ID');
      return '00000000-0000-0000-0000-000000000000';
    }

    // Special handling for home page assistant - <NAME_EMAIL>
    const HOME_PAGE_ASSISTANT_ID = 'e3fff1dd-2e82-4cce-ac6c-8c3271eb0865';

    if (assistantId === HOME_PAGE_ASSISTANT_ID) {
      console.log('Home page assistant detected, <NAME_EMAIL>');

      // Find <EMAIL> attorney
      const { data: scoutAttorney, error: scoutError } = await supabase
        .from('attorneys')
        .select('id')
        .eq('email', '<EMAIL>')
        .single();

      if (scoutError || !scoutAttorney) {
        console.warn('Scout attorney not found, using default attorney');
        // Return default attorney ID as fallback
        return '00000000-0000-0000-0000-000000000000';
      }

      console.log('Using <EMAIL> attorney:', scoutAttorney.id);
      return scoutAttorney.id;
    }

    // For other assistants, find by assistant ID
    const { data, error } = await supabase
      .from('attorneys')
      .select('id')
      .eq('vapi_assistant_id', assistantId)
      .single();

    if (error || !data) {
      console.error('Error getting attorney ID from assistant ID:', error);
      return null;
    }

    return data.id;
  } catch (error) {
    console.error('Error getting attorney ID from assistant ID:', error);
    return null;
  }
}

/**
 * Store call data in Supabase and send notifications if needed
 * @param {Object} callData - The call data from Vapi
 * @returns {Promise<Object>} - The result of the operation
 */
async function storeCallData(callData) {
  try {
    const supabase = await getSupabaseClient();
    if (!supabase) {
      console.warn('Supabase not configured, running in mock mode');
      return {
        success: true,
        data: { mock: true, message: 'Call data would be stored in Supabase' }
      };
    }

    // Get attorney ID from assistant ID
    const attorneyId = await getAttorneyIdFromAssistantId(callData.assistant_id);

    if (!attorneyId) {
      return {
        success: false,
        error: 'Attorney not found for assistant ID: ' + callData.assistant_id
      };
    }

    // Extract relevant data from the call
    const callRecord = {
      call_id: callData.id,
      assistant_id: callData.assistant_id,
      attorney_id: attorneyId,
      customer_phone: callData.customer?.phone_number,
      status: callData.status,
      duration: callData.duration,
      start_time: callData.start_time,
      end_time: callData.end_time,
      transcripts: callData.transcripts || [],
      messages: callData.messages || [],
      tool_executions: callData.tool_executions || [],
      metadata: callData.metadata || {}
    };

    // Store in Supabase
    const { data, error } = await supabase
      .from('call_records')
      .upsert(callRecord, { onConflict: 'call_id' })
      .select();

    if (error) {
      throw error;
    }

    // Handle notifications based on call status
    await handleCallNotifications(callData, attorneyId);

    // If call is completed or ended, also create a consultation record
    if (callData.status === 'completed' || callData.status === 'ended') {
      await createConsultationRecord(callData, attorneyId);
    }

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Error storing call data:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Handle call notifications based on call status
 * @param {Object} callData - The call data from Vapi
 * @param {string} attorneyId - The attorney ID
 * @returns {Promise<void>}
 */
async function handleCallNotifications(callData, attorneyId) {
  try {
    const supabase = await getSupabaseClient();
    if (!supabase) {
      console.warn('Supabase not configured, skipping notifications');
      return;
    }

    // Get attorney details
    const { data: attorney, error } = await supabase
      .from('attorneys')
      .select('*')
      .eq('id', attorneyId)
      .single();

    if (error || !attorney) {
      console.error('Error getting attorney details:', error);
      return;
    }

    // Skip notifications if attorney has no phone number
    if (!attorney.phone) {
      console.warn(`Attorney ${attorneyId} has no phone number for notifications`);
      return;
    }

    // Generate simple call control token (for SMS links)
    const token = `call-${callData.id}-${attorneyId}-${Date.now()}`;

    // Create call control URL
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://legalscout.net';
    const callControlUrl = `${baseUrl}/call-control?token=${token}`;

    // Send notification based on call status
    if (callData.status === 'in-progress' && callData.transcripts && callData.transcripts.length >= 3) {
      // Call has started and has some conversation
      const clientInfo = extractClientInfo(callData);
      const legalIssue = clientInfo.practice_area || 'Not specified yet';

      // Create message
      const message = `You have an active call from ${callData.customer?.phone_number || 'a potential client'}. ` +
        `Legal issue: ${legalIssue}. Click here to monitor and control: ${callControlUrl}`;

      // Send SMS using Vapi's SMS API
      await sendSms(attorney.phone, message);
    } else if (callData.status === 'completed') {
      // Call has ended
      const durationMinutes = Math.round((callData.duration || 0) / 60);

      // Create message
      const message = `Call with ${callData.customer?.phone_number || 'a potential client'} has ended. ` +
        `Duration: ${durationMinutes} minutes. Click here to view summary: ${callControlUrl}`;

      // Send SMS using Vapi's SMS API
      await sendSms(attorney.phone, message);
    }
  } catch (error) {
    console.error('Error handling call notifications:', error);
  }
}

/**
 * Send SMS using Vapi's SMS API
 * @param {string} phoneNumber - The phone number to send to
 * @param {string} message - The message to send
 * @returns {Promise<Object>} - The result of the SMS send operation
 */
async function sendSms(phoneNumber, message) {
  try {
    // Get Vapi API key
    const vapiApiKey = process.env.VAPI_TOKEN || process.env.VAPI_SECRET_KEY;

    if (!vapiApiKey) {
      throw new Error('Vapi API key not configured');
    }

    // Send SMS using Vapi's SMS API
    const response = await fetch('https://api.vapi.ai/sms', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${vapiApiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        to: phoneNumber,
        message
      })
    });

    if (!response.ok) {
      throw new Error(`SMS API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error sending SMS:', error);
    throw error;
  }
}

/**
 * Create a consultation record from call data
 * @param {Object} callData - The call data from Vapi
 * @param {string} attorneyId - The attorney ID
 * @returns {Promise<Object>} - The result of the operation
 */
async function createConsultationRecord(callData, attorneyId) {
  try {
    const supabase = await getSupabaseClient();
    if (!supabase) {
      console.warn('Supabase not configured, skipping consultation creation');
      return {
        success: true,
        data: { mock: true, message: 'Consultation would be created in Supabase' }
      };
    }

    // Extract client information from tool executions
    const clientInfo = extractClientInfo(callData);

    // Create consultation record
    const consultationRecord = {
      attorney_id: attorneyId,
      client_name: clientInfo.name || 'Anonymous Client',
      client_email: clientInfo.email,
      client_phone: callData.customer?.phone_number,
      summary: generateSummary(callData),
      transcript: generateTranscript(callData),
      duration: callData.duration,
      practice_area: clientInfo.practice_area,
      location: clientInfo.location,
      location_data: clientInfo.location_data,
      metadata: {
        call_id: callData.id,
        assistant_id: callData.assistant_id,
        ...clientInfo.metadata
      },
      status: 'new'
    };

    // Store in Supabase
    const { data, error } = await supabase
      .from('consultations')
      .insert(consultationRecord)
      .select();

    if (error) {
      throw error;
    }

    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Error creating consultation record:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Extract client information from tool executions
 * @param {Object} callData - The call data from Vapi
 * @returns {Object} - The extracted client information
 */
function extractClientInfo(callData) {
  const clientInfo = {
    name: '',
    email: '',
    practice_area: '',
    location: '',
    location_data: {},
    metadata: {}
  };

  // Extract information from tool executions
  if (callData.tool_executions && Array.isArray(callData.tool_executions)) {
    for (const toolExecution of callData.tool_executions) {
      // Check for LIVE_DOSSIER tool
      if (toolExecution.tool_name === 'LIVE_DOSSIER' && toolExecution.input) {
        try {
          const input = typeof toolExecution.input === 'string'
            ? JSON.parse(toolExecution.input)
            : toolExecution.input;

          // Extract client name
          if (input.client_name) {
            clientInfo.name = input.client_name;
          }

          // Extract client email
          if (input.email) {
            clientInfo.email = input.email;
          }

          // Extract practice area
          if (input.practice_area) {
            clientInfo.practice_area = input.practice_area;
          }

          // Extract location
          if (input.location) {
            clientInfo.location = input.location;
          }

          // Extract location data
          if (input.location_data) {
            clientInfo.location_data = input.location_data;
          }

          // Add any other fields to metadata
          for (const [key, value] of Object.entries(input)) {
            if (!['client_name', 'email', 'practice_area', 'location', 'location_data'].includes(key)) {
              clientInfo.metadata[key] = value;
            }
          }
        } catch (error) {
          console.error('Error parsing LIVE_DOSSIER input:', error);
        }
      }
    }
  }

  return clientInfo;
}

/**
 * Generate a summary from call data
 * @param {Object} callData - The call data from Vapi
 * @returns {string} - The generated summary
 */
function generateSummary(callData) {
  // For now, just return a simple summary
  // In the future, this could use an LLM to generate a more detailed summary
  return `Call with ${callData.customer?.phone_number || 'unknown'} on ${new Date(callData.start_time).toLocaleString()}. Duration: ${Math.round(callData.duration / 60)} minutes.`;
}

/**
 * Generate a transcript from call data
 * @param {Object} callData - The call data from Vapi
 * @returns {string} - The generated transcript
 */
function generateTranscript(callData) {
  if (!callData.transcripts || !Array.isArray(callData.transcripts)) {
    return '';
  }

  // Format transcripts into a readable string
  return callData.transcripts
    .map(transcript => {
      const speaker = transcript.role === 'assistant' ? 'Assistant' : 'Client';
      const timestamp = new Date(transcript.timestamp).toLocaleTimeString();
      return `[${timestamp}] ${speaker}: ${transcript.text}`;
    })
    .join('\n\n');
}

/**
 * Verify webhook signature from Vapi
 * @param {string} payload - The raw request payload
 * @param {string} signature - The signature from Vapi
 * @param {string} secret - The webhook secret
 * @returns {boolean} - Whether the signature is valid
 */
async function verifyWebhookSignature(payload, signature, secret) {
  if (!signature || !secret) {
    return false;
  }

  try {
    // Import crypto dynamically to avoid issues in serverless
    const crypto = await import('crypto');

    // Vapi sends signatures in the format "sha256=<hash>"
    const expectedSignature = crypto.default
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('hex');

    const receivedSignature = signature.replace('sha256=', '');

    // Simple string comparison for now (can enhance with timing-safe comparison later)
    return expectedSignature === receivedSignature;
  } catch (error) {
    console.error('Error verifying webhook signature:', error);
    return false;
  }
}

/**
 * Handle webhook request
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 */
export default async function handler(req, res) {
  try {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS, GET');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, X-Vapi-Signature');

    // Handle OPTIONS request for CORS
    if (req.method === 'OPTIONS') {
      return res.status(200).json({ message: 'CORS preflight successful' });
    }

    // Handle GET request for testing
    if (req.method === 'GET') {
      return res.status(200).json({
        message: 'Vapi webhook endpoint is working',
        timestamp: new Date().toISOString(),
        method: 'GET'
      });
    }

    // Only allow POST requests for actual webhooks
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }
    // Get webhook secret from environment
    const webhookSecret = process.env.VAPI_WEBHOOK_SECRET;

    // Verify webhook signature if secret is configured
    if (webhookSecret) {
      const signature = req.headers['x-vapi-signature'] || req.headers['x-signature'];
      const rawBody = JSON.stringify(req.body);

      const isValidSignature = await verifyWebhookSignature(rawBody, signature, webhookSecret);
      if (!isValidSignature) {
        console.error('Invalid webhook signature');
        return res.status(401).json({ error: 'Unauthorized - Invalid signature' });
      }

      console.log('✅ Webhook signature verified');
    } else {
      console.warn('⚠️  Webhook secret not configured - skipping signature verification');
    }

    // Get call data from request body
    const callData = req.body;

    // Validate call data
    if (!callData || !callData.id || !callData.assistant_id) {
      return res.status(400).json({ error: 'Invalid call data' });
    }

    console.log('📞 Processing webhook for call:', callData.id, 'status:', callData.status);

    // Store call data in Supabase
    const result = await storeCallData(callData);

    // Return result
    if (result.success) {
      return res.status(200).json({ success: true, message: 'Call data stored successfully' });
    } else {
      return res.status(500).json({ success: false, error: result.error });
    }
  } catch (error) {
    console.error('Error handling webhook:', error);
    return res.status(500).json({
      error: 'Internal server error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
