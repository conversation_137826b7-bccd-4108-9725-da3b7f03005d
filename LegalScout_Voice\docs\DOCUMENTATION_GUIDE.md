# LegalScout Documentation Guide

## Overview

This guide provides an overview of the LegalScout documentation structure and helps you navigate the various documentation files. The documentation is organized into several categories to make it easier to find the information you need.

## Documentation Structure

The LegalScout documentation is organized into the following categories:

1. **Project Overview**
   - High-level information about the project, its goals, and architecture

2. **Technical Architecture**
   - Detailed technical information about the system architecture and components

3. **Development Workflow**
   - Guidelines for development processes, branching, testing, and deployment

4. **Feature Documentation**
   - Detailed documentation for specific features and components

5. **Project Status and Tasks**
   - Current status, roadmap, and task lists

6. **User Experience**
   - Information about user flows, interfaces, and design patterns

7. **Integration Guides**
   - Documentation for integrating with external services and APIs

## Core Documentation Files

### Project Overview
- **[PROJECT_OVERVIEW_UPDATED.md](PROJECT_OVERVIEW_UPDATED.md)** - Comprehensive overview of the project, including vision, architecture, and key features
- **[README.md](../README.md)** - Quick start guide and basic project information

### Technical Architecture
- **[TECHNICAL_ARCHITECTURE.md](TECHNICAL_ARCHITECTURE.md)** - Detailed technical architecture documentation
- **[TECH_STACK.md](TECH_STACK.md)** - Information about the technology stack used in the project

### Development Workflow
- **[DEVELOPMENT_WORKFLOW.md](DEVELOPMENT_WORKFLOW.md)** - Guidelines for the development process, including branching, testing, and deployment

### Feature Documentation
- **[ATTORNEY_DASHBOARD.md](ATTORNEY_DASHBOARD.md)** - Documentation for the attorney dashboard feature
- **[VAPI_INTEGRATION.md](VAPI_INTEGRATION.md)** - Documentation for the Vapi voice AI integration
- **[SUBDOMAIN_SYSTEM_UPDATED.md](SUBDOMAIN_SYSTEM_UPDATED.md)** - Updated documentation for the subdomain system
- **[CUSTOM_FIELDS.md](CUSTOM_FIELDS.md)** - Documentation for the custom fields feature
- **[MAP_VISUALIZATION.md](MAP_VISUALIZATION.md)** - Documentation for the map visualization system

### Project Status and Tasks
- **[PROJECT_STATUS_AND_ROADMAP.md](PROJECT_STATUS_AND_ROADMAP.md)** - Current status and roadmap for the project
- **[TASK_LIST_CONSOLIDATED.md](TASK_LIST_CONSOLIDATED.md)** - Consolidated task list with current status and priorities
- **[todo.md](../todo.md)** - Original task list (being replaced by TASK_LIST_CONSOLIDATED.md)

### User Experience
- **[APP_FLOW.md](APP_FLOW.md)** - Documentation of application flow and user journeys
- **[user_experience_flow.md](user_experience_flow.md)** - Information about user experience design and flows

### Integration Guides
- **[SUPABASE_SETUP.md](SUPABASE_SETUP.md)** - Guide for setting up and configuring Supabase
- **[VAPI_MCP_INTEGRATION.md](VAPI_MCP_INTEGRATION.md)** - Guide for integrating with Vapi using the Model Context Protocol
- **[GOOGLE_OAUTH_SETUP.md](GOOGLE_OAUTH_SETUP.md)** - Guide for setting up Google OAuth authentication

## Legacy Documentation

The following files are being replaced by newer, more comprehensive documentation:

- **[SUBDOMAIN_SYSTEM.md](SUBDOMAIN_SYSTEM.md)** - Original subdomain system documentation (replaced by SUBDOMAIN_SYSTEM_UPDATED.md)
- **[project_brief.md](project_brief.md)** - Original project brief (incorporated into PROJECT_OVERVIEW_UPDATED.md)
- **[memory.md](../memory.md)** - Project memory and implementation details (being consolidated into feature-specific documentation)

## Documentation Conventions

### File Naming
- **ALL_CAPS.md** - Core documentation files
- **camelCase.md** - Supplementary or legacy documentation files
- **kebab-case.md** - Specific guides or tutorials

### Content Structure
- Each documentation file should start with a clear title and overview
- Use consistent heading levels (# for title, ## for main sections, ### for subsections)
- Include code examples where appropriate
- Use tables for structured data
- Include diagrams for complex concepts

### Updating Documentation
- When updating documentation, create a new file with "_UPDATED" suffix if making significant changes
- Update the DOCUMENTATION_GUIDE.md file to reflect any changes to the documentation structure
- Mark legacy documentation as deprecated but keep it for reference until fully migrated

## How to Use This Documentation

### For New Team Members
1. Start with [PROJECT_OVERVIEW_UPDATED.md](PROJECT_OVERVIEW_UPDATED.md) to understand the project
2. Review [TECHNICAL_ARCHITECTURE.md](TECHNICAL_ARCHITECTURE.md) for technical details
3. Read [DEVELOPMENT_WORKFLOW.md](DEVELOPMENT_WORKFLOW.md) to understand the development process
4. Explore feature-specific documentation based on your area of focus

### For Existing Team Members
1. Refer to [TASK_LIST_CONSOLIDATED.md](TASK_LIST_CONSOLIDATED.md) for current tasks and priorities
2. Use feature-specific documentation as reference when working on those features
3. Consult integration guides when working with external services

### For Project Managers
1. Review [PROJECT_STATUS_AND_ROADMAP.md](PROJECT_STATUS_AND_ROADMAP.md) for project status and planning
2. Use [TASK_LIST_CONSOLIDATED.md](TASK_LIST_CONSOLIDATED.md) for task tracking and prioritization

## Contributing to Documentation

When contributing to the documentation:

1. Follow the established conventions for file naming and content structure
2. Update existing documentation rather than creating new files when possible
3. If creating new documentation, add it to the appropriate category in this guide
4. Keep documentation up-to-date with code changes
5. Include examples, diagrams, and screenshots where helpful
6. Cross-reference related documentation

## Documentation Roadmap

The following documentation improvements are planned:

1. Create comprehensive API documentation
2. Develop user manuals and help guides
3. Document database schema and relationships in detail
4. Create attorney portal usage guide
5. Update technical documentation with latest architecture changes
6. Create deployment and operations guide
7. Develop troubleshooting and FAQ documentation

## Conclusion

This documentation guide provides a structure for navigating and maintaining the LegalScout documentation. By following this guide, team members can easily find the information they need and contribute to keeping the documentation up-to-date and comprehensive.
