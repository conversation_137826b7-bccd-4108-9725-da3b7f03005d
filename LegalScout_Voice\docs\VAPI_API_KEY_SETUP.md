# Vapi API Key Setup Guide

This document provides instructions on how to properly set up the Vapi API key for the LegalScout application.

## The Issue

The application was previously using an assistant ID (`310f0d43-27c2-47a5-a76d-e55171d024f7`) as the API key in the environment variables. This caused authentication failures when trying to connect to the Vapi API.

## Getting a Valid Vapi API Key

To get a valid Vapi API key:

1. Create an account at [https://dashboard.vapi.ai](https://dashboard.vapi.ai) if you don't already have one
2. Log in to your Vapi account
3. Navigate to Settings > API Keys
4. Create a new API key (give it a name like "LegalScout Development")
5. Copy the API key (you won't be able to see it again)

## Setting Up the API Key

Once you have a valid API key, you need to update your environment variables:

1. Open the `.env.development` file in the root of the project
2. Replace the placeholder value for `VITE_VAPI_PUBLIC_KEY` with your actual API key:

```
VITE_VAPI_PUBLIC_KEY=your_actual_api_key_here
```

3. Also update the `.env` file with the same API key:

```
VITE_VAPI_PUBLIC_KEY=your_actual_api_key_here
VAPI_PUBLIC_KEY=your_actual_api_key_here
VAPI_SECRET_KEY=your_actual_api_key_here
VAPI_TOKEN=your_actual_api_key_here
```

4. Save the files

## Testing the API Key

After updating the environment variables, you can test the API key:

1. Restart the development server:
   ```
   npm run dev
   ```

2. Open the browser console and run the Vapi diagnostics:
   ```javascript
   VapiDebug.diagnose()
   ```

3. Check the network logs to see if the API calls are successful:
   ```javascript
   VapiMcpDebugger.getNetworkLogs()
   ```

## Understanding Vapi Components

The Vapi integration consists of several components:

1. **API Key**: Used for authentication with the Vapi API
2. **Assistant ID**: Identifies a specific assistant in the Vapi system
3. **MCP Server**: Model Context Protocol server for interacting with Vapi
4. **Direct API**: Direct calls to the Vapi API endpoints

The application uses the API key to authenticate with the Vapi API, and then uses the assistant ID to identify which assistant to use for voice interactions.

## Default Assistant ID

The application uses a default assistant ID (`e3fff1dd-2e82-4cce-ac6c-8c3271eb0865`) as defined in `src/constants/vapiConstants.js`. This ID is used as a fallback when creating new assistants.

## Troubleshooting

If you continue to have issues with the Vapi API key:

1. **Check API Key Format**: Make sure the API key is in the correct format (typically a long string of letters and numbers)
2. **Verify Account Status**: Make sure your Vapi account is active and in good standing
3. **Check API Endpoints**: The application is configured to use the following API endpoints:
   - `https://api.vapi.ai/assistant`
   - `https://api.vapi.ai/call`
   - `https://api.vapi.ai/phone-number`
   - `https://api.vapi.ai/tool`
4. **Check Network Logs**: Look for specific error messages in the network logs
5. **Try Mock Mode**: If you're unable to get a valid API key, you can temporarily use mock mode by setting `forceMock` to `true` in the `initialize` method call in `src/utils/initAttorneyProfileManager.js`

## References

- [Vapi Documentation](https://docs.vapi.ai)
- [Vapi Dashboard](https://dashboard.vapi.ai)
- [VAPI_DEBUGGING.md](./VAPI_DEBUGGING.md)
- [VAPI_MOCK_MODE.md](./VAPI_MOCK_MODE.md)
