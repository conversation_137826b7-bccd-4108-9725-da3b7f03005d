/**
 * Agent-specific "Configure Your Assistant" button
 * This script ensures the button appears ONLY on the agent page
 */

(function() {
  console.log('[AgentConfigureButton] Script loaded');

  // Function to create the Configure Your Assistant button
  function createConfigureButton() {
    // ONLY create the button on the agent page
    const isAgentPage = window.location.pathname.includes('/agent');

    if (!isAgentPage) {
      console.log('[AgentConfigureButton] Not on agent page, skipping button creation');
      return;
    }

    // Check if button already exists
    if (document.getElementById('agent-configure-button')) {
      console.log('[AgentConfigureButton] Button already exists');
      return;
    }

    console.log('[AgentConfigureButton] Creating button on agent page');

    // Create the button
    const configureButton = document.createElement('button');
    configureButton.id = 'agent-configure-button';
    configureButton.textContent = 'Configure Your Assistant';
    configureButton.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      background-color: #D85722;
      color: white;
      border: none;
      border-radius: 30px;
      padding: 15px 40px;
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 6px 15px rgba(216, 87, 34, 0.4);
      transition: all 0.3s ease;
      opacity: 0.9;
    `;

    // Add hover effect
    configureButton.addEventListener('mouseover', function() {
      this.style.transform = 'scale(1.05)';
      this.style.boxShadow = '0 8px 20px rgba(216, 87, 34, 0.5)';
    });

    configureButton.addEventListener('mouseout', function() {
      this.style.transform = 'scale(1)';
      this.style.boxShadow = '0 6px 15px rgba(216, 87, 34, 0.4)';
    });

    // Add click handler
    configureButton.addEventListener('click', function() {
      console.log('[AgentConfigureButton] Button clicked');

      // Try multiple methods to show the auth overlay
      if (typeof window.setShowAuthOverlay === 'function') {
        console.log('[AgentConfigureButton] Using window.setShowAuthOverlay');
        window.setShowAuthOverlay(true);
      } else if (typeof window.handleGetStarted === 'function') {
        console.log('[AgentConfigureButton] Using window.handleGetStarted');
        window.handleGetStarted();
      } else {
        console.log('[AgentConfigureButton] Redirecting to /login');
        window.location.href = '/login';
      }
    });

    // Add to the document
    document.body.appendChild(configureButton);
    console.log('[AgentConfigureButton] Button created successfully');
  }

  // Function to check URL changes and create/remove button as needed
  function handleUrlChange() {
    const isAgentPage = window.location.pathname.includes('/agent');
    const existingButton = document.getElementById('agent-configure-button');

    if (isAgentPage && !existingButton) {
      createConfigureButton();
    } else if (!isAgentPage && existingButton) {
      existingButton.remove();
      console.log('[AgentConfigureButton] Button removed - not on agent page');
    }
  }

  // Run when the DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      createConfigureButton();
    });
  } else {
    createConfigureButton();
  }

  // Listen for URL changes (for single-page app navigation)
  let lastUrl = window.location.href;

  // DISABLED: Check for URL changes every 500ms to prevent excessive polling
  // setInterval(() => {
  //   const currentUrl = window.location.href;
  //   if (currentUrl !== lastUrl) {
  //     lastUrl = currentUrl;
  //     console.log('[AgentConfigureButton] URL changed, checking if button needed');
  //     handleUrlChange();
  //   }
  // }, 500);

  console.log('[AgentConfigureButton] URL polling disabled to prevent performance issues');

  // Also listen for popstate events (browser back/forward)
  window.addEventListener('popstate', handleUrlChange);
})();
