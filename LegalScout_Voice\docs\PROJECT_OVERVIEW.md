# LegalScout Project Overview

## Project Description

LegalScout is a voice-guided legal consultation platform that connects users with attorneys based on their legal needs. The platform uses Vapi.ai for voice interactions and provides interactive map visualizations for attorney recommendations. The system includes a customizable attorney dashboard that allows legal professionals to configure their AI assistant, manage consultations, and integrate with external services.

## Core Architecture

### Frontend Framework
- **React 18.2.0** with functional components and hooks
- **Vite 4.4.5** for development and build processes
- **React Router v7** for navigation
- **TailwindCSS** for styling

### Backend Services
- **Supabase** for database and authentication
- **Vapi.ai** for voice AI integration
- **Model Context Protocol (MCP)** for service integrations

### Key Integrations
- **Vapi MCP Server** for programmatic control of voice assistants
- **Gmail OAuth** for authentication
- **Supabase Storage** for file uploads (logos, voice samples)
- **Vercel** for deployment

## Application Structure

### Main Components

1. **Home Page**
   - Landing page with voice assistant button
   - Animated background
   - Navigation to other sections

2. **Attorney Dashboard**
   - Profile management
   - AI agent configuration
   - Custom fields setup
   - Automation rules
   - Consultation history
   - Integration settings

3. **Voice Interface**
   - Real-time voice interaction
   - Text transcription
   - Case dossier generation
   - Attorney recommendations

4. **Map Visualization**
   - Interactive map showing attorney locations
   - 3D globe visualization option
   - Attorney markers with information popups

5. **Subdomain System**
   - Custom attorney subdomains (e.g., attorneyname.legalscout.net)
   - Branded experience for each attorney
   - Configuration loaded from database

## Database Schema

### Main Tables

1. **attorneys**
   - Basic information (name, firm, contact)
   - Branding (logo, colors)
   - Voice configuration
   - AI assistant settings
   - Subdomain configuration

2. **call_records**
   - Consultation history
   - Timestamps
   - Client information
   - Assistant ID reference

3. **custom_fields**
   - Attorney-specific data collection fields
   - Field types and validation rules
   - Display order

## Voice AI Integration

### Vapi.ai Integration
- Custom assistant creation for each attorney
- Voice selection and customization
- Real-time transcription
- Conversation management
- Post-call summaries

### MCP Integration
- Programmatic control of Vapi assistants
- Call creation and management
- Assistant configuration
- Voice customization

## User Flows

### Client Flow
1. User visits attorney subdomain or main site
2. Clicks "Start Consultation" button
3. Engages with voice assistant
4. Provides information about legal needs
5. Receives attorney recommendations
6. Views consultation summary
7. Connects with recommended attorney

### Attorney Flow
1. Attorney signs up/logs in
2. Configures profile and branding
3. Sets up AI assistant parameters
4. Customizes data collection fields
5. Reviews consultation history
6. Manages client communications

## Development Environment

### Local Development
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

### Environment Variables
- `VITE_SUPABASE_URL` - Supabase project URL
- `VITE_SUPABASE_KEY` - Supabase anon key
- `VITE_VAPI_PUBLIC_KEY` - Vapi API key
- `VAPI_TOKEN` - Vapi MCP server token

## Deployment

The application is deployed on Vercel with the following configuration:
- Production URL: legalscout.ai
- Custom domain setup for attorney subdomains
- Environment variables configured in Vercel dashboard
- Automatic deployments from GitHub

## Current Status and Roadmap

### Implemented Features
- Basic voice interaction with Vapi
- Attorney dashboard with profile management
- AI assistant configuration
- Custom subdomain system
- Map visualization
- Authentication with Gmail OAuth

### In Progress
- Enhanced voice customization
- Improved consultation history
- Custom fields implementation
- Integration with external services

### Planned Features
- Multi-party calls with attorney liaison
- Document upload and analysis
- Advanced search filters
- Payment processing
- Calendar integration

## Technical Challenges and Solutions

### Subdomain Handling
- Custom subdomain system using Vercel rewrites
- Attorney configuration loaded from database
- Testing utility for local development

### Voice Integration
- Vapi assistant creation and management
- Voice customization with PlayHT
- Real-time transcription and response

### Authentication
- Gmail OAuth integration
- Supabase authentication
- Row-level security policies

## Documentation References

For more detailed information, refer to:
- `docs/TECH_STACK.md` - Detailed technology stack
- `docs/FRONTEND_GUIDELINES.md` - Frontend development guidelines
- `docs/BACKEND_STRUCTURE.md` - Backend structure and database schema
- `docs/APP_FLOW.md` - Application flow and user journeys
- `docs/MAP_VISUALIZATION.md` - Map visualization system
- `memory.md` - Project memory and implementation details
- `todo.md` - Current task list and progress
