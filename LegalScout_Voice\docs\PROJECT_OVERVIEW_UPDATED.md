# LegalScout Project Overview

## Project Vision

LegalScout aims to democratize access to legal services by providing an intelligent voice-guided platform that helps users navigate legal issues, find appropriate attorneys, and visualize relevant information through interactive maps and dossiers. The system serves as both an initial legal consultant and a matchmaker between clients and attorneys, enhancing the legal support experience through modern technology.

## Project Description

LegalScout is a voice-guided legal consultation platform that connects users with attorneys based on their legal needs. The platform uses Vapi.ai for voice interactions and provides interactive map visualizations for attorney recommendations. The system includes a customizable attorney dashboard that allows legal professionals to configure their AI assistant, manage consultations, and integrate with external services.

## Target Users

- Individuals with urgent legal questions
- People searching for attorneys in specialized fields
- Clients needing guidance through complex legal jurisdictions
- Legal service providers looking to connect with potential clients

## Core Architecture

### Frontend Framework
- **React 18.2.0** with functional components and hooks
- **Vite 4.4.5** for development and build processes
- **React Router v7** for navigation
- **TailwindCSS** for styling

### Backend Services
- **Supabase** for database and authentication
- **Vapi.ai** for voice AI integration
- **Model Context Protocol (MCP)** for service integrations

### Key Integrations
- **Vapi MCP Server** for programmatic control of voice assistants
- **Gmail OAuth** for authentication
- **Supabase Storage** for file uploads (logos, voice samples)
- **Vercel** for deployment
- **Apify** for external attorney searches
- **Leaflet.js** for mapping capabilities
- **Google Maps** for location data and geocoding

## Application Structure

### Main Components

1. **Home Page**
   - Landing page with voice assistant button
   - Animated background
   - Navigation to other sections

2. **Attorney Dashboard**
   - Profile management
   - AI agent configuration
   - Custom fields setup
   - Automation rules
   - Consultation history
   - Integration settings

3. **Voice Interface**
   - Real-time voice interaction
   - Text transcription
   - Case dossier generation
   - Attorney recommendations

4. **Map Visualization**
   - Interactive map showing attorney locations
   - 3D globe visualization option
   - Attorney markers with information popups

5. **Subdomain System**
   - Custom attorney subdomains (e.g., attorneyname.legalscout.net)
   - Branded experience for each attorney
   - Configuration loaded from database

## Database Schema

### Main Tables

1. **attorneys**
   - Basic information (name, firm, contact)
   - Branding (logo, colors)
   - Voice configuration
   - AI assistant settings
   - Subdomain configuration

2. **call_records**
   - Consultation history
   - Timestamps
   - Client information
   - Assistant ID reference

3. **custom_fields**
   - Attorney-specific data collection fields
   - Field types and validation rules
   - Display order

## Voice AI Integration

### Vapi.ai Integration
- Custom assistant creation for each attorney
- Voice selection and customization
- Real-time transcription
- Conversation management
- Post-call summaries

### MCP Integration
- Programmatic control of Vapi assistants
- Call creation and management
- Assistant configuration
- Voice customization

## User Flows

### Client Flow
1. User visits attorney subdomain or main site
2. Clicks "Start Consultation" button
3. Engages with voice assistant
4. Provides information about legal needs
5. Receives attorney recommendations
6. Views consultation summary
7. Connects with recommended attorney

### Attorney Flow
1. Attorney signs up/logs in
2. Configures profile and branding
3. Sets up AI assistant parameters
4. Customizes data collection fields
5. Reviews consultation history
6. Manages client communications

## Development Environment

### Quick Start

1. **Install Dependencies**
   ```
   npm install
   ```

2. **Add Your Vapi Public Key**
   Open `src/App.jsx` and replace the placeholder public key with your own.

3. **Start Development Server**
   ```
   npm run dev
   ```

### Environment Variables
- `VITE_SUPABASE_URL` - Supabase project URL
- `VITE_SUPABASE_KEY` - Supabase anon key
- `VITE_VAPI_PUBLIC_KEY` - Vapi API key
- `VAPI_TOKEN` - Vapi MCP server token

## Deployment

The application is deployed on Vercel with the following configuration:
- Production URL: legalscout.ai
- Custom domain setup for attorney subdomains
- Environment variables configured in Vercel dashboard
- Automatic deployments from GitHub

### Vercel Deployment Steps

1. Ensure your project is linked to Vercel: `npx vercel link`
2. Check environment variables: `npx vercel env ls`
3. Push changes to GitHub to trigger automatic deployment
4. Monitor deployment status in the Vercel dashboard

## Recent Updates

- Improved deployment process with Vercel integration
- Implemented attorney dashboard with configuration options
- Created tabbed interface with Auto-Configure and Manual Setup options
- Added color pickers for primary and secondary brand colors
- Implemented responsive preview with embedded chat widget
- Fixed visibility issues with Start Consultation button in preview
- Added two-step configuration flow (setup → preview)
- Implemented preview controls bar with active configuration display
- Enhanced subdomain testing UI with collapsible panel and toggle button
- Improved theme support for subdomain testing interface
- Fixed logo display issues in the Navbar component
- Optimized navbar height for better UI proportions
- Adjusted logo size to fit properly in the navbar
- Enhanced error handling for API connection failures
- Improved subdomain testing capabilities for local development

## Current Status and Roadmap

### Implemented Features
- Basic voice interaction with Vapi
- Attorney dashboard with profile management
- AI assistant configuration
- Custom subdomain system
- Map visualization
- Authentication with Gmail OAuth

### In Progress
- Enhanced voice customization
- Improved consultation history
- Custom fields implementation
- Integration with external services

### Planned Features
- Multi-party calls with attorney liaison
- Document upload and analysis
- Advanced search filters
- Payment processing
- Calendar integration

## Success Metrics

- **User Engagement**: Average consultation duration > 5 minutes
- **Conversion Rate**: >30% of users view attorney matches
- **Attorney Matching**: >15% of users contact recommended attorneys
- **Satisfaction**: >85% positive feedback on voice experience
- **Retention**: >40% of users return for additional consultations

## Technical Challenges and Solutions

### Subdomain Handling
- Custom subdomain system using Vercel rewrites
- Attorney configuration loaded from database
- Testing utility for local development

### Voice Integration
- Vapi assistant creation and management
- Voice customization with PlayHT
- Real-time transcription and response

### Authentication
- Gmail OAuth integration
- Supabase authentication
- Row-level security policies

## Market Differentiation

Unlike traditional legal directories or chatbots, LegalScout combines voice interaction, visual representation, and attorney matching in a single platform. The voice-guided map feature creates an intuitive way to explore legal information that bridges the gap between text-based research and in-person consultation.

## Regulatory Considerations

- All consultations include appropriate disclaimers about not constituting legal advice
- Attorney data is sourced ethically and in compliance with bar association rules
- User data is handled in accordance with privacy regulations
- Clear distinction between partner attorneys and externally sourced listings

## Documentation References

For more detailed information, refer to:
- `docs/TECHNICAL_ARCHITECTURE.md` - Detailed technical architecture
- `docs/DEVELOPMENT_WORKFLOW.md` - Development workflow guidelines
- `docs/ATTORNEY_DASHBOARD.md` - Attorney dashboard documentation
- `docs/VAPI_INTEGRATION.md` - Vapi integration details
- `docs/SUBDOMAIN_SYSTEM_UPDATED.md` - Subdomain system documentation
- `docs/CUSTOM_FIELDS.md` - Custom fields feature documentation
- `docs/MAP_VISUALIZATION.md` - Map visualization system
- `docs/PROJECT_STATUS_AND_ROADMAP.md` - Current status and roadmap
- `memory.md` - Project memory and implementation details
- `todo.md` - Current task list and progress
