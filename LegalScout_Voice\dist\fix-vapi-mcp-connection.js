/**
 * Fix for Vapi MCP Connection Issues
 * 
 * This script adds retry mechanisms and better error handling for Vapi MCP connections.
 * It patches the vapiMcpService to handle connection failures gracefully.
 */

(function() {
  console.log('[FixVapiMcpConnection] Starting fix...');

  // Wait for the vapiMcpService to be available
  const waitForVapiMcpService = () => {
    if (window.vapiMcpService) {
      applyFix();
    } else {
      console.log('[FixVapiMcpConnection] Waiting for vapiMcpService...');
      setTimeout(waitForVapiMcpService, 100);
    }
  };

  // Apply the fix to the vapiMcpService
  const applyFix = () => {
    console.log('[FixVapiMcpConnection] Applying fix to vapiMcpService...');

    // Store the original connect method
    const originalConnect = window.vapiMcpService.connect;

    // Override the connect method to add retry logic
    window.vapiMcpService.connect = async function(apiKey, forceDirect = false) {
      console.log('[FixVapiMcpConnection] Enhanced connect called with forceDirect:', forceDirect);

      // Add retry logic
      const maxRetries = 3;
      let retryCount = 0;
      let lastError = null;

      while (retryCount < maxRetries) {
        try {
          // Try to connect using the original method
          const result = await originalConnect.call(this, apiKey, forceDirect);
          
          if (result) {
            console.log('[FixVapiMcpConnection] Connection successful after', retryCount, 'retries');
            return result;
          } else {
            // If result is falsy but no error was thrown, try again
            console.warn('[FixVapiMcpConnection] Connection returned falsy result, retrying...');
            retryCount++;
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // Exponential backoff
          }
        } catch (error) {
          lastError = error;
          console.warn(`[FixVapiMcpConnection] Connection attempt ${retryCount + 1} failed:`, error.message);
          retryCount++;
          
          // Wait before retrying with exponential backoff
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
        }
      }

      // If we've exhausted all retries, use mock mode if available
      console.error('[FixVapiMcpConnection] All connection attempts failed after', maxRetries, 'retries');
      
      if (window.vapiMockMcpService) {
        console.log('[FixVapiMcpConnection] Falling back to mock MCP service');
        try {
          await window.vapiMockMcpService.connect(apiKey);
          // Replace the real service with the mock service
          Object.keys(window.vapiMockMcpService).forEach(key => {
            if (typeof window.vapiMockMcpService[key] === 'function' && key !== 'connect') {
              const originalMethod = window.vapiMcpService[key];
              window.vapiMcpService[key] = async function(...args) {
                try {
                  return await window.vapiMockMcpService[key](...args);
                } catch (mockError) {
                  console.error(`[FixVapiMcpConnection] Mock method ${key} failed:`, mockError);
                  // Try original method as last resort
                  return originalMethod.apply(this, args);
                }
              };
            }
          });
          
          // Set connected state
          this.connected = true;
          return true;
        } catch (mockError) {
          console.error('[FixVapiMcpConnection] Failed to initialize mock service:', mockError);
        }
      }
      
      // If all else fails, throw the last error
      if (lastError) {
        throw lastError;
      } else {
        throw new Error('Failed to connect to Vapi MCP Server after multiple attempts');
      }
    };

    // Patch the createAssistant method to handle errors better
    const originalCreateAssistant = window.vapiMcpService.createAssistant;
    window.vapiMcpService.createAssistant = async function(assistantData) {
      try {
        return await originalCreateAssistant.call(this, assistantData);
      } catch (error) {
        console.error('[FixVapiMcpConnection] Error creating assistant:', error);
        
        // Return a mock assistant if we can't create a real one
        return {
          id: 'mock-' + Date.now(),
          name: assistantData.name || 'Mock Assistant',
          instructions: assistantData.instructions || 'Mock instructions',
          firstMessage: assistantData.firstMessage || 'Hello, I am a mock assistant.',
          isMock: true,
          createdAt: new Date().toISOString()
        };
      }
    };

    console.log('[FixVapiMcpConnection] Fix applied successfully');
  };

  // Start the fix process
  waitForVapiMcpService();
})();
