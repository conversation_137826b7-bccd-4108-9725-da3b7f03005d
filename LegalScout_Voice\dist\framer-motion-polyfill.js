/**
 * Framer Motion Polyfill
 * 
 * This script fixes the "Cannot read properties of undefined (reading 'createContext')" error
 * by ensuring React is available globally before Framer Motion tries to use it.
 */

(function() {
  // Create a placeholder React object if it doesn't exist
  if (typeof window.React === 'undefined') {
    console.log('[Polyfill] Creating React placeholder');
    window.React = {};
  }

  // Ensure createContext exists
  if (typeof window.React.createContext === 'undefined') {
    console.log('[Polyfill] Adding createContext placeholder');
    window.React.createContext = function() {
      return {
        Provider: function() {},
        Consumer: function() {}
      };
    };
  }

  // Add other essential React methods that Framer Motion might need
  const reactMethods = [
    'useState', 'useEffect', 'useLayoutEffect', 'useRef', 
    'useCallback', 'useMemo', 'useContext', 'forwardRef'
  ];

  reactMethods.forEach(method => {
    if (typeof window.React[method] === 'undefined') {
      console.log(`[Polyfill] Adding ${method} placeholder`);
      window.React[method] = function() { 
        return arguments[0] instanceof Function ? arguments[0]() : null;
      };
    }
  });

  console.log('[Polyfill] Framer Motion polyfill applied');
})();
