<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LegalScout</title>
    <link rel="icon" type="image/png" href="/favicon.png">
    
    <!-- Immediate error handler for S(...).catch is not a function -->
    <script>
        // Global error handler specifically for the S(...).catch error
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
                console.log('[GlobalErrorHandler] Caught the specific error: ' + event.error.message);
                event.preventDefault();
                return false;
            }
        }, true);
    </script>
    
    <!-- Fix scripts - load these before the application -->
    <script src="/fix-promise-catch.js"></script>
    <script src="/fix-profile-editing.js"></script>
    <script src="/fix-api-errors.js"></script>
    <script src="/fix-attorney-id-v2.js"></script>
    <script src="/fix-profile-tab.js"></script>
    <script src="/fix-supabase-client.js"></script>
    <script src="/fix-react-context-timeout.js"></script>
    
    <!-- Preload key resources -->
    <link rel="preload" href="/PRIMARY CLEAR.png" as="image">
    
    <!-- Supabase client -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    
    <!-- Three.js and Globe visualization -->
    <script src="https://unpkg.com/three-globe@2.42.1/dist/three-globe.min.js"></script>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    
    <!-- Additional error handling -->
    <script>
        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error caught:', event.error);
            
            // Check if it's the specific error we're trying to fix
            if (event.error && event.error.message && event.error.message.includes('catch is not a function')) {
                console.log('Caught the Promise.catch error, preventing page crash');
                event.preventDefault();
            }
        });
        
        // Initialize Supabase if needed
        document.addEventListener('DOMContentLoaded', function() {
            // Check if Supabase is already initialized
            if (!window.supabase && typeof supabase !== 'undefined' && typeof supabase.createClient === 'function') {
                try {
                    // Supabase URL and key
                    const supabaseUrl = 'https://utopqxsvudgrtiwenlzl.supabase.co';
                    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU';
                    
                    console.log('Creating Supabase client manually');
                    window.supabase = supabase.createClient(supabaseUrl, supabaseKey);
                    console.log('Supabase client created successfully');
                } catch (error) {
                    console.error('Error initializing Supabase client:', error);
                }
            }
        });
        
        // Specific fix for S(...).catch is not a function
        (function() {
            console.log('[SCatchFix] Adding specific fix for S(...).catch');
            
            // Monitor for any function that might be the S function
            const originalCall = Function.prototype.call;
            Function.prototype.call = function(...args) {
                try {
                    // Check if this is the S function or similar
                    if (this && (this.name === 'S' || this.name === 'Qr')) {
                        console.log(`[SCatchFix] Intercepted call to ${this.name} function`);
                        
                        const result = originalCall.apply(this, args);
                        
                        // Ensure the result has a catch method
                        if (result && typeof result.then === 'function' && typeof result.catch !== 'function') {
                            console.log(`[SCatchFix] Adding catch method to ${this.name} function result`);
                            result.catch = function(onRejected) {
                                return Promise.resolve(result).catch(onRejected);
                            };
                        }
                        
                        return result;
                    }
                    
                    return originalCall.apply(this, args);
                } catch (error) {
                    console.error('[SCatchFix] Error in function call:', error);
                    // Return a safe value that has a catch method
                    return Promise.resolve(null);
                }
            };
            
            console.log('[SCatchFix] S(...).catch fix applied');
        })();
    </script>
    
    <!-- Application script will be injected here by the build process -->
</body>
</html>
