/**
 * Fix for validateAttorneyData Method
 * 
 * This script ensures that the validateAttorneyData method is always available
 * on the StandaloneAttorneyManager, even if there's a race condition during initialization.
 */

(function() {
  console.log('[FixValidateAttorneyData] Starting fix...');
  
  // Function to add the validateAttorneyData method to the StandaloneAttorneyManager
  function addValidateAttorneyDataMethod() {
    // Check if the StandaloneAttorneyManager is available
    if (!window.standaloneAttorneyManager) {
      console.warn('[FixValidateAttorneyData] StandaloneAttorneyManager not available yet, will retry');
      return false;
    }
    
    // Check if the validateAttorneyData method already exists
    if (typeof window.standaloneAttorneyManager.validateAttorneyData === 'function') {
      console.log('[FixValidateAttorneyData] validateAttorneyData method already exists');
      return true;
    }
    
    console.log('[FixValidateAttorneyData] Adding validateAttorneyData method');
    
    // Add the validateAttorneyData method
    window.standaloneAttorneyManager.validateAttorneyData = function(attorneyData) {
      console.log('[FixValidateAttorneyData] validateAttorneyData called');
      
      if (!attorneyData) {
        console.warn('[FixValidateAttorneyData] No attorney data provided');
        return this.createDefaultAttorney();
      }
      
      // Check if ID is valid
      if (!this.isValidUUID(attorneyData.id)) {
        console.warn('[FixValidateAttorneyData] Invalid attorney ID, creating new one');
        return this.createDefaultAttorney();
      }
      
      // Get default voice from Vapi config if available
      const defaultVoice = { provider: '11labs', voiceId: 'sarah' };
      
      // Create a default attorney to use as a base for missing fields
      const defaultAttorney = {
        id: attorneyData.id, // Keep the original ID
        subdomain: 'default',
        firm_name: 'Your Law Firm',
        name: 'Your Name',
        is_active: true,
        created_at: attorneyData.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        voice_provider: defaultVoice.provider,
        voice_id: defaultVoice.voiceId,
        welcome_message: 'Hello! I\'m your legal assistant. How can I help you today?',
        information_gathering_prompt: 'Tell me about your situation, and I\'ll help find the right solution for you.',
        vapi_instructions: 'You are a legal assistant helping potential clients understand their legal needs and connecting them with appropriate attorneys.',
        vapi_assistant_id: '8d962209-530e-45d2-b2d6-17ed1ef55b3c' // Use the known working assistant ID
      };
      
      // Merge the attorney data with the default attorney
      const validatedAttorney = {
        ...defaultAttorney,
        ...attorneyData,
        updated_at: new Date().toISOString() // Always update the timestamp
      };
      
      // Ensure ID doesn't change
      validatedAttorney.id = attorneyData.id;
      
      console.log('[FixValidateAttorneyData] Validated attorney data:', validatedAttorney.id);
      
      return validatedAttorney;
    };
    
    // Also patch the loadAttorneyForUser method to handle errors gracefully
    const originalLoadAttorneyForUser = window.standaloneAttorneyManager.loadAttorneyForUser;
    if (originalLoadAttorneyForUser) {
      window.standaloneAttorneyManager.loadAttorneyForUser = async function(userId) {
        try {
          // Call the original method
          return await originalLoadAttorneyForUser.call(this, userId);
        } catch (error) {
          // If the error is about validateAttorneyData not being a function,
          // add the method and try again
          if (error.message && error.message.includes('validateAttorneyData is not a function')) {
            console.warn('[FixValidateAttorneyData] Caught validateAttorneyData error, retrying');
            
            // Add the method if it doesn't exist
            if (typeof this.validateAttorneyData !== 'function') {
              addValidateAttorneyDataMethod();
            }
            
            // Try again
            return await originalLoadAttorneyForUser.call(this, userId);
          }
          
          // Otherwise, re-throw the error
          throw error;
        }
      };
    }
    
    // Also patch the saveToLocalStorage method to use validateAttorneyData
    const originalSaveToLocalStorage = window.standaloneAttorneyManager.saveToLocalStorage;
    if (originalSaveToLocalStorage) {
      window.standaloneAttorneyManager.saveToLocalStorage = function(attorney) {
        // If no attorney is provided, use the current attorney
        const attorneyToSave = attorney || this.attorney;
        
        if (!attorneyToSave || !this.isValidUUID(attorneyToSave.id)) {
          console.warn('[FixValidateAttorneyData] Cannot save invalid attorney to localStorage');
          return;
        }
        
        try {
          // Validate the attorney data before saving
          const validatedAttorney = this.validateAttorneyData(attorneyToSave);
          
          // Call the original method with the validated attorney
          return originalSaveToLocalStorage.call(this, validatedAttorney);
        } catch (error) {
          console.error('[FixValidateAttorneyData] Error in saveToLocalStorage:', error);
        }
      };
    }
    
    console.log('[FixValidateAttorneyData] Fix applied successfully');
    return true;
  }
  
  // Try to add the method immediately
  if (addValidateAttorneyDataMethod()) {
    console.log('[FixValidateAttorneyData] Fix applied immediately');
    return;
  }
  
  // If the StandaloneAttorneyManager is not available yet, wait for it
  console.log('[FixValidateAttorneyData] Waiting for StandaloneAttorneyManager to be available');
  
  // Set up an interval to check for the StandaloneAttorneyManager
  const checkInterval = setInterval(() => {
    if (addValidateAttorneyDataMethod()) {
      clearInterval(checkInterval);
      console.log('[FixValidateAttorneyData] Fix applied after waiting');
    }
  }, 100);
  
  // Set a timeout to clear the interval after 10 seconds
  setTimeout(() => {
    clearInterval(checkInterval);
    console.warn('[FixValidateAttorneyData] Timed out waiting for StandaloneAttorneyManager');
  }, 10000);
})();
