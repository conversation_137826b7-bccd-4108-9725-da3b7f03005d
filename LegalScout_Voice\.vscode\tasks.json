{"version": "2.0.0", "tasks": [{"label": "Start Dev Server", "type": "shell", "command": "npm run dev", "isBackground": true, "problemMatcher": {"owner": "custom", "pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "VITE v", "endsPattern": "Local:   http://localhost"}}, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "Start AI Meta MCP Server", "type": "shell", "command": "node ai-meta-mcp-server/build/index.js", "isBackground": true, "problemMatcher": {"owner": "custom", "pattern": {"regexp": ".", "file": 1, "location": 2, "message": 3}, "background": {"activeOnStart": true, "beginsPattern": "AI Meta MCP Server starting", "endsPattern": "AI Meta MCP Server connected"}}, "presentation": {"reveal": "always", "panel": "new"}}]}