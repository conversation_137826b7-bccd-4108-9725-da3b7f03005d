/**
 * Attorney State Context Fix
 * 
 * This script ensures that the AttorneyStateContext is properly initialized
 * with the global React object that's patched by the react-context-fix.js script.
 */

(function() {
  console.log('[AttorneyStateContextFix] Starting fix...');
  
  // Wait for the global React object to be available
  const checkReact = setInterval(() => {
    if (window.React && window.React.createContext) {
      clearInterval(checkReact);
      
      console.log('[AttorneyStateContextFix] Global React object found');
      
      // Create a global AttorneyStateContext
      window.AttorneyStateContext = window.React.createContext(null);
      
      console.log('[AttorneyStateContextFix] Created global AttorneyStateContext');
      
      // Create a global attorney state manager
      if (!window.attorneyStateManager) {
        window.attorneyStateManager = {
          attorney: null,
          isLoading: false,
          isSaving: false,
          isSyncing: false,
          lastError: null,
          subscribers: [],
          
          // Methods
          subscribe: function(callback) {
            if (typeof callback !== 'function') {
              console.warn('[AttorneyStateContextFix] Cannot subscribe with non-function callback');
              return () => {};
            }
            
            this.subscribers.push(callback);
            
            // Return unsubscribe function
            return () => {
              this.subscribers = this.subscribers.filter(cb => cb !== callback);
            };
          },
          
          notifySubscribers: function() {
            if (!this.attorney) return;
            
            this.subscribers.forEach(callback => {
              try {
                callback(this.attorney);
              } catch (error) {
                console.error('[AttorneyStateContextFix] Error in subscriber callback:', error);
              }
            });
          },
          
          // Load attorney from localStorage
          loadFromLocalStorage: function() {
            try {
              const storedAttorney = localStorage.getItem('attorney');
              if (storedAttorney) {
                this.attorney = JSON.parse(storedAttorney);
                console.log('[AttorneyStateContextFix] Loaded attorney from localStorage:', this.attorney.id);
                this.notifySubscribers();
                return this.attorney;
              }
            } catch (error) {
              console.error('[AttorneyStateContextFix] Error loading from localStorage:', error);
            }
            return null;
          },
          
          // Initialize
          initialize: function() {
            console.log('[AttorneyStateContextFix] Initializing attorney state manager');
            this.loadFromLocalStorage();
          }
        };
        
        // Initialize the attorney state manager
        window.attorneyStateManager.initialize();
        
        console.log('[AttorneyStateContextFix] Created global attorney state manager');
      }
      
      console.log('[AttorneyStateContextFix] Fix completed');
    }
  }, 100);
  
  // Clear interval after 10 seconds if React is not available
  setTimeout(() => {
    clearInterval(checkReact);
    console.warn('[AttorneyStateContextFix] Timed out waiting for React');
  }, 10000);
})();
