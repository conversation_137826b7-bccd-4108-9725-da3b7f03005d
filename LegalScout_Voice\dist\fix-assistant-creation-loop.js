/**
 * Fix Assistant Creation Loop
 * 
 * This script prevents the infinite assistant creation loop by:
 * 1. Blocking duplicate assistant creation calls
 * 2. Using existing assistants when available
 * 3. Preventing multiple VapiCall component initializations
 */

(function() {
  console.log('[FixAssistantLoop] Preventing assistant creation loops...');

  // Track created assistants to prevent duplicates
  const createdAssistants = new Set();
  const assistantCreationInProgress = new Set();

  // Track VapiCall component instances
  let vapiCallInstanceCount = 0;
  const MAX_VAPI_CALL_INSTANCES = 1;

  // Override assistant creation to prevent loops
  const patchAssistantCreation = () => {
    // Patch vapiMcpService if available
    const patchVapiMcpService = () => {
      if (window.vapiMcpService && window.vapiMcpService.createAssistant) {
        const originalCreateAssistant = window.vapiMcpService.createAssistant;
        
        window.vapiMcpService.createAssistant = async function(config) {
          const assistantKey = `${config.name}-${config.instructions?.substring(0, 50) || 'default'}`;
          
          // Check if we're already creating this assistant
          if (assistantCreationInProgress.has(assistantKey)) {
            console.log('[FixAssistantLoop] Assistant creation already in progress, skipping:', assistantKey);
            return new Promise(resolve => {
              // Wait for the in-progress creation to complete
              const checkInterval = setInterval(() => {
                if (!assistantCreationInProgress.has(assistantKey)) {
                  clearInterval(checkInterval);
                  // Return a mock assistant to prevent errors
                  resolve({
                    id: 'duplicate-prevented-' + Date.now(),
                    name: config.name,
                    mock: true,
                    duplicate: true
                  });
                }
              }, 100);
            });
          }

          // Check if we've already created a similar assistant
          if (createdAssistants.has(assistantKey)) {
            console.log('[FixAssistantLoop] Assistant already created, skipping duplicate:', assistantKey);
            return {
              id: 'duplicate-prevented-' + Date.now(),
              name: config.name,
              mock: true,
              duplicate: true
            };
          }

          // Mark as in progress
          assistantCreationInProgress.add(assistantKey);
          console.log('[FixAssistantLoop] Creating assistant:', assistantKey);

          try {
            const result = await originalCreateAssistant.call(this, config);
            
            // Mark as created
            createdAssistants.add(assistantKey);
            console.log('[FixAssistantLoop] Assistant created successfully:', result.id);
            
            return result;
          } catch (error) {
            console.error('[FixAssistantLoop] Error creating assistant:', error);
            throw error;
          } finally {
            // Remove from in-progress
            assistantCreationInProgress.delete(assistantKey);
          }
        };

        console.log('[FixAssistantLoop] Patched vapiMcpService.createAssistant');
      }
    };

    // Try to patch immediately and periodically
    patchVapiMcpService();
    const interval = setInterval(() => {
      patchVapiMcpService();
    }, 1000);

    // Stop trying after 10 seconds
    setTimeout(() => clearInterval(interval), 10000);
  };

  // Prevent multiple VapiCall component instances
  const preventMultipleVapiCalls = () => {
    // Override React.createElement to track VapiCall components
    if (window.React && window.React.createElement) {
      const originalCreateElement = window.React.createElement;
      
      window.React.createElement = function(type, props, ...children) {
        // Check if this is a VapiCall component
        if (type && (type.name === 'VapiCall' || type.displayName === 'VapiCall')) {
          vapiCallInstanceCount++;
          
          if (vapiCallInstanceCount > MAX_VAPI_CALL_INSTANCES) {
            console.warn('[FixAssistantLoop] Preventing additional VapiCall instance:', vapiCallInstanceCount);
            
            // Return a placeholder div instead
            return originalCreateElement.call(this, 'div', {
              className: 'vapi-call-prevented',
              style: { display: 'none' }
            }, 'VapiCall instance prevented to avoid loops');
          }
          
          console.log('[FixAssistantLoop] Allowing VapiCall instance:', vapiCallInstanceCount);
        }
        
        return originalCreateElement.call(this, type, props, ...children);
      };
      
      console.log('[FixAssistantLoop] Patched React.createElement to prevent multiple VapiCall instances');
    }
  };

  // Prevent duplicate component mounting
  const preventDuplicateMounting = () => {
    // Track mounted components
    const mountedComponents = new Set();
    
    // Override useEffect to track VapiCall mounting
    if (window.React && window.React.useEffect) {
      const originalUseEffect = window.React.useEffect;
      
      window.React.useEffect = function(effect, deps) {
        // Check if this looks like a VapiCall mount effect
        const effectString = effect.toString();
        if (effectString.includes('VapiCall') || effectString.includes('startCall') || effectString.includes('vapi')) {
          const componentKey = effectString.substring(0, 100);
          
          if (mountedComponents.has(componentKey)) {
            console.warn('[FixAssistantLoop] Preventing duplicate component effect');
            return; // Skip the effect
          }
          
          mountedComponents.add(componentKey);
          
          // Clean up after 30 seconds
          setTimeout(() => {
            mountedComponents.delete(componentKey);
          }, 30000);
        }
        
        return originalUseEffect.call(this, effect, deps);
      };
    }
  };

  // Force use of existing assistants
  const forceUseExistingAssistants = () => {
    // Override attorney sync to use existing assistants
    const patchAttorneySync = () => {
      // Look for attorney sync functions
      if (window.attorneys && window.attorneys.syncAttorneyToVapi) {
        const originalSync = window.attorneys.syncAttorneyToVapi;
        
        window.attorneys.syncAttorneyToVapi = async function(attorneyData) {
          console.log('[FixAssistantLoop] Attorney sync called, checking for existing assistant...');
          
          // If attorney already has an assistant ID, don't create a new one
          if (attorneyData.vapi_assistant_id) {
            console.log('[FixAssistantLoop] Attorney already has assistant ID, skipping creation:', attorneyData.vapi_assistant_id);
            return {
              success: true,
              assistantId: attorneyData.vapi_assistant_id,
              skipped: true,
              reason: 'Assistant already exists'
            };
          }
          
          return originalSync.call(this, attorneyData);
        };
        
        console.log('[FixAssistantLoop] Patched attorney sync function');
      }
    };

    // Try to patch immediately and periodically
    patchAttorneySync();
    const interval = setInterval(() => {
      patchAttorneySync();
    }, 1000);

    // Stop trying after 10 seconds
    setTimeout(() => clearInterval(interval), 10000);
  };

  // Apply all patches
  patchAssistantCreation();
  preventMultipleVapiCalls();
  preventDuplicateMounting();
  forceUseExistingAssistants();

  // Set global flag
  window.ASSISTANT_LOOP_PREVENTION_ACTIVE = true;

  console.log('[FixAssistantLoop] Assistant creation loop prevention active');
})();
