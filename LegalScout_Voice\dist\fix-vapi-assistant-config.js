// Fix Vapi Assistant Configuration for Web Calls
// This script ensures the assistant has the required fields for web calls

(function() {
  console.log('[FixVapiAssistantConfig] Starting fix...');

  // Function to update assistant configuration
  const updateAssistantConfig = async () => {
    try {
      console.log('[FixVapiAssistantConfig] Updating assistant configuration for web calls...');

      // Get the Vapi API key
      const vapiApiKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Private key for updates

      // Assistant ID to update
      const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';

      // Updated configuration with required fields for web calls
      const assistantConfig = {
        name: "LegalScout Assistant",
        firstMessage: "Hello! How can I help you today?",
        firstMessageMode: "assistant-speaks-first",
        model: {
          provider: "openai",
          model: "gpt-4o",
          messages: [
            {
              role: "system",
              content: "You are a legal assistant helping clients with their legal needs."
            }
          ]
        },
        voice: {
          provider: "11labs",
          voiceId: "sarah"
        },
        transcriber: {
          provider: "deepgram",
          model: "nova-3"
        }
      };

      console.log('[FixVapiAssistantConfig] Sending update request to Vapi API...');

      // Update the assistant
      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${vapiApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(assistantConfig)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[FixVapiAssistantConfig] Failed to update assistant:', response.status, errorText);
        return;
      }

      const updatedAssistant = await response.json();
      console.log('[FixVapiAssistantConfig] ✅ Assistant updated successfully:', updatedAssistant);

      // Verify the assistant now has the required fields
      if (updatedAssistant.firstMessage && updatedAssistant.model?.messages) {
        console.log('[FixVapiAssistantConfig] ✅ Assistant now has required fields for web calls');
        console.log('[FixVapiAssistantConfig] - firstMessage:', updatedAssistant.firstMessage);
        console.log('[FixVapiAssistantConfig] - model.messages:', updatedAssistant.model.messages);
      } else {
        console.warn('[FixVapiAssistantConfig] ⚠️ Assistant may still be missing required fields');
      }

    } catch (error) {
      console.error('[FixVapiAssistantConfig] Error updating assistant:', error);
    }
  };

  // Run the update
  updateAssistantConfig();

  console.log('[FixVapiAssistantConfig] Fix script loaded');
})();
