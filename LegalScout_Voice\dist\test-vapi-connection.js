/**
 * Test Vapi Connection - Quick diagnostic script
 * 
 * This script tests the Vapi MCP connection to verify if voice updates are working
 */

(function() {
  'use strict';

  console.log('🔧 [VapiConnectionTest] Starting connection test...');

  async function testVapiConnection() {
    try {
      // Test 1: Check if we can connect to Vapi MCP service
      console.log('📡 [Test 1] Testing Vapi MCP service connection...');
      
      // Import the service
      const { vapiMcpService } = await import('/src/services/vapiMcpService.js');
      
      // Try to connect with the API key
      const apiKey = '6734febc-fc65-4669-93b0-929b31ff6564'; // Your private key
      const connected = await vapiMcpService.connect(apiKey, true); // Force direct API
      
      console.log('✅ [Test 1] Connection result:', connected);
      
      if (!connected) {
        console.error('❌ [Test 1] Failed to connect to Vapi service');
        return;
      }

      // Test 2: Try to get the attorney's assistant
      console.log('📋 [Test 2] Testing assistant retrieval...');
      
      const assistantId = 'f9b97d13-f9c4-40af-a660-62ba5925ff2a';
      const assistant = await vapiMcpService.getAssistant(assistantId);
      
      console.log('✅ [Test 2] Assistant data:', assistant);
      
      if (!assistant) {
        console.error('❌ [Test 2] Failed to get assistant');
        return;
      }

      // Test 3: Try to update the voice
      console.log('🎵 [Test 3] Testing voice update...');
      
      const testVoice = {
        provider: 'playht',
        voiceId: 'waylon'
      };
      
      const updateResult = await vapiMcpService.updateAssistant(assistantId, {
        voice: testVoice
      });
      
      console.log('✅ [Test 3] Voice update result:', updateResult);
      
      // Test 4: Verify the voice was updated
      console.log('🔍 [Test 4] Verifying voice update...');
      
      const updatedAssistant = await vapiMcpService.getAssistant(assistantId);
      console.log('✅ [Test 4] Updated assistant voice:', updatedAssistant?.voice);
      
      if (updatedAssistant?.voice?.voiceId === testVoice.voiceId) {
        console.log('🎉 [SUCCESS] Voice update working correctly!');
      } else {
        console.warn('⚠️ [WARNING] Voice update may not be persisting');
      }

      // Test 5: Restore original voice (sarah)
      console.log('🔄 [Test 5] Restoring original voice...');
      
      const originalVoice = {
        provider: '11labs',
        voiceId: 'sarah'
      };
      
      await vapiMcpService.updateAssistant(assistantId, {
        voice: originalVoice
      });
      
      console.log('✅ [Test 5] Voice restored to sarah');

    } catch (error) {
      console.error('💥 [VapiConnectionTest] Error during test:', error);
    }
  }

  // Auto-run the test
  console.log('🚀 [VapiConnectionTest] Starting test in 2 seconds...');
  setTimeout(testVapiConnection, 2000);

  // Expose test function globally
  window.testVapiConnection = testVapiConnection;

})();
