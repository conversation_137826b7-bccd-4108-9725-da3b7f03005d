# Vapi Blocks Integration

This document explains how we've integrated concepts from Vapi Blocks into LegalScout while maintaining our existing UI design.

## Overview

[Vapi Blocks](https://www.vapiblocks.com/) provides pre-built components and hooks for integrating Vapi voice AI services into web applications. We've adapted these concepts to create custom components that match LegalScout's UI design while leveraging the functionality provided by Vapi Blocks.

## Components

### 1. use-vapi Hook

The `use-vapi` hook provides a consistent way to interact with the Vapi SDK throughout the application.

**Location:** `src/hooks/use-vapi.ts`

**Features:**
- Volume level tracking
- Session state management
- Conversation history
- Speaker detection (user vs. assistant)
- Error handling

**Usage:**
```jsx
import useVapi from '../hooks/use-vapi';

const MyComponent = () => {
  const { 
    volumeLevel, 
    isSessionActive, 
    conversation, 
    currentSpeaker,
    toggleCall,
    startCall,
    stopCall
  } = useVapi();
  
  // Use these values and functions to control Vapi
};
```

### 2. EnhancedSpeechParticles

This component provides a visualization of speech audio levels, with different colors for user and assistant speech.

**Location:** `src/components/EnhancedSpeechParticles.jsx`

**Features:**
- Real-time audio visualization
- Different colors for user and assistant
- Ambient animation when not speaking
- Integration with existing SpeechParticles component

**Usage:**
```jsx
import EnhancedSpeechParticles from '../components/EnhancedSpeechParticles';

const MyComponent = () => {
  return (
    <EnhancedSpeechParticles className="custom-class" />
  );
};
```

### 3. EnhancedCallController

This component provides a UI for controlling calls with Vapi, including a call button, status indicators, and speech visualization.

**Location:** `src/components/call/EnhancedCallController.jsx`

**Features:**
- Call button with status indicators
- Real-time speech visualization
- Transcript display
- Error handling

**Usage:**
```jsx
import EnhancedCallController from '../components/call/EnhancedCallController';

const MyComponent = () => {
  return (
    <EnhancedCallController 
      assistantId="your-assistant-id"
      showTranscript={true}
      showVisualization={true}
    />
  );
};
```

### 4. MeetingScheduler

This component provides a UI for scheduling meetings with attorneys using Vapi's function calling capabilities.

**Location:** `src/components/MeetingScheduler.jsx`

**Features:**
- Integration with EnhancedCallController
- Meeting confirmation display
- Error handling
- Instructions for users

**Usage:**
```jsx
import MeetingScheduler from '../components/MeetingScheduler';

const MyComponent = () => {
  return (
    <MeetingScheduler 
      attorneyId="attorney-id"
      attorneyName="Jane Smith, Esq."
      assistantId="your-assistant-id"
    />
  );
};
```

## Demo Page

A demo page is available to showcase these components:

**Location:** `src/pages/VapiDemo.jsx`

This page demonstrates:
- Call Controller
- Meeting Scheduler
- Speech Visualization

## Integration with Existing Components

These new components are designed to work alongside existing components:

1. **SpeechParticles**: The EnhancedSpeechParticles component can use the existing updateAudioSource function if available.

2. **CallController**: The EnhancedCallController can replace or complement the existing CallController.

3. **Agent Preview**: The EnhancedCallController can be used in the agent preview section of the dashboard.

## Styling

All components use CSS variables to match LegalScout's design system:

```css
:root {
  --primary-color: #3b82f6;
  --primary-color-dark: #2563eb;
  --success-color: #10b981;
  --error-color: #ef4444;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;
  --card-background: #ffffff;
  --border-color: rgba(0, 0, 0, 0.1);
  /* ... other variables ... */
}
```

You can customize these variables in your global CSS to match your design system.

## Dependencies

These components require the following dependencies:

```json
{
  "dependencies": {
    "@vapi-ai/web": "^1.0.0",
    "framer-motion": "^6.0.0"
  }
}
```

## Configuration

To use these components, you need to set the following environment variables:

```
NEXT_PUBLIC_VAPI_PUBLIC_KEY=your-vapi-public-key
NEXT_PUBLIC_VAPI_ASSISTANT_ID=your-default-assistant-id
```

## Next Steps

1. **Integration Testing**: Test these components with real Vapi assistants and phone numbers.

2. **Custom Function Calling**: Implement custom function calling for the meeting scheduler.

3. **Analytics Integration**: Add analytics tracking for calls and meetings.

4. **Mobile Optimization**: Further optimize the components for mobile devices.

## Resources

- [Vapi Blocks Documentation](https://www.vapiblocks.com/docs)
- [Vapi API Reference](https://docs.vapi.ai/api-reference)
- [Framer Motion Documentation](https://www.framer.com/motion/)
