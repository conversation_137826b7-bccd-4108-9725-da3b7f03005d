/**
 * Immediate Label Fix
 *
 * This script runs immediately and aggressively to catch and fix
 * the specific label issue that the browser is detecting.
 */

(function() {
  // Browser-compatible development mode detection
  const isDevelopment = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname.includes('localhost');

  if (isDevelopment) {
    console.log('⚡ [ImmediateLabelFix] Starting immediate label fix...');
  }

  function immediateFixLabels() {
    const labels = document.querySelectorAll('label[for]');
    let fixed = 0;

    if (isDevelopment) {
      console.log(`⚡ [ImmediateLabelFix] Checking ${labels.length} labels...`);
    }

    labels.forEach((label, index) => {
      const forValue = label.getAttribute('for');
      const target = document.getElementById(forValue);

      if (!target) {
        if (isDevelopment) {
          console.error(`⚡ [ImmediateLabelFix] FIXING: Label ${index + 1} with for="${forValue}" has no target`);
          console.log(`   Label text: "${label.textContent.trim()}"`);
          console.log(`   Label HTML:`, label.outerHTML);
        }

        // Try to find the most likely target
        const allFormElements = document.querySelectorAll('input, textarea, select, button');
        let bestMatch = null;
        let bestScore = 0;

        allFormElements.forEach(element => {
          if (!element.id) return;

          const elementId = element.id.toLowerCase();
          const forValueLower = forValue.toLowerCase();
          const labelText = label.textContent.trim().toLowerCase();

          let score = 0;

          // Check for common patterns
          if (elementId === forValueLower) {
            score = 100; // Perfect match
          } else if (elementId.includes(forValueLower) || forValueLower.includes(elementId)) {
            score = 80; // Partial match
          } else if (labelText.includes(elementId) || elementId.includes(labelText.replace(/\s+/g, ''))) {
            score = 60; // Text similarity
          } else if (element.name && element.name.toLowerCase() === forValueLower) {
            score = 70; // Name match
          }

          // Type-specific matching
          if (score === 0) {
            if (labelText.includes('email') && element.type === 'email') score = 50;
            else if (labelText.includes('password') && element.type === 'password') score = 50;
            else if (labelText.includes('name') && element.type === 'text') score = 40;
            else if (labelText.includes('phone') && element.type === 'tel') score = 50;
          }

          if (score > bestScore) {
            bestScore = score;
            bestMatch = element;
          }
        });

        if (bestMatch && bestScore >= 40) {
          if (isDevelopment) {
            console.log(`⚡ [ImmediateLabelFix] FIXED: Updating for="${forValue}" to for="${bestMatch.id}" (score: ${bestScore})`);
          }
          label.setAttribute('for', bestMatch.id);
          fixed++;
        } else {
          if (isDevelopment) {
            console.warn(`⚡ [ImmediateLabelFix] Could not fix label with for="${forValue}"`);
            console.log(`⚡ [ImmediateLabelFix] Removing invalid 'for' attribute from label`);
          }

          // As a last resort, try to remove the for attribute if no suitable target exists
          label.removeAttribute('for');
          fixed++;
        }
      }
    });

    // Check for form elements without labels and try to associate them
    const formElements = document.querySelectorAll('input, textarea, select');
    formElements.forEach(element => {
      if (element.id) {
        const hasLabel = document.querySelector(`label[for="${element.id}"]`);
        const hasParentLabel = element.closest('label');

        if (!hasLabel && !hasParentLabel) {
          // Try to find a nearby label without 'for' attribute
          const nearbyLabels = document.querySelectorAll('label:not([for])');
          let closestLabel = null;
          let closestDistance = Infinity;

          nearbyLabels.forEach(label => {
            try {
              const labelRect = label.getBoundingClientRect();
              const elementRect = element.getBoundingClientRect();

              const distance = Math.sqrt(
                Math.pow(labelRect.left - elementRect.left, 2) +
                Math.pow(labelRect.top - elementRect.top, 2)
              );

              if (distance < closestDistance && distance < 100) {
                closestDistance = distance;
                closestLabel = label;
              }
            } catch (e) {
              // Skip if elements are not visible
            }
          });

          if (closestLabel) {
            if (isDevelopment) {
              console.log(`⚡ [ImmediateLabelFix] Associating label "${closestLabel.textContent.trim()}" with element id="${element.id}"`);
            }
            closestLabel.setAttribute('for', element.id);
            fixed++;
          }
        }
      }
    });

    if (isDevelopment) {
      console.log(`⚡ [ImmediateLabelFix] Fixed ${fixed} label issues`);
    }
    return fixed;
  }

  // Run immediately
  immediateFixLabels();

  // Run again very quickly
  setTimeout(immediateFixLabels, 10);
  setTimeout(immediateFixLabels, 50);
  setTimeout(immediateFixLabels, 100);
  setTimeout(immediateFixLabels, 250);
  setTimeout(immediateFixLabels, 500);
  setTimeout(immediateFixLabels, 1000);

  // Set up aggressive monitoring
  const observer = new MutationObserver(() => {
    setTimeout(immediateFixLabels, 10);
  });

  observer.observe(document.documentElement, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['for', 'id']
  });

  if (isDevelopment) {
    console.log('⚡ [ImmediateLabelFix] Aggressive label fixing initialized');
  }
})();
