import React, { useEffect, useRef, useState, forwardRef } from 'react';
import './GlobeDossierView.css';
import './Map.css';
import * as THREE from 'three';

/**
 * Interactive 3D globe visualization component that displays case location
 * and attorney positions
 * 
 * This component creates an immersive 3D globe using Three.js to visualize:
 * - Client case location
 * - Available attorneys in the area
 * - Geographic context for legal consultations
 * 
 * The globe rotates slowly for visual engagement and highlights relevant
 * locations based on the case data.
 * 
 * @param {Object} props Component props
 * @param {boolean} props.isVisible Controls whether the globe is rendered
 * @param {Object} props.caseData Case information including location data
 * @param {Array} props.attorneys List of attorney objects with location data
 * @param {Function} props.onSelectAttorney Callback when an attorney is selected
 * @param {boolean} props.isDarkTheme Whether to render in dark mode (true) or light mode (false)
 * @param {React.Ref} ref Forwarded ref
 */
const GlobeDossierView = forwardRef(({ 
  isVisible = true,
  caseData = {},
  attorneys = [],
  onSelectAttorney = () => {},
  isDarkTheme = true
}, ref) => {
  console.log("GlobeDossierView render called, isVisible:", isVisible, "caseData:", caseData, "attorneys:", attorneys);
  
  const mountRef = useRef(null);
  const animationRef = useRef(null);
  const globeRef = useRef(null);
  const [hasLocation, setHasLocation] = useState(false);
  const [locationName, setLocationName] = useState(null);
  
  // Expose the mount ref to parent components via forwarded ref
  React.useImperativeHandle(ref, () => ({
    mountRef: mountRef.current,
    globeRef: globeRef.current
  }));
  
  // Set up simplified Three.js scene
  useEffect(() => {
    // Early return if component is not visible or mount point not ready
    if (!isVisible || !mountRef.current) return;
    
    console.log("Setting up Three.js scene");
    
    // Clear any existing canvas
    while (mountRef.current.firstChild) {
      mountRef.current.removeChild(mountRef.current.firstChild);
    }

    try {
      // Scene setup
      const scene = new THREE.Scene();
      
      // Set scene background color based on theme
      scene.background = isDarkTheme ? null : new THREE.Color(0xf0f8ff); // Light blue background in light mode
      
      // Get full viewport dimensions
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight - 60; // Subtract navbar height
      
      // Make the globe larger for more impact
      let size = Math.max(viewportWidth, viewportHeight) * 1.8;
      size = Math.max(size, 1500); // Larger minimum size
      
      // Camera setup with wider field of view
      const camera = new THREE.PerspectiveCamera(50, 1, 0.1, 3000);
      camera.position.z = 350; // Position closer to get a better view
      
      // Renderer setup
      const renderer = new THREE.WebGLRenderer({ 
        antialias: true, 
        alpha: true,
        logarithmicDepthBuffer: true
      });
      
      // Set equal width and height for perfect circle
      renderer.setSize(size, size);
      renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
      renderer.setClearColor(0x000000, 0);
      
      // Position the renderer in the center of the viewport
      const rendererStyle = renderer.domElement.style;
      rendererStyle.position = 'absolute';
      rendererStyle.left = '50%';
      rendererStyle.top = '50%';
      rendererStyle.transform = 'translate(-50%, -50%)';
      rendererStyle.maxWidth = 'none'; // Remove maxWidth constraint
      rendererStyle.maxHeight = 'none'; // Remove maxHeight constraint
      rendererStyle.border = 'none';
      
      // Add to container
      console.log("Appending renderer to container");
      mountRef.current.appendChild(renderer.domElement);
      
      // Create a simple sphere to represent a globe
      const geometry = new THREE.SphereGeometry(100, 64, 64);
      const texture = new THREE.TextureLoader().load('/earth-blue-marble.jpg');
      
      // Use a more dramatic material with higher specular highlights
      const material = new THREE.MeshPhongMaterial({ 
        map: texture,
        shininess: 15,
        specular: new THREE.Color(isDarkTheme ? 0x3333ff : 0xaaaaff)
      });
      
      const globe = new THREE.Mesh(geometry, material);
      scene.add(globe);
      globeRef.current = globe;
      
      // Add appropriate lighting based on theme
      if (isDarkTheme) {
        // Dark mode lighting (nighttime)
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.4);
        scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);
        
        // Add a deep blue atmosphere for dark mode
        const atmosphereGeometry = new THREE.SphereGeometry(104, 64, 64);
        const atmosphereMaterial = new THREE.MeshPhongMaterial({
          color: 0x0066ff,
          transparent: true,
          opacity: 0.2,
          side: THREE.BackSide
        });
        
        const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        scene.add(atmosphere);
      } else {
        // Light mode lighting (daytime)
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.7);
        scene.add(ambientLight);
        
        // Add brighter directional light for daytime effect
        const directionalLight = new THREE.DirectionalLight(0xffffcc, 2.0); // Slightly yellow sunlight
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);
        
        // Add a soft blue atmosphere for light mode
        const atmosphereGeometry = new THREE.SphereGeometry(104, 64, 64);
        const atmosphereMaterial = new THREE.MeshPhongMaterial({
          color: 0x87ceeb, // Sky blue
          transparent: true,
          opacity: 0.15,
          side: THREE.BackSide
        });
        
        const atmosphere = new THREE.Mesh(atmosphereGeometry, atmosphereMaterial);
        scene.add(atmosphere);
        
        // Add a distant background sphere to create a blue sky backdrop
        const skyGeometry = new THREE.SphereGeometry(1500, 32, 32);
        const skyMaterial = new THREE.MeshBasicMaterial({
          color: 0xdeeeff, // Light blue sky
          side: THREE.BackSide,
          transparent: true,
          opacity: 1.0
        });
        
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        scene.add(sky);
      }
      
      // Handle window resize - adjust for full screen
      const handleResize = () => {
        const newViewportWidth = window.innerWidth;
        const newViewportHeight = window.innerHeight - 60;
        
        // Make globe larger on resize too
        let newSize = Math.max(newViewportWidth, newViewportHeight) * 1.8;
        newSize = Math.max(newSize, 1500); 
        
        // Update renderer size
        renderer.setSize(newSize, newSize);
        
        // Keep camera aspect ratio square
        camera.aspect = 1;
        camera.updateProjectionMatrix();
        
        // Camera position
        camera.position.z = 350;
      };
      
      window.addEventListener('resize', handleResize);
      
      // Slower rotation for more dramatic effect
      const animate = () => {
        if (globe) {
          globe.rotation.y += 0.001; // Slower rotation
        }
        
        renderer.render(scene, camera);
        animationRef.current = requestAnimationFrame(animate);
      };
      
      // Start animation
      animate();
      
      // Cleanup
      return () => {
        window.removeEventListener('resize', handleResize);
        
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
        
        if (mountRef.current && renderer.domElement) {
          mountRef.current.removeChild(renderer.domElement);
        }
        
        // Dispose of resources
        if (geometry) geometry.dispose();
        if (material) material.dispose();
        
        renderer.dispose();
      };
    } catch (error) {
      console.error("Error setting up Three.js scene:", error);
    }
  }, [isVisible, isDarkTheme]);
  
  // Track selected location for highlighting
  useEffect(() => {
    // Check if we have location data in caseData
    if (caseData && caseData.location) {
      console.log("Location data detected:", caseData.location);
      setHasLocation(true);
      
      // Try to get a display name for the location
      if (caseData.location.address) {
        setLocationName(caseData.location.address);
      } else if (caseData.location.city) {
        setLocationName(caseData.location.city + (caseData.location.state ? `, ${caseData.location.state}` : ''));
      } else if (caseData.location.state) {
        setLocationName(caseData.location.state);
      } else {
        setLocationName("Selected Location");
      }
    } else {
      setHasLocation(false);
      setLocationName(null);
    }
  }, [caseData]);
  
  // Get attorneys for this location
  const locationAttorneys = attorneys.filter(attorney => attorney.location);

  return (
    <div
      className="globe-background"
      data-testid="globe-background"
      style={{
        position: 'fixed',
        top: 0, 
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: isDarkTheme ? 'rgba(0, 0, 0, 0.85)' : 'rgba(0, 0, 0, 0)',
        color: isDarkTheme ? 'white' : '#333',
        zIndex: isDarkTheme ? -5 : -10, // Lower z-index in light mode to ensure it's below everything
        overflow: 'hidden',
        pointerEvents: 'none',
        margin: 0,
        padding: 0,
        opacity: 1,
        visibility: 'visible',
        transition: 'opacity 0.5s ease-in-out, visibility 0.5s ease-in-out'
      }}
    >
      {/* Container that takes full width and height */}
      <div 
        className="globe-renderer-container" 
        ref={mountRef}
        data-testid="globe-container"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          overflow: 'hidden'
        }}
      ></div>
      
      {/* Location Information */}
      {hasLocation && (
        <div 
          className="location-info"
          style={{
            position: 'absolute',
            bottom: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            background: isDarkTheme ? 'rgba(10, 20, 40, 0.7)' : 'rgba(255, 255, 255, 0.85)',
            padding: '10px 20px',
            borderRadius: '10px',
            textAlign: 'center',
            maxWidth: '300px',
            zIndex: 1
          }}
        >
          <h3 style={{ color: isDarkTheme ? '#ffffff' : '#333333' }}>
            {locationName}
          </h3>
          {locationAttorneys.length > 0 && (
            <p style={{ 
              color: isDarkTheme ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.7)', 
              marginTop: '10px' 
            }}>
              {locationAttorneys.length} attorneys available
            </p>
          )}
        </div>
      )}
    </div>
  );
});

// Add a display name for better debugging
GlobeDossierView.displayName = 'GlobeDossierView';

export default GlobeDossierView; 