/**
 * Error Loop Fix
 * 
 * Fixes the error loops on load by:
 * 1. Preventing multiple script initializations
 * 2. Fixing module import errors
 * 3. Stopping infinite state handler loops
 * 4. Optimizing component lifecycle
 */

(function errorLoopFix() {
  'use strict';
  
  // Prevent multiple runs
  if (window.ERROR_LOOP_FIX_APPLIED) {
    console.log('🔧 [ErrorLoopFix] Already applied, skipping...');
    return;
  }
  
  console.log('🔧 [ErrorLoopFix] Starting error loop fixes...');
  
  // 1. Fix import.meta errors globally
  function fixImportMetaErrors() {
    console.log('🔧 [ErrorLoopFix] Fixing import.meta errors...');
    
    // Create safe import.meta polyfill
    if (typeof window.import === 'undefined') {
      window.import = {};
    }
    
    if (!window.import.meta) {
      window.import.meta = {
        env: {
          VITE_VAPI_PUBLIC_KEY: '310f0d43-27c2-47a5-a76d-e55171d024f7',
          VITE_VAPI_SECRET_KEY: '6734febc-fc65-4669-93b0-929b31ff6564',
          VITE_SUPABASE_URL: 'https://utopqxsvudgrtiwenlzl.supabase.co',
          VITE_SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV0b3BxeHN2dWRncnRpd2VubHpsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3Mzg5NDgwMDcsImV4cCI6MjA1NDUyNDAwN30.CCv9bvV5Ee2r7A8TcFR4QXHFCNVo3QfiTz5nPzK4cRU',
          MODE: 'development',
          DEV: true,
          PROD: false
        }
      };
    }
    
    console.log('✅ [ErrorLoopFix] Import.meta polyfill created');
  }
  
  // 2. Prevent multiple script initializations
  function preventMultipleInitializations() {
    console.log('🔧 [ErrorLoopFix] Setting up initialization guards...');
    
    // Track initialized scripts
    window.INITIALIZED_SCRIPTS = window.INITIALIZED_SCRIPTS || new Set();
    
    // Override common initialization patterns
    const originalSetInterval = window.setInterval;
    const activeIntervals = new Map();
    
    window.setInterval = function(callback, delay, ...args) {
      // Prevent duplicate intervals for state handlers
      const callbackStr = callback.toString();
      if (callbackStr.includes('RobustStateHandler') || 
          callbackStr.includes('Waiting for dependencies')) {
        
        const key = `${callbackStr.substring(0, 100)}_${delay}`;
        if (activeIntervals.has(key)) {
          console.log('🔧 [ErrorLoopFix] Preventing duplicate interval:', key.substring(0, 50) + '...');
          return activeIntervals.get(key);
        }
        
        const intervalId = originalSetInterval.call(this, callback, delay, ...args);
        activeIntervals.set(key, intervalId);
        return intervalId;
      }
      
      return originalSetInterval.call(this, callback, delay, ...args);
    };
    
    console.log('✅ [ErrorLoopFix] Initialization guards set up');
  }
  
  // 3. Fix DOM observer timing issues
  function fixDOMObserverTiming() {
    console.log('🔧 [ErrorLoopFix] Fixing DOM observer timing...');
    
    // Override MutationObserver to ensure DOM is ready
    const OriginalMutationObserver = window.MutationObserver;
    
    window.MutationObserver = function(callback) {
      return new OriginalMutationObserver(function(mutations, observer) {
        try {
          callback.call(this, mutations, observer);
        } catch (error) {
          console.warn('🔧 [ErrorLoopFix] MutationObserver error caught:', error.message);
        }
      });
    };
    
    // Override observe method to check for valid targets
    const originalObserve = OriginalMutationObserver.prototype.observe;
    OriginalMutationObserver.prototype.observe = function(target, options) {
      if (!target || !target.nodeType) {
        console.warn('🔧 [ErrorLoopFix] Invalid observe target, skipping:', target);
        return;
      }
      
      try {
        return originalObserve.call(this, target, options);
      } catch (error) {
        console.warn('🔧 [ErrorLoopFix] Observer setup failed:', error.message);
      }
    };
    
    console.log('✅ [ErrorLoopFix] DOM observer timing fixed');
  }
  
  // 4. Optimize component lifecycle
  function optimizeComponentLifecycle() {
    console.log('🔧 [ErrorLoopFix] Optimizing component lifecycle...');
    
    // Track component mounts to prevent excessive re-renders
    window.COMPONENT_MOUNT_TRACKER = window.COMPONENT_MOUNT_TRACKER || new Map();
    
    // Add debouncing for rapid state changes
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }
    
    // Make debounce available globally
    window.debounce = debounce;
    
    console.log('✅ [ErrorLoopFix] Component lifecycle optimized');
  }
  
  // 5. Clean up problematic scripts and prevent auto-refresh
  function cleanupProblematicScripts() {
    console.log('🔧 [ErrorLoopFix] Cleaning up problematic scripts and preventing auto-refresh...');

    // Disable scripts that are causing loops
    const problematicScripts = [
      'force-new-assistant.js',
      'aggressive-fix.js',
      'critical-fixes-v2.js',
      'agent-configure-button.js',
      'fix-banner-functionality.js',
      'fix-banner-removal-persistence.js'
    ];

    problematicScripts.forEach(scriptName => {
      const scripts = document.querySelectorAll(`script[src*="${scriptName}"]`);
      scripts.forEach(script => {
        console.log(`🔧 [ErrorLoopFix] Disabling problematic script: ${scriptName}`);
        script.remove();
      });
    });

    // Prevent auto-refresh mechanisms
    preventAutoRefresh();

    console.log('✅ [ErrorLoopFix] Problematic scripts cleaned up and auto-refresh prevented');
  }

  // 5a. Prevent auto-refresh mechanisms
  function preventAutoRefresh() {
    console.log('🔧 [ErrorLoopFix] Preventing auto-refresh mechanisms...');

    // Override window.location.reload to prevent unwanted refreshes
    const originalReload = window.location.reload;
    let reloadCount = 0;
    const maxReloads = 1; // Allow only 1 reload per session

    window.location.reload = function(forcedReload) {
      reloadCount++;
      if (reloadCount > maxReloads) {
        console.warn('🔧 [ErrorLoopFix] Preventing excessive page reload (attempt #' + reloadCount + ')');
        return;
      }
      console.log('🔧 [ErrorLoopFix] Allowing page reload (attempt #' + reloadCount + ')');
      return originalReload.call(this, forcedReload);
    };

    // Override setTimeout for auto-refresh patterns
    const originalSetTimeout = window.setTimeout;
    window.setTimeout = function(callback, delay, ...args) {
      // Block auto-refresh timeouts
      if (typeof callback === 'function') {
        const callbackStr = callback.toString();
        if (callbackStr.includes('location.reload') ||
            callbackStr.includes('window.location.href') ||
            callbackStr.includes('Auto-refreshing')) {
          console.warn('🔧 [ErrorLoopFix] Blocking auto-refresh setTimeout');
          return null;
        }
      }
      return originalSetTimeout.call(this, callback, delay, ...args);
    };

    // Clear existing problematic intervals
    clearProblematicIntervals();

    console.log('✅ [ErrorLoopFix] Auto-refresh mechanisms prevented');
  }

  // 5b. Clear problematic intervals
  function clearProblematicIntervals() {
    // Get all intervals and check if they're problematic
    const highestIntervalId = window.setInterval(() => {}, 0);
    window.clearInterval(highestIntervalId);

    // Clear intervals that might be causing issues
    for (let i = 1; i < highestIntervalId; i++) {
      try {
        window.clearInterval(i);
      } catch (e) {
        // Ignore errors
      }
    }

    console.log('🔧 [ErrorLoopFix] Cleared existing intervals');
  }
  
  // 6. Set up error boundaries
  function setupErrorBoundaries() {
    console.log('🔧 [ErrorLoopFix] Setting up error boundaries...');
    
    // Global error handler
    window.addEventListener('error', function(event) {
      if (event.error && event.error.message) {
        const message = event.error.message;
        
        // Handle specific error types
        if (message.includes('Cannot use import statement outside a module')) {
          console.warn('🔧 [ErrorLoopFix] Import statement error caught and handled');
          event.preventDefault();
          return false;
        }
        
        if (message.includes('Failed to execute \'observe\' on \'MutationObserver\'')) {
          console.warn('🔧 [ErrorLoopFix] MutationObserver error caught and handled');
          event.preventDefault();
          return false;
        }
      }
    });
    
    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(event) {
      if (event.reason && event.reason.message) {
        const message = event.reason.message;
        
        if (message.includes('import') || message.includes('module')) {
          console.warn('🔧 [ErrorLoopFix] Promise rejection for import error caught and handled');
          event.preventDefault();
          return false;
        }
      }
    });
    
    console.log('✅ [ErrorLoopFix] Error boundaries set up');
  }
  
  // 7. Initialize all fixes
  function initializeAllFixes() {
    try {
      fixImportMetaErrors();
      preventMultipleInitializations();
      fixDOMObserverTiming();
      optimizeComponentLifecycle();
      cleanupProblematicScripts();
      setupErrorBoundaries();
      
      // Mark as complete
      window.ERROR_LOOP_FIX_APPLIED = true;
      
      console.log('🎉 [ErrorLoopFix] All error loop fixes applied successfully');
      
      // Dispatch completion event
      window.dispatchEvent(new CustomEvent('errorLoopFixComplete', {
        detail: {
          timestamp: new Date().toISOString(),
          fixes: [
            'importMetaErrors',
            'multipleInitializations',
            'domObserverTiming',
            'componentLifecycle',
            'problematicScripts',
            'errorBoundaries'
          ]
        }
      }));
      
    } catch (error) {
      console.error('❌ [ErrorLoopFix] Error during initialization:', error);
    }
  }
  
  // Execute immediately
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAllFixes);
  } else {
    initializeAllFixes();
  }
  
})();
