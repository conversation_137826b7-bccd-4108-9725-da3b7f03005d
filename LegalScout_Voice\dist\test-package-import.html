<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Import Test</title>
    <style>
        body {
            font-family: monospace;
            padding: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .test-result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .pass { background: #004400; }
        .fail { background: #440000; }
        .info { background: #004444; }
        button {
            padding: 10px 20px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover { background: #0088ff; }
        pre {
            background: #333;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>📦 Package Import Test</h1>
    <p>This page tests if the @vapi-ai/web package can be imported correctly.</p>
    
    <button onclick="testPackageImport()">Test Package Import</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <div id="results"></div>

    <script type="module">
        let results = [];

        function addResult(message, type = 'info') {
            results.push({ message, type, timestamp: new Date().toLocaleTimeString() });
            updateDisplay();
        }

        function updateDisplay() {
            const container = document.getElementById('results');
            container.innerHTML = results.map(r => 
                `<div class="test-result ${r.type}">[${r.timestamp}] ${r.message}</div>`
            ).join('');
        }

        window.clearResults = function() {
            results = [];
            updateDisplay();
        }

        window.testPackageImport = async function() {
            results = [];
            addResult('🧪 Testing @vapi-ai/web package import...');

            try {
                // Test 1: Basic import
                addResult('📦 Attempting to import @vapi-ai/web...', 'info');
                
                // This will only work if we're running from the actual Vite dev server
                // that can resolve the node_modules
                const VapiModule = await import('@vapi-ai/web');
                
                addResult('✅ Package imported successfully', 'pass');
                
                // Test 2: Analyze module structure
                addResult('🔍 Analyzing module structure...', 'info');
                
                const moduleInfo = {
                    hasDefault: !!VapiModule.default,
                    hasVapi: !!VapiModule.Vapi,
                    moduleKeys: Object.keys(VapiModule),
                    defaultType: typeof VapiModule.default,
                    vapiType: typeof VapiModule.Vapi
                };
                
                addResult(`📋 Module info: ${JSON.stringify(moduleInfo, null, 2)}`, 'info');
                
                // Test 3: Extract Vapi class
                let VapiClass = VapiModule.default || VapiModule.Vapi || VapiModule;
                
                // If we got an object with a default property, try that
                if (VapiClass && typeof VapiClass === 'object' && VapiClass.default) {
                    VapiClass = VapiClass.default;
                }
                
                if (typeof VapiClass === 'function') {
                    addResult('✅ Vapi class extracted successfully', 'pass');
                    addResult(`📋 Vapi class type: ${typeof VapiClass}`, 'info');
                    
                    // Test 4: Create instance
                    try {
                        const testInstance = new VapiClass('test-api-key');
                        addResult('✅ Vapi instance created successfully', 'pass');
                        
                        // Test 5: Check methods
                        const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(testInstance))
                            .filter(name => typeof testInstance[name] === 'function');
                        
                        addResult(`📋 Available methods: ${methods.join(', ')}`, 'info');
                        
                        if (typeof testInstance.start === 'function') {
                            addResult('✅ start() method available', 'pass');
                        } else {
                            addResult('❌ start() method missing', 'fail');
                        }
                        
                        if (typeof testInstance.stop === 'function') {
                            addResult('✅ stop() method available', 'pass');
                        } else {
                            addResult('❌ stop() method missing', 'fail');
                        }
                        
                        // Test 6: Test with real API key
                        addResult('🔑 Testing with real API key...', 'info');
                        const realApiKey = '6734febc-fc65-4669-93b0-929b31ff6564';
                        
                        try {
                            const realInstance = new VapiClass(realApiKey);
                            addResult('✅ Real instance created successfully', 'pass');
                            
                            // Make it globally available for further testing
                            window.Vapi = VapiClass;
                            window.vapiInstance = realInstance;
                            addResult('✅ Vapi made globally available as window.Vapi', 'pass');
                            
                        } catch (realError) {
                            addResult(`❌ Real instance creation failed: ${realError.message}`, 'fail');
                        }
                        
                    } catch (instanceError) {
                        addResult(`❌ Instance creation failed: ${instanceError.message}`, 'fail');
                    }
                    
                } else {
                    addResult(`❌ Vapi class not found. Got: ${typeof VapiClass}`, 'fail');
                }
                
            } catch (importError) {
                addResult(`❌ Package import failed: ${importError.message}`, 'fail');
                addResult('💡 This test only works when running from Vite dev server', 'info');
                addResult('💡 Try accessing this page via http://localhost:5174/test-package-import.html', 'info');
            }

            addResult('🏁 Package import test completed!');
        }
    </script>
</body>
</html>
