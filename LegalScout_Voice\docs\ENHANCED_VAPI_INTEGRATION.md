# Enhanced Vapi Integration Guide

This guide explains how to use the enhanced Vapi integration in LegalScout Voice.

## Overview

The enhanced Vapi integration provides a more reliable and consistent way to interact with Vapi's voice AI services. It uses the Model Context Protocol (MCP) server to provide a standardized interface for all Vapi operations.

## Key Benefits

- **More Reliable Connections**: Uses streamable HTTP instead of long-lived SSE connections
- **Standardized Interface**: Consistent way to interact with Vapi services
- **Simplified Implementation**: Replaces complex fallback mechanisms with standardized tool calls
- **One-Way Sync Pattern**: Clear data flow from UI → Supabase → Vapi

## Components

### Enhanced Vapi Assistant Manager

The Enhanced Vapi Assistant Manager handles the creation and management of Vapi assistants. It ensures that each attorney has a valid Vapi assistant ID and that the assistant configuration is synchronized with Supabase.

```javascript
import { enhancedVapiAssistantManager } from '../services/EnhancedVapiAssistantManager';

// Initialize the manager
await enhancedVapiAssistantManager.initialize({
  directApiKey: 'your-api-key',
  mcpUrl: 'https://mcp.vapi.ai/sse'
});

// Ensure an attorney has a valid assistant
const updatedAttorney = await enhancedVapiAssistantManager.ensureAssistant(attorney);

// Sync an attorney's assistant configuration
const syncResult = await enhancedVapiAssistantManager.syncAssistant(attorney);
```

### Enhanced Vapi MCP Service

The Enhanced Vapi MCP Service provides a client for interacting with the Vapi MCP server. It handles the connection to the MCP server and provides methods for making tool calls.

```javascript
import { enhancedVapiMcpService } from '../services/EnhancedVapiMcpService';

// Connect to the MCP server
await enhancedVapiMcpService.connect('your-api-key');

// Get an assistant by ID
const assistant = await enhancedVapiMcpService.getAssistant('assistant-id');

// Create a call
const call = await enhancedVapiMcpService.createCall('assistant-id', '+1234567890');
```

### Enhanced Sync Tools

The Enhanced Sync Tools provide utilities for synchronizing attorney profiles with Vapi. They handle the one-way sync pattern from UI → Supabase → Vapi.

```javascript
import { syncAttorneyProfile } from '../services/EnhancedSyncTools';

// Sync an attorney's profile
const syncResult = await syncAttorneyProfile({
  attorneyId: 'attorney-id',
  forceUpdate: true
});
```

### Enhanced Integration Utility

The Enhanced Integration Utility provides a simplified interface for integrating with Vapi. It handles the initialization of the Enhanced Vapi Assistant Manager and Enhanced Vapi MCP Service.

```javascript
import { initializeEnhancedVapi, ensureAttorneyAssistant } from '../utils/enhancedIntegration';

// Initialize the enhanced Vapi integration
await initializeEnhancedVapi();

// Ensure an attorney has a valid assistant
const updatedAttorney = await ensureAttorneyAssistant(attorney);
```

## Usage

### Dashboard Integration

The enhanced Vapi integration is integrated into the dashboard through the AgentTab component. It provides a toggle for enabling/disabling the enhanced components and a sync button for manually syncing the attorney's profile with Vapi.

```jsx
// In AgentTab.jsx
import EnhancedPreviewTab from './EnhancedPreviewTab';
import { syncAttorneyProfile } from '../../services/EnhancedSyncTools';

// State for enhanced components
const [useEnhancedComponents, setUseEnhancedComponents] = useState(true);

// Handle sync with enhanced components
const handleSync = async () => {
  if (!attorney || !attorney.id) {
    setError('No attorney ID available for synchronization');
    return;
  }
  
  try {
    setSyncStatus('syncing');
    
    const syncResult = await syncAttorneyProfile({
      attorneyId: attorney.id,
      forceUpdate: true
    });
    
    // Handle sync result
  } catch (error) {
    // Handle error
  }
};

// Render enhanced components
{useEnhancedComponents && (
  <div className="dashboard-card">
    <h3>Agent Preview</h3>
    <p className="card-description">
      Preview how your AI assistant will appear and function for potential clients.
    </p>
    
    <EnhancedPreviewTab
      attorney={attorney}
      isDarkTheme={isDarkTheme}
      onToggleTheme={() => handleThemeChange(isDarkTheme ? 'light' : 'dark')}
    />
  </div>
)}
```

### Preview Integration

The enhanced Vapi integration provides an EnhancedPreviewTab component that can be used to preview the attorney's AI assistant. It uses the EnhancedAgentPreview component to render the preview.

```jsx
// In EnhancedPreviewTab.jsx
import EnhancedAgentPreview from '../EnhancedAgentPreview';

// Render the preview
<EnhancedAgentPreview
  attorney={attorney}
  isDarkTheme={isDarkTheme}
  onError={handleError}
/>
```

## Configuration

The enhanced Vapi integration is configured through the MCP configuration file. It includes settings for the MCP server URL, API keys, and default values.

```javascript
// In mcp.config.js
export const mcpConfig = {
  voice: {
    vapi: {
      publicKey: 'your-public-key',
      secretKey: 'your-secret-key',
      mcpUrl: 'https://mcp.vapi.ai/sse',
      mcpProxyPath: '/vapi-mcp-server/sse',
      defaultVoice: 'sarah',
      defaultProvider: '11labs',
      defaultAssistantId: 'default-assistant-id'
    }
  },
  
  // Other configuration options
};
```

## Troubleshooting

### Common Issues

- **Connection Failures**: If the MCP server connection fails, the system will fall back to using the direct Vapi API. Check the console for error messages.
- **Missing Assistant ID**: If an attorney doesn't have a valid Vapi assistant ID, the system will create a new one. Check the console for messages about assistant creation.
- **Sync Failures**: If synchronization fails, check the console for error messages. The system will retry with exponential backoff.

### Debugging

The enhanced Vapi integration includes extensive logging. Check the browser console for messages with the following prefixes:

- `[EnhancedVapiAssistantManager]`: Messages from the Enhanced Vapi Assistant Manager
- `[EnhancedVapiMcpService]`: Messages from the Enhanced Vapi MCP Service
- `[EnhancedSyncTools]`: Messages from the Enhanced Sync Tools
- `[enhancedIntegration]`: Messages from the Enhanced Integration Utility
- `[AgentTab]`: Messages from the AgentTab component

## Best Practices

- **Always Save to Supabase First**: Always save data to Supabase before syncing with Vapi
- **Validate Data**: Validate data before saving to either system
- **Handle Errors**: Log errors and provide user feedback for sync failures
- **Use the Enhanced Components**: Use the enhanced components for improved reliability and performance
- **Sync Manually**: Use the sync button to manually sync the attorney's profile with Vapi if needed
