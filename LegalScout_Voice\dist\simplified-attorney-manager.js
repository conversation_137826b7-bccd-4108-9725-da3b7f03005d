/**
 * Simplified Attorney Manager
 * 
 * A streamlined implementation for attorney profile management with robust
 * validation and persistence.
 */

(function() {
  // Storage keys
  const STORAGE_KEYS = {
    ATTORNEY: 'attorney',
    ATTORNEY_ID: 'attorney_id',
    ATTORNEY_VERSION: 'attorney_version'
  };

  // Default attorney template
  const DEFAULT_ATTORNEY = {
    id: '',
    name: 'Attorney',
    email: '',
    firm_name: 'Law Firm',
    welcome_message: 'Hello, I\'m your legal assistant. How can I help you today?',
    vapi_instructions: 'You are a legal assistant. Help potential clients understand their legal needs and collect relevant information for consultation.',
    voice_provider: '11labs',
    voice_id: 'sarah',
    vapi_assistant_id: '',
    created_at: '',
    updated_at: ''
  };

  /**
   * Generate a UUID v4
   * @returns {string} A UUID v4 string
   */
  function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Validate if a string is a valid UUID
   * @param {string} id - The ID to validate
   * @returns {boolean} Whether the ID is a valid UUID
   */
  function isValidUUID(id) {
    if (!id) return false;
    
    // Check if it's a development ID
    if (typeof id === 'string' && id.startsWith('dev-')) {
      return true;
    }
    
    // Regular UUID validation
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
  }

  /**
   * Create a default attorney with optional overrides
   * @param {Object} overrides - Optional property overrides
   * @returns {Object} A default attorney object
   */
  function createDefaultAttorney(overrides = {}) {
    const now = new Date().toISOString();
    
    return {
      ...DEFAULT_ATTORNEY,
      id: generateUUID(),
      created_at: now,
      updated_at: now,
      ...overrides
    };
  }

  /**
   * Validate and enhance attorney data
   * @param {Object} attorneyData - The attorney data to validate
   * @returns {Object} Validated and enhanced attorney data
   */
  function validateAttorneyData(attorneyData) {
    console.log('[SimplifiedAttorneyManager] Validating attorney data');
    
    // If no data provided, create a default attorney
    if (!attorneyData) {
      console.warn('[SimplifiedAttorneyManager] No attorney data provided, creating default');
      return createDefaultAttorney();
    }
    
    // If ID is invalid, create a new attorney with the provided data
    if (!isValidUUID(attorneyData.id)) {
      console.warn('[SimplifiedAttorneyManager] Invalid attorney ID, creating new one with provided data');
      return createDefaultAttorney(attorneyData);
    }
    
    // Create a validated attorney by merging with defaults
    const validatedAttorney = {
      ...createDefaultAttorney(),
      ...attorneyData,
      updated_at: new Date().toISOString()
    };
    
    // Ensure ID doesn't change
    validatedAttorney.id = attorneyData.id;
    
    console.log('[SimplifiedAttorneyManager] Attorney data validated:', validatedAttorney.id);
    
    return validatedAttorney;
  }

  /**
   * Load attorney from localStorage with validation
   * @returns {Object|null} The attorney object or null if not found
   */
  function loadFromLocalStorage() {
    try {
      console.log('[SimplifiedAttorneyManager] Loading attorney from localStorage');
      
      // Try to get from localStorage
      const storedAttorney = localStorage.getItem(STORAGE_KEYS.ATTORNEY);
      if (!storedAttorney) {
        console.log('[SimplifiedAttorneyManager] No attorney found in localStorage');
        return null;
      }
      
      try {
        // Parse the stored attorney
        const parsedAttorney = JSON.parse(storedAttorney);
        
        // Validate the attorney data
        if (parsedAttorney && isValidUUID(parsedAttorney.id)) {
          console.log('[SimplifiedAttorneyManager] Loaded attorney from localStorage:', parsedAttorney.id);
          
          // Validate and enhance the attorney data
          return validateAttorneyData(parsedAttorney);
        } else {
          console.warn('[SimplifiedAttorneyManager] Invalid attorney data in localStorage');
          return null;
        }
      } catch (parseError) {
        console.error('[SimplifiedAttorneyManager] Error parsing attorney from localStorage:', parseError);
        return null;
      }
    } catch (error) {
      console.error('[SimplifiedAttorneyManager] Error loading from localStorage:', error);
      return null;
    }
  }

  /**
   * Save attorney to localStorage with validation
   * @param {Object} attorney - The attorney object to save
   * @returns {Object} The saved attorney object
   */
  function saveToLocalStorage(attorney) {
    try {
      // Validate the attorney before saving
      if (!attorney || !isValidUUID(attorney.id)) {
        console.warn('[SimplifiedAttorneyManager] Cannot save invalid attorney to localStorage');
        
        // Try to load existing attorney as fallback
        const existingAttorney = loadFromLocalStorage();
        if (existingAttorney) {
          console.log('[SimplifiedAttorneyManager] Using existing attorney as fallback');
          return existingAttorney;
        }
        
        // Create a default attorney if no existing one
        const defaultAttorney = createDefaultAttorney();
        console.log('[SimplifiedAttorneyManager] Created default attorney as fallback');
        
        // Save the default attorney
        try {
          localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(defaultAttorney));
          localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, defaultAttorney.id);
          localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());
        } catch (saveError) {
          console.error('[SimplifiedAttorneyManager] Error saving default attorney:', saveError);
        }
        
        return defaultAttorney;
      }
      
      // Validate the attorney data
      const validatedAttorney = validateAttorneyData(attorney);
      
      // Save full attorney object
      localStorage.setItem(STORAGE_KEYS.ATTORNEY, JSON.stringify(validatedAttorney));
      
      // Save ID separately for redundancy
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_ID, validatedAttorney.id);
      
      // Save version and timestamp
      localStorage.setItem(STORAGE_KEYS.ATTORNEY_VERSION, Date.now().toString());
      
      console.log('[SimplifiedAttorneyManager] Saved attorney to localStorage:', validatedAttorney.id);
      
      return validatedAttorney;
    } catch (error) {
      console.error('[SimplifiedAttorneyManager] Error saving to localStorage:', error);
      
      // Return the original attorney even if saving failed
      return attorney;
    }
  }

  /**
   * Get current attorney or create a default one
   * @returns {Object} The current attorney or a default one
   */
  function getCurrentAttorney() {
    // Try to load from localStorage
    const attorney = loadFromLocalStorage();
    
    // If no attorney found, create a default one
    if (!attorney) {
      const defaultAttorney = createDefaultAttorney();
      saveToLocalStorage(defaultAttorney);
      return defaultAttorney;
    }
    
    return attorney;
  }

  /**
   * Update attorney with new data
   * @param {Object} data - The data to update
   * @returns {Object} The updated attorney
   */
  function updateAttorney(data) {
    // Get current attorney
    const currentAttorney = getCurrentAttorney();
    
    // Merge with new data
    const updatedAttorney = {
      ...currentAttorney,
      ...data,
      updated_at: new Date().toISOString()
    };
    
    // Ensure ID doesn't change
    updatedAttorney.id = currentAttorney.id;
    
    // Save to localStorage
    return saveToLocalStorage(updatedAttorney);
  }

  // Create the simplified attorney manager
  const simplifiedAttorneyManager = {
    generateUUID,
    isValidUUID,
    createDefaultAttorney,
    validateAttorneyData,
    loadFromLocalStorage,
    saveToLocalStorage,
    getCurrentAttorney,
    updateAttorney
  };

  // Make it available globally
  window.simplifiedAttorneyManager = simplifiedAttorneyManager;
  
  // Also patch the existing attorney manager if it exists
  if (window.standaloneAttorneyManager) {
    console.log('[SimplifiedAttorneyManager] Patching existing standaloneAttorneyManager');
    
    // Add missing methods
    if (!window.standaloneAttorneyManager.validateAttorneyData) {
      window.standaloneAttorneyManager.validateAttorneyData = validateAttorneyData;
    }
    
    // Override problematic methods
    window.standaloneAttorneyManager.saveToLocalStorage = function(attorney) {
      return saveToLocalStorage(attorney);
    };
    
    window.standaloneAttorneyManager.loadFromLocalStorage = function() {
      return loadFromLocalStorage();
    };
  }
  
  console.log('[SimplifiedAttorneyManager] Initialized');
})();
